/*
 * Copyright 2014 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.infowarelab.tvbox.grafika;

import android.annotation.SuppressLint;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaFormat;
import android.os.Build;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.RequiresApi;

import com.infowarelab.tvbox.render.HWCodec;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Arrays;

/**
 * This class wraps up the core components used for surface-input video encoding.
 * <p>
 * Once created, frames are fed to the input surface.  Remember to provide the presentation
 * time stamp, and always call drainEncoder() before swapBuffers() to ensure that the
 * producer side doesn't get backed up.
 * <p>
 * This class is not thread-safe, with one exception: it is valid to use the input surface
 * on one thread, and drain the output on a different thread.
 */
public class VideoEncoderCore {
    private static final String TAG = "GLVideoEncodeViewEx";
    private static final boolean VERBOSE = false;

    // TODO: these ought to be configurable as well
    private static final String MIME_TYPE_H264 = "video/avc";    // H.264 Advanced Video Coding
    private static final String MIME_TYPE_H265 = "video/hevc";
    private static final int FRAME_RATE = 30;               // 30fps
    private static final int IFRAME_INTERVAL = 1;           // 5 seconds between I-frames
    private int mWidth = 0;
    private int mHeight = 0;
    private boolean useH265 = false;

    private final int H264_CODEC_TYPE = 1;
    private final int H265_CODEC_TYPE = 2;

    private Surface mInputSurface;
    //private MediaMuxer mMuxer;
    private MediaCodec mEncoder;
    private MediaCodec.BufferInfo mBufferInfo;
    //private int mTrackIndex;
    //private boolean mMuxerStarted;

    private byte[] mMediaHead = null;

    private static MediaCodecInfo getCodecInfo(final String mimeType) {
        final int numCodecs = MediaCodecList.getCodecCount();
        for (int i = 0; i < numCodecs; i++) {
            final MediaCodecInfo codecInfo = MediaCodecList.getCodecInfoAt(i);
            if (!codecInfo.isEncoder()) {
                continue;
            }
            final String[] types = codecInfo.getSupportedTypes();
            for (String type : types) {
                if (type.equalsIgnoreCase(mimeType)) {
                    Log.d(TAG, "codec info:" + codecInfo.getName()+" supportedTypes:" + type);
                    return codecInfo;
                }
            }
        }
        return null;
    }

    /**
     * Configures encoder and muxer state, and prepares the input Surface.
     */
    @SuppressLint("NewApi")
    public VideoEncoderCore(VideoCommonImpl videoCommon, int width, int height, int bitRate, File outputFile)
            throws IOException {

        if (getCodecInfo(MIME_TYPE_H265) == null) {
            useH265 = false;
        }

        if (!useH265) {
            if (getCodecInfo(MIME_TYPE_H264) == null) {
                Log.d(TAG, "VideoEncoderCore Error: H264 encoder cannot be support!");
                return;
            }
        }

        Log.d(TAG, "VideoEncoderCore: H265 encoder support: " + useH265);

        mBufferInfo = new MediaCodec.BufferInfo();

        MediaFormat format;

        if (useH265) {
            format = MediaFormat.createVideoFormat(MIME_TYPE_H265, width, height);
            if (videoCommon != null) videoCommon.setVideoCodeType(H265_CODEC_TYPE);
        }
        else {
            format = MediaFormat.createVideoFormat(MIME_TYPE_H264, width, height);
            if (videoCommon != null) videoCommon.setVideoCodeType(H264_CODEC_TYPE);
        }

        // Set some properties.  Failing to specify some of these can cause the MediaCodec
        // configure() call to throw an unhelpful exception.
        format.setInteger(MediaFormat.KEY_COLOR_FORMAT,
                MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
        format.setInteger(MediaFormat.KEY_BIT_RATE, bitRate);
        format.setInteger(MediaFormat.KEY_FRAME_RATE, FRAME_RATE);
        format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, IFRAME_INTERVAL);
        //format.setInteger(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR);
        //format.setInteger("vendor.rtc-ext-enc-low-latency.enable",1);

        //format.setInteger(MediaFormat.KEY_PRIORITY, 0);
        //format.setInteger(MediaFormat.KEY_LATENCY, 1);

        if (VERBOSE) Log.d(TAG, "format: " + format);

        // Create a MediaCodec encoder, and configure it with our format.  Get a Surface
        // we can use for input and wrap it with a class that handles the EGL work.
        if (useH265)
        {
            if (VERBOSE) Log.d(TAG, "===>VideoEncoderCore: H265 encode");
            mEncoder = MediaCodec.createEncoderByType(MIME_TYPE_H265);
        }
        else
        {
            if (VERBOSE) Log.d(TAG, "===>VideoEncoderCore: H264 encode");
            mEncoder = MediaCodec.createEncoderByType(MIME_TYPE_H264);
        }

        mEncoder.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        mInputSurface = mEncoder.createInputSurface();
        mEncoder.start();

        // Create a MediaMuxer.  We can't add the video track and start() the muxer here,
        // because our MediaFormat doesn't have the Magic Goodies.  These can only be
        // obtained from the encoder after it has started processing data.
        //
        // We're not actually interested in multiplexing audio.  We just want to convert
        // the raw H.264 elementary stream we get from MediaCodec into a .mp4 file.
//        mMuxer = new MediaMuxer(outputFile.toString(),
//                MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);

        //mTrackIndex = -1;
        //mMuxerStarted = false;
        mWidth = width;
        mHeight = height;
    }

    /**
     * Returns the encoder's input surface.
     */
    public Surface getInputSurface() {
        return mInputSurface;
    }

    /**
     * Releases encoder resources.
     */
    public void release() {
        if (VERBOSE) Log.d(TAG, "releasing encoder objects");

        try {
            if (mEncoder != null) {
                mEncoder.stop();
                mEncoder.release();
                mEncoder = null;
            }
        }
        catch (java.lang.IllegalStateException exception){
            Log.d(TAG, "Encoder release exception: " + exception.getMessage());
            mEncoder = null;
        }

//        if (mMuxer != null) {
//            // TODO: stop() throws an exception if you haven't fed it any data.  Keep track
//            //       of frames submitted, and don't call stop() if we haven't written anything.
//            mMuxer.stop();
//            mMuxer.release();
//            mMuxer = null;
//        }
    }

    public static byte[] splicingArrays(byte[]... bytes) {
        int length = 0;
        for (byte[] b : bytes) {
            length += b.length;
        }
        int interimLength = 0;
        byte[] result = new byte[length];
        for (byte[] b : bytes) {
            System.arraycopy(b, 0, result, interimLength, b.length);
            interimLength += b.length;
        }
        return result;
    }

    public String byteTo16(byte bt){
        String[] strHex={"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"};
        String resStr="";
        int low =(bt & 15);
        int high = bt>>4 & 15;
        resStr = strHex[high]+strHex[low];
        return resStr;
    }


    /**
     * Extracts all pending data from the encoder and forwards it to the muxer.
     * <p>
     * If endOfStream is not set, this returns when there is no more data to drain.  If it
     * is set, we send EOS to the encoder, and then iterate until we see EOS on the output.
     * Calling this with endOfStream set should be done once, right before stopping the muxer.
     * <p>
     * We're just using the muxer to get a .mp4 file (instead of a raw H.264 stream).  We're
     * not recording audio.
     */
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    public void drainEncoder(VideoCommonImpl videoCommon, ShareDtCommonImpl shareDtCommon, boolean endOfStream, boolean share) {
        final int TIMEOUT_USEC = 0;
        if (VERBOSE) Log.d(TAG, "drainEncoder(" + endOfStream + ")");

        try {
            if (endOfStream) {
                if (VERBOSE) Log.d(TAG, "sending EOS to encoder");
                mEncoder.signalEndOfInputStream();
            }


            ByteBuffer[] encoderOutputBuffers = mEncoder.getOutputBuffers();
            while (true) {
                int encoderStatus = mEncoder.dequeueOutputBuffer(mBufferInfo, TIMEOUT_USEC);
                if (encoderStatus == MediaCodec.INFO_TRY_AGAIN_LATER) {
                    // no output available yet
                    if (!endOfStream) {
                        break;      // out of while
                    } else {
                        if (VERBOSE) Log.d(TAG, "no output available, spinning to await EOS");
                    }
                } else if (encoderStatus == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {
                    // not expected for an encoder
                    encoderOutputBuffers = mEncoder.getOutputBuffers();
                } else if (encoderStatus == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                    // should happen before receiving buffers, and should only happen once
                    //                if (mMuxerStarted) {
                    //                    throw new RuntimeException("format changed twice");
                    //                }
                    MediaFormat newFormat = mEncoder.getOutputFormat();
                    Log.d(TAG, "encoder output format changed: " + newFormat);

                    if (!useH265) {
                        //Log.d(TAG, "drainEncoder MediaCodec.INFO_OUTPUT_FORMAT_CHANGED");
                        ByteBuffer spsb = mEncoder.getOutputFormat().getByteBuffer("csd-0");
                        ByteBuffer ppsb = mEncoder.getOutputFormat().getByteBuffer("csd-1");

                        byte[] sps = new byte[spsb.remaining()];
                        spsb.get(sps, 0, sps.length);

                        byte[] pps = new byte[ppsb.remaining()];
                        ppsb.get(pps, 0, pps.length);

                        mMediaHead = splicingArrays(sps, pps);
                    } else {
                        searchVpsSpsPpsFromH265(mEncoder.getOutputFormat().getByteBuffer("csd-0"));
                    }

                    // now that we have the Magic Goodies, start the muxer
                    //mTrackIndex = mMuxer.addTrack(newFormat);
                    //mMuxer.start();
                    //mMuxerStarted = true;
                } else if (encoderStatus < 0) {
                    Log.w(TAG, "unexpected result from encoder.dequeueOutputBuffer: " +
                            encoderStatus);
                    // let's ignore it
                } else {
                    ByteBuffer encodedData = encoderOutputBuffers[encoderStatus];
                    if (encodedData == null) {
                        throw new RuntimeException("encoderOutputBuffer " + encoderStatus +
                                " was null");
                    }

                    if ((mBufferInfo.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) {
                        // The codec config data was pulled out and fed to the muxer when we got
                        // the INFO_OUTPUT_FORMAT_CHANGED status.  Ignore it.
                        if (VERBOSE) Log.d(TAG, "ignoring BUFFER_FLAG_CODEC_CONFIG");
                        mBufferInfo.size = 0;
                    }

                    if (mBufferInfo.size != 0) {
                        //                    if (!mMuxerStarted) {
                        //                        throw new RuntimeException("muxer hasn't started");
                        //                    }

                        // adjust the ByteBuffer values to match BufferInfo (not needed?)
                        encodedData.position(mBufferInfo.offset);
                        encodedData.limit(mBufferInfo.offset + mBufferInfo.size);

                        //mMuxer.writeSampleData(mTrackIndex, encodedData, mBufferInfo);

                        //                    if (VERBOSE) {
                        //                        if (useH265)
                        //                            Log.d(TAG, "===> 4K H265 encoded " + mBufferInfo.size + " bytes, ts=" +
                        //                                    mBufferInfo.presentationTimeUs);
                        //                        else
                        //                            Log.d(TAG, "===> 4K H264 encoded " + mBufferInfo.size + " bytes, ts=" +
                        //                                mBufferInfo.presentationTimeUs);
                        //                    }

                        if (share)
                            sendEncodedFrame(videoCommon, shareDtCommon, encodedData, mBufferInfo);
                    }

                    mEncoder.releaseOutputBuffer(encoderStatus, false);

                    if ((mBufferInfo.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                        if (!endOfStream) {
                            Log.w(TAG, "reached end of stream unexpectedly");
                        } else {
                            if (VERBOSE) Log.d(TAG, "end of stream reached");
                        }
                        break;      // out of while
                    }
                }
            }
        } catch (java.lang.IllegalStateException exception) {
            Log.d(TAG, "drainEncoder IllegalStateException Error:" + exception.getMessage());
        }
    }


    private void sendEncodedFrame(VideoCommonImpl videoCommon, ShareDtCommonImpl shareDtCommon, ByteBuffer encodedData, MediaCodec.BufferInfo bufferInfo) {
        byte[] data = new byte[bufferInfo.size];  //delete []data;

        encodedData.get(data);

        if (mMediaHead != null) {

            int length = bufferInfo.size;

            boolean keyFrame = false;

            if (!useH265)
                keyFrame = isKeyFrame_H264(data);
            else
                keyFrame = isKeyFrame_H265(data);

            if (VERBOSE && length > 21) {

                String showInfo = "";

                if (keyFrame) {
                    showInfo = "encodeFrame =>!Key frame length = " + length + "; ";

                } else {
                    showInfo = "encodeFrame =>P frame length = " + length + "; ";
                }

                for (int count = 0; count <= 21; count++) {

                    if (keyFrame) {
                        //showInfo = "encodeFrame =>!Key frame length = " + length + "; ";
                        showInfo += byteTo16(data[count]);
                        if (count < 21) showInfo += ",";

                    } else {
                        //showInfo = "encodeFrame =>P frame length = " + length + "; ";
                        showInfo += byteTo16(data[count]);
                        if (count < 21) showInfo += ",";
                    }
                }

                Log.d(TAG, showInfo);
            }

            if (keyFrame) { //IDR frame

                if (!useH265)
                    //int len;
                    mMediaHead[4] = (byte) (mMediaHead[4] | (3 << 5));

                if (videoCommon != null) {
                    if (VERBOSE) {

                        if (!useH265)
                            Log.d("InfowareLab.Encoder", "(Video)I Frame PPS.SPS length: " + mMediaHead.length);
                        else
                            Log.d("InfowareLab.Encoder", "(Video)I Frame APS.PPS.SPS length: " + mMediaHead.length);

                        Log.d("InfowareLab.Encoder", "(Video)I Frame APS.PPS.SPS: " + Arrays.toString(mMediaHead));
                    }
                    if (videoCommon != null)
                        videoCommon.sendMyVideoData(mMediaHead, mMediaHead.length, true, this.mWidth, this.mHeight, true);
                } else {
                    if (VERBOSE) {
                        if (!useH265)
                            Log.d("InfowareLab.Encoder", "(Video)I Frame PPS.SPS length: " + mMediaHead.length);
                        else
                            Log.d("InfowareLab.Encoder", "(Video)I Frame APS.PPS.SPS length: " + mMediaHead.length);
                        Log.d("InfowareLab.Encoder", "(Video)I Frame APS.PPS.SPS: " + Arrays.toString(mMediaHead));
                    }
                    if (shareDtCommon != null)
                        shareDtCommon.sendScreenData(this.mWidth, this.mHeight, 24, mMediaHead, mMediaHead.length, true, true);
                }

                //if (!useH265)
                //    data[4] = (byte) (data[4] | (3 << 5)); //nal_ref_idc = 3,IDR frame high priority
                //len = data.length;

                if (videoCommon != null) {
                    if (VERBOSE) {
                        Log.d("InfowareLab.Encoder", "(Video)I Frame length: " + data.length);
                        Log.d("InfowareLab.Encoder", "(Video)I Frame resolution: " + mWidth + "x" + mHeight);
                    }
                    videoCommon.sendMyVideoData(data, data.length, true, this.mWidth, this.mHeight, true);
                } else {
                    if (VERBOSE) {
                        Log.d("InfowareLab.Encoder", "(ShareDt)I Frame length: " + data.length);
                        Log.d("InfowareLab.Encoder", "(ShareDt)I Frame resolution: " + mWidth + "x" + mHeight);
                    }
                    if (shareDtCommon != null)
                        shareDtCommon.sendScreenData(this.mWidth, this.mHeight, 24, data, data.length, true, true);
                }

            } else { //P frame

                if (videoCommon != null) {

                    if (VERBOSE) {
                        Log.d("InfowareLab.Encoder", "(Video)P Frame length: " + data.length);
                        Log.d("InfowareLab.Encoder", "(Video)P Frame resolution: " + mWidth + "x" + mHeight);
                    }

                    videoCommon.sendMyVideoData(data, data.length, false, this.mWidth, this.mHeight, true);
                } else {
                    if (VERBOSE) {
                        Log.d("InfowareLab.Encoder", "(ShareDt)P Frame length: " + data.length);
                        Log.d("InfowareLab.Encoder", "(ShareDt)P Frame resolution: " + mWidth + "x" + mHeight);
                    }

                    if (shareDtCommon != null)
                        shareDtCommon.sendScreenData(this.mWidth, this.mHeight, 24, data, data.length, true, false);
                }
            }
        }
    }

    private boolean isKeyFrame_H264(byte[] buffer) {

        if (buffer.length < 5) {
            return false;
        }

        //00 00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 0
                && buffer[3] == 1) {
            int nalType = buffer[4] & 0x1f;

            if (Build.VERSION.SDK_INT == 31) {
                if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08 || nalType == 0x06) {
                    //Log.d(TAG, ">>>>>>isKeyFrame: nalType = " + nalType);
                    return true;
                }
            }
            else
            {
                if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                    //Log.d(TAG, ">>>>>>isKeyFrame: nalType = " + nalType);
                    return true;
                }
            }
        }

        //00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 1) {
            int nalType = buffer[3] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                //Log.d(TAG, ">>>>>>isKeyFrame: nalType = " + nalType);
                return true;
            }
        }

        return false;
    }

    public boolean isKeyFrame_H265(byte[] buffer)
    {
        int i;
        int type;

        for (i = 2; i + 1 < buffer.length; i++)
        {
            if (0x01 == buffer[i] && 0x00 == buffer[i - 1] && 0x00 == buffer[i - 2])
            {
                type = (buffer[i + 1] >> 1) & 0x3f;
                if (type < 32)
                    return (16 <= type && type <= 23) ? true : false;
            }
        }

        return false;
    }

    final protected static char[] hexArray = "0123456789ABCDEF".toCharArray();
    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for ( int j = 0; j < bytes.length; j++ ) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    //查找sps pps vps
    public void searchSPSandPPSFromH264(ByteBuffer buffer, MediaCodec.BufferInfo bufferInfo){

        byte[] csd = new byte[128];
        int len = 0, p = 4, q = 4;

        len = bufferInfo.size;
        Log.d(TAG,"len="+len);
        if (len<128) {
            buffer.get(csd,0,len);
            if (len>0 && csd[0]==0 && csd[1]==0 && csd[2]==0 && csd[3]==1) {
                // Parses the SPS and PPS, they could be in two different packets and in a different order
                //depending on the phone so we don't make any assumption about that
                while (p<len) {
                    while (!(csd[p+0]==0 && csd[p+1]==0 && csd[p+2]==0 && csd[p+3]==1) && p+3<len) p++;
                    if (p+3>=len) p=len;
                    if ((csd[q]&0x1F)==7) {
                        byte[] sps = new byte[p-q];
                        System.arraycopy(csd, q, sps, 0, p-q);
                        Log.d(TAG,"searchSPSandPPSFromH264 SPS="+bytesToHex(sps));
                        //chris, searchSPSandPPSFromH264 SPS=6764001FACB402802DD2905020206D0A1350
                    } else {
                        byte[] pps = new byte[p-q];
                        System.arraycopy(csd, q, pps, 0, p-q);
                        Log.d(TAG,"searchSPSandPPSFromH264 PPS="+bytesToHex(pps));
                        //chris, searchSPSandPPSFromH264 PPS=68EE06E2C0
                    }
                    p += 4;
                    q = p;
                }
            }
        }
    }

    public void searchVpsSpsPpsFromH265(ByteBuffer csd0byteBuffer) {
        int vpsPosition = -1;
        int spsPosition = -1;
        int ppsPosition = -1;
        int contBufferInitiation = 0;
        byte[] csdArray = csd0byteBuffer.array();
        for (int i = 0; i < csdArray.length; i++) {
            if (contBufferInitiation == 3 && csdArray[i] == 1) {
                if (vpsPosition == -1) {
                    vpsPosition = i - 3;
                } else if (spsPosition == -1) {
                    spsPosition = i - 3;
                } else {
                    ppsPosition = i - 3;
                }
            }
            if (csdArray[i] == 0) {
                contBufferInitiation++;
            } else {
                contBufferInitiation = 0;
            }
        }
        byte[] vps = new byte[spsPosition];
        byte[] sps = new byte[ppsPosition - spsPosition];
        byte[] pps = new byte[csdArray.length - ppsPosition];
        for (int i = 0; i < csdArray.length; i++) {
            if (i < spsPosition) {
                vps[i] = csdArray[i];
            } else if (i < ppsPosition) {
                sps[i - spsPosition] = csdArray[i];
            } else {
                pps[i - ppsPosition] = csdArray[i];
            }
        }

        Log.d(TAG, "===> searchVpsSpsPpsFromH265: vps="+ bytesToHex(vps)+",sps="+bytesToHex(sps)+",pps="+bytesToHex(pps));

        mMediaHead = splicingArrays(vps, sps, pps);

        //vps=0000000140010C01FFFF016000000300B0000003000003005DAC59,sps=00000001420101016000000300B0000003000003005DA00280802E1F1396BB9324BB948281010176850940,pps=000000014401C0F1800420
    }
}
