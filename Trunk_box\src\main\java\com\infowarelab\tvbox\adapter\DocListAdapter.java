package com.infowarelab.tvbox.adapter;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.base.BaseViewAdapter;
import com.infowarelab.tvbox.base.BaseViewHolder;
import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.FileUtils;
import com.infowarelab.tvbox.view.SquareLinearLayout;
import com.infowarelabsdk.conference.domain.DocBean;

import java.util.List;

/**
 * Created by xiaor on 2019/12/28.
 * <AUTHOR>
 * 分享文档列表适配器
 */

public class DocListAdapter extends BaseViewAdapter<DocBean>{

    private OnSelectListener onSelectListener;
    private int mGridViewRows;

    public DocListAdapter(Context context) {
        super(context);
    }

    @Override
    public void setDatas(List<DocBean> datas) {
        super.setDatas(datas);
        setRows();
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        if (null == convertView) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_doc_list, null);
        }
        SquareLinearLayout mainLayout = BaseViewHolder.get(convertView,R.id.item_docList_mainLayout);
        ImageView image = BaseViewHolder.get(convertView,R.id.item_docList_image);
        TextView titleText = BaseViewHolder.get(convertView,R.id.item_docList_titleText);

        final DocBean bean = getDatas().get(position);
        titleText.setText(bean.getTitle());
        if (FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("doc")){
            //word
            image.setImageResource(R.drawable.word);
        }else if (FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("ppt")){
            //ppt
            image.setImageResource(R.drawable.ppt);
        }else if (FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("xls") ||
                FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("csv")){
            //excel
            image.setImageResource(R.drawable.excel);
        }else if (FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("pdf")){
            //pdf
            image.setImageResource(R.drawable.pdf);
        }else if (FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("zip")
                || FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("rar")){
            //压缩文件
            image.setImageResource(R.drawable.zip);
        }else if (FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("txt")){
            image.setImageResource(R.drawable.txt);
        }else if (FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("jpg")){
            image.setImageResource(R.drawable.jpg);
        }else if (FileUtils.getExtensionName(bean.getTitle()).toLowerCase().contains("png")){
            image.setImageResource(R.drawable.png);
        }else if (bean.getTitle().contains("白板")){
            image.setImageResource(R.drawable.board);
        }else {
            //未知
            image.setImageResource(R.drawable.unknown);
        }
        mainLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.e("YYYYYY","666666666666");
                onSelectListener.doSelect(position);
            }
        });
        return convertView;
    }

    public interface OnSelectListener {
        void doSelect(int position);
    }
    public void setOnSelectListener(OnSelectListener onSelectListener) {
        this.onSelectListener = onSelectListener;
    }
    public void doSelect(int position) {
        if (onSelectListener != null) {
            onSelectListener.doSelect(position);
        }
    }
    public void update(List<DocBean> data){
        setDatas(data);
        setRows();
        notifyDataSetChanged();
    }
    public void setRows() {
        int remainder = getDatas().size() % 4;
        mGridViewRows =(remainder == 0)? (this.getDatas().size() > 0 ?  this.getDatas().size() / 4:0) : (getDatas().size() / 4) +1;
    }
    public int getNumRows() {
        return mGridViewRows;
    }

    public int getNumColumns(){
        return 4;
    }
}
