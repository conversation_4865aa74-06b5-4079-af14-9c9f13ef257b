package com.infowarelab.tvbox.activity;


import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import androidx.fragment.app.FragmentActivity;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.Toast;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.view.LodingDialog;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.util.Constants;
import com.readystatesoftware.systembartint.SystemBarTintManager;

public abstract class BaseFragmentActivity extends FragmentActivity {

	protected LodingDialog loadingDialog;

	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
			//透明状态栏
			getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
			//透明导航栏
			getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
			SystemBarTintManager tintManager = new SystemBarTintManager(this);
			// 激活状态栏
			tintManager.setStatusBarTintEnabled(true);
			// enable navigation bar tint 激活导航栏
			tintManager.setNavigationBarTintEnabled(true);
			//设置系统栏设置颜色
			//tintManager.setTintColor(R.color.red);
			//给状态栏设置颜色
			tintManager.setStatusBarTintResource(R.color.transparent);
			//Apply the specified drawable or color resource to the system navigation bar.
			//给导航栏设置资源
			tintManager.setNavigationBarTintResource(R.color.transparent);
		}
	}
	@Override
	protected void onPause() {
		// TODO Auto-generated method stub
		super.onPause();
	}

	@Override
	protected void onResume() {
		// TODO Auto-generated method stub
		super.onResume();
	}

	/**
	 * 注销
	 */
	public void logout() {
		SharedPreferences preferences = getSharedPreferences(
				Constants.SHARED_PREFERENCES, Context.MODE_WORLD_READABLE);
		preferences.edit().putString(Constants.LOGIN_NAME, "").commit();
		preferences.edit().putString(Constants.LOGIN_NICKNAME, "").commit();
		preferences.edit().putString(Constants.LOGIN_ROLE, "").commit();
		preferences.edit().putInt(Constants.USER_ID, 0).commit();
		preferences.edit().putString(Constants.LOGIN_ROLE, "").commit();
		CommonFactory.getInstance().getConferenceCommon().logout();
	}

	/**
	 * 弹出输入法
	 * 
	 * @param v
	 */
	public void showInput(View v) {
		((InputMethodManager) getSystemService(INPUT_METHOD_SERVICE))
				.showSoftInput(v, 0);
	}

	public void setFocus(View v) {
		((EditText) v).requestFocus();
		if (((EditText) v).getText() != null) {
			((EditText) v).setSelection(((EditText) v).getText().toString()
					.length());
		}
	}

	public void showLongToast(int resId) {
		Toast.makeText(getApplicationContext(), resId, Toast.LENGTH_LONG)
				.show();
	}

	public void showShortToast(int resId) {
		Toast.makeText(getApplicationContext(), resId, Toast.LENGTH_SHORT)
				.show();
	}

	public void showLoading() {

//		if (loadingDialog == null) {
//			loadingDialog = new LodingDialog(this);
//		}

		if (loadingDialog != null && !loadingDialog.isShowing()) {
			loadingDialog.show();
		}
	}

	public void showLoading(String title) {

//		if (loadingDialog == null) {
//			loadingDialog = new LodingDialog(this);
//		}

		loadingDialog.setTitle(title);

		if (loadingDialog != null && !loadingDialog.isShowing()) {
			loadingDialog.show();
		}
	}
//
	public void hideLoading() {
		if (loadingDialog != null && loadingDialog.isShowing()) {
			loadingDialog.dismiss();
			loadingDialog = null;
		}
	}

}
