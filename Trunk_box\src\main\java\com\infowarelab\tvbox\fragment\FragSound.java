package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.Spinner;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;

@SuppressLint("ValidFragment")
public class FragSound extends BaseFragment{

    private View rootView;
    private LinearLayout backLayout;
    private Spinner spinner;
    private SeekBar sbSound;
    private Button button;
    private AudioManager audioManager;
    private MediaPlayer mediaPlayer;

    //调节音量的大小
    private int maxVolume = 50; // 最大音量值
    private int curVolume = 20; // 当前音量值
    private int stepVolume = 0; // 每次调整的音量幅度
    private AudioManager audioMgr = null; // Audio管理器，用了控制音量
    //与Activity交互
    private FragmentInteraction listterner;

    //控制焦点
    private int Tag = 0;
    private LinearLayout llCancel;

    public FragSound(ICallParentView iCallParentView) {
        super(iCallParentView);
    }

    public FragSound(){
        super();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.frag_sound, container, false);
        return rootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initView();
    }

    private void initView(){
        backLayout = (LinearLayout)rootView.findViewById(R.id.frag_sound_backLayout);
        audioMgr = (AudioManager)getActivity().getSystemService(Context.AUDIO_SERVICE);
        audioMgr.setMode(AudioManager.STREAM_MUSIC);
        // 获取最大音乐音量
        maxVolume = audioMgr.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        // 初始化音量大概为最大音量的1/2
        curVolume = SharedPreferencesUrls.getInstance().getInt("progress",maxVolume * 4/5);
        // 每次调整的音量大概为最大音量的1/6
        stepVolume = maxVolume / 6;

        SharedPreferences sharedPreferences = getActivity().getSharedPreferences("sound", Context.MODE_PRIVATE);
        final SharedPreferences.Editor editor = sharedPreferences.edit();
        audioManager = (AudioManager)getActivity().getSystemService(Context.AUDIO_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB){
            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
        } else {
            audioManager.setMode(AudioManager.MODE_IN_CALL);
        }
        audioManager.setSpeakerphoneOn(SharedPreferencesUrls.getInstance().getBoolean("speakerphoneon",false));//false走usb麦，true走hdmi
        audioManager.setStreamVolume(AudioManager.STREAM_SYSTEM, 5, AudioManager.FLAG_SHOW_UI);
        sbSound = (SeekBar)rootView.findViewById(R.id.sb_sound);
        sbSound.setMax(maxVolume);
        sbSound.setProgress(curVolume);

        adjustVolume();
        spinner = (Spinner)rootView.findViewById(R.id.sp_sound);
        String[] items = {"全向麦", "电视机"};
        //初始化时焦点给Seek
        initFocus();
        ArrayAdapter<String> stringArrayAdapter = new ArrayAdapter<>(getContext(), R.layout.item_select, items);
        stringArrayAdapter.setDropDownViewResource(R.layout.item_drop);
        spinner.setAdapter(stringArrayAdapter);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                editor.putInt("position", position);
                editor.commit();
                switch (position) {
                    case 0:
//                        audioManager.setSpeakerphoneOn(false);
                        listterner.allMak();
//                        SharedPreferencesUrls.getInstance().putBoolean("speakerphoneon",false);
                        break;
                    case 1:
//                        audioManager.setSpeakerphoneOn(true);
                        listterner.TVMak();
//                        SharedPreferencesUrls.getInstance().putBoolean("speakerphoneon",true);
                        break;
                }
            }

            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                audioManager.setSpeakerphoneOn(false);
                listterner.allMak();
            }
        });
        spinner.setSelection(sharedPreferences.getInt("position", 0));
        button = (Button)rootView.findViewById(R.id.btn_sounds);
        backLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (ActConf.mActivity != null){
                    ActConf.mActivity.onBackPressed();
                }
            }
        });
        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startMediaPlay();
                button.setEnabled(false);
            }
        });

        sbSound.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                curVolume = progress;
                SharedPreferencesUrls.getInstance().putInt("progress",curVolume);
                adjustVolume();
            }
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });

        llCancel = (LinearLayout) rootView.findViewById(R.id.ll_frag_set_cancel);

        llCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                callParentView(ACTION_RETURN, null);
            }
        });
    }
    /**
     * 重置播放器
     * */
    public void resetMediaPlay(){
        if (button != null){
            button.setEnabled(true);
        }
        if (mediaPlayer != null && mediaPlayer.isPlaying()){
            mediaPlayer.stop();
            mediaPlayer.release();
            mediaPlayer = null;
        }
    }
    /**
     * 播放音频
     * */
    private void startMediaPlay(){
        //播放器
        mediaPlayer = MediaPlayer.create(getContext(), R.raw.wave_cn);
        mediaPlayer.start();
        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                resetMediaPlay();
                button.setEnabled(true);
            }
        });
    }
    public void add(){
       addVolume();
    }
    public void subtraction(){
        reduceVolume();
    }
    public void back(){
        SharedPreferencesUrls.getInstance().putInt("progress",curVolume);
    }
    @Override
    public void onDestroyView() {
        resetMediaPlay();
        super.onDestroyView();
    }
    @Override
    public void onDestroy() {
        resetMediaPlay();
        super.onDestroy();
    }
    /**
     * 调整音量
     */
    private void adjustVolume() {
        audioMgr.setStreamVolume(AudioManager.STREAM_MUSIC, curVolume,
                AudioManager.FLAG_PLAY_SOUND);
    }
    //增加音量
    private void addVolume(){
        curVolume += stepVolume;
        if (curVolume >= maxVolume) {
            curVolume = maxVolume;
        }
        sbSound.setProgress(curVolume);
        adjustVolume();
    }
    //降低音量
    private void reduceVolume(){
        curVolume -= stepVolume;
        if (curVolume <= 0) {
            curVolume = 0;
        }
        sbSound.setProgress(curVolume);
        adjustVolume();
    }

    //初始化焦点
    public void
    initFocus(){
        sbSound.setFocusable(true);
        sbSound.requestFocus();
    }

    /**
     * 当FRagmen被加载到activity的时候会被回调
     *
     * @param activity
     */
    /**
     * 定义了所有activity必须实现的接口
     */
    public interface FragmentInteraction {
        //全向麦
        void allMak();
        //打开麦克风
        void TVMak();
    }
    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity instanceof FragMenu.FragmentInteraction) {
            listterner = (FragmentInteraction) activity;
        } else {
            throw new IllegalArgumentException("activity must implements FragmentInteraction");
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        listterner = null;
    }
}
