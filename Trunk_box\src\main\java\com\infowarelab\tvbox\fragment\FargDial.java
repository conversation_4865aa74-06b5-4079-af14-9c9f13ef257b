package com.infowarelab.tvbox.fragment;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.error.ErrorMessage;
import com.infowarelab.tvbox.utils.XMLUtils;
import com.infowarelab.tvbox.view.JoinDialog;
import com.infowarelab.tvbox.view.LoadingDialog;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.confctrl.ConferenceCommon;
import com.infowarelabsdk.conference.domain.ConferenceBean;
import com.infowarelabsdk.conference.domain.ConfigBean;
import com.infowarelabsdk.conference.domain.LoginBean;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.StringUtil;
import com.infowarelabsdk.conference.util.ToastUtil;

////import org.apache.log4j.Logger;

public class FargDial extends BaseFragment implements View.OnClickListener {

    //protected Logger log = Logger.getLogger(getClass());

    public static final int FINISH = 4;
    public static final int LOGINFAILED = 6;
    public static final int MEETINGINVALIDATE = 7;
    public static final int MEETINGNOTJOINBEFORE = 8;
    public static final int HOSTERROR = 9;
    public static final int SPELLERROR = 10;
    public static final int GET_ERROR_MESSAGE = 11;
    public static final int JOIN_CONFERENCE = 12;
    public static final int CREATECONF_ERROR = 15;
    public static final int READY_JOINCONF = 16;
    public static final int NEED_LOGIN = 1001;
    public static final int NO_CONFERENCE = 1002;
    protected static final int INIT_SDK_FAILED = 102;
    protected static final int CONF_CONFLICT = -5;
    //需要密码
    protected static final int NEED_PASSWORD = 10086;

    private View rootView;
    private Context mContet;
    private EditText numEdit;
    private LinearLayout confirm;

    private CommonFactory commonFactory = CommonFactory.getInstance();
    private ConferenceCommonImpl conferenceCommon;

    private SharedPreferences preferences;

    private LoginBean loginBean;
    private Config config;
    private JoinDialog joinDialog;

    private String result = "";

    private LoadingDialog loadingDialog;


    private Handler listHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case NEED_LOGIN:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_1);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case NEED_PASSWORD:
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    ConferenceBean conferenceBean = new ConferenceBean();
                    conferenceBean.setId(numEdit.getText().toString());
                    conferenceBean.setName("非公开的会议");
                    XMLUtils.CONFIGID = conferenceBean.getId();
                    XMLUtils.CONFIGNAME = conferenceBean.getName();
                    XMLUtils.CONFERENCEPATTERN = 0;
                    showJoinDialog(conferenceBean);
                    break;
                case JOIN_CONFERENCE:
                    conferenceCommon.setLogPath(ConferenceApplication.getConferenceApp().getFilePath("hslog"));
                    commonFactory.getConferenceCommon().initSDK();
                    joinConference();
                    break;
                case NO_CONFERENCE:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_3);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case GET_ERROR_MESSAGE:
                    hideLoading();
                    showShortToast(config.getConfigBean().getErrorMessage());
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case MEETINGNOTJOINBEFORE:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_7);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case HOSTERROR:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_13);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case SPELLERROR:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_8);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case LOGINFAILED:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_9);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case CREATECONF_ERROR:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_14);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case MEETINGINVALIDATE:
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_2);
                    break;
                case READY_JOINCONF:
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case FINISH:
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                default:
                    break;
            }
        }
    };
    private String mConfId = null;

    public FargDial(ICallParentView iCallParentView) {
        super(iCallParentView);
    }
    public FargDial() {
        super();
    }
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        rootView = inflater.inflate(R.layout.frag_home_dial, container, false);
        return rootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mContet = getContext();
        initView();
    }

    private void initView(){

        loadingDialog = new LoadingDialog(getContext());
        loadingDialog.setCancelable(false);
        loadingDialog.setCanceledOnTouchOutside(false);

        loadingDialog.setTitle("正在加会");

        preferences = getActivity().getSharedPreferences(Constants.SHARED_PREFERENCES,
                Context.MODE_WORLD_READABLE);

        numEdit = (EditText) rootView.findViewById(R.id.et_frag_dial_numEdit);
        confirm = (LinearLayout)rootView.findViewById(R.id.ll_frag_dial_confirm);

        confirm.setOnClickListener(this);

        conferenceCommon = (ConferenceCommonImpl) commonFactory.getConferenceCommon();
        if (confHandler != null && conferenceCommon != null) {
            if (conferenceCommon.getHandler() != confHandler) {
                conferenceCommon.setHandler(confHandler);
            }
        }
    }
    public Handler confHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case ConferenceCommon.RESULT_SUCCESS:

                    if (getActivity() == null) break;

//                    //通知是box
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    Intent intent = new Intent(getActivity(), ActConf.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                    if (mConfId != null){
                        intent.putExtra("confId", mConfId);
                    }
                    getActivity().startActivity(intent);
                    getActivity().finish();
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (loadingDialog != null && loadingDialog.isShowing()){
                                loadingDialog.dismiss();
                            }
                        }
                    }, 5 * 1000);
                    break;
                case ConferenceCommon.BEYOUNGMAXCOUNT:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    post(new Runnable() {
                        @Override
                        public void run() {
                            showShortToast(R.string.item_meetings_err_5);
                        }
                    });
                    break;
                case ConferenceCommon.BEYOUNGJIAMI:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    post(new Runnable() {
                        @Override
                        public void run() {
                            showShortToast(R.string.item_meetings_err_6);
                        }
                    });
                    break;
                case INIT_SDK_FAILED:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_15);
                    break;
                case CONF_CONFLICT:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_9);
                    break;
                case ConferenceCommon.LEAVE:
                    break;
                case ConferenceCommon.LICENSE_ERR:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast("终端授权点数不足，请联系管理员处理!");
                    break;
                default:
                    ErrorMessage errorMessage = new ErrorMessage(getActivity());
                    String message = errorMessage.getErrorMessageByCode(msg.what);
//                    Toast.makeText(getActivity(), message, Toast.LENGTH_LONG).show();
                    hideLoading();
                    break;
            }
        }

    };

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.ll_frag_dial_confirm:
                String num = numEdit.getText().toString().trim();
                if (num == null || "".equals(num)){
                    ToastUtil.showMessage(mContet,"会议号不能为空",2000);
                    return;
                }
                if (!StringUtil.checkInput(num, Constants.PATTERN)){
                    ToastUtil.showMessage(mContet,"用户名含有非法字符,请输入数字、字母或者下划线",2000);
                    return;
                }

                mConfId = num;

                conferenceCommon.saveMyVideoAudioSync(false, false);

                showLoading();
                if (loadingDialog != null && !loadingDialog.isShowing()){
                    loadingDialog.show();
                }
                ConferenceBean conf = conferenceCommon.isLogin(num);
                if(conf != null){
                    joinConf(conf);
                }else {
                    conf = conferenceCommon.getConferenceByID(num);
                    if (conf != null){
                        joinConf(conf);
                    }else {
                        listHandler.sendEmptyMessage(MEETINGINVALIDATE);
                    }
                }
                break;
            default:
                break;
        }
    }
    //加会操作
    private void joinConf(ConferenceBean conf){
        confHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (loadingDialog != null && loadingDialog.isShowing()){
                    loadingDialog.dismiss();
                }
            }
        }, 10 * 1000);
        //清空视频路数
        ((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).clearMap();
//                                //通知是box
        if (conf.getNeedLogin() == 1 && !isLogin()) {
            ToastUtil.showMessage(getContext(),getContext().getResources().getString(R.string.no_login_text),
                    10 * 1000);
            hideLoading();
            if (loadingDialog != null && loadingDialog.isShowing()){
                loadingDialog.dismiss();
            }
        } else {
            XMLUtils.CONFIGID = conf.getId();
            XMLUtils.CONFIGNAME = conf.getName();
            XMLUtils.CONFERENCEPATTERN = conf.getConferencePattern();
            String password = conf.getConfPassword();
            if (TextUtils.isEmpty(password) || "".equals(password)) {
                joinConf(conf, false, "");
            } else {
                showJoinDialog(conf);
            }
        }
    }
    private void joinConf(final ConferenceBean conferenceBean, boolean isNeedPwd, String pwd) {
        showLoading();
        final String password = StringUtil.toSemiangle(pwd);
        getLoginBean(conferenceBean, password);

        //remember the conference bean
        conferenceCommon.setConfPwd(pwd);
        conferenceCommon.setConfBean(conferenceBean);

        new Thread() {
            @Override
            public void run() {
//                if (conferenceBean.getStatus().equals("1")) {
//                    joinConf(conferenceBean, getLoginBean(conferenceBean, pwd));
//                } else {
                String nickName = "";
                if (!TextUtils.isEmpty(FileUtil.readSharedPreferences(getActivity(),
                        Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME))){
                    nickName = FileUtil.readSharedPreferences(getActivity(),
                            Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME);
                }
//                    if (conferenceBean.getHostID().equals(String.valueOf(getUid()))) {
                if (nickName.equals(conferenceBean.getHostName()) || nickName.equals(conferenceBean.getCreatorName())) {
                    startConf(conferenceBean, getLoginBean(conferenceBean, password));
                }
//                    else if (!conferenceBean.getConfType().equals("2")) {//不能加入
//                        listHandler.sendEmptyMessage(MEETINGNOTJOINBEFORE);
//                    }
                else {
                    joinConf(conferenceBean, getLoginBean(conferenceBean, password));
                }
//                }
            }

            ;
        }.start();
    }

    private LoginBean getLoginBean(ConferenceBean conferenceBean, String pwd) {
        String showName = FileUtil.readSharedPreferences(getActivity(),
                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
        int userid = preferences.getInt(Constants.USER_ID, 0);
        loginBean = new LoginBean(conferenceBean.getId(), showName, pwd);
        loginBean.setType(conferenceBean.getType());
        loginBean.setUid(userid);
        return loginBean;
    }

    private void joinConf(ConferenceBean confBean, LoginBean loginBean) {
        config = conferenceCommon.initConfig(loginBean);
        if ("0".equals(config.getConfigBean().getErrorCode())) {
            listHandler.sendEmptyMessage(JOIN_CONFERENCE);
        } else {
            ((ConferenceCommonImpl) commonFactory.getConferenceCommon()).setJoinStatus(
                    ConferenceCommon.NOTJOIN);
            if ("-1".equals(config.getConfigBean().getErrorCode())) {
                if (config.getConfigBean().getErrorMessage().startsWith("0x0604003")) {
                    if (config.getConfigBean().getErrorMessage().equals("0x0604003:you should login to meeting system! ")) {
                        loginSystem();
                    } else {
                        listHandler.sendEmptyMessage(FINISH);
                    }
                }else if (config.getConfigBean().getErrorMessage().equals("0x0604006:conference password error!")){
                    Message msg = listHandler.obtainMessage(NEED_PASSWORD);
                    msg.sendToTarget();
                }else {
                    listHandler.sendEmptyMessage(LOGINFAILED);
                }
            } else if ("-2".equals(config.getConfigBean().getErrorCode())) {
                listHandler.sendEmptyMessage(FINISH);
            } else if ("-10".equals(config.getConfigBean().getErrorCode())) {
                if (Config.HAS_LIVE_SERVER) {
                    if (confBean.getType().equals(Config.MEETING)) {
                        confBean.setType(Config.LIVE);
                        joinConf(confBean, getLoginBean(confBean, loginBean.getPassword()));
                        return;
                    }
                }
                listHandler.sendEmptyMessage(MEETINGINVALIDATE);

            } else if ("-18".equals(config.getConfigBean().getErrorCode())) {
                listHandler.sendEmptyMessage(MEETINGNOTJOINBEFORE);
            } else if (ConferenceCommon.HOSt_ERROR.equals(config.getConfigBean().getErrorCode())) {
                listHandler.sendEmptyMessage(HOSTERROR);
            } else if (ConferenceCommon.SPELL_ERROR.equals(config.getConfigBean().getErrorCode())) {
                //log.info("spell error");
                listHandler.sendEmptyMessage(SPELLERROR);
            } else {
                listHandler.sendEmptyMessage(GET_ERROR_MESSAGE);
            }
        }
    }

    private void joinConference() {
        Config config = conferenceCommon.getConfig();
        if (config != null) {
            ConfigBean configBean = config.getConfigBean();
            if (configBean != null) {
                configBean.setUserInfo_m_dwStatus(ConferenceCommon.RT_STATE_RESOURCE_AUDIO);
            } //else
                //log.info("configBean is null");
        } //else
            //log.info("config is null");
        Log.e("ldy","会议信息:"+conferenceCommon.getParam());
        commonFactory.getConferenceCommon().setMeetingBox();

        boolean existCamera = ConferenceApplication.existCamera();
        boolean existMicrophone = ConferenceApplication.existMicrophone(getActivity().getApplicationContext());

        Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
        commonFactory.getConferenceCommon().setDeviceStatus(existMicrophone, existCamera);

        commonFactory.getConferenceCommon().joinConference(conferenceCommon.getParam());
    }

    private void loginSystem() {
        Message msg = listHandler.obtainMessage(NEED_LOGIN);
        msg.sendToTarget();
    }

    private void startConf(ConferenceBean confBean, LoginBean loginBean) {
        config = conferenceCommon.initConfig();
        int uid = preferences.getInt(Constants.USER_ID, 0);
        String siteId = preferences.getString(Constants.SITE_ID, "");
        String showName = preferences.getString(Constants.LOGIN_JOINNAME, "");

        result = Config.startConf(uid, showName, siteId, confBean);
        if (result.equals("-1:error")) {
            listHandler.sendEmptyMessage(CREATECONF_ERROR);
        } else {
            conferenceCommon.setLogPath(((ConferenceApplication) getActivity().getApplication()).getFilePath("hslog"));
            conferenceCommon.initSDK();
            Config config = conferenceCommon.getConfig();
            conferenceCommon.setMeetingBox();

            boolean existCamera = ConferenceApplication.existCamera();
            boolean existMicrophone = ConferenceApplication.existMicrophone(getActivity().getApplicationContext());

            Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
            commonFactory.getConferenceCommon().setDeviceStatus(existMicrophone, existCamera);

            conferenceCommon.joinConference(conferenceCommon.getParam(loginBean, true));
            Log.e("gggg","会议参数::"+conferenceCommon.getParam(loginBean, true));
            config.setMyConferenceBean(confBean);
            listHandler.sendEmptyMessage(READY_JOINCONF);
        }
    }

    public void showJoinDialog(final ConferenceBean conferenceBean) {
        if (joinDialog == null) {
            joinDialog = new JoinDialog(getActivity(), 0);
            joinDialog.setClickListener(new JoinDialog.OnResultListener() {

                @Override
                public void doYes(String pwd) {
                    // TODO Auto-generated method stub
                    if (loadingDialog != null && !loadingDialog.isShowing()){
                        loadingDialog.setTitle("正在加会");
                        loadingDialog.show();
                    }
                    joinConf(conferenceBean, true, pwd);
                }

                @Override
                public void doNo() {
                    // TODO Auto-generated method stub
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.hide();
                    }
                }
            });
        }
        if (joinDialog != null && !joinDialog.isShowing()) {
            joinDialog.show(conferenceBean);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (loadingDialog != null && loadingDialog.isShowing()){
            loadingDialog.dismiss();
            loadingDialog = null;
        }
    }

}
