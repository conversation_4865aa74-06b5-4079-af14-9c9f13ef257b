package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.transfer.Config;

/**
 * Created by sdvye on 2019/6/12.
 */

@SuppressLint("ValidFragment")
public class FragNoMeeting extends BaseFragment {
    private View noView;
    private ImageView netImage;
    private TextView tv,tv1,tv2;
    private TextView tv_ip;
    public FragNoMeeting(ICallParentView iCallParentView) {
        super(iCallParentView);
    }
    //无参构造器
    public FragNoMeeting(){
        super();
    }
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        noView = inflater.inflate(R.layout.frag_no_meeting, container, false);
        return noView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        tv_ip = (TextView) noView.findViewById(R.id.tv_ip);
        netImage = (ImageView)noView.findViewById(R.id.no_feeting_image);
        tv = (TextView)noView.findViewById(R.id.no_feeting_tv);
        tv1 = (TextView)noView.findViewById(R.id.tv1);
        tv2 = (TextView)noView.findViewById(R.id.tv2);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (isNetConnect()){
            netImage.setImageResource(R.drawable.no_meeting);
            tv.setText(getResources().getString(R.string.no_meeting));
            tv1.setText(getResources().getString(R.string.please));
            if (!TextUtils.isEmpty(Config.Site_URL)){
                tv_ip.setText(Config.Site_URL);
            }else {
                tv_ip.setText(getResources().getString(R.string.ip));
            }
            tv2.setText(getResources().getString(R.string.create_meeting));
        }else {
            netImage.setImageResource(R.drawable.no_wifi);
            tv.setText(getResources().getString(R.string.no_wifi_title));
            tv1.setText(getResources().getString(R.string.qd_title));
            tv_ip.setText(getResources().getString(R.string.sett_title));
            tv2.setText(getResources().getString(R.string.check_net));
        }
    }

    /* <AUTHOR>
   * @category 判断是否有外网连接（普通方法不能判断外网的网络是否连接，比如连接上局域网）
   * @return
   */
    public boolean isNetConnect() {
        try {
            ConnectivityManager cm = (ConnectivityManager)getActivity().getSystemService(getActivity().CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = cm.getActiveNetworkInfo();
            if (networkInfo != null){
                return true;
            }
        }catch (Exception e){
        }
        return false;
    }
}
