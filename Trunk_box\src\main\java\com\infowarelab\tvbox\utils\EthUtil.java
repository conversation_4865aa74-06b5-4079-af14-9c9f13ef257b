package com.infowarelab.tvbox.utils;

public class EthUtil {
//    public static final String TAG = "[liusl]EthUitl";
//
//    public static String dhcp_gateway = null;
//    public static String wifi_gateway = null;
//    public static EthernetManager mEthManager = null;
//    public static WifiManager mWifiManager = null;
//    public static ConnectivityManager mConnectivityManager = null;
//    public static WifiAdmin wifiAdmin = null;
//
//    public static void getInstance(Context context) {
//
//        mEthManager = (EthernetManager) context.getSystemService(Context.ETHERNET_SERVICE);
//        mWifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
//        mConnectivityManager = (ConnectivityManager) context
//                .getSystemService(Context.CONNECTIVITY_SERVICE);
//        wifiAdmin = new WifiAdmin(context);
//    }
//
//
//    public static void setStaticIp() {
//        mWifiManager.setWifiEnabled(false);
//        mEthManager.setEthernetEnabled(false);
//        DhcpInfoInternal mDhcpInfoInternal = new DhcpInfoInternal();
//        String ipAddress = "*************";
//        String DNS1 = "**************";
//        String DNS2 = "**************";
//        InetAddress iRoute = NetworkUtils.numericToInetAddress("************");
//        InetAddress iNetmask = NetworkUtils.numericToInetAddress("*************");
//        try {
//            int netmask = NetworkUtils.inetAddressToInt(iNetmask);
//            int prefixLength = NetworkUtils.netmaskIntToPrefixLength(netmask);
//            mDhcpInfoInternal.prefixLength = prefixLength;
//        } catch (IllegalArgumentException e) {
//            e.printStackTrace();
//        }
//        mDhcpInfoInternal.ipAddress = ipAddress;
//        mDhcpInfoInternal.addRoute(new RouteInfo(iRoute));
//        mDhcpInfoInternal.dns1 = DNS1;
//        mDhcpInfoInternal.dns2 = DNS2;
//        mDhcpInfoInternal.serverAddress = "*************";
//
//        mEthManager.saveEthernetIpInfo(mDhcpInfoInternal.makeDhcpInfo(),
//                EthernetManager.ETHERNET_CONNECT_MODE_MANUAL);
//        mEthManager.setEthernetEnabled(true);
//        DhcpInfo dhcpInfo = mEthManager.getSavedEthernetIpInfo();
//        dhcp_gateway = Formatter.formatIpAddress(dhcpInfo.gateway);
//    }
//
//    public static void setDhcp() {
//        mWifiManager.setWifiEnabled(false);
//        mEthManager.setEthernetEnabled(false);
//        Log.i(TAG, "getEthernetState=" + mEthManager.getEthernetState());
//        mEthManager.setEthernetDefaultConf();
//        mEthManager.setInterfaceName("eth0");
//        mEthManager.setEthernetEnabled(true);
//        Log.i(TAG, "getEthernetState=" + mEthManager.getEthernetState());
//    }
//
//    public static void openWifi() {
//        mEthManager.setEthernetEnabled(false);
//        boolean open = wifiAdmin.openWifi();
//        Log.i(TAG, "wifi open:" + open);
//        connectWifi();
//    }
//
//    public static void connectWifi() {
//        wifiAdmin.addNetwork(wifiAdmin.CreateWifiInfo("liusl", "222", 3));
//    }
//
//    public static String getEthGateWay() {
//        String ip = mConnectivityManager.getLinkProperties(ConnectivityManager.TYPE_ETHERNET)
//                .getAddresses().toString();
//        String mGW = mConnectivityManager.getLinkProperties(ConnectivityManager.TYPE_ETHERNET)
//                .getRoutes().toString();
//        String mDns = mConnectivityManager.getLinkProperties(ConnectivityManager.TYPE_ETHERNET)
//                .getDnses().toString();
//        Log.i(TAG, "getEthGateWay ip=" + ip);
//        Log.i(TAG, "getEthGateWay mGW=" + mGW);
//        Log.i(TAG, "getEthGateWay mDns=" + mDns);
//        if (mGW.contains(">")) {
//            mGW = mGW.substring(mGW.lastIndexOf('>') + 2, mGW.length() - 1);
//        }
//
//        dhcp_gateway = mGW;
//        return mGW;
//    }
//
//    public static String getWifiGateWay() {
//
//        DhcpInfo dhcpInfo = wifiAdmin.getDhcpInfo();
//        Log.v(" wifi ipAddress", Formatter.formatIpAddress(dhcpInfo.ipAddress) + "");
//        Log.v("wifi gateway", Formatter.formatIpAddress(dhcpInfo.gateway) + "");
//        Log.v("wifi dns1", Formatter.formatIpAddress(dhcpInfo.dns1) + "");
//        Log.v("wifi dns2", Formatter.formatIpAddress(dhcpInfo.dns2) + "");
//
//        wifi_gateway = Formatter.formatIpAddress(dhcpInfo.gateway);
//        return wifi_gateway;
//    }

}