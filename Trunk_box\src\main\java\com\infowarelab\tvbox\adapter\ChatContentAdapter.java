package com.infowarelab.tvbox.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.CutString;
import com.infowarelabsdk.conference.domain.MessageBean;

import java.util.List;

public class ChatContentAdapter extends BaseAdapter {

	private static int IMVT_COM_MSG = 0;
	private static int IMVT_TO_MSG = 1;
	private List<MessageBean> coll;
	private LayoutInflater mInflater;
	private int preIndex = 0;
	private boolean isPub = false;

	public ChatContentAdapter(Context context, List<MessageBean> coll, boolean isPub) {
		this.coll = coll;
		this.isPub = isPub;
		mInflater = LayoutInflater.from(context);
	}

	@Override
	public int getCount() {
		return coll.size();
	}

	@Override
	public MessageBean getItem(int position) {
		return coll.get(position);
	}

	@Override
	public long getItemId(int position) {
		return position;
	}

	public int getItemViewType(int position) {
		// TODO Auto-generated method stub
		MessageBean entity = getItem(position);

		if (entity.isComeMeg()) {
			return IMVT_COM_MSG;
		} else {
			return IMVT_TO_MSG;
		}

	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {
		ViewHolder viewHolder = null;
		if (convertView == null) {
			convertView = mInflater.inflate(R.layout.item_pad_inconf_chat_list,
					null);
			viewHolder = new ViewHolder();
			viewHolder.llTime = (LinearLayout) convertView
					.findViewById(R.id.ll_inconf_chat_list_item_time);
			viewHolder.time = (TextView) convertView
					.findViewById(R.id.tv_inconf_chat_list_item_time);
			viewHolder.llLeft = (LinearLayout) convertView
					.findViewById(R.id.ll_inconf_chat_list_item_left);
			viewHolder.name = (TextView) convertView
					.findViewById(R.id.tv_inconf_chat_list_item_left_name);
			viewHolder.contentLeft = (TextView) convertView
					.findViewById(R.id.tv_inconf_chat_list_item_left_content);
			viewHolder.llRight = (LinearLayout) convertView
					.findViewById(R.id.ll_inconf_chat_list_item_right);
			viewHolder.contentRight = (TextView) convertView
					.findViewById(R.id.tv_inconf_chat_list_item_right_content);
			convertView.setTag(viewHolder);
		} else {
			viewHolder = (ViewHolder) convertView.getTag();
		}
		MessageBean entity = getItem(position);
		boolean isComMsg = entity.isComeMeg();
		
		if (entity.isShowTime()) {
			viewHolder.llTime.setVisibility(View.VISIBLE);
		} else {
			viewHolder.llTime.setVisibility(View.GONE);
		}
		/*
		 * if(position > 0){ MessageBean preEntity = coll.get(preIndex); long
		 * pre = StringUtil.strToDate(preEntity.getDate(), "HH:mm").getTime();
		 * long current = StringUtil.strToDate(entity.getDate(),
		 * "HH:mm").getTime();
		 * System.out.println(preIndex+"/"+pre+"/"+current+"/"+(current -
		 * pre)/60/1000); //判断与前一条聊天内容相比，时间是否间隔在3分钟以上（毫秒级） if(Math.abs((current
		 * - pre)/60/1000) <= 3){ viewHolder.time.setVisibility(View.GONE);
		 * }else{ preIndex = position; }
		 * 
		 * }
		 */

		// 从小时开始截取
		viewHolder.time.setText(entity.getDate().substring(0, 5));
//		if(isPub){
//			viewHolder.name.setVisibility(View.VISIBLE);
//		}else {
//			viewHolder.name.setVisibility(View.GONE);
//		}
		if(isComMsg){
			viewHolder.llLeft.setVisibility(View.VISIBLE);
			viewHolder.llRight.setVisibility(View.GONE);
			String name = entity.getUsername();
			name = CutString.cutString(name, 8);
			Log.e("PPPPPP","名字::"+name);
			viewHolder.name.setText(name);
			viewHolder.contentLeft.setText(entity.getMessage());
		}else {
			viewHolder.llLeft.setVisibility(View.GONE);
			viewHolder.llRight.setVisibility(View.VISIBLE);
			viewHolder.contentRight.setText(entity.getMessage());
		}
		

		return convertView;
	}

	static class ViewHolder {
		public LinearLayout llTime;
		public TextView time;
		public LinearLayout llLeft;
		public TextView name;
		public TextView contentLeft;
		public LinearLayout llRight;
		public TextView contentRight;
	}

}
