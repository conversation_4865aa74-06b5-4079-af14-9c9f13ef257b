<?xml version="1.0" encoding="utf-8"?><project>
	<parent>
		<artifactId>maven-parent</artifactId>
		<groupId>com.infowarelab</groupId>
		<version>*******</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.infowarelab</groupId>
	<artifactId>androidPad</artifactId>
	<version>*******</version>
	<name>androidPad-*******</name>
	<ciManagement>
		<system>jenkins</system>
		<url>http://${buildhost}/jenkins</url>
		<notifiers>
			<!--notifier>
			<type>msn</type>
			<configuration>
				<address><EMAIL></address>
			</configuration>
		</notifier>
		<notifier>
			<type>msn</type>
			<configuration>
				<address><EMAIL></address>
			</configuration>
		</notifier>
		<notifier>
			<type>msn</type>
			<configuration>
				<address><EMAIL></address>
			</configuration>
		</notifier-->
	</notifiers>
</ciManagement>
<scm>
	<connection>scm:svn:http://svn1.infowarelab.inf/newtech/android-conference/androidStudioProjects/trunk/Infowarelab</connection>
	<developerConnection>scm:svn:http://svn1.infowarelab.inf/newtech/android-conference/androidStudioProjects/trunk/Infowarelab</developerConnection>
</scm>
<build>
	<plugins>
		<plugin>
			<groupId>org.apache.maven.plugins</groupId>
			<artifactId>maven-compiler-plugin</artifactId>
			<configuration>
				<source>1.5</source>
				<target>1.5</target>
				<encoding>utf-8</encoding>
			</configuration>
		</plugin>
		<plugin>
			<groupId>org.apache.maven.plugins</groupId>
			<artifactId>maven-clean-plugin</artifactId>
			<configuration>
				<version>2.1.1</version>
			</configuration>
		</plugin>
	</plugins>
</build>
<profiles>
	<profile>
		<id>build_all</id>
		<build>
			<plugins>
				<plugin>
					<artifactId>maven-antrun-plugin</artifactId>
					<version>1.6</version>
					<executions>
						<execution>
							<id>batch</id>
							<phase>pre-integration-test</phase>
							<goals>
								<goal>run</goal>
							</goals>
						</execution>
					</executions>
					<configuration>
						<tasks>
	  						<taskdef resource="net/sf/antcontrib/antcontrib.properties"/>
							<property name="sourceDir" value="."/>
							<property name="outputDir" value="."/>
							<property name="moduleName" value="Trunk_pad"/>
							<!--property name="sourceDir_sdk" value="/data/JENKINS_HOME/workspace/InfowarelabSDK/pack"/>
							<property name="sourceDir_so" value="/data/JENKINS_HOME/workspace/android-conference-ucp5.3.3.3-so/code"/-->

							
							<tstamp>
								<format property="timestamp" pattern="yyMMdd-HHmm"/>
								<format property="today" pattern="yyMMdd"/>
							</tstamp>	
							
							<!--copy file="${sourceDir_sdk}/InfowarelabSDK.jar"  todir="${sourceDir}/libs/" overwrite="true"/-->
							
							<!-- 如果需要和so一起编译，拿到最新的so，需要放开下面这句-->

							<!--copy file="${sourceDir_so}/bin/Android_build_results/armeabi/libAndroidAdapter.so"  todir="${sourceDir}/libs/armeabi" overwrite="true"/-->
																	
							

							
							<!-- adds the ant-contrib tasks (if/then/else used below) -->
							<taskdef resource="net/sf/antcontrib/antcontrib.properties"/>
							<!--exec dir="${sourceDir}/" executable="/bin/sh">
                                <arg line="-c 'chmod +x  ./android-apk-clean'" />
                                </exec>
							<if>
								<equals arg1="${clean}" arg2="1" />
								<then>
									<exec dir="${sourceDir}" executable="/bin/sh" >
								        <arg line="-c './android-apk-clean'"/>
							        </exec>
								</then>
							</if-->
							<copy file="${sourceDir}/configuredFiles/localProperties/local.properties" todir="${sourceDir}/" overwrite="true"/>
							<!--delete file="${sourceDir}/Trunk_lib/build.gradle"/>
							<copy file="${sourceDir}/configuredFiles/buildGradle/Trunk_lib-build.gradle" tofile="${sourceDir}/Trunk_lib/build.gradle"/-->

							<delete file="${sourceDir}/${moduleName}/build.gradle"/>
							<copy file="${sourceDir}/configuredFiles/buildGradle/${moduleName}-build.gradle" tofile="${sourceDir}/${moduleName}/build.gradle"/>

                             <!--用sed命令指定需要编译的项目 start-->
							 <echo file="${sourceDir}/settings.gradle" append="false">include  ':Trunk_lib', ':${moduleName}'</echo>
							 <!--end-->

						     <!--将gradle软链接到工程根目录 start-->
								<exec dir="${sourceDir}/" executable="/bin/sh">
									<arg line="-c 'ln -s -f /data/JENKINS_HOME/gradle-3.3/bin/gradle ./'" />
                                </exec>
							 <!--end-->
								<!--copy file="${sourceDir}/configuredFiles/localProperties/gradleBuild_sh" todir="${sourceDir}/" overwrite="true"/>
								<exec dir="${sourceDir}/" executable="/bin/sh">
									<arg line="-c 'chmod +x  gradleBuild_sh'" />
                                </exec>
					        	<exec dir="${sourceDir}/" executable="/bin/sh">
									<arg line="-c ./gradleBuild_sh" />
                                </exec-->

								<exec dir="${sourceDir}/" executable="/bin/sh">
									<arg line="-c './gradle clean build --stacktrace --info'" />
                                </exec>

							<mkdir dir="${outputDir}/pack"/>
							<mkdir dir="${outputDir}/install"/>
							<copy file="${outputDir}/${moduleName}/build/outputs/apk/${moduleName}-release.apk"  todir="${outputDir}/pack"/>
							<move file="${outputDir}/pack/${moduleName}-release.apk" tofile="${outputDir}/pack/${artifactId}.apk"/>

						  <ftp action="mkdir" server="ftp.infowarelab.dev" 
						  	userid="${ftp_upload}" 
						  	password="${ftpps}" 
						  	remotedir="/Release/${ftpPath}/${today}">
              </ftp>
							<zip destfile="${outputDir}/install/${artifactId}-${version}-${timestamp}.zip" basedir="${outputDir}/pack"></zip>
							<ftp server="ftp.infowarelab.dev" binary="true" verbose="true"  passive="yes" userid="${ftp_upload}" password="${ftpps}" remotedir="/Release/${ftpPath}/${today}">
                            <fileset file="${outputDir}/install/${artifactId}-${version}-${timestamp}.zip"/>
                            </ftp>
                            <!--
                            <delete file="${outputDir}/libs/InfowarelabSDK.jar"/>
                            -->
							<!--delete file="${outputDir}/libs/armeabi/libAndroidAdapter.so"/-->
							<delete dir="${outputDir}/pack"/>
							<delete dir="${outputDir}/install"/>
			
					</tasks>
					</configuration>
				<dependencies>
							<dependency>
								    <groupId>org.apache.ant</groupId>
								        <artifactId>ant-nodeps</artifactId>
									    <version>1.8.1</version>
								    </dependency>
							<dependency>
								<groupId>com.sun</groupId>
								<artifactId>tools</artifactId>
								<version>1.5.0</version>
								<scope>system</scope>
								<systemPath>${java.home}/../lib/tools.jar</systemPath>
							</dependency>
							<dependency>
								<groupId>ant-contrib</groupId>
								<artifactId>ant-contrib</artifactId>
								<version>20020829</version>
							</dependency>
							<dependency>
								    <groupId>org.apache.ant</groupId>
								        <artifactId>ant-commons-net</artifactId>
									    <version>1.8.2</version>
								    </dependency>
							<dependency>
								<groupId>ant</groupId>
								<artifactId>optional</artifactId>
								<version>1.5.4</version>
							</dependency>
							<dependency>
								<groupId>commons-net</groupId>
								<artifactId>commons-net</artifactId>
								<version>1.4.1</version>
							</dependency>
					</dependencies>	
				</plugin>
			</plugins>
		</build>
	</profile>	
</profiles>

<distributionManagement>
	<site>
		<id>scm-maven-site</id>
		<name>scm-maven-site</name>
		<url>scp://192.168.1.120/var/www/html/site/${artifactId}</url>
	</site>
</distributionManagement>
<pluginRepositories>
	<pluginRepository>
		<id>120</id>
		<url>http://192.168.1.120/maven2</url>
	</pluginRepository>
</pluginRepositories>
<properties>
	<WEB_CONFIG_HOME>/tmp</WEB_CONFIG_HOME>
	<ftp_upload>ftp_upload</ftp_upload>
	<ftpps>123456</ftpps>
</properties>
</project>