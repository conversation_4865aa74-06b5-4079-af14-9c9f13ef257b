package com.infowarelab.tvbox.base;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Always on 2017/10/24.
 */

public class ResTerminals implements Serializable {
        private String action;
        private String inviteID;
        private List<Terminal> terminals;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public List<Terminal> getTerminals() {
        return terminals;
    }

    public void setTerminals(List<Terminal> terminals) {
        this.terminals = terminals;
    }

    public String getInviteID() {
        return inviteID;
    }

    public void setInviteID(String inviteID) {
        this.inviteID = inviteID;
    }

    @Override
    public String toString() {
        return "ResTerminals{" +
                "action='" + action + '\'' +
                ", inviteID='" + inviteID + '\'' +
                ", terminals=" + terminals +
                '}';
    }
}
