package com.infowarelab.tvbox.view;


import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;

@SuppressLint("NewApi")
public class ASDecodeView extends FrameLayout implements SurfaceHolder.Callback{
	
	/** surface生命周期 **/
	private SurfaceHolder holder;
	private Context activity;
	/** 视频SDK接口 **/
	protected CommonFactory commonFactory = CommonFactory.getInstance();
	private ShareDtCommonImpl asCommon =  (ShareDtCommonImpl) commonFactory.getSdCommon();;
	private SurfaceView surface;
	private int pHeight;
	private TextView tvWait;
	private boolean mOpened = false;
//	private FrameLayout relative;
	
	
	public ASDecodeView(Context context) {
		super(context);
	}
	public ASDecodeView(Context context, AttributeSet attrs) {
		super(context, attrs);
		this.activity = context;
	}
	public ASDecodeView(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);
		this.activity = context;
	}
	/**
	 * 初始化界面大小
	 * @param height 容器高
	 * @param width  宽
	 */
	public void initSize(int width ,int height,int sw,int sh) {

		Log.d("InfowareLab.Debug", "ASDecodeView.initSize: width=" + width + "; height=" + height + "; sw=" + sw + "; sh=" +sh);

		setScreenSize(sw,sh);
		setParams(width, height);
		syncMatrix(width, height);
		refreshFrameLayout();
	}
	public void resetSize(int width,int height,int left, int right, int top, int bottom){
		RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
		params.width = width;
		params.height = height;
		params.setMargins(left, top, right, bottom);
		setLayoutParams(params);
	}
	private void refreshFrameLayout(){
		matrix.getValues(targetValues);
        resetSize(
        		(int)(this.width*targetValues[Matrix.MSCALE_X]),
        		(int)(this.height*targetValues[Matrix.MSCALE_Y]),
        		(int)targetValues[Matrix.MTRANS_X],
        		(int)(screenW-this.width*targetValues[Matrix.MSCALE_X]-targetValues[Matrix.MTRANS_X]),
        		(int)targetValues[Matrix.MTRANS_Y],
        		(int)(screenH-this.height*targetValues[Matrix.MSCALE_Y]-targetValues[Matrix.MTRANS_Y])
        		);
	}
	private void syncMatrix(int width ,int height){
			float[] mv = new float[9];
			matrix.getValues(mv);
			mv[Matrix.MSCALE_X]=1;
			mv[Matrix.MSCALE_Y]=1;
			mv[Matrix.MTRANS_X]=(float) ((screenW-width)*1.0/2);
			mv[Matrix.MTRANS_Y]=(float) ((screenH-height)*1.0/2);
			matrix.setValues(mv);
	}
	/**
	 * 视频接收/停止接收
	 * @param isOpen
	 */
	public void changeStatus(boolean isOpen) {

			if(isOpen){
				if (mOpened == isOpen)
					return;

				Log.d("InfowareLab.Debug", "ASDecodeView.changeStatus=true");

				if(null!=surface){
					surface.getHolder().getSurface().release();
					removeView(surface);
					surface = null;
				}
				surface = new SurfaceView(this.activity);
		        surface.setLayoutParams(new LayoutParams(android.view.ViewGroup.LayoutParams.MATCH_PARENT, 
		        		android.view.ViewGroup.LayoutParams.MATCH_PARENT, Gravity.CENTER));
		        surface.setZOrderMediaOverlay(true);
		        holder = surface.getHolder();
			    holder.addCallback(this);
			    addView(surface);

				mOpened = true;

			}else {

				Log.d("InfowareLab.Debug", "ASDecodeView.changeStatus=false");

				asCommon.setIsReceive(false, (SurfaceView)null);

				if(null!=surface){
					surface.getHolder().getSurface().release();
					removeView(surface);
					surface = null;
				}

				mOpened = false;
			}
	}
	private void showWait(){
		tvWait = new TextView(this.activity);
		tvWait.setTextSize(TypedValue.COMPLEX_UNIT_SP,40);
		tvWait.setTextColor(Color.BLACK);
		tvWait.setBackgroundColor(Color.WHITE);
		tvWait.setGravity(Gravity.CENTER);
		tvWait.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT,  
                LayoutParams.MATCH_PARENT));
		tvWait.setText("Wait");
		addView(tvWait);
	}
	private void removeWait(){
		if(null!=tvWait){
			removeView(tvWait);
			tvWait = null;
		}
	}
	public void showClosed(){
		if(null!=tvWait){
			tvWait.setText("Closed");
			tvWait.setVisibility(View.VISIBLE);
		}
	}
	public void showSurface(){
		if(null!=tvWait){
			tvWait.setVisibility(View.GONE);
		}
	}
	@Override
	public void surfaceCreated(SurfaceHolder holder) {
		Log.d("InfowareLab.Debug", "ASDecodeView.surfaceCreated");
		if(null!=surface){
//			HandlerThread surfaceThread = new HandlerThread("SurfaceHandler");
//			surfaceThread.start();
			asCommon.setIsReceive(true, surface);
		}
	}

	@Override
	public void surfaceChanged(SurfaceHolder holder, int format, int width,
			int height) {
		Log.d("InfowareLab.Debug", "ASDecodeView.surfaceChanged");
		this.holder=holder;
	}
	@Override
	public void surfaceDestroyed(SurfaceHolder holder) {
		Log.d("InfowareLab.Debug", "ASDecodeView.surfaceDestroyed");
		
		asCommon.setIsReceive(false, (SurfaceView) null);
	}
	public void setParams(int width, int height) {
		this.width = width;
		this.height = height;
	}

	public void setScreenSize(int width, int height) {
		this.screenW = width;
		this.screenH = height;
	}

	public SurfaceView getSurfaceView(){
		return surface;
	}
	public void show(){
		setVisibility(View.VISIBLE);
	}
	public void hide(){
		setVisibility(View.GONE);
	}
	
	protected int width;
	protected int height;
	protected int screenW = 100;
	protected int screenH = 100;
	protected float minZoom = 1;
	protected float maxZoom = 3;
	protected PointF start = new PointF();
	protected PointF mid = new PointF();
	/** 图像缩放比例 */
	protected Matrix matrix = new Matrix();
	protected Matrix savedMatrix = new Matrix();
	protected float oldDist = 1f;
	protected float[] matrixValues = new float[9];
	protected float[] targetValues = new float[9];
    public void setP1(){
		RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
		params.width = 1;
		params.height = 1;
		setLayoutParams(params);
	}
    public void setPM(int width,int height){
    	syncMatrix(width, height);
		refreshFrameLayout();
	}
	
}
