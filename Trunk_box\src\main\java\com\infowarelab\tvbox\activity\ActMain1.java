package com.infowarelab.tvbox.activity;

import android.app.Activity;
import android.hardware.Camera;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import androidx.annotation.Nullable;

import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.FileUtils;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;

import java.io.IOException;

/**
 * Created by sdvye on 2019/6/12.
 */

public class ActMain1 extends Activity implements SurfaceHolder.Callback{
    public static ActMain1 mActivity = null;
    private TextView titleText;
    private Spinner spinner;
    private SurfaceView tvMain;
    private VideoCommonImpl videoCommon;
    private Camera mCamera;
    private SurfaceHolder mHolder;//
    private Camera.Parameters parameters;//
    private int cameraPosition = 0; // 0代表前置摄像头，1代表后置摄像头
    private LinearLayout llCancel;

    protected void hideBottomUIMenu() {
        //隐藏虚拟按键，并且全屏
        if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) {
            View v = this.getWindow().getDecorView();
            v.setSystemUiVisibility(View.GONE);
        } else if (Build.VERSION.SDK_INT >= 19) {
            View decorView = getWindow().getDecorView();
            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_IMMERSIVE;
            decorView.setSystemUiVisibility(uiOptions);
        }
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.frag_main1);

        hideBottomUIMenu();

        cameraPosition = getIntent().getExtras().getInt("cameraPosition");

        FileUtils.DeleteFolder(Environment.getExternalStorageDirectory().getPath() + "/AudioRecord/");

        titleText = (TextView)findViewById(R.id.titleText);
        if (0 == cameraPosition){
            titleText.setText(getResources().getString(R.string.main));
        }else {
            titleText.setText(getResources().getString(R.string.fu));
        }
        videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        spinner = (Spinner) findViewById(R.id.sp);
        spinner.requestFocus();
        String[] items = {"一般", "标清", "高清", "超清"};
//        String[] items = {"一般", "标清", "高清"};
        ArrayAdapter<String> stringArrayAdapter = new ArrayAdapter<>(this, R.layout.item_select, items);
        stringArrayAdapter.setDropDownViewResource(R.layout.item_drop);
        spinner.setAdapter(stringArrayAdapter);

        tvMain = (SurfaceView) findViewById(R.id.tv_main);
        mHolder = tvMain.getHolder();
        mHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                switch (position) {
                    case 0:
                        if (0 == cameraPosition){
                            SharedPreferencesUrls.getInstance().putInt("width",320);
                            SharedPreferencesUrls.getInstance().putInt("height",180);
                        }else {
                            SharedPreferencesUrls.getInstance().putInt("width1",320);
                            SharedPreferencesUrls.getInstance().putInt("height1",180);
                        }
                        mHolder.setFixedSize(320,180);
                        break;
                    case 1:
                        if (0 == cameraPosition){
                            SharedPreferencesUrls.getInstance().putInt("width",640);
                            SharedPreferencesUrls.getInstance().putInt("height",360);
                        }else {
                            SharedPreferencesUrls.getInstance().putInt("width1",640);
                            SharedPreferencesUrls.getInstance().putInt("height1",360);
                        }
                        mHolder.setFixedSize(640,360);
                        break;
                    case 2:
                        if (0 == cameraPosition){
                            SharedPreferencesUrls.getInstance().putInt("width",1280);
                            SharedPreferencesUrls.getInstance().putInt("height",720);
                        }else {
                            SharedPreferencesUrls.getInstance().putInt("width1",1280);
                            SharedPreferencesUrls.getInstance().putInt("height1",720);
                        }
                        mHolder.setFixedSize(1280,720);
                        break;
                    case 3:
                        if (0 == cameraPosition){
                            SharedPreferencesUrls.getInstance().putInt("width",1920);
                            SharedPreferencesUrls.getInstance().putInt("height",1080);
                        }else {
                            SharedPreferencesUrls.getInstance().putInt("width1",1920);
                            SharedPreferencesUrls.getInstance().putInt("height1",1080);
                        }
                        mHolder.setFixedSize(1920,1080);
                        break;
                    default:
                        break;
                }
                if (0 == cameraPosition){
                    SharedPreferencesUrls.getInstance().putInt("position",position);
                }else {
                    SharedPreferencesUrls.getInstance().putInt("position1",position);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });
        if (0 == cameraPosition){
            //主流
            spinner.setSelection(SharedPreferencesUrls.getInstance().getInt("position",2));
            mHolder.setFixedSize(SharedPreferencesUrls.getInstance().getInt("width",1280),
                    SharedPreferencesUrls.getInstance().getInt("height",720));
        }else {
            //辅流
            spinner.setSelection(SharedPreferencesUrls.getInstance().getInt("position1",2));
            mHolder.setFixedSize(SharedPreferencesUrls.getInstance().getInt("width1",1920),
                    SharedPreferencesUrls.getInstance().getInt("height1",1080));
        }
        mHolder.addCallback(this);

        llCancel = (LinearLayout) findViewById(R.id.ll_frag_set_cancel);

        llCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                releaseCamera();
                finish();
            }
        });
    }
    //相机的启动和Activity的生命周期进行绑定
    @Override
    protected void onResume() {
        mActivity = ActMain1.this;
        super.onResume();
        if (mCamera == null) {
            mCamera = getCamera();
            if (mHolder != null) {
                setStartPrevicw(mCamera, mHolder);
                if (Build.MODEL.toUpperCase().startsWith("Lenovo".toUpperCase())){
                    mCamera.setDisplayOrientation(0);
                }
                setCamera();
            }
        }
    }
    //相机的启动和Activity的生命周期进行绑定
    @Override
    protected void onPause() {
        super.onPause();
        releaseCamera();
    }
    /**
     * 打开摄像头
     *
     * @return
     */
    public Camera getCamera() {
        Camera camera;
        try {
            camera = Camera.open();
        } catch (Exception e) {
            camera = null;
            e.printStackTrace();
        }
        return camera;
    }

    /**
     * 开始预览相机内容
     */
    private void setStartPrevicw(Camera camera, SurfaceHolder holder) {
        try {
            camera.setPreviewDisplay(holder);
            //打开摄像头
            camera.startPreview();
        } catch (IOException e) {
            e.printStackTrace();
            //释放摄像头
            releaseCamera();
        }
    }

    /**
     * 释放相机资源
     */
    private void releaseCamera() {
        if (mCamera != null) {
            mCamera.setPreviewCallback(null);
            mCamera.stopPreview();
            mCamera.release();
            mCamera = null;
        }
    }
    //在开始的时候创建画面显示的东西
    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        setStartPrevicw(mCamera, mHolder);
    }
    //当屏幕发生变化时候要做的事儿
    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        mCamera.stopPreview();
        setStartPrevicw(mCamera, mHolder);
    }
    //当界面销毁的时候要处理的事儿
    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        releaseCamera();
    }
    //切换摄像头
    private void setCamera(){
        //切换前后摄像头
        int cameraCount = 0;
        Camera.CameraInfo cameraInfo = new Camera.CameraInfo();
        cameraCount = Camera.getNumberOfCameras();//得到摄像头的个数
        for (int i = 0; i < cameraCount; i++) {
            Camera.getCameraInfo(i, cameraInfo);//得到每一个摄像头的信息
            if (cameraPosition == 0) {
                //现在是后置，变更为前置
                if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
                    //代表摄像头的方位，CAMERA_FACING_FRONT前置 CAMERA_FACING_BACK后置
                    mCamera.stopPreview();//停掉原来摄像头的预览
                    mCamera.release();//释放资源
                    mCamera = null;//取消原来摄像头
                    mCamera = Camera.open(i);//打开当前选中的摄像头
                    try {
                        mCamera.setPreviewDisplay(mHolder);//通过surfaceview显示取景画面
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    setStartPrevicw(mCamera, mHolder);
                    break;
                }
            } else {
                //现在是前置， 变更为后置
                if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_BACK) {
                    //代表摄像头的方位，CAMERA_FACING_FRONT前置 CAMERA_FACING_BACK后置
                    mCamera.stopPreview();//停掉原来摄像头的预览
                    mCamera.release();//释放资源
                    mCamera = null;//取消原来摄像头
                    mCamera = Camera.open(i);//打开当前选中的摄像头
                    try {
                        mCamera.setPreviewDisplay(mHolder);//通过surfaceview显示取景画面
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    setStartPrevicw(mCamera, mHolder);
                    break;
                }
            }
        }
    }
    @Override
    protected void onDestroy() {
        mActivity = null;
        super.onDestroy();
    }
}
