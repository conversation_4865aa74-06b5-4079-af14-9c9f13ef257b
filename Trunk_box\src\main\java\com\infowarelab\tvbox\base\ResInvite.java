package com.infowarelab.tvbox.base;

import java.io.Serializable;

/**
 * Created by Always on 2017/10/24.
 */

public class ResInvite implements Serializable {
        private String action;
        private String requestId;
        private Terminal fromTerminal;
        private String confId;
        private String confTitle;
        private String siteId;
        private String inviteId;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Terminal getFromTerminal() {
        return fromTerminal;
    }

    public void setFromTerminal(Terminal fromTerminal) {
        this.fromTerminal = fromTerminal;
    }

    public String getConfId() {
        return confId;
    }

    public void setConfId(String confId) {
        this.confId = confId;
    }

    public String getConfTitle() {
        return confTitle;
    }

    public void setConfTitle(String confTitle) {
        this.confTitle = confTitle;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getInviteId() {
        return inviteId;
    }

    public void setInviteId(String inviteId) {
        this.inviteId = inviteId;
    }

    @Override
    public String toString() {
        return "ResInvite{" +
                "action='" + action + '\'' +
                ", requestId='" + requestId + '\'' +
                ", fromTerminal=" + fromTerminal +
                ", confId='" + confId + '\'' +
                ", confTitle='" + confTitle + '\'' +
                ", siteId='" + siteId + '\'' +
                ", inviteId='" + inviteId + '\'' +
                '}';
    }
}
