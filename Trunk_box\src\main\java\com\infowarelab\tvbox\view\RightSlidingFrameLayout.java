package com.infowarelab.tvbox.view;


import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.Log;
import android.widget.FrameLayout;
import android.widget.Scroller;

import com.infowarelab.tvbox.ConferenceApplication;


/**
 * Scroller应用
 * <AUTHOR>
 * blog : http://blog.csdn.net/vipzjyno1/
 */
public class RightSlidingFrameLayout extends FrameLayout {
	/** Scroller 拖动类 */
	private Scroller mScroller;
	/** 屏幕高度  */
	private int mScreenHeigh = 0;
	/** 屏幕宽度  */
	private int mScreenWidth = 0;
	/** 是否在移动*/
	private Boolean isMoving = false;
	/** 布局的高度*/
	private int viewHeight = 0;
	/** 布局的宽度*/
	private int viewWidth = 0;
	/** 是否打开*/	
	public boolean isShow = false;
	/** 点击外面是否关闭该界面*/	
	public boolean mOutsideTouchable = true;
	/** 动画时间 */
	private int mDuration = 500;
	private final static String TAG = "AlwaysTest";
    public void setViewWidth(int viewWidth) {
        this.viewWidth = viewWidth;
    }

    public RightSlidingFrameLayout(Context context) {
		super(context);
		init(context);
	}
	public RightSlidingFrameLayout(Context context, AttributeSet attrs) {
		super(context, attrs);
		init(context);
	}
	public RightSlidingFrameLayout(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);
		init(context);
	}
	private void init(Context context) {
		setDescendantFocusability(FOCUS_AFTER_DESCENDANTS);
		setFocusable(true);
		mScroller = new Scroller(context);
		mScreenHeigh = ConferenceApplication.Root_H;
		mScreenWidth = ConferenceApplication.Root_W;
		// 背景设置成透明
		this.setBackgroundColor(Color.argb(0, 0, 0, 0));
		this.post(new Runnable() {
			@Override
			public void run() {
				viewHeight = RightSlidingFrameLayout.this.getHeight();
//				viewWidth = RightSlidingFrameLayout.this.getWidth();
				Log.i(TAG, "attsize="+mScreenHeigh+";"+viewHeight+";"+mScreenWidth+";"+viewWidth);
			}
		});
		int sx = -mScreenWidth;
		RightSlidingFrameLayout.this.scrollTo(sx, 0);
	}
	/**
	 * @param startX  
	 * @param endX  移动到某点的X坐标距离
	 * @param duration 时间
	 */
	public void startMoveAnim(int startX, int endX, int duration) {
		isMoving = true;
//		mScroller.startScroll(0, startY, 0, dy, duration);
		mScroller.startScroll(startX, 0, endX, 0, duration);
		invalidate();//通知UI线程的更新
	}
	@Override
	public void computeScroll() {
		if (mScroller.computeScrollOffset()) {
			scrollTo(mScroller.getCurrX(), mScroller.getCurrY());
			// 更新界面
			postInvalidate();
			isMoving = true;
		} else {
			isMoving = false;
		}
		super.computeScroll();
	}
	/** 打开 */
	public void show(){
		if(!isShow && !isMoving){
			RightSlidingFrameLayout.this.startMoveAnim(-viewWidth,viewWidth, mDuration);
			isShow = true;
			Log.d("isShow", "true");
			changed();
		}
	}
	/** 关闭 */
	public void dismiss(){
		if(isShow && !isMoving){
			RightSlidingFrameLayout.this.startMoveAnim(0, -viewWidth, mDuration);
			isShow = false;
			changed();
		}
	}
	/** 是否打开 */
	public boolean isShow(){
		return isShow;
	}
	/**
	 * 设置监听接口，实现开关
	 */
	public void setOnStatusListener(onStatusListener listener){
		this.statusListener = listener;
	}
    public void setOutsideTouchable(boolean touchable) {
        mOutsideTouchable = touchable;
    }
	/**
	 * 显示状态发生改变时候执行回调接口
	 */
	public void changed(){
		if(statusListener != null){
			if(isShow){
				statusListener.onShow();
			}else{
				statusListener.onDismiss();
			}
		}
	}
	/** 监听接口*/
	public onStatusListener statusListener;
	/**
	 * 监听接口，来在主界面监听界面变化状态
	 */
	public interface onStatusListener{
		/**  开打状态  */
		public void onShow();
		/**  关闭状态  */
		public void onDismiss();
	}
	
	@Override
	protected void onLayout(boolean changed, int l, int t, int r, int b) {
		// TODO Auto-generated method stub
		super.onLayout(changed, l, t, r, b);
	}
}
