package com.infowarelab.tvbox.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.transfer.Config;

/**
 * Created by sdvye on 2019/6/12.
 */

public class ActTool extends Activity implements View.OnClickListener {
    private Button btnUserSet;
    private Button btnWifiSet;
    private Button btnReset;
    private Button btnGetHelp;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.frag_tool);
        btnUserSet = (Button) findViewById(R.id.btn_user_set);
        btnWifiSet = (Button) findViewById(R.id.btn_wifi_set);
        btnReset = (Button) findViewById(R.id.btn_reset);
        btnGetHelp = (Button) findViewById(R.id.btn_get_help);
        btnUserSet.setOnClickListener(this);
        btnWifiSet.setOnClickListener(this);
        btnReset.setOnClickListener(this);
        btnGetHelp.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_user_set:
                Intent home = new Intent(this, ActHome.class);
                startActivity(home);
                break;
            case R.id.btn_wifi_set:
                startActivity(new Intent(Settings.ACTION_SETTINGS));
                break;
            case R.id.btn_reset:
                Toast.makeText(this, R.string.loading_user, Toast.LENGTH_SHORT).show();
                int id;
                if (Config.SiteId.equals("")) {
                    id = 0;
                } else {
                    id = Integer.parseInt(Config.SiteId);
                }
//                if (Config.terminateRegist(DeviceIdFactory.getDeviceId(this), Build.MODEL, id).equals("0")) {
//                    Toast.makeText(this, R.string.regist_success, Toast.LENGTH_SHORT).show();
//                } else if (Config.terminateRegist(DeviceIdFactory.getDeviceId(this), Build.MODEL, id).equals("-1")) {
//                    Toast.makeText(this, R.string.regist_fail, Toast.LENGTH_SHORT).show();
//                }
                break;
            case R.id.btn_get_help:
                Toast.makeText(this, R.string.get_for_help, Toast.LENGTH_SHORT).show();
                break;
        }
    }
}
