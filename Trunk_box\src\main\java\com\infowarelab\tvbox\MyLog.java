package com.infowarelab.tvbox;

public class MyLog {
    private boolean DEBUG = false;

    public static void getLineInfo() {
        StackTraceElement[] ste3 = Thread.currentThread().getStackTrace();
        StackTraceElement stack = ste3[ste3.length - 1];
        System.out.println("******************************************");
        System.out.println("Thread ID: "+Thread.currentThread().getId());
        System.out.println("Thread Name: "+Thread.currentThread().getName());
        System.out.println();
        System.out.println("File Name: "+stack.getFileName());
        System.out.println("Method Name: "+stack.getMethodName());
        System.out.println("Line Number: "+stack.getLineNumber());
        System.out.println("Class Name: "+stack.getClassName());
    }
}
