package com.infowarelab.tvbox;
 
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.widget.Toast;
import com.infowarelab.tvbox.activity.ActLogo;

public class UpdateStartBroadcastReceiver extends BroadcastReceiver{
    private final String ACTION_BOOT = "android.intent.action.PACKAGE_REPLACED";

    /**
     * 接收广播消息后都会进入 onReceive 方法，然后要做的就是对相应的消息做出相应的处理
     *
     * @param context 表示广播接收器所运行的上下文
     * @param intent  表示广播接收器收到的Intent
     */
    @Override
    public void onReceive(Context context, Intent intent) {

        /**
         * 如果 系统 启动的消息，则启动 APP 主页活动
         */
//        if (ACTION_BOOT.equals(intent.getAction())) {
//            Intent intentMainActivity = new Intent(context,ActLogo.class);
//            intentMainActivity.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            context.startActivity(intentMainActivity);
//            Toast.makeText(context, "恭喜您，红杉通视频会议App更新成功。", Toast.LENGTH_LONG).show();
//        }
    }
}