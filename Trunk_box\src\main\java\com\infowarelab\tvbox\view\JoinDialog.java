package com.infowarelab.tvbox.view;



import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.domain.ConferenceBean;

public class Join<PERSON><PERSON>og extends Dialog {
	private int width = 0;
	private OnResultListener onResultListener;
    private ConferenceBean conferenceBean;

    public JoinDialog(Context context) {
        super(context, R.style.style_dialog_normal);
    }
    public JoinDialog(Context context, int width) {
    	super(context, R.style.style_dialog_normal);
    	this.width = width;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_join);
//        if(width>0){
//        	LinearLayout ll = (LinearLayout) findViewById(R.id.dialog_cache_ll);
//        	LayoutParams params = (LayoutParams) ll.getLayoutParams();
//        	params.width = this.width;
//        	ll.setLayoutParams(params);
//        }
        setCanceledOnTouchOutside(true);

        setOnCancelListener(new OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                conferenceBean = null;
                doNo();
            }
        });

        final EditText etPwd = (EditText) findViewById(R.id.dialog_join_et_pwd);
        etPwd.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                //当actionId == XX_SEND 或者 XX_DONE时都触发  
                //或者event.getKeyCode == ENTER 且 event.getAction == ACTION_DOWN时也触发  
                //注意，这是一定要判断event != null。因为在某些输入法上会返回null。  
                if ((event != null && KeyEvent.KEYCODE_ENTER == event.getKeyCode() && KeyEvent.ACTION_DOWN == event.getAction())) {
                    String pwd = etPwd.getText().toString().replace(" ","");
                    if(null==pwd||pwd.equals("")){

                    }else {
                        doYes(pwd);
                        cancel();
                    }
                    return true;
                }
                    return false;
            }
        });
        TextView tvYes = (TextView) findViewById(R.id.dialog_join_tv_join);
        tvYes.setOnClickListener(new View.OnClickListener() {
			
			@Override
			public void onClick(View arg0) {
                String pwd = etPwd.getText().toString().replace(" ","");
                if(null==pwd||pwd.equals("")){

                }else {
                    doYes(pwd);
                    cancel();
                }
			}
		});
        TextView tvNo = (TextView) findViewById(R.id.dialog_join_tv_exit);
        tvNo.setOnClickListener(new View.OnClickListener() {
        	
        	@Override
        	public void onClick(View arg0) {
        		doNo();
        		cancel();
        	}
        });
        
    }

    @Override
    public void show() {
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        super.show();
    }

    public void show(ConferenceBean conferenceBean){
        this.conferenceBean = conferenceBean;
        show();
    }

    /**
     * 在AlertDialog的 onStart() 生命周期里面执行开始动画
     */
    @Override
    protected void onStart() {
        super.onStart();
    }

    /**
     * 在AlertDialog的onStop()生命周期里面执行停止动画
     */
    @Override
    protected void onStop() {
        conferenceBean = null;
        super.onStop();

    }
    
    public void setClickListener(OnResultListener onResultListener) {
		this.onResultListener = onResultListener;
	}
    public interface OnResultListener {
		public void doYes(String pwd);
		public void doNo();
	}
	private void doYes(String pwd) {
		if (onResultListener != null) {
			onResultListener.doYes(pwd);
		}
	}
	private void doNo() {

	if (onResultListener != null) {
		onResultListener.doNo();
	}
}
}
