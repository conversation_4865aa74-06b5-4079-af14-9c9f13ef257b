package com.infowarelab.tvbox.render;

import android.content.Context;
import android.util.Log;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;

public class DecodeFilter extends BaseFilter {

    public DecodeFilter(Context c) {
        super(c);
        mTextureUnit = ConferenceApplication.getTextureUnit();
        Log.d("InfowareLab.Debug", ">>>>>>DecodeFilter.textureUnit = " + mTextureUnit);
    }

    @Override
    public void setPath() {

        path1 = R.raw.base_vertex_shader;
        path2 = R.raw.base_fragment_shader;

    }

    @Override
    public void onDrawArraysPre() {

    }

    @Override
    public void onDrawArraysAfter() {

    }


}
