package com.infowarelab.tvbox.view;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;

import android.view.View;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.adapter.AttendersAdapter;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.confctrl.UserCommon;
import com.infowarelabsdk.conference.domain.UserBean;

public class PersonListDialog extends AlertDialog implements View.OnClickListener,
        AdapterView.OnItemSelectedListener{

    private TextView sumText;
    private ImageView backImage;
    private ListView listView;

    private AttendersAdapter attsAdapter;

    private UserCommonImpl userCommon;

    //获取焦点的下标
    private int fousePos = 0;

    public PersonListDialog(@NonNull Context context) {
        super(context, R.style.dialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_person_list);
        setCanceledOnTouchOutside(true);

        sumText = (TextView)findViewById(R.id.frag_att_tv_atts_sum);
        backImage = (ImageView) findViewById(R.id.dialog_personList_back);
        listView = (ListView)findViewById(R.id.dialog_personList_listview);

        userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();

        attsAdapter = new AttendersAdapter(getContext(), userCommon.getUsersList());
        listView.setAdapter(attsAdapter);

        sumText.setText(""+(attsAdapter.getCount()));


        backImage.setOnClickListener(this);

        listView.setOnItemSelectedListener(this);
        listView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (attsAdapter.getUsers() != null && attsAdapter.getUsers().size() > fousePos){
                    attsAdapter.getUsers().get(fousePos).setFouse(hasFocus);
                }
                attsAdapter.notifyDataSetChanged();
            }
        });

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.dialog_personList_back:
                if (isShowing()){
                    dismiss();
                }
                break;
            default:
                break;
        }
    }

    public void refreshAttenders() {
        fousePos = 0;
        if (attsAdapter != null){
            for (UserBean userBean : userCommon.getUsersList()){
                if(userBean.getDevice() == UserCommon.DEVICE_HIDEATTENDEE || userBean.getDevice() == UserCommon.DEVICE_CLOUDRECORD){
                    userCommon.getUsersList().remove(userBean);
                }
            }
            for (UserBean bean : userCommon.getUsersList()){
                if (0 == bean.getUid()){
                    userCommon.getUsersList().remove(bean);
                }
            }
            attsAdapter.update(userCommon.getUsersList(), sumText);
        }
        if (sumText != null){
            sumText.setText(""+(attsAdapter.getCount()));
        }
    }

    @Override
    public void show() {
        super.show();
        if (attsAdapter != null){
            refreshAttenders();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        backImage.setFocusable(false);
       if (attsAdapter != null && attsAdapter.getUsers().size() >0){
           listView.setFocusable(true);
           listView.requestFocus();
           attsAdapter.notifyDataSetChanged();
       }
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        UserBean bean = attsAdapter.getUsers().get(position);
        if (fousePos <= attsAdapter.getCount()-1){
            if (fousePos != position){
                attsAdapter.getUsers().get(fousePos).setFouse(false);
                bean.setFouse(true);
                fousePos = position;
                attsAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }
}
