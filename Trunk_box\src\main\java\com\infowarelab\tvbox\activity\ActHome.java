package com.infowarelab.tvbox.activity;

import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.media.AudioManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.ActionMode;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.SearchEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.View.OnFocusChangeListener;
import android.view.Window;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityEvent;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.content.FileProvider;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.base.LocalCameraPosition;
import com.infowarelab.tvbox.fragment.BaseFragment;
import com.infowarelab.tvbox.fragment.FargDial;
import com.infowarelab.tvbox.fragment.FargJoinConf;
import com.infowarelab.tvbox.fragment.FragConfList;
import com.infowarelab.tvbox.fragment.FragConfPwd;
import com.infowarelab.tvbox.fragment.FragDesktop;
import com.infowarelab.tvbox.fragment.FragInit;
import com.infowarelab.tvbox.fragment.FragMeetings;
import com.infowarelab.tvbox.fragment.FragNoMeeting;
import com.infowarelab.tvbox.fragment.FragSet;
import com.infowarelab.tvbox.fragment.FragSetting;
import com.infowarelab.tvbox.fragment.FragVersion;
import com.infowarelab.tvbox.grafika.PermissionHelper;
import com.infowarelab.tvbox.okhttp.abc.bean.VersionInfo;
import com.infowarelab.tvbox.okhttp.abc.constant.HttpConstant;
import com.infowarelab.tvbox.okhttp.abc.download.DownloadListener;
import com.infowarelab.tvbox.okhttp.abc.request.RequestCenter;
import com.infowarelab.tvbox.okhttp.listener.DisposeDataListener;
import com.infowarelab.tvbox.utils.DeviceIdFactory;
import com.infowarelab.tvbox.utils.DisplayUtil;
import com.infowarelab.tvbox.utils.FileUtils;
import com.infowarelab.tvbox.utils.PublicWay;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelab.tvbox.utils.SntpClient;
import com.infowarelab.tvbox.utils.XMLUtils;
import com.infowarelab.tvbox.view.CustomDialog;
import com.infowarelab.tvbox.view.ExitDialog;
import com.infowarelab.tvbox.view.RejoinDialog;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.AudioCommonImpl;
import com.infowarelabsdk.conference.common.impl.ChatCommomImpl;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.domain.ConferenceBean;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.ToastUtil;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.TimeZone;
import java.util.Timer;
import java.util.TimerTask;

public class ActHome extends BaseFragmentActivity implements OnClickListener, OnFocusChangeListener, BaseFragment.ICallParentView {

    private static final String TAG = "InfowareLab.Debug";
    public static String SHUTDOWN = "android.intent.action.system.shutdown";

    public volatile static ActHome mActivity = null;
    public static boolean isSetting = false;
    private ImageView iv1, iv2;
    //private TextView tvTitle;
    private ImageView ivWifi;
    private LinearLayout llNew;
    private LinearLayout llClose;
    private ImageButton ibClose;
    private TextView tvTime;
    private TextView tvDate;
    private TextView tvWifi;
    private FragmentManager fragManager;
    private int currentFrag = 0;
    private int lastFrag = 0;
    private boolean isSwitching = false;
    private FragConfList fragMeetings = null; //the list of meeting from server
    private FragDesktop fragDesktop = null;
    private FragConfPwd fragConfPwd = null;
    private FragSet fragSettings; //user settings: server address, name, account and password
    //	private FragSet fragSet;
    private FragSetting fragSet; //system setting page
    private FragVersion fragVersion; //show version information
    private FragNoMeeting fragNoMeeting;
    private FragInit fragInit; //system network and user settings
    SimpleDateFormat simpleDateFormat;
    SimpleDateFormat dateFormat;

    //记录上个页面
    private int lastPage = -1;
    private ImageView iv3 = null;
    private FargJoinConf fargDial = null;
    private boolean mRecovered = false;
    private String mExitMessage = "";
    private boolean mInitStatus = false;
    private BroadcastReceiver mNetReceiver;
    private boolean mExitedDueToNetworkIssue = false;
    private long mStartRejoinTime = 0;
    private boolean mRejoining = false;
    private LinearLayout llRoot;
    private Configuration mNewConfig;

    public void setLastPage(int lastPage) {
        this.lastPage = lastPage;
    }

    private ExitDialog exitDialog = null;
    private RejoinDialog rejoinDialog = null;

    private long mLastUpdateTime = 0;
    private long mUpdateIntevalTime = 10 * 1000;

    private long mMaxRejoinWaitTime = 15 * 1000;
    private long mMaxRejoinTimes = 20;
    private long mRejoinTimes = 0;

    private ConferenceBean mCurrentConfBean = null;

    private Handler handler = new Handler(Looper.getMainLooper());

    private void getScreenWidthHeight() {
        DisplayMetrics dm = new DisplayMetrics();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            getWindowManager().getDefaultDisplay().getRealMetrics(dm);
        } else {
            getWindowManager().getDefaultDisplay().getMetrics(dm);
        }
        ConferenceApplication.DENSITY = dm.density;
        if (dm.widthPixels > dm.heightPixels) {
            ConferenceApplication.SCREEN_WIDTH = dm.widthPixels;
            ConferenceApplication.SCREEN_HEIGHT = dm.heightPixels;
            ConferenceApplication.PPI_WIDTH = dm.xdpi;
            ConferenceApplication.PPI_HEIGHT = dm.ydpi;
        } else {
            ConferenceApplication.SCREEN_WIDTH = dm.heightPixels;
            ConferenceApplication.SCREEN_HEIGHT = dm.widthPixels;
            ConferenceApplication.PPI_WIDTH = dm.ydpi;
            ConferenceApplication.PPI_HEIGHT = dm.xdpi;
        }
        ConferenceApplication.Screen_W = ConferenceApplication.SCREEN_WIDTH;
        ConferenceApplication.Screen_H = ConferenceApplication.SCREEN_HEIGHT;
        ConferenceApplication.Root_W = llRoot.getWidth() > 0 ? llRoot.getWidth() : ConferenceApplication.Screen_W;
        ConferenceApplication.Root_H = llRoot.getHeight() > 0 ? llRoot.getHeight() : ConferenceApplication.Screen_H;
        int[] location = new int[2];
        llRoot.getLocationOnScreen(location);
        if (location[1] > 0) {
            ConferenceApplication.StateBar_H = location[1];
            ConferenceApplication.NavigationBar_H = ConferenceApplication.Screen_H - ConferenceApplication.Root_H - ConferenceApplication.StateBar_H;
        }

        ConferenceApplication.Top_H = 0;
        ConferenceApplication.Bottom_H = 0;

        Log.d(TAG, "ActHome ScreenW=" + ConferenceApplication.Screen_W
                + " ScreenH=" + ConferenceApplication.Screen_H
                + " RootW=" + ConferenceApplication.Root_W
                + " RootH=" + ConferenceApplication.Root_H
                + " StateH=" + ConferenceApplication.StateBar_H
                + " KeyH=" + ConferenceApplication.NavigationBar_H
                + " Density=" + ConferenceApplication.DENSITY
                + " Top_H=" + ConferenceApplication.Top_H
                + " Bottom_H=" + ConferenceApplication.Bottom_H);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        super.onCreate(savedInstanceState);

        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        Log.d("InfowareLab.Debug", "ActHome.onCreate");
        //DisplayUtil.disabledDisplayDpiChange(this.getResources());

        //
        setContentView(R.layout.act_home);
        fragMeetings = null;

        llRoot = findViewById(R.id.ll_root);

        getScreenWidthHeight();

        simpleDateFormat = new SimpleDateFormat("HH:mm");
        dateFormat = new SimpleDateFormat("yyyy"+getString(R.string.year)+"MM"+getString(R.string.month)+"dd"+getString(R.string.day));

        if (CommonFactory.getInstance().getConferenceCommon() == null) {
            CommonFactory.getInstance().setAudioCommon(new AudioCommonImpl()).setConferenceCommon(new ConferenceCommonImpl())
                    .setDocCommon(new DocCommonImpl()).setSdCommon(new ShareDtCommonImpl())
                    .setUserCommon(new UserCommonImpl()).setVideoCommon(new VideoCommonImpl()).setChatCommom(new ChatCommomImpl());

            Log.d("InfowareLab.Debug", "ActHome.onCreate: NULL common data!!!! and recreated!!!");
        }

        DocCommonImpl.mWidth = getWindowManager().getDefaultDisplay().getWidth();
        DocCommonImpl.mHeight = getWindowManager().getDefaultDisplay().getHeight();

        if (savedInstanceState != null){

            currentFrag = savedInstanceState.getInt("currentFrag", 0);
            Log.d("InfowareLab.Debug", "ActHome.onCreate: recovered currentFrag=" + currentFrag);
            mRecovered = true;
        }

        //setWakeLock();

        initView();

        if (!TextUtils.isEmpty( Config.Site_URL)){
            getVersionInfo();
        }

        Configuration config = getResources().getConfiguration();
        int smallestScreenWidth = config.smallestScreenWidthDp;
        Log.d("InfowareLab.Debug",">>>>> smallest width : "+ smallestScreenWidth);

        mLastUpdateTime = ConferenceApplication.currentTimeMillis();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (!PermissionHelper.hasPermission(this)) {
                PermissionHelper.requestPermission(this);
            }
        }

        initReceiver();

        Window homwWindow = getWindow();
        homwWindow.setCallback(new Window.Callback() {
            @Override
            public boolean dispatchKeyEvent(KeyEvent event) {
                Log.d(TAG, "===> ActHome.dispatchKeyEvent");
                mLastUpdateTime = ConferenceApplication.currentTimeMillis();
                return ActHome.super.dispatchKeyEvent(event);
            }

            @Override
            public boolean dispatchKeyShortcutEvent(KeyEvent event) {
                Log.d(TAG, "===> ActHome.dispatchKeyShortcutEvent");
                mLastUpdateTime = ConferenceApplication.currentTimeMillis();
                return ActHome.super.dispatchKeyShortcutEvent(event);
            }

            @Override
            public boolean dispatchTouchEvent(MotionEvent event) {
                Log.d(TAG, "===> ActHome.dispatchTouchEvent");
                mLastUpdateTime = ConferenceApplication.currentTimeMillis();
                return ActHome.super.dispatchTouchEvent(event);
            }

            @Override
            public boolean dispatchTrackballEvent(MotionEvent event) {
                Log.d(TAG, "===> ActHome.dispatchTrackballEvent");
                mLastUpdateTime = ConferenceApplication.currentTimeMillis();
                return ActHome.super.dispatchTrackballEvent(event);
            }

            @Override
            public boolean dispatchGenericMotionEvent(MotionEvent event) {
                //Log.d(TAG, "===> ActHome.dispatchGenericMotionEvent");
                mLastUpdateTime = ConferenceApplication.currentTimeMillis();
                return ActHome.super.dispatchGenericMotionEvent(event);
            }

            @Override
            public boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent event) {
                return ActHome.super.dispatchPopulateAccessibilityEvent(event);
            }

            @Nullable
            @Override
            public View onCreatePanelView(int featureId) {
                return ActHome.super.onCreatePanelView(featureId);
            }

            @Override
            public boolean onCreatePanelMenu(int featureId, @NonNull Menu menu) {
                return ActHome.super.onCreatePanelMenu(featureId, menu);
            }

            @Override
            public boolean onPreparePanel(int featureId, @Nullable View view, @NonNull Menu menu) {
                return ActHome.super.onPreparePanel(featureId, view, menu);
            }

            @Override
            public boolean onMenuOpened(int featureId, @NonNull Menu menu) {
                return ActHome.super.onMenuOpened(featureId, menu);
            }

            @Override
            public boolean onMenuItemSelected(int featureId, @NonNull MenuItem item) {
                return ActHome.super.onMenuItemSelected(featureId, item);
            }

            @Override
            public void onWindowAttributesChanged(WindowManager.LayoutParams attrs) {
                ActHome.super.onWindowAttributesChanged(attrs);
            }

            @Override
            public void onContentChanged() {
                ActHome.super.onContentChanged();
            }

            @Override
            public void onWindowFocusChanged(boolean hasFocus) {
                ActHome.super.onWindowFocusChanged(hasFocus);
            }

            @Override
            public void onAttachedToWindow() {
                ActHome.super.onAttachedToWindow();
            }

            @Override
            public void onDetachedFromWindow() {
                ActHome.super.onDetachedFromWindow();
            }

            @Override
            public void onPanelClosed(int featureId, @NonNull Menu menu) {
                ActHome.super.onPanelClosed(featureId, menu);
            }

            @Override
            public boolean onSearchRequested() {
                return ActHome.super.onSearchRequested();
            }

            @Override
            public boolean onSearchRequested(SearchEvent searchEvent) {
                return ActHome.super.onSearchRequested(searchEvent);
            }

            @Nullable
            @Override
            public ActionMode onWindowStartingActionMode(ActionMode.Callback callback) {
                return ActHome.super.onWindowStartingActionMode(callback);
            }

            @Nullable
            @Override
            public ActionMode onWindowStartingActionMode(ActionMode.Callback callback, int type) {
                return ActHome.super.onWindowStartingActionMode(callback, type);
            }

            @Override
            public void onActionModeStarted(ActionMode mode) {
                ActHome.super.onActionModeStarted(mode);
            }

            @Override
            public void onActionModeFinished(ActionMode mode) {
                ActHome.super.onActionModeFinished(mode);
            }
        });

        checkRejoinConfAction();
    }

    private void checkRejoinConfAction() {

        if (mExitedDueToNetworkIssue){

            mRejoinTimes = 0;

            int postDelayed = (int) (Math.random() * 3000);

            iv2.postDelayed(new Runnable() {
                @Override
                public void run() {
                    retryJoinConference();
                }
            }, postDelayed);
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        Log.d("InfowareLab.Debug", "ActHome.onSaveInstanceState");
        if (currentFrag == 3)
            outState.putInt("currentFrag", currentFrag);
        else
            outState.putInt("currentFrag", 0);

        //super.onSaveInstanceState(outState); //禁止保存Fragment信息
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {

        Log.d("InfowareLab.Debug","ActHome.onConfigurationChanged: " + newConfig.toString());

//        if (newConfig.fontScale != 1) {
//            //非默认值
//            getResources();
//        }

        super.onConfigurationChanged(newConfig);
    }

    @Override
    protected void onPause() {

        Log.d("InfowareLab.Debug", "ActHome.onPause");

        mTimeCorrected = false;

        super.onPause();
    }

    @Override
    protected void onResume() {

        Log.d("InfowareLab.Debug", "ActHome.onResume");
        mActivity = ActHome.this;
        if (fragMeetings != null) {
            fragMeetings.onParentResume();
        }

        CommonFactory commonFactory = CommonFactory.getInstance();
        if (null != commonFactory)
        {
            VideoCommonImpl vCommon = (VideoCommonImpl)commonFactory.getVideoCommon();
            if (null != vCommon)
                vCommon.clearMap();
        }
        //清空视频路数
        //((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).clearMap();

        FileUtils.DeleteFolder(Environment.getExternalStorageDirectory().getPath() + "/AudioRecord/");
        SharedPreferencesUrls.getInstance().putString("configId","");
        super.onResume();
//        if (PublicWay.socketBinder != null){
//            showLoading();
//            String RequestID = ""+ConferenceApplication.currentTimeMillis();
//            String deviceId = DeviceIdFactory.getUUID1(ActHome.this);
//            String siteId = FileUtil.readSharedPreferences(ActHome.this, Constants.SHARED_PREFERENCES,
//                    Constants.SITE_ID);
//            PublicWay.socketBinder.getService().sendOrderEx(XMLUtils.getXml_list(RequestID,deviceId,siteId));
//            new Handler().postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    hideLoading();
//                }
//            }, 1000);
//        }
    }

    @Override
    public void onBackPressed() {

        if (ConferenceApplication.enterFromIntent){
            //mEnterFromIntent = false;
            finish();
            //System.exit(0);
            lastPage = -1;
            return;
        }

        if (mInitStatus && (6 == currentFrag || 1 == currentFrag || 7 == currentFrag || 2 == currentFrag))
        {
            finish();
            //System.exit(0);
            lastPage = -1;
            return;
        }

        if (4 == currentFrag){
            switchFrag(2);
        }else if (0 == lastPage){
            switchFrag(6);
        }else if (1 == lastPage){
            switchFrag(2);
        }else if (9 == currentFrag){
            switchFrag(lastFrag);
        }
        else
            switchFrag(1);

        lastPage = -1;
    }

    private void showLeaveConfDialog(String exitMessage)
    {
        if (exitMessage.length() > 0) {

            if (exitDialog == null) {
                exitDialog = new ExitDialog(this, 0, true);
                exitDialog.setContext(exitMessage);
                exitDialog.setClickListener(new ExitDialog.OnResultListener() {
                    @Override
                    public void doYes() {
                        // TODO Auto-generated method stub
                    }

                    @Override
                    public void doNo() {
                    }
                });
                if (!exitDialog.isShowing()) {
                    exitDialog.show();
                }
            } else {
                exitDialog.setContext(exitMessage);
                if (!exitDialog.isShowing()) {
                    exitDialog.show();
                }
            }
        }
    }

    private void showRejoinConfDialog(){

        if (rejoinDialog == null) {
            rejoinDialog = new RejoinDialog(mActivity, 0);
            rejoinDialog.setCancelable(false);
            //dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);

            rejoinDialog.setOnResultListener(new RejoinDialog.OnResultListener() {
                @Override
                public void doYes() {
                    // TODO Auto-generated method stub
                }
                @Override
                public void doNo() {
                    // TODO Auto-generated method stub
                    mExitedDueToNetworkIssue = false;
                }
            });
            if (!rejoinDialog.isShowing()) {
                rejoinDialog.show();
            }
        } else {

            if (!rejoinDialog.isShowing()){
                rejoinDialog.show();
            }
        }
    }

    private void hideRejoinConfDialog(){

        if (rejoinDialog != null) {
            if (rejoinDialog.isShowing()){
                rejoinDialog.hide();
            }
        }
    }

    private void initView() {
        Log.d("InfowareLab.Debug", "ActHome.initView");
        fragManager = getSupportFragmentManager();
        ibClose = (ImageButton) findViewById(R.id.ib_close);
        ibClose.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(SHUTDOWN);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                sendBroadcast(intent);
            }
        });

        ivWifi = (ImageView) findViewById(R.id.iv_wifi);
        tvWifi = (TextView) findViewById(R.id.tv_wifi);
        tvWifi.setText(getIPAddress(this));
        tvTime = (TextView) findViewById(R.id.tv_time);
        tvDate = (TextView) findViewById(R.id.tv_date);

        llNew = findViewById(R.id.ll_new);
        llClose = (LinearLayout) findViewById(R.id.ll_close);
        //tvTitle = findViewById(R.id.tv_title);
        iv1 = findViewById(R.id.iv_home_1);
        iv2 = (ImageView) findViewById(R.id.iv_home_2);
        iv3 = (ImageView)findViewById(R.id.iv_home_3);


        iv1.setFocusable(true);
        iv1.requestFocus();

        iv1.setOnClickListener(this);
        iv2.setOnClickListener(this);
        iv3.setOnClickListener(this);
        iv1.setOnFocusChangeListener(this);
        iv2.setOnFocusChangeListener(this);
        iv3.setOnFocusChangeListener(this);
        iv1.setVisibility(View.GONE);
        iv2.setVisibility(View.GONE);
        iv3.setVisibility(View.GONE);
        iv1.post(new Runnable() {
            @Override
            public void run() {
                Intent intent = getIntent();
                String action = intent.getAction();
                if (action != null && action.equals("Init")) {

                    mInitStatus = true;

                    if (mRecovered && currentFrag > 0) {
                        int targetFragment = currentFrag;
                        currentFrag = 0;
                        Log.d("InfowareLab.Debug", "ActHome.initView: recovered targetFragment = " + targetFragment);
                        switchFrag(targetFragment);
                    }else
                        switchFrag(6);
                } else {

                    mInitStatus = false;

                    iv1.setVisibility(View.VISIBLE);
                    iv2.setVisibility(View.VISIBLE);
                    iv3.setVisibility(View.VISIBLE);
                    switchFrag(1);
                }
            }
        });
        
        new TimeThread().start(); //启动新的线程

        mExitMessage = "";
        Intent intent = getIntent();
        boolean showExitMessage = intent.getBooleanExtra("showExitMessage", false);

        if (showExitMessage) {
            mExitMessage = intent.getStringExtra("Message");
            mExitedDueToNetworkIssue = intent.getBooleanExtra("exitedDueToNetworkIssue", false);
        }

        if (!mExitedDueToNetworkIssue && showExitMessage && mExitMessage.length() > 0){
            iv2.post(new Runnable() {
                @Override
                public void run() {
                   showLeaveConfDialog(mExitMessage);
                }
            });
        }
    }

    private void retryJoinConference() {

        showRejoinConfDialog();

        if (fragMeetings != null) {

            Log.d("InfowareLab.Net", "retryJoinConference: times = " + mRejoinTimes);

            if (mRejoinTimes > mMaxRejoinTimes){

                Log.d("InfowareLab.Net", "retryJoinConference: reached max retry times = " + mMaxRejoinTimes);
                mRejoinTimes = 0;
                mRejoining = false;
                mExitedDueToNetworkIssue = false;
                hideRejoinConfDialog();
                return;
            }

            if (!mExitedDueToNetworkIssue){

                Log.d("InfowareLab.Net", "retryJoinConference: cancelled! try times = " + mRejoinTimes);
                mRejoinTimes = 0;
                mRejoining = false;
                hideRejoinConfDialog();
                return;
            }

            Log.d("InfowareLab.Net", "=========> rejoining ..." + mRejoinTimes);

            fragMeetings.rejoinConf(mHandler);

            mRejoinTimes++;

            mStartRejoinTime = System.currentTimeMillis();

            mRejoining = true;
        }
    }

    public String getIPAddress(Context context) {
        NetworkInfo info = ((ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE)).getActiveNetworkInfo();
        if (info != null && info.isConnected()) {
            if (info.getType() == ConnectivityManager.TYPE_MOBILE) {    // 当前使用2G/3G/4G网络
                try {
                    for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements(); ) {
                        NetworkInterface intf = en.nextElement();
                        for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
                            InetAddress inetAddress = enumIpAddr.nextElement();
                            if (!inetAddress.isLoopbackAddress() && inetAddress instanceof Inet4Address) {
                                return inetAddress.getHostAddress();
                            }
                        }
                    }
                } catch (SocketException e) {
                    e.printStackTrace();
                }

            } else if (info.getType() == ConnectivityManager.TYPE_WIFI) {    // 当前使用无线网络
                WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                String ipAddress = intIP2StringIP(wifiInfo.getIpAddress());    // 得到IPV4地址
                return ipAddress;
            }
        }
        return null;
    }

    private void setWakeLock() {
        try {
            Process process = null;
            process = Runtime.getRuntime().exec("su");
            OutputStream out = process.getOutputStream();
            out.write(("echo PowerManagerService.noSuspend > /sys/power/wake_lock").getBytes());
            Thread.sleep(100);
            out.flush();
            out.close();
            Log.w(TAG, "setPowerWakeLock: 写入成功");
        } catch (InterruptedException | IOException e) {
            Log.w(TAG, "setPowerWakeLock: 写入失败");
            e.printStackTrace();
        }
    }

    public static String intIP2StringIP(int ip) {
        return (ip & 0xFF) + "." +
                ((ip >> 8) & 0xFF) + "." +
                ((ip >> 16) & 0xFF) + "." +
                (ip >> 24 & 0xFF);
    }

//    public static String getIP(int ip) {
//        return (ip & 0xFF) + "." + ((ip >> 8) & 0xFF) + "." + ((ip >> 16) & 0xFF) + "." + ((ip >> 24) & 0xFF);
//    }

    public void switchFrag(int which) {
        if (currentFrag == which || isSwitching) return;
        isSwitching = true;

        Log.d("InfowareLab.Debug", "ActHome.switchFrag to " + which);

        FragmentTransaction ft;
        ft = fragManager.beginTransaction();
        switch (which) {
            case 1:
                Intent intent = getIntent();
                String action = intent.getAction();
                if (action != null && action.equals("Init")) {
                    intent.setAction("List");
                    mInitStatus = false;
                }

                isSetting = false;
                iv1.setFocusable(true);
                iv1.requestFocus();
                //tvTitle.setVisibility(View.VISIBLE);
                llNew.setVisibility(View.VISIBLE);
                if (Build.MODEL.contains("HSYP")){
                    llClose.setVisibility(View.VISIBLE);
                }
                setTitlePadding(1);
                ft.setCustomAnimations(R.anim.anim_in_back_activity, R.anim.anim_out_back_activity);
                if (fragSet != null && fragSet.isAdded())
                    ft.hide(fragSet);
                if (fragInit != null && fragInit.isAdded())
                    ft.hide(fragInit);
                if (fragNoMeeting != null && fragNoMeeting.isAdded())
                    ft.hide(fragNoMeeting);
                if (fragVersion != null && fragVersion.isAdded())
                    ft.hide(fragVersion);
                if (fragSettings != null && fragSettings.isAdded())
                    ft.hide(fragSettings);
                if (fargDial != null && fargDial.isAdded())
                    ft.hide(fargDial);
                if (fragDesktop != null && fragDesktop.isAdded())
                    ft.hide(fragDesktop);
                if (fragConfPwd != null && fragConfPwd.isAdded())
                    ft.hide(fragConfPwd);
                if (fragMeetings == null) {
//                    fragMeetings = new FragMeetings(this);
                    Log.d("InfowareLab.Debug", "new FragMeetings(1)");
                    fragMeetings = new FragConfList();
                    //回调
                    fragMeetings.setiCallParentView(this);
                    ft.add(R.id.fl_home_container, fragMeetings, "Meetings");
                } else {
                    ft.show(fragMeetings);
                    fragMeetings.getConfListData();
                }
                lastFrag = currentFrag;
                currentFrag = 1;
                break;
            case 2:
                isSetting = true;
                //tvTitle.setVisibility(View.VISIBLE);
                llNew.setVisibility(View.VISIBLE);
                iv2.setFocusable(true);
                iv2.requestFocus();
                if (Build.MODEL.contains("HSYP")){
                    llClose.setVisibility(View.VISIBLE);
                }
                setTitlePadding(2);
                ft.setCustomAnimations(R.anim.anim_in_forward_activity, R.anim.anim_out_forward_activity);
                if (fragMeetings != null && fragMeetings.isAdded())
                    ft.hide(fragMeetings);
                if (fragVersion != null && fragVersion.isAdded())
                    ft.hide(fragVersion);
                if (fragInit != null && fragInit.isAdded())
                    ft.hide(fragInit);
                if (fragNoMeeting != null && fragNoMeeting.isAdded())
                    ft.hide(fragNoMeeting);
                if (fragSettings != null && fragSettings.isAdded())
                    ft.hide(fragSettings);
                if (fargDial != null && fargDial.isAdded())
                    ft.hide(fargDial);
                if (fragDesktop != null && fragDesktop.isAdded())
                    ft.hide(fragDesktop);
                if (fragConfPwd != null && fragConfPwd.isAdded())
                    ft.hide(fragConfPwd);
                if (fragSet == null) {
//                    fragSet = new FragSetting(this);
                    fragSet = new FragSetting();
                    fragSet.setiCallParentView(this);
                    ft.add(R.id.fl_home_container, fragSet, "Set");
                } else {
                    ft.show(fragSet);
                }
                fragSet.setFouse();
                lastFrag = currentFrag;
                currentFrag = 2;
                break;
            case 3:
                isSetting = false;
                //tvTitle.setVisibility(View.GONE);
                llNew.setVisibility(View.GONE);
                iv2.setFocusable(true);
                iv2.requestFocus();
                llClose.setVisibility(View.GONE);
                ft.setCustomAnimations(R.anim.anim_in_forward_activity, R.anim.anim_out_forward_activity);
                if (fragMeetings != null && fragMeetings.isAdded())
                    ft.hide(fragMeetings);
                if (fragNoMeeting != null && fragNoMeeting.isAdded())
                    ft.hide(fragNoMeeting);
                if (fragInit != null && fragInit.isAdded())
                    ft.hide(fragInit);
                if (fragSet != null && fragSet.isAdded())
                    ft.hide(fragSet);
                if (fragVersion != null && fragVersion.isAdded())
                    ft.hide(fragVersion);
                if (fragDesktop != null && fragDesktop.isAdded())
                    ft.hide(fragDesktop);
                if (fragConfPwd != null && fragConfPwd.isAdded())
                    ft.hide(fragConfPwd);
                if (fragSettings == null) {
//                    fragSettings = new FragSet(this);
                    fragSettings = new FragSet();
                    fragSettings.setiCallParentView(this);
                    ft.add(R.id.fl_home_container, fragSettings, "Settings");
                } else {
                    ft.show(fragSettings);
                }
                if (fargDial != null && fargDial.isAdded())
                    ft.hide(fargDial);
                lastFrag = currentFrag;
                currentFrag = 3;
                break;
            case 4:
                isSetting = false;
                //tvTitle.setVisibility(View.VISIBLE);
                llNew.setVisibility(View.VISIBLE);
                if (Build.MODEL.contains("HSYP")){
                    llClose.setVisibility(View.VISIBLE);
                }
                setTitlePadding(1);
                if (fragMeetings != null && fragMeetings.isAdded())
                    ft.hide(fragMeetings);
                if (fragInit != null && fragInit.isAdded())
                    ft.hide(fragInit);
                if (fragNoMeeting != null && fragNoMeeting.isAdded())
                    ft.hide(fragNoMeeting);
                if (fragSettings != null && fragSettings.isAdded())
                    ft.hide(fragSettings);
                if (fargDial != null && fargDial.isAdded())
                    ft.hide(fargDial);
                if (fragSet != null && fragSet.isAdded())
                    ft.hide(fragSet);
                if (fragDesktop != null && fragDesktop.isAdded())
                    ft.hide(fragDesktop);
                if (fragConfPwd != null && fragConfPwd.isAdded())
                    ft.hide(fragConfPwd);
                if (fragVersion == null){
//                    fragVersion = new FragVersion(this);
                    fragVersion = new FragVersion();
                    fragVersion.setiCallParentView(this);
                    ft.add(R.id.fl_home_container, fragVersion, "Version");
                } else {
                    ft.show(fragVersion);
                }
                lastFrag = currentFrag;
                currentFrag = 4;
                break;
            case 5:
                isSetting = false;
                //tvTitle.setVisibility(View.VISIBLE);
                llNew.setVisibility(View.VISIBLE);
                if (Build.MODEL.contains("HSYP")){
                    llClose.setVisibility(View.VISIBLE);
                }
                setTitlePadding(1);
                if (fragMeetings != null && fragMeetings.isAdded())
                    ft.hide(fragMeetings);
                if (fragInit != null && fragInit.isAdded())
                    ft.hide(fragInit);
                if (fragSettings != null && fragSettings.isAdded())
                    ft.hide(fragSettings);
                if (fargDial != null && fargDial.isAdded())
                    ft.hide(fargDial);
                if (fragVersion != null && fragVersion.isAdded())
                    ft.hide(fragVersion);
                if (fragSet != null && fragSet.isAdded())
                    ft.hide(fragSet);
                if (fragDesktop != null && fragDesktop.isAdded())
                    ft.hide(fragDesktop);
                if (fragConfPwd != null && fragConfPwd.isAdded())
                    ft.hide(fragConfPwd);
                if (fragNoMeeting == null) {
//                    fragNoMeeting = new FragNoMeeting(this);
                    fragNoMeeting = new FragNoMeeting();
                    fragNoMeeting.setiCallParentView(this);
                    ft.add(R.id.fl_home_container, fragNoMeeting, "No");
                } else {
                    ft.show(fragNoMeeting);
                }
                lastFrag = currentFrag;
                currentFrag = 5;
                break;
            case 6:
                isSetting = false;
                //tvTitle.setVisibility(View.GONE);
                llNew.setVisibility(View.GONE);
                if (Build.MODEL.contains("HSYP")){
                    llClose.setVisibility(View.VISIBLE);
                }
                ft.setCustomAnimations(R.anim.anim_in_forward_activity, R.anim.anim_out_forward_activity);
                if (fragMeetings != null && fragMeetings.isAdded())
                    ft.hide(fragMeetings);
                if (fragSettings != null && fragSettings.isAdded())
                    ft.hide(fragSettings);
                if (fargDial != null && fargDial.isAdded())
                    ft.hide(fargDial);
                if (fragNoMeeting != null && fragNoMeeting.isAdded())
                    ft.hide(fragNoMeeting);
                if (fragVersion != null && fragVersion.isAdded())
                    ft.hide(fragVersion);
                if (fragSet != null && fragSet.isAdded())
                    ft.hide(fragSet);
                if (fragDesktop != null && fragDesktop.isAdded())
                    ft.hide(fragDesktop);
                if (fragConfPwd != null && fragConfPwd.isAdded())
                    ft.hide(fragConfPwd);
                if (fragInit == null) {
//                    fragInit = new FragInit(this);
                    fragInit = new FragInit();
                    fragInit.setiCallParentView(this);
                    ft.add(R.id.fl_home_container, fragInit, "Init");
                } else {
                    ft.show(fragInit);
                }
                lastFrag = currentFrag;
                currentFrag = 6;
                break;
            case 7:
                isSetting = false;
                setTitlePadding(3);
                iv3.setFocusable(true);
                iv3.requestFocusFromTouch();
                iv3.requestFocus();
                //tvTitle.setVisibility(View.VISIBLE);
                llNew.setVisibility(View.VISIBLE);
                if (Build.MODEL.contains("HSYP")){
                    llClose.setVisibility(View.VISIBLE);
                }
                ft.setCustomAnimations(R.anim.anim_in_forward_activity, R.anim.anim_out_forward_activity);
                if (fragMeetings != null && fragMeetings.isAdded())
                    ft.hide(fragMeetings);
                if (fragSettings != null && fragSettings.isAdded())
                    ft.hide(fragSettings);
                if (fragNoMeeting != null && fragNoMeeting.isAdded())
                    ft.hide(fragNoMeeting);
                if (fragVersion != null && fragVersion.isAdded())
                    ft.hide(fragVersion);
                if (fragSet != null && fragSet.isAdded())
                    ft.hide(fragSet);
                if (fragDesktop != null && fragDesktop.isAdded())
                    ft.hide(fragDesktop);
                if (fragConfPwd != null && fragConfPwd.isAdded())
                    ft.hide(fragConfPwd);
                if (fargDial == null) {
                    fargDial = new FargJoinConf();
                    fargDial.setiCallParentView(this);
                    ft.add(R.id.fl_home_container, fargDial, "fargDial");
                } else {
                    ft.show(fargDial);
                }
                lastFrag = currentFrag;
                currentFrag = 7;
                break;
            case 8:
                isSetting = false;
                //tvTitle.setVisibility(View.VISIBLE);
                llNew.setVisibility(View.VISIBLE);
                iv2.setFocusable(true);
                iv2.requestFocus();
                if (Build.MODEL.contains("HSYP")){
                    llClose.setVisibility(View.VISIBLE);
                }
                setTitlePadding(2);
                ft.setCustomAnimations(R.anim.anim_in_forward_activity, R.anim.anim_out_forward_activity);
                if (fragMeetings != null && fragMeetings.isAdded())
                    ft.hide(fragMeetings);
                if (fragVersion != null && fragVersion.isAdded())
                    ft.hide(fragVersion);
                if (fragInit != null && fragInit.isAdded())
                    ft.hide(fragInit);
                if (fragNoMeeting != null && fragNoMeeting.isAdded())
                    ft.hide(fragNoMeeting);
                if (fragSettings != null && fragSettings.isAdded())
                    ft.hide(fragSettings);
                if (fargDial != null && fargDial.isAdded())
                    ft.hide(fargDial);
                if (fragSet != null && fragSet.isAdded())
                    ft.hide(fragSet);
                if (fragConfPwd != null && fragConfPwd.isAdded())
                    ft.hide(fragConfPwd);
                if (fragDesktop == null) {
//                    fragSet = new FragSetting(this);
                    fragDesktop = new FragDesktop();
                    fragDesktop.setiCallParentView(this);
                    ft.add(R.id.fl_home_container, fragDesktop, "Desktop");
                } else {
                    ft.show(fragDesktop);
                }
                lastFrag = currentFrag;
                currentFrag = 8;
                break;
            case 9:
                isSetting = false;
                //tvTitle.setVisibility(View.VISIBLE);
                llNew.setVisibility(View.VISIBLE);
                iv2.setFocusable(true);
                iv2.requestFocus();
                if (Build.MODEL.contains("HSYP")){
                    llClose.setVisibility(View.VISIBLE);
                }
                setTitlePadding(2);
                ft.setCustomAnimations(R.anim.anim_in_forward_activity, R.anim.anim_out_forward_activity);
                if (fragMeetings != null && fragMeetings.isAdded())
                    ft.hide(fragMeetings);
                if (fragVersion != null && fragVersion.isAdded())
                    ft.hide(fragVersion);
                if (fragInit != null && fragInit.isAdded())
                    ft.hide(fragInit);
                if (fragNoMeeting != null && fragNoMeeting.isAdded())
                    ft.hide(fragNoMeeting);
                if (fragSettings != null && fragSettings.isAdded())
                    ft.hide(fragSettings);
                if (fargDial != null && fargDial.isAdded())
                    ft.hide(fargDial);
                if (fragSet != null && fragSet.isAdded())
                    ft.hide(fragSet);
                if (fragDesktop != null && fragDesktop.isAdded())
                    ft.hide(fragDesktop);
                if (fragConfPwd == null) {
//                    fragSet = new FragSetting(this);
                    fragConfPwd = new FragConfPwd();
                    fragConfPwd.setiCallParentView(this);
                    fragConfPwd.setConferenceBean(mCurrentConfBean);
                    ft.add(R.id.fl_home_container, fragConfPwd, "ConfPwd");
                } else {
                    fragConfPwd.setConferenceBean(mCurrentConfBean);
                    ft.show(fragConfPwd);
                }
                lastFrag = currentFrag;
                currentFrag = 9;
                break;
            default:
                break;
        }
        ft.commitAllowingStateLoss();
        isSwitching = false;
    }

    private void setTitlePadding(int n) {
        if (n == 1) {
            iv1.setImageResource(R.drawable.btn_home_home_selected);
            iv2.setImageResource(R.drawable.bg_home_set_noselected_nor);
            iv3.setImageResource(R.drawable.bg_home_tab3_noselected_nor);
        } else if (n == 2){
            iv1.setImageResource(R.drawable.bg_home_home_noselected_nor);
            iv2.setImageResource(R.drawable.btn_home_set_selected);
            iv3.setImageResource(R.drawable.bg_home_tab3_noselected_nor);
        }else {
            iv1.setImageResource(R.drawable.bg_home_home_noselected_nor);
            iv2.setImageResource(R.drawable.bg_home_set_noselected_nor);
            iv3.setImageResource(R.drawable.btn_home_tab3_selected);
        }
    }

    @Override
    public void onClick(View v) {
        // TODO Auto-generated method stub
        switch (v.getId()) {
            case R.id.iv_home_1:
                switchFrag(1);
                break;
            case R.id.iv_home_2:
                switchFrag(2);
                break;
            case R.id.iv_home_3:
                switchFrag(7);
            default:
                break;
        }
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        // TODO Auto-generated method stub
        if (isFinishing()) return;
//        if (v.getId() == R.id.iv_home_1 && hasFocus) {
//            if (4 != currentFrag && 2 != currentFrag && 3 != currentFrag){
//                switchFrag(1);
//                iv2.setFocusable(true);
//                iv1.setFocusable(true);
//                iv3.setFocusable(true);
//                if (fragMeetings != null) fragMeetings.onLosefocus();
//            }
//        } else if (v.getId() == R.id.iv_home_2 && hasFocus) {
//            if (currentFrag == 1 || currentFrag == 5
//                    || currentFrag == 7) {
//                switchFrag(2);
//            }
//            iv2.setFocusable(true);
//            iv1.setFocusable(true);
//            iv3.setFocusable(true);
//        }else if (v.getId() == R.id.iv_home_3 && hasFocus){
//            if (currentFrag == 1 || currentFrag == 2 || currentFrag == 5){
//                switchFrag(7);
//            }
//            iv2.setFocusable(true);
//            iv1.setFocusable(true);
//            iv3.setFocusable(true);
//        }else if (!iv1.isFocused() && !iv2.isFocused() && !iv3.isFocused()) {
//            if (currentFrag == 1) {
//                iv2.setFocusable(false);
//                iv1.setFocusable(true);
//                iv3.setFocusable(false);
//            } else if (currentFrag == 2){
//                iv1.setFocusable(false);
//                iv2.setFocusable(true);
//                iv3.setFocusable(false);
//            }else{
//                iv1.setFocusable(true);
//                iv2.setFocusable(true);
//                iv3.setFocusable(true);
//            }
//        }
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event)
    {
        if (null == fragMeetings) return super.dispatchKeyEvent(event);

        if (event.getAction() == KeyEvent.ACTION_DOWN)
        {
            //Log.d("InfowareLab.Debug", "======>ActHome.dispatchKeyEvent: " + event.getKeyCode());
            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {

                if (fragMeetings.isVisible() && fragMeetings.isGridViewFocused() && fragMeetings.getConferenceCount() <= 1)
                    // doSth...
                    return true;
            }
        }

        if (event.getAction() == KeyEvent.ACTION_DOWN)
        {
            Log.d("InfowareLab.Key", "======>ActHome.dispatchKeyEvent: " + event.getKeyCode());
            if (KeyEvent.KEYCODE_DEL == event.getKeyCode() || (event.getKeyCode() >= KeyEvent.KEYCODE_0 && event.getKeyCode() <= KeyEvent.KEYCODE_9)) {

                if (fargDial != null && fargDial.isAdded() && fargDial.isVisible()){
                    fargDial.onKeyPressed(event.getKeyCode());
                    // doSth...
                    return true;
                }

                if (fragConfPwd != null && fragConfPwd.isAdded() && fragConfPwd.isVisible()){
                    fragConfPwd.onKeyPressed(event.getKeyCode());
                    // doSth...
                    return true;
                }
            }
        }


        return super.dispatchKeyEvent(event);
    }

    @Override
    public void onCallParentView(String msg, Object obj) {
        switch (msg) {
            case BaseFragment.ACTION_UPDATELIST:
                if (fragMeetings != null) fragMeetings.getConfListData();
                break;
            case BaseFragment.ACTION_SETFINISH:
                iv1.setVisibility(View.VISIBLE);
                iv2.setVisibility(View.VISIBLE);
                iv3.setVisibility(View.VISIBLE);
                switchFrag(1);
                //此处没有必要去刷数据
//                if (fragMeetings != null) fragMeetings.getConfListData();
                break;
            case BaseFragment.ACTION_SHOWLOADING:
                showLoading();
                break;
            case BaseFragment.ACTION_HIDELOADING:
                hideLoading();
                break;
            case BaseFragment.ACTION_JOIN_CONF:
                switchFrag(7);
                break;
            case BaseFragment.ACTION_SHARE_SCREEN:
                switchFrag(8);
                break;
            case BaseFragment.ACTION_RETURN_DESKTOP:
                moveTaskToBack(true);
                //finish();
                break;
            case BaseFragment.ACTION_SETTINGS:
                switchFrag(2);
                break;
            case BaseFragment.ACTION_SHOWHOME:
                switchFrag(1);
                break;
            case BaseFragment.ACTION_RETURN:
                onBackPressed();
                break;
            case BaseFragment.ACTION_CONF_PWD:
                mCurrentConfBean = (ConferenceBean) obj;
                switchFrag(9);
                break;
            default:
                break;
        }
    }

    class TimeThread extends Thread {
        @Override
        public void run() {
            do {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP){
                    try {

                        boolean needCorrectTime = !mTimeCorrected;

                        if (!mTimeCorrected) correctTimeFromServer();

                        if (!mTimeCorrected) correctTimeFromAliyunNTP();

                        if (!mTimeCorrected) correctTimeFromBaidu();

                        if (!mTimeCorrected) correctTimeFromNTP();

                        if (mActivity == null /*|| currentFrag != 1*/) {
                            Log.d(TAG, "exit the time correction since home is destroyed.");
                            break;
                        }

                        if (needCorrectTime) {
                            Log.d(TAG, "refresh conference list after time correction.");
                            if (fragMeetings != null) fragMeetings.getConfListData();
                        }

                        Message msg = new Message();
                        msg.what = 1;  //消息(一个整型值)
                        msg.obj = ConferenceApplication.currentTimeMillis();

                        //Log.d(TAG, ">>>>>>refresh time: " + msg.obj);

                        mHandler.sendMessage(msg);// 每隔1秒发送一个msg给mHandler

                    } catch (Exception e) {
                        Log.d("InfowareLab.Debug", "(ActHome)Time exception: " + e.getMessage());
                        e.printStackTrace();
                        Message msg = new Message();
                        msg.what = 1;  //消息(一个整型值)
                        msg.obj = ConferenceApplication.currentTimeMillis();//获取系统时间
                        mHandler.sendMessage(msg);// 每隔1秒发送一个msg给mHandler
                    }
                }
                else
                {
                    Message msg = new Message();
                    msg.what = 1;  //消息(一个整型值)
                    msg.obj = ConferenceApplication.currentTimeMillis();
                    mHandler.sendMessage(msg);// 每隔1秒发送一个msg给mHandler
                }

            } while (null != mActivity);

            Log.d("InfowareLab.Debug", "ActHome: Exit the time thread");
        }
    }

    private boolean correctTimeFromAliyunNTP() {

        String NTPServer = "ntp1.aliyun.com";
        NTPServer = NTPServer.replace("http://", "");

        Log.d("InfowareLab", "(ActHome) NTPServer(Aliyun): " + NTPServer);

        SntpClient sntpClient = new SntpClient();
        long now;

        if (sntpClient.requestTime(NTPServer, 10000)) {
            now = sntpClient.getNtpTime() + SystemClock.elapsedRealtime() - sntpClient.getNtpTimeReference();

            if (now <= 0)
                return false;

            long systemDate = System.currentTimeMillis();

            ConferenceApplication.setTimeGap(now-systemDate);

            Date current = new Date(now);

            mTimeCorrected = true;

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+08"));
            Log.d("InfowareLab", "(ActHome) Date From NTP: " + dateFormat.format(current));

            return true;
        }

        return false;
    }

    private boolean correctTimeFromNTP() {

        if (Config.Site_URL == null || Config.Site_URL.equals(""))
            return false;

        String NTPServer = Config.Site_URL;
        NTPServer = NTPServer.replace("http://", "");

        Log.d("InfowareLab", "(ActHome) NTPServer(Internal): " + NTPServer);

        SntpClient sntpClient = new SntpClient();
        long now;

        if (sntpClient.requestTime(NTPServer, 10000)) {
            now = sntpClient.getNtpTime() + SystemClock.elapsedRealtime() - sntpClient.getNtpTimeReference();

            if (now <= 0)
                return false;

            long systemDate = System.currentTimeMillis();

            ConferenceApplication.setTimeGap(now-systemDate);

            Date current = new Date(now);

            mTimeCorrected = true;

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+08"));
            Log.d("InfowareLab", "(ActHome) Date From NTP: " + dateFormat.format(current));

            return true;
        }

        return false;
    }

    private boolean correctTimeFromBaidu() {

        String strUrl = "http://www.baidu.com";//Config.Site_URL + "?from=ET";

        //if (Config.Site_URL == null || Config.Site_URL.equals(""))
        //    strUrl = "http://www.baidu.com";

        URL url;//取得资源对象
        try {
            url = new URL(strUrl);
            URLConnection uc=url.openConnection();//生成连接对象
            uc.connect();
            long date = uc.getDate();
            long systemDate = System.currentTimeMillis();
            Date date2 = new Date(date);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+08"));
            Log.d("InfowareLab", "(ActHome) Date From Baidu: " + dateFormat.format(date2));

            mTimeCorrected = true;
            ConferenceApplication.setTimeGap(date-systemDate);

            Log.d("InfowareLab.Debug", "(ActHome) Time gap: " + String.valueOf(date-systemDate));

            return true;

        } catch (MalformedURLException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        //SystemClock.setCurrentTimeMillis(date);
    }

    private boolean correctTimeFromServer() {

        String strUrl = Config.Site_URL + "?from=ET";

        if (Config.Site_URL == null || Config.Site_URL.equals(""))
            return false;

        URL url;//取得资源对象
        try {
            url = new URL(strUrl);
            URLConnection uc=url.openConnection();//生成连接对象
            uc.connect();
            long date = uc.getDate();
            long systemDate = System.currentTimeMillis();
            Date date2 = new Date(date);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+08"));
            Log.d("InfowareLab", "(ActHome) Date From Server: " + dateFormat.format(date2));

            mTimeCorrected = true;
            ConferenceApplication.setTimeGap(date-systemDate);

            Log.d("InfowareLab.Debug", "(ActHome) Time gap: " + String.valueOf(date-systemDate));

            return true;

        } catch (MalformedURLException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        //SystemClock.setCurrentTimeMillis(date);
    }

    private boolean mTimeCorrected = false;

    /**
     * 获取当前日期是星期几<br>
     *
     * @param date
     * @return 当前日期是星期几
     */
    public String getWeekOfDate(Date date) {
        String[] weekDays = { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }

    //在主线程里面处理消息并更新UI界面
    private Handler mHandler = new Handler(){
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case 1:
                    long sysTime = (long)msg.obj;

                    if (fragMeetings != null && fragMeetings.isVisible()) {

                        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+08"));
                        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+08"));

                        Date date = new Date(sysTime);

                        //tvTime.setText(simpleDateFormat.format(sysTime));
                        //tvDate.setText(dateFormat.format(sysTime) + " " + getWeekOfDate(date));

                        fragMeetings.updateDateAndTime(dateFormat.format(sysTime) + " " + getWeekOfDate(date), simpleDateFormat.format(sysTime));
                    }

                    //Log.d("InfowareLab.update", "check time elapsed: " + (sysTime - mLastUpdateTime));

                    if (sysTime - mLastUpdateTime > mUpdateIntevalTime){

                        mLastUpdateTime = ConferenceApplication.currentTimeMillis();

                        if (currentFrag == 1)
                            checkConfListUpdate();
                    }

                    if (mRejoining && mExitedDueToNetworkIssue) {
                        if (sysTime - mStartRejoinTime > mMaxRejoinWaitTime) {
                            Log.d("InfowareLab.Net", "rejoin due to time expired: elapsed time = " + (sysTime - mStartRejoinTime));
                            retryJoinConference();
                        }
                    }

                    break;
                case 2:

                    int errorCode = (int)msg.obj;

                    if (errorCode == 0){
                        Log.d("InfowareLab.Net", "Rejoin successfully. errorCode = " + msg.obj);
                        hideRejoinConfDialog();
                        mRejoining = false;
                        mExitedDueToNetworkIssue = false;
                    }

                    if (mRejoining && mExitedDueToNetworkIssue) {
                         Log.d("InfowareLab.Net", "rejoin due to rejoin failure. errorCode = " + msg.obj);
                         retryJoinConference();
                    }

                    break;
                default:
                    break;

            }
        }
    };

    private void checkConfListUpdate() {

        if (fragMeetings != null && fragMeetings.isAdded() && fragMeetings.isVisible()){
            fragMeetings.getConfListData();
        }

        mLastUpdateTime = ConferenceApplication.currentTimeMillis();
    }

    public void checkUpdate() {

        String downUrl =  Config.Site_URL = FileUtil.readSharedPreferences(getApplicationContext(),
                Constants.SHARED_PREFERENCES, Constants.SITE);
        if (TextUtils.isEmpty(downUrl)){
            downUrl = HttpConstant.BASEURL;
        }else {
            if (!downUrl.startsWith("http://")){
                downUrl = "http://"+ downUrl;
            }
        }
        String medurl = "";
        if (!"box".equals(Config.SiteName)){
            medurl = HttpConstant.MEDURL+Config.SiteName+"/";
        }else {
            medurl = HttpConstant.MEDURL;
        }

        Log.d("InfowareLab.update","ActHome.checkUpdate: upgrade version file：" + downUrl + medurl + HttpConstant.VERSION);

        RequestCenter.getVersionInfo(downUrl + medurl + HttpConstant.VERSION, new DisposeDataListener() {
            @Override
            public void onSuccess(Object responseObj) {
                if (responseObj instanceof VersionInfo) {
                    VersionInfo versionInfo = (VersionInfo) responseObj;

                    Log.d("InfowareLab.update",">>>>>>Server Version Code：" + versionInfo.getVersionCode());
                    Log.d("InfowareLab.update",">>>>>>Server version Name：" + versionInfo.getVersionName());

                    try {
                        PackageInfo packageInfo = getApplicationContext()
                                .getPackageManager()
                                .getPackageInfo(getPackageName(), 0);

                        Log.d("InfowareLab.update",">>>>>>Local Version Code：" + packageInfo.versionCode);
                        Log.d("InfowareLab.update",">>>>>>Local version Name：" + packageInfo.versionName);

                        boolean isNew = versionInfo.getVersionCode() > packageInfo.versionCode;

//                        if (!isNew && !onStart) {
//                            Toast.makeText(getActivity(), "目前已是最新版本", Toast.LENGTH_LONG).show();
//                            return;
//                        }

//                        if (isNew)
//                        {
//                            Toast.makeText(getApplicationContext(), "正在下载更新，请稍候...", Toast.LENGTH_LONG).show();
//
//                            FileUtils.deleteFile((String) versionInfo.getFileName());
//                            downloadApk(versionInfo.getFileName());
//                        }

                    } catch (PackageManager.NameNotFoundException e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onFailure(Object responseObj) {

                Log.d("InfowareLab.update","Failed to download upgrading version information.");
            }
        });
    }

    private void downloadApk(String fileName) {

        String downUrl =  Config.Site_URL = FileUtil.readSharedPreferences(getApplicationContext(),
                Constants.SHARED_PREFERENCES, Constants.SITE);
        if (TextUtils.isEmpty(downUrl)){
            downUrl = HttpConstant.BASEURL;
        }else {
            if (!downUrl.startsWith("http://")){
                downUrl = "http://"+ downUrl;
            }
        }
        String medurl = "";
        if (!"box".equals(Config.SiteName)){
            medurl = HttpConstant.MEDURL+Config.SiteName+"/";
        }else {
            medurl = HttpConstant.MEDURL;
        }
        RequestCenter.downloadFile(downUrl + medurl, fileName, new DownloadListener() {
            @Override
            public void start(long max) {

                Log.d("InfowareLab.update","RequestCenter.downloadFile start：max=" + max);

//                Message m = new Message();
//                m.what = 0;
//                m.arg1 = 0;
//                m.arg2 = (int) max;
//                setParaHandler.sendMessage(m);
            }

            @Override
            public void loading(int cur, int total) {

                Log.d("InfowareLab.update","RequestCenter.downloadFile loading：cur=" + cur);

//                Message m = new Message();
//                m.what = 0;
//                m.arg1 = cur;
//                m.arg2 = total;
//                setParaHandler.sendMessage(m);
            }
            @Override
            public void complete(String path) {
                Log.d("InfowareLab.update", ">>>>>>downloadFile complete = " + path);

//                runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        installApk(getApplicationContext(), path);
//                    }
//                });

                installApk(path);
            }

            @Override
            public void fail(int code, String message) {
            }
            @Override
            public void loadfail(String message) {
            }
        });
    }

    public void ExecuteSUCmd(Context context, String currenttempfilepath) {

        Process process = null;
        OutputStream out = null;
        InputStream in = null;
        try {
            // 请求root
            process = Runtime.getRuntime().exec("su");
            out = process.getOutputStream();

            // 调用安装
            out.write(("pm install -r " + currenttempfilepath + "\n").getBytes());
            in = process.getInputStream();
            int len = 0;
            byte[] bs = new byte[256];
            while (-1 != (len = in.read(bs))) {
                String state = new String(bs, 0, len);
                if (state.equals("success\n")) {

                    //安装成功后的操作

                    //静态注册自启动广播
                    Intent intent=new Intent();
                    //与清单文件的receiver的anction对应
                    intent.setAction("android.intent.action.PACKAGE_REPLACED");

                    //发送广播
                    context.sendBroadcast(intent);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.flush();
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void installApk(String downloadApk) {
//        Intent intent = new Intent(Intent.ACTION_VIEW);
//        File file = new File(downloadApk);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            Uri apkUri = FileProvider.getUriForFile(getApplicationContext(), getPackageName() + ".fileProvider", file);
////            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
//            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
//        } else {
////            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            Uri uri = Uri.fromFile(file);
//            intent.setDataAndType(uri, "application/vnd.android.package-archive");
//        }
//        startActivity(intent);

//        AutoInstaller installer = AutoInstaller.getDefault(context);
//
//        installer.setOnStateChangedListener(new AutoInstaller.OnStateChangedListener() {
//            @Override
//            public void onStart() {
//                // 当后台安装线程开始时回调
//                //mProgressDialog.show();
//                Toast.makeText(context, "后台安装线程开始!", Toast.LENGTH_SHORT).show();
//            }
//            @Override
//            public void onComplete() {
//                // 当请求安装完成时回调
//                Toast.makeText(context, "安装完成!", Toast.LENGTH_SHORT).show();
//                //mProgressDialog.dismiss();
//            }
//            @Override
//            public void onNeed2OpenService() {
//                // 当需要用户手动打开 `辅助功能服务` 时回调
//                // 可以在这里提示用户打开辅助功能
//                Toast.makeText(context, "请打开辅助功能服务", Toast.LENGTH_SHORT).show();
//            }
//        });
//
//        Log.d(TAG, "installApk = " + downloadApk);
//        installer.install(downloadApk);

        String packageName = getApplicationContext().getPackageName();
        try {
            new ProcessBuilder()
                    .command("pm", "install", "-i", packageName, downloadApk)
                    .start();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Override
    protected void onDestroy() {
        Log.d("InfowareLab.Debug", "ActHome.onDestroy");

        if (mNetReceiver != null) {
            unregisterReceiver(mNetReceiver);
            mNetReceiver = null;
        }

        mActivity = null;

        super.onDestroy();
    }

    private void getVersionInfo() {
        String downUrl =  Config.Site_URL = FileUtil.readSharedPreferences(this,
                Constants.SHARED_PREFERENCES, Constants.SITE);
        if (TextUtils.isEmpty(downUrl)){
            downUrl = HttpConstant.BASEURL;
        }else {
            if (!downUrl.startsWith("http://")){
                downUrl = "http://"+ downUrl;
            }
        }
        String medurl = "";
        if (!"box".equals(Config.SiteName)){
            medurl = HttpConstant.MEDURL+Config.SiteName+"/";
        }else {
            medurl = HttpConstant.MEDURL;
        }
        RequestCenter.getVersionInfo(downUrl+ medurl + HttpConstant.VERSION, new DisposeDataListener() {
            @Override
            public void onSuccess(Object responseObj) {
                if (responseObj instanceof VersionInfo) {
                    VersionInfo versionInfo = (VersionInfo) responseObj;
                    try {
                        PackageInfo packageInfo = ActHome.this
                                .getApplicationContext()
                                .getPackageManager()
                                .getPackageInfo(ActHome.this.getPackageName(), 0);
                        String v = packageInfo.versionName;
                        if (!v.equals(versionInfo.getVersionName())){
                            if ("1".equals(versionInfo.getType())){
                                //downApk(versionInfo.getFileName());
                            }
                        } else {
//                            Toast.makeText(ActHome.this, "目前已是最新版本", Toast.LENGTH_LONG).show();
                        }
                    } catch (PackageManager.NameNotFoundException e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onFailure(Object responseObj) {
            }
        });
    }

    private void downApk(String fileName) {
        String downUrl =  Config.Site_URL = FileUtil.readSharedPreferences(this,
                Constants.SHARED_PREFERENCES, Constants.SITE);
        if (TextUtils.isEmpty(downUrl)){
            downUrl = HttpConstant.BASEURL;
        }else {
            if (!downUrl.startsWith("http://")){
                downUrl = "http://"+ downUrl;
            }
        }
        String medurl = "";
        if (!"box".equals(Config.SiteName)){
            medurl = HttpConstant.MEDURL+Config.SiteName+"/";
        }else {
            medurl = HttpConstant.MEDURL;
        }
        RequestCenter.downloadFile(downUrl + medurl, fileName, new DownloadListener() {
            @Override
            public void start(long max) {
                Message m = new Message();
                m.what = 0;
                m.arg1 = 0;
                m.arg2 = (int) max;
                setParaHandler.sendMessage(m);
            }

            @Override
            public void loading(int cur, int total) {
                Message m = new Message();
                m.what = 0;
                m.arg1 = cur;
                m.arg2 = total;
                setParaHandler.sendMessage(m);
            }
            @Override
            public void complete(String path) {
                Log.i("downloadFile", "downloadFile path = " + path);
                installApk(ActHome.this, path);
            }

            @Override
            public void fail(int code, String message) {
            }
            @Override
            public void loadfail(String message) {
            }
        });
    }

    public static void installApk(Context context, String downloadApk) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        File file = new File(downloadApk);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Uri apkUri = FileProvider.getUriForFile(context, context.getPackageName() + ".fileProvider", file);
//            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        } else {
//            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            Uri uri = Uri.fromFile(file);
            intent.setDataAndType(uri, "application/vnd.android.package-archive");
        }
        context.startActivity(intent);

    }

    Handler setParaHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
//            if (msg.what == 0) {
//                home = (ActHome) getActivity();
//                Toast.makeText(home, "正在下载...(" + msg.arg1 + "/" + msg.arg2 + ")", Toast.LENGTH_LONG).show();
//            }
        }
    };

    public void applyFouse(int type){
        if (0 == type){
            if (iv1 != null){
                iv1.setFocusable(true);
                iv1.requestFocus();
            }
        }else {
            if (iv2 != null){
                iv2.setFocusable(true);
                iv2.requestFocus();
            }
        }
    }

    private void checkNetType() {

        wifiHandler.removeCallbacksAndMessages(null);
        wifiHandler.sendEmptyMessageDelayed(0, 500);
    }

    Handler wifiHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {

            ConnectivityManager manager = (ConnectivityManager) getSystemService(CONNECTIVITY_SERVICE);

            if (NetworkInfo.State.CONNECTED == manager.getNetworkInfo(ConnectivityManager.TYPE_WIFI).getState()) {

                Log.d("InfowareLab.Net", "ActHome.checkNetType: Wifi is connected!");
                if (tvWifi != null) {
                    tvWifi.setText(getIPAddress(getApplicationContext()));
                    tvWifi.setVisibility(View.VISIBLE);
                }

                if (fragMeetings != null)
                    fragMeetings.updateNetworkStatus(true);

                //RegisterDevice();

            } else if (NetworkInfo.State.CONNECTED == manager.getNetworkInfo(ConnectivityManager.TYPE_ETHERNET).getState()){

                Log.d("InfowareLab.Net", "ActHome.checkNetType: Ethernet is connected!");
                if (tvWifi != null) {
                    tvWifi.setVisibility(View.GONE);
                }

                if (fragMeetings != null)
                    fragMeetings.updateNetworkStatus(true);

                //RegisterDevice();
            }
            else
            {
                Log.d("InfowareLab.Net", "ActHome.checkNetType: Net is disconnected!");

                if (tvWifi != null)
                    tvWifi.setVisibility(View.GONE);

                if (fragMeetings != null)
                    fragMeetings.updateNetworkStatus(false);
            }
        }
    };

    private void RegisterDevice() {

        new Thread(new Runnable() {
            @Override
            public void run() {

                PublicWay.getIpAndPort(ActHome.this,Config.Site_URL,FileUtil.readSharedPreferences(ActHome.this,
                        Constants.SHARED_PREFERENCES, Constants.SITE_ID));

                Config.Site_URL = FileUtil.readSharedPreferences(ActHome.this,
                        Constants.SHARED_PREFERENCES, Constants.SITE);
                FileUtil.saveSharedPreferences(ActHome.this,
                        Constants.SHARED_PREFERENCES,
                        Constants.SITE_NAME, Config.getSiteName(Config.Site_URL));
                String id = FileUtil.readSharedPreferences(ActHome.this,
                        Constants.SHARED_PREFERENCES, Constants.SITE_ID);

                int userId = getApplicationContext().getSharedPreferences(Constants.SHARED_PREFERENCES,
                        getApplicationContext().MODE_WORLD_READABLE).getInt(Constants.USER_ID, 0);
                if (TextUtils.isEmpty(id)){
                    id = "0";
                }


                if (Config.terminateRegist(DeviceIdFactory.getUUID1(ActHome.this), FileUtil.readSharedPreferences(ActHome.this,
                        Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME), Integer.parseInt(id),userId).equals("0")) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(ActHome.this, R.string.regist_success, Toast.LENGTH_SHORT).show();
                        }
                    });
                } else if (Config.terminateRegist(DeviceIdFactory.getUUID1(ActHome.this), FileUtil.readSharedPreferences(ActHome.this,
                        Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME), Integer.parseInt(id),userId).equals("-1")) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(ActHome.this, R.string.regist_fail, Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        }).start();
    }

    private void initReceiver() {

        mNetReceiver = new BroadcastReceiver() {

            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (ConnectivityManager.CONNECTIVITY_ACTION.equals(action)) {
                    checkNetType();
                }
            }
        };

        IntentFilter filter = new IntentFilter();

        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);

        if (Build.VERSION.SDK_INT < 26) {
            filter.addAction(BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED);
        }

        registerReceiver(mNetReceiver, filter);
    }


//    @Override
//    public Resources getResources() {
//        Resources res = super.getResources();
//        if (res.getConfiguration().fontScale != 1) //非默认值
//        {
//            Configuration newConfig = new Configuration();
//            newConfig.setToDefaults();//设置默认
//            res.updateConfiguration(newConfig, res.getDisplayMetrics());
//
//        }
//
//        return res;
//    }
}