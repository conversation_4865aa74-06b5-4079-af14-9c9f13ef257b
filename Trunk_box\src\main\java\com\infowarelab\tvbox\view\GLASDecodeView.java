package com.infowarelab.tvbox.view;


import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.graphics.SurfaceTexture;
import android.opengl.GLES20;
import android.opengl.GLSurfaceView;
import android.os.HandlerThread;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.render.BaseFilter;
import com.infowarelab.tvbox.render.FilterFactory;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;

import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;

@SuppressLint("NewApi")
public class GLASDecodeView extends FrameLayout implements SurfaceHolder.Callback{

	public GLASDecodeView.GLRenderer renderer = null;
	private BaseFilter mCurrentFilter;
	private int mTextureId;
	private SurfaceTexture mSurfaceTexture = null;
	private Surface mSurface = null;
	private float[] mSTMatrix = new float[16];
	private  FilterFactory.FilterType type;

	/** surface生命周期 **/
	private SurfaceHolder holder;
	private Context activity;
	/** 视频SDK接口 **/
	protected CommonFactory commonFactory = CommonFactory.getInstance();
	private ShareDtCommonImpl asCommon =  (ShareDtCommonImpl) commonFactory.getSdCommon();;
	private GLSurfaceView surfaceView;
	private int pHeight;
	private TextView tvWait;
	private boolean mOpened = false;
	private String TAG = "InfowareLab.AS";

	private boolean isSvc = false;
	private boolean isCreated = false;
	private boolean isWaiting = true;
	private boolean isSupport = true;

	public GLASDecodeView(Context context) {
		super(context);
	}
	public GLASDecodeView(Context context, AttributeSet attrs) {
		super(context, attrs);
		this.activity = context;
	}
	public GLASDecodeView(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);
		this.activity = context;
	}
	/**
	 * 初始化界面大小
	 * @param height 容器高
	 * @param width  宽
	 */
	public void initSize(int width ,int height,int sw,int sh) {

		Log.d("InfowareLab.AS", "GLASDecodeView.initSize: width=" + width + "; height=" + height + "; sw=" + sw + "; sh=" +sh);

		setScreenSize(sw,sh);
		setParams(width, height);
		syncMatrix(width, height);
		refreshFrameLayout();
	}
	public void resetSize(int width,int height,int left, int right, int top, int bottom){
		RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
		params.width = width;
		params.height = height;
		params.setMargins(left, top, right, bottom);
		setLayoutParams(params);
	}
	private void refreshFrameLayout(){
		matrix.getValues(targetValues);
        resetSize(
        		(int)(this.width*targetValues[Matrix.MSCALE_X]),
        		(int)(this.height*targetValues[Matrix.MSCALE_Y]),
        		(int)targetValues[Matrix.MTRANS_X],
        		(int)(screenW-this.width*targetValues[Matrix.MSCALE_X]-targetValues[Matrix.MTRANS_X]),
        		(int)targetValues[Matrix.MTRANS_Y],
        		(int)(screenH-this.height*targetValues[Matrix.MSCALE_Y]-targetValues[Matrix.MTRANS_Y])
        		);
	}
	private void syncMatrix(int width ,int height){
			float[] mv = new float[9];
			matrix.getValues(mv);
			mv[Matrix.MSCALE_X]=1;
			mv[Matrix.MSCALE_Y]=1;
			mv[Matrix.MTRANS_X]=(float) ((screenW-width)*1.0/2);
			mv[Matrix.MTRANS_Y]=(float) ((screenH-height)*1.0/2);
			matrix.setValues(mv);
	}
	/**
	 * 视频接收/停止接收
	 * @param isOpen
	 */
	public void changeStatus(boolean isOpen) {

		if (isOpen == mOpened) return;

		mOpened = isOpen;

		if (isOpen) {

			Log.d("InfowareLab.AS", "ASDecodeView.changeStatus=true");

			//this.channelId = channelID;
			if (null != surfaceView) {
				Log.d(TAG, "GLASDecodeView.changeStatus: Removed old surfaceView");
				//if (mSurface != null) mSurface.release();
				//surfaceView.setRenderer(null);

				if (mSurface != null){
					mSurface.release();
					mSurface = null;
				}

				try {
					if (mSurfaceTexture != null) {
						mSurfaceTexture.releaseTexImage();
						mSurfaceTexture.release();
						mSurfaceTexture = null;
					}
				}
				catch (java.lang.IllegalStateException e)
				{
					mSurfaceTexture.release();
					mSurfaceTexture = null;
				}

				removeView(surfaceView);
				surfaceView = null;
				//removeNameCamera();
			}
			surfaceView = new GLSurfaceView(this.activity);
			surfaceView.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT,
					LayoutParams.MATCH_PARENT, Gravity.CENTER));
			//holder = surfaceView.getHolder();
			//holder.addCallback(this);

			Log.d(TAG, "GLASDecodeView.addView: surfaceView");

			addView(surfaceView);

			surfaceView.setEGLContextClientVersion(2);

			type = FilterFactory.FilterType.Decode;

			Log.d(TAG, "GLASDecodeView.create new GLRenderer");
			renderer = new GLASDecodeView.GLRenderer(surfaceView,type);
			surfaceView.setRenderer(renderer);
			surfaceView.setRenderMode(surfaceView.RENDERMODE_WHEN_DIRTY);

			surfaceView.setZOrderOnTop(true);
			surfaceView.setZOrderMediaOverlay(true);
			//setTag(channelID);
			showWait();
			isWaiting = true;
			//addName();
		} else {
			Log.d("InfowareLab.AS", "ASDecodeView.changeStatus=false");

			asCommon.setIsReceive(false, (Surface)null);

			if (null != surfaceView) {
				//Log.d(TAG, "GLVideoDecoderView.destroy the surfaceView");

				//mSurfaceLock.acquire();
				if (mSurface != null){
					mSurface.release();
					mSurface = null;
				}

				if (mSurfaceTexture != null){

					try {
						mSurfaceTexture.releaseTexImage();
						mSurfaceTexture.release();
						mSurfaceTexture = null;
					}
					catch (java.lang.IllegalStateException e)
					{
						mSurfaceTexture.release();
						mSurfaceTexture = null;
					}
				}

				Log.d(TAG, "GLASDecodeView.removeView: surfaceView");

				//mSurfaceTexture = null;
				removeView(surfaceView);
				surfaceView = null;
				//removeNameCamera();
			}

			isCreated = false;

			//this.channelId = 0;
		}
	}
	private void showWait(){
//		tvWait = new TextView(this.activity);
//		tvWait.setTextSize(TypedValue.COMPLEX_UNIT_SP,40);
//		tvWait.setTextColor(Color.BLACK);
//		tvWait.setBackgroundColor(Color.WHITE);
//		tvWait.setGravity(Gravity.CENTER);
//		tvWait.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT,
//                LayoutParams.MATCH_PARENT));
//		tvWait.setText("Wait");
//		addView(tvWait);

		this.isWaiting = true;
		if (null != surfaceView)
		{
			//surfaceView.setBackgroundColor(android.R.color.white);
			surfaceView.setBackgroundResource(R.drawable.bg_sharedt_wait_black);
		}
	}

	public void showSupport(boolean isSupport) {
		this.isSupport = isSupport;
		showSupport();
	}

	private void showSupport() {
		if (isSupport) {
			if (isWaiting) {
				showWait();
			} else {
				showSurface();
			}
		} else {
//			surfaceView.setBackgroundResource(R.drawable.bg_nosupport);
		}
	}

	public void showSupportReady() {
		this.isWaiting = false;
		if (isSupport) {
			showSurface();
		} else {
//			surfaceView.setBackgroundResource(R.drawable.bg_nosupport);
		}
	}

	private void showSurface() {

		if(null!=tvWait){
			tvWait.setVisibility(View.GONE);
		}

		if (surfaceView != null)
			surfaceView.setBackgroundColor(0);
	}

	private void removeWait(){
		if(null!=tvWait){
			removeView(tvWait);
			tvWait = null;
		}
	}
	public void showClosed(){
		if(null!=tvWait){
			tvWait.setText("Closed");
			tvWait.setVisibility(View.VISIBLE);
		}
	}

	@Override
	public void surfaceCreated(SurfaceHolder holder) {
		Log.d("InfowareLab.AS", "ASDecodeView.surfaceCreated");
//		if(null!=surfaceView){
//			asCommon.setIsReceive(true, surfaceView);
//		}
	}

	@Override
	public void surfaceChanged(SurfaceHolder holder, int format, int width,
			int height) {
		Log.d("InfowareLab.AS", "ASDecodeView.surfaceChanged");
		this.holder=holder;
	}
	@Override
	public void surfaceDestroyed(SurfaceHolder holder) {
		Log.d("InfowareLab.AS", "ASDecodeView.surfaceDestroyed");
		
		asCommon.setIsReceive(false, (Surface)null);
	}
	public void setParams(int width, int height) {
		this.width = width;
		this.height = height;
	}

	public void setScreenSize(int width, int height) {
		this.screenW = width;
		this.screenH = height;
	}

	public SurfaceView getSurfaceView(){
		return surfaceView;
	}
	public void show(){
		setVisibility(View.VISIBLE);
	}
	public void hide(){
		setVisibility(View.GONE);
	}
	
	protected int width;
	protected int height;
	protected int screenW = 100;
	protected int screenH = 100;
	protected float minZoom = 1;
	protected float maxZoom = 3;
	protected PointF start = new PointF();
	protected PointF mid = new PointF();
	/** 图像缩放比例 */
	protected Matrix matrix = new Matrix();
	protected Matrix savedMatrix = new Matrix();
	protected float oldDist = 1f;
	protected float[] matrixValues = new float[9];
	protected float[] targetValues = new float[9];
    public void setP1(){
		RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
		params.width = 1;
		params.height = 1;
		setLayoutParams(params);
	}
    public void setPM(int width,int height){
    	syncMatrix(width, height);
		refreshFrameLayout();
	}

	public class GLRenderer implements GLSurfaceView.Renderer, SurfaceTexture.OnFrameAvailableListener {

		GLSurfaceView surfaceView;

		public GLRenderer(GLSurfaceView surfaceView, FilterFactory.FilterType type) {

			this.surfaceView = surfaceView;

			mCurrentFilter = FilterFactory.createFilter(activity,type);

		}

		@Override
		public void onSurfaceCreated(GL10 gl, EGLConfig config) {

			Log.d(TAG,"GLASDecodeView.onSurfaceCreated");
		}

		@Override
		public void onSurfaceChanged(GL10 gl, int width, int height) {

			Log.d(TAG,"GLASDecodeView.onSurfaceChanged: " + width + "x" + height);

			if (!isCreated){
				screenW=width;
				screenH=height;

				mCurrentFilter.createProgram();
				mCurrentFilter.onInputSizeChanged(width,height);

				mTextureId = BaseFilter.bindTexture();

				try {
					if (mSurfaceTexture != null) {
						mSurfaceTexture.releaseTexImage();
						mSurfaceTexture.release();
					}
				}
				catch (java.lang.IllegalStateException e){
					Log.d(TAG,"GLASDecodeView.onSurfaceChanged: IllegalStateException");
				}

				if (mSurface != null){
					mSurface.release();
				}

				mSurfaceTexture = new SurfaceTexture(mTextureId);
				mSurface = new Surface(mSurfaceTexture);
				mSurfaceTexture.setOnFrameAvailableListener(this);

				if(null!=mSurface){
					asCommon.setIsReceive(true, mSurface);
				}

				isCreated = true;
			}

			if (isCreated)
				GLES20.glViewport(0, 0, width, height);

		}

		/**
		 * 关于预览出现镜像，旋转等问题，有两种方案:
		 * 1.在相机预览的地方进行调整
		 * 2.通过opengl的矩阵变换在绘制的时候进行调整
		 * 这里我采用了前者
		 */

		@Override
		public void onDrawFrame(GL10 gl) {

			//runAll(runOnDraw);
			if (mSurfaceTexture == null || mCurrentFilter == null) return;

			try {
				if (mSurfaceTexture != null)
					mSurfaceTexture.updateTexImage();

				if (mSurfaceTexture != null)
					mSurfaceTexture.getTransformMatrix(mSTMatrix);

				if (mCurrentFilter != null)
					mCurrentFilter.draw(mTextureId, mSTMatrix);
			}
			catch (java.lang.RuntimeException e){

				Log.d(TAG,"GLASDecodeView.onDrawFrame: java.lang.RuntimeException");
			}

		}

		@Override
		public void onFrameAvailable(SurfaceTexture surfaceTexture) {
			//Log.d(TAG,"GLASDecodeView.onFrameAvailable");

			if (surfaceTexture == null || surfaceView == null) return;

			if (isWaiting == true){
				showSurface();
				isWaiting = false;
			}

			surfaceView.requestRender();
		}

	}


}
