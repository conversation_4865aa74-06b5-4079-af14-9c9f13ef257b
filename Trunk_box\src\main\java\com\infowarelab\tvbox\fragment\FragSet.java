package com.infowarelab.tvbox.fragment;

import static androidx.core.content.ContextCompat.getSystemService;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.Nullable;

import android.provider.Settings;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.DeviceIdFactory;
import com.infowarelab.tvbox.utils.PublicWay;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelab.tvbox.utils.UDPHelper;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.domain.LoginBean;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.NetUtil;
import com.infowarelabsdk.conference.util.StringUtil;

import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by Always on 2017/8/7.
 */

@SuppressLint("ValidFragment")
public class FragSet extends BaseFragment implements View.OnClickListener, View.OnFocusChangeListener {
    public static final int LOGIN_PROGRESS_HIDE = 0;
    public static final int LOGIN_PROGRESS_SHOWE = 1;
    public static final int LOGIN_SUCCESS = 2;
    public static final int LOGIN_INPUT_ILLEGAL = 3;
    public static final int LOGIN_INPUT_NULL = 4;
    public static final int LOGIN_UPDATESTATE = 5;
    private static final int GETCACHE = 10;
    private static final int CLEARCACHE = 11;
    private static final int SETFINISH = 12;

    private static final int DIALOG_HIDE_ERR = 6;
    private static final int DIALOG_HIDE_SUC = 7;

    private static final int DIALOG_LICENSE_ERR = 10;

    private static final int DIALOG_SHOW = 8;
    private static final int PROGRESS_HIDE = 9;
    private View userView;
    private LinearLayout llConfirm;
    private LinearLayout llCancel;

    private LinearLayout llFragSetLayout0;
    private LinearLayout llFragSetLayout;

    private EditText et1, et2, et3, et4;
    private CheckBox cbAutoAccept;
    private String siteText = "";
    private String joinnameText = "";
    private LoginBean loginBean = null;
    private SharedPreferences preferences;
//    private String defaultSite = "http://192.168.2.143";
//    private String defaultSite = "http://192.168.2.121:8001";
    private String defaultSite = "http://meeting.hongshantong.cn";
    private String defaultDeviceName = "Android_TV";

    public FragSet(ICallParentView iCallParentView) {
        super(iCallParentView);
    }
    //无参构造器
    public FragSet(){
        super();
    }
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        userView = inflater.inflate(R.layout.frag_home_set, container, false);
        initView();
        return userView;
    }

    private void initView() {
        defaultSite = SharedPreferencesUrls.getInstance().getString("host_url",defaultSite);

        preferences = getActivity().getSharedPreferences(
                Constants.SHARED_PREFERENCES, Context.MODE_WORLD_READABLE);

        et1 = (EditText) userView.findViewById(R.id.et_frag_set_1);
        et2 = (EditText) userView.findViewById(R.id.et_frag_set_2);
        et3 = (EditText) userView.findViewById(R.id.et_frag_set_3);
        et4 = (EditText) userView.findViewById(R.id.et_frag_set_4);
        cbAutoAccept = (CheckBox) userView.findViewById(R.id.auto_accept);

        et1.setFocusable(true);
        et1.requestFocus();
        InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.toggleSoftInput(InputMethodManager.SHOW_IMPLICIT, InputMethodManager.HIDE_NOT_ALWAYS);
        String facilityTitle = FileUtil.readSharedPreferences(getContext(),
                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);

//        if (TextUtils.isEmpty(facilityTitle)) facilityTitle = Settings.Global.getString(getActivity().getContentResolver(), "device_name");
//        if (TextUtils.isEmpty(facilityTitle)) facilityTitle = android.os.Build.MODEL;
        if (TextUtils.isEmpty(facilityTitle))facilityTitle = "Rooms";

        et2.setText(facilityTitle);
        et2.setSelection(facilityTitle.length());

        defaultDeviceName = facilityTitle;

        String userName=FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME);
        String password = FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES, Constants.LOGIN_PASS);
        boolean autoAccept = FileUtil.readSharedPreferencesBoolean(getActivity(), Constants.SHARED_PREFERENCES, Constants.AUTO_ACCEPT, true);

        if (userName != null && !"".equals(userName)){
            et3.setText(userName);
            et3.setSelection(userName.length());
        }
        if (password != null && !"".equals(password)){
            et4.setText(password);
            et4.setSelection(password.length());
        }

        cbAutoAccept.setChecked(autoAccept);

        llConfirm = (LinearLayout) userView.findViewById(R.id.ll_frag_set_confirm);

        llConfirm.setOnClickListener(this);

        llCancel = (LinearLayout) userView.findViewById(R.id.ll_frag_set_cancel);

        llCancel.setOnClickListener(this);

        llFragSetLayout = (LinearLayout) userView.findViewById(R.id.ll_frag_set_v1);

        llFragSetLayout.setOnClickListener(this);

        llFragSetLayout0 = (LinearLayout) userView.findViewById(R.id.ll_frag_set_v0);

        llFragSetLayout0.setOnClickListener(this);

        setSiteHint();
        setInOrOut();
    }
    public Handler setHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case LOGIN_PROGRESS_HIDE:
                    hideLoading();
                    showShortToast(R.string.set_user_err6);
                    preferences.edit().putString(Constants.LOGIN_ROLE, "").commit();
                    preferences.edit().putString(Constants.LOGIN_NAME, "").commit();
                    preferences.edit().putString(Constants.LOGIN_PASS, "").commit();
                    setHandler.sendEmptyMessageDelayed(SETFINISH,1000);
                    break;
                case LOGIN_PROGRESS_SHOWE:
                    showLoading();
                    break;
                case LOGIN_UPDATESTATE:
                    setInOrOut();
                    break;
                case LOGIN_SUCCESS:
                    hideLoading();
                    FileUtil.saveSharedPreferences(getActivity(),
                            Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME,
                            et3.getText().toString().trim());
                    FileUtil.saveSharedPreferences(getActivity(),
                            Constants.SHARED_PREFERENCES, Constants.LOGIN_PASS,
                            et4.getText().toString().trim());
                    if (StringUtil.isNullOrBlank(joinnameText) || !StringUtil.checkInput(joinnameText, Constants.PATTERN)) {
                        saveJoinname(et3.getText().toString().trim());
                    } else {
                        saveJoinname(joinnameText);
                    }
//                    if (!TextUtils.isEmpty(et3.getText().toString().trim())){
                        FileUtil.saveSharedPreferences(getActivity(),
                                Constants.SHARED_PREFERENCES, Constants.LOGIN_ROLE,
                                loginBean.getCreateConfRole());
//                        if (loginBean.getCreateConfRole().contains("meeting.role.init.host;")){
                            //启动子线程网络请求
//                            new Thread(new Runnable() {
//                                @Override
//                                public void run() {
//                                    PublicWay.getIpAndPort(getActivity(),Config.Site_URL,Config.SiteId);
//                                }
//                            }).start();
//                        }
//                    }else {
//                        FileUtil.saveSharedPreferences(getActivity(),
//                                Constants.SHARED_PREFERENCES, Constants.LOGIN_ROLE,"");
//                    }
                    saveUId();
                    showShortToast(R.string.set_user_login_suc);
                    setInOrOut();
                    setHandler.sendEmptyMessageDelayed(SETFINISH,1000);
                    break;
                case LOGIN_INPUT_ILLEGAL:
                    showShortToast(R.string.set_user_err5);
                    break;
                case LOGIN_INPUT_NULL:
                    showShortToast(R.string.set_user_err4);
                    preferences.edit().putString(Constants.LOGIN_ROLE, "").commit();
                    preferences.edit().putString(Constants.LOGIN_NAME, "").commit();
                    preferences.edit().putString(Constants.LOGIN_PASS, "").commit();
                    setHandler.sendEmptyMessageDelayed(SETFINISH,1000);
                    break;
                case DIALOG_HIDE_ERR:
                    hideLoading();
                    showLongToast(R.string.set_user_err3);
                    break;
                case DIALOG_LICENSE_ERR:
                    hideLoading();
                    showLongToast(R.string.set_user_err8);
                    break;
                case DIALOG_HIDE_SUC:
                    hideLoading();
                    logout();
                    clearUid();
                    SharedPreferencesUrls.getInstance().putString("host_url",et1.getText().toString().trim());
                    showShortToast(R.string.set_user_site_suc);
                    new Thread(new Runnable() {
                        @Override
                        public void run() {

                            PublicWay.getIpAndPort(getActivity(),Config.Site_URL,Config.SiteId);

                            Config.Site_URL = FileUtil.readSharedPreferences(getActivity(),
                                    Constants.SHARED_PREFERENCES, Constants.SITE);
                            FileUtil.saveSharedPreferences(getActivity(),
                                    Constants.SHARED_PREFERENCES,
                                    Constants.SITE_NAME, Config.getSiteName(Config.Site_URL));
                            String id = FileUtil.readSharedPreferences(getActivity(),
                                    Constants.SHARED_PREFERENCES, Constants.SITE_ID);
                            int userId = 0;
                            if (loginBean != null){
                                userId = loginBean.getUid();
                            }
                            if (TextUtils.isEmpty(id)){
                                id = "0";
                            }
                            if (Config.terminateRegist(DeviceIdFactory.getUUID1(getActivity()), et2.getText().toString().trim(), Integer.parseInt(id),userId).equals("0")) {
//                                post(new Runnable() {
//                                    @Override
//                                    public void run() {
//                                        Toast.makeText(getActivity(), R.string.regist_success, Toast.LENGTH_SHORT).show();
//                                    }
//                                });
                            } else if (Config.terminateRegist(DeviceIdFactory.getUUID1(getActivity()), et2.getText().toString().trim(), Integer.parseInt(id),userId).equals("-1")) {
//                                post(new Runnable() {
//                                    @Override
//                                    public void run() {
//                                        Toast.makeText(getActivity(), R.string.regist_fail, Toast.LENGTH_SHORT).show();
//                                    }
//                                });
                            }
                        }
                    }).start();
                    break;
                case DIALOG_SHOW:
                    showLoading();
                    break;
                case PROGRESS_HIDE:
                    hideLoading();
                    break;
                case CLEARCACHE:
                    break;
                case SETFINISH:
                    callParentView(ACTION_SETFINISH, null);
                    break;
                default:
                    break;
            }
        }

        ;
    };

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ll_frag_set_confirm:
                hideInput(v);

                FileUtil.saveSharedPreferences(getActivity(),
                        Constants.SHARED_PREFERENCES,
                        Constants.AUTO_ACCEPT, cbAutoAccept.isChecked());

                if (checkSite()) {
                    setHandler.sendEmptyMessage(DIALOG_SHOW);
                    new Thread() {
                        @Override
                        public void run() {
                            Config.SiteName = Config.getSiteName(siteText);
                            if (Config.SiteName.equals("")
                                    || Config.SiteName == null) {
                                setHandler.sendEmptyMessage(DIALOG_HIDE_ERR);
                            } else {

                                if (Config.SiteType <= 1){
                                    setHandler.sendEmptyMessage(DIALOG_LICENSE_ERR);
                                    return;
                                }

                                Config.Site_URL = siteText;
                                // 保存SiteName、SiteId信息
                                FileUtil.saveSharedPreferences(getActivity(),
                                        Constants.SHARED_PREFERENCES,
                                        Constants.SITE_NAME, Config.SiteName);
                                FileUtil.saveSharedPreferences(getActivity(),
                                        Constants.SHARED_PREFERENCES,
                                        Constants.SITE_ID, Config.SiteId);
                                FileUtil.saveSharedPreferences(getActivity(),
                                        Constants.SHARED_PREFERENCES,
                                        Constants.SITE, Config.Site_URL);
                                FileUtil.saveSharedPreferences(getActivity(),
                                        Constants.SHARED_PREFERENCES,
                                        Constants.HAS_LIVE_SERVER, ""
                                                + Config.HAS_LIVE_SERVER);
                                if (!StringUtil.isNullOrBlank(joinnameText) && StringUtil.checkInput(joinnameText, Constants.PATTERN)) {
                                    saveJoinname(joinnameText);
                                    UDPHelper.getInstance(getActivity()).reSetSocket(joinnameText, Config.Site_URL, Config.SiteId);
                                } else {
//                                    saveJoinNameDefault();
                                    UDPHelper.getInstance(getActivity()).reSetSocket(getResources().getString(R.string.comm_defaultname) + "_" + DeviceIdFactory.getDeviceId(getActivity()), Config.Site_URL, Config.SiteId);
                                }
                                setHandler.sendEmptyMessage(DIALOG_HIDE_SUC);

                                if (checkIsNull()) {
                                    setHandler.sendEmptyMessage(LOGIN_INPUT_NULL);
                                } else if (!checkIsIlegal()) {
                                    setHandler.sendEmptyMessage(LOGIN_INPUT_ILLEGAL);
                                } else {
                                    setHandler.sendEmptyMessage(LOGIN_PROGRESS_SHOWE);
                                    doLogin();
                                }
                            }
                        }

                    }.start();

                }
                break;
            case R.id.ll_frag_set_cancel:
                hideInput(v);
                callParentView(ACTION_RETURN, null);
                break;
            case R.id.ll_frag_set_v1:
            case R.id.ll_frag_set_v0:
                InputMethodManager imm = (InputMethodManager) getContext(). getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                break;
            default:
                break;
        }
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
    }


    /**
     * 检测出入站点IP的格式
     *
     * @return
     */
    private boolean checkSite() {
        siteText = et1.getText().toString().trim();
        joinnameText = et2.getText().toString().trim();
        if (siteText.trim().length() <= 0 || siteText.trim().equals("")) {
            showShortToast(R.string.set_user_err7);
            return false;
        } else if (siteText.length() > 0) {
            if (!siteText.contains("http://")) {
                if (!siteText.contains("https://") && !StringUtil.isIP(siteText)) {
                    // text="http://"+InputEncode.doEncode(text);
                    String[] ss = siteText.split(":");
                    String s = "";
                    for (String string : ss) {
                        s = s + ":" + doEncode(string);
                    }
                    s = s.replaceFirst(":", "");
                    siteText = "http://" + s;
                }
            } else {
                String s = siteText.replace("http://", "");
                String[] ss = s.split(":");
                s = "";
                for (String string : ss) {
                    s = s + ":" + doEncode(string);
                }
                s = s.replaceFirst(":", "");
                siteText = "http://" + s;
            }
            String regex = "^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]";
            Pattern patt = Pattern.compile(regex);
            Matcher matcher = patt.matcher(siteText.trim());
            boolean isMatch = matcher.matches();
            if (!isMatch) {
                showShortToast(R.string.set_user_err1);
                return false;
            }
        } else {
            if (siteText.equals(FileUtil.readSharedPreferences(getActivity(),
                    Constants.SHARED_PREFERENCES, Constants.SITE))) {
                return false;
            }
        }
        if (TextUtils.isEmpty(et2.getText().toString().trim())){
            showShortToast("请填写会议室名称");
            return false;
        }
        if (true) {
            if (!NetUtil.isNetworkConnected(getActivity())) {
                showShortToast(R.string.error_net);
                return false;
            }
        }
        return true;
    }


    private void doLogin() {
        Config.SiteName = FileUtil.readSharedPreferences(getActivity(),
                Constants.SHARED_PREFERENCES, Constants.SITE_NAME);
        loginBean = new LoginBean(null, et3.getText().toString(), et4
                .getText().toString());
        loginBean = ((ConferenceCommonImpl) CommonFactory.getInstance()
                .getConferenceCommon()).checkUser(loginBean);
        if (loginBean != null) {
            setHandler.sendEmptyMessage(LOGIN_SUCCESS);
        } else {
            setHandler.sendEmptyMessage(LOGIN_PROGRESS_HIDE);
        }
    }

    private boolean checkIsNull() {
        if (StringUtil.isNullOrBlank(et3.getText().toString())) {
            return true;
        } else if (StringUtil.isNullOrBlank(et4.getText().toString())) {
            return true;
        }
        return false;
    }

    private boolean checkIsIlegal() {
        if (StringUtil.checkInput(et3.getText().toString().trim(),
                Constants.PATTERN)) {
            return true;
        } else if (StringUtil.checkInput(et4.getText().toString().trim(),
                Constants.PATTERN)) {
            return true;
        }
        return false;
    }

    /**
     * 注销后清空保存的用户名
     */
    private void clearUid() {
        preferences.edit().putInt(Constants.CONF_LIST_TYPE, 1)
                .putInt(Constants.USER_ID, 0).commit();

    }

    private void saveUId() {
        preferences.edit().putInt(Constants.CONF_LIST_TYPE, 0)
                .putInt(Constants.USER_ID, loginBean.getUid()).commit();
    }

    public String doEncode(String s) {
        String s1 = URLEncoder.encode(s);
        return s1;
    }

    private void setSiteHint() {
        String site = FileUtil.readSharedPreferences(getActivity(),
                Constants.SHARED_PREFERENCES, Constants.SITE);
        if (site.equals("")) {
//            et1.setHint(getResources().getString(R.string.set_user_sitehint));
            et1.setText(defaultSite);
            et1.setSelection(defaultSite.length());
        } else {
            et1.setText(site);
            et1.setSelection(site.length());
        }
    }

    private void setInOrOut() {
        String username = FileUtil.readSharedPreferences(getActivity(),
                Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME);
        String usernickname = FileUtil.readSharedPreferences(getActivity(),
                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
        if (username.equals("")) {
            et3.setText("");
            et4.setText("");
        }
        if (usernickname.equals("")) {
            UDPHelper.getInstance(getActivity()).setName(defaultDeviceName + "_" + DeviceIdFactory.getDeviceId(getActivity()));
            //et2.setText("");
        } else {
            UDPHelper.getInstance(getActivity()).setName(usernickname);
            et2.setText(usernickname);
        }
    }

    private void saveJoinname(String joinName) {
        FileUtil.saveSharedPreferences(getActivity(),
                Constants.SHARED_PREFERENCES,
                Constants.LOGIN_JOINNAME, joinName);
    }

//    private void saveJoinNameDefault() {
//        FileUtil.saveSharedPreferences(getActivity(),
//                Constants.SHARED_PREFERENCES,
//                Constants.LOGIN_JOINNAME, getResources().getStrin g(R.string.comm_defaultname) + "_" + DeviceIdFactory.getDeviceId(getActivity()));
//    }
}
