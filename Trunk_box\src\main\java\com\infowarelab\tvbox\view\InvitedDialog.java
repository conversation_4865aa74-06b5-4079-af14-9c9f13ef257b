package com.infowarelab.tvbox.view;


import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.domain.ConferenceBean;

public class InvitedDialog extends AlertDialog {
    private int width = 0;
    private String confId = "";
    private OnResultListener onResultListener;
    private ConferenceBean conferenceBean;
    private TextView tv1;
    private TextView tv2;



    public InvitedDialog(Context context) {
        super(context, R.style.style_dialog_normal);
    }

    public InvitedDialog(Context context, int width) {
        super(context, R.style.style_dialog_normal);
        this.width = width;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_invite);
        setCanceledOnTouchOutside(true);

        tv1 = (TextView) findViewById(R.id.dialog_invite_tv1);
        tv2 = (TextView) findViewById(R.id.dialog_invite_tv2);

        Button btnYes = (Button) findViewById(R.id.btn_dialoginvite_confirm);
        btnYes.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                doYes(confId);
                cancel();
                dismiss();
            }
        });
        Button btnNo = (Button) findViewById(R.id.btn_dialoginvite_cancel);
        btnNo.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                doNo();
                cancel();
                dismiss();
            }
        });

    }

    public void show(String theme,String name,String confid){
        this.confId = confid;

        show();
        tv1.setText(getContext().getResources().getString(R.string.invite_dialog_theme)+theme);

        tv2.setText(getContext().getResources().getString(R.string.invite_dialog_host)+name);
    }


    public void setClickListener(OnResultListener onResultListener) {
        this.onResultListener = onResultListener;
    }

    public interface OnResultListener {
        public void doYes(String confid);

        public void doNo();
    }

    private void doYes(String confid) {
        if (onResultListener != null) {
            onResultListener.doYes(confid);
        }
    }

    private void doNo() {
        if (onResultListener != null) {
            onResultListener.doNo();
        }
    }
}
