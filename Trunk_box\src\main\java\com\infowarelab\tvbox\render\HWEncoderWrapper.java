package com.infowarelab.tvbox.render;


import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.SurfaceTexture;
import android.media.MediaCodecInfo;
import android.opengl.EGLContext;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

import com.infowarelab.tvbox.gles.EglCore;
import com.infowarelab.tvbox.gles.WindowSurface;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;

public class HWEncoderWrapper {

    private static final long MAX_TIMEOUT = 3000;
    private static final String TAG = "InfowareLab.Debug";

    private Thread mVExecutor;
    private HWVideoEncoder mEncoder = new HWVideoEncoder();
    private WindowSurface mInputWindowSurface;
    private EglCore mEglCore;
    private BaseFilter mFullScreen;
    private Context c;
    private FilterFactory.FilterType type = FilterFactory.FilterType.Original;
    private int mImageFormat = MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface;
    private Handler mVideoHandler;
    private boolean mAsyncCodec = false;

    private boolean isReady = false;
    private boolean isStarted = false;

    public HWEncoderWrapper(Context c) {
        this.c = c;
    }

    public void start(VideoCommonImpl videoCommon, int width, int height, int bitRate, int frameRate,
                      EGLContext eglContext) {

        try {
            mEncoder.init(videoCommon, width, height, mImageFormat, bitRate, frameRate, mAsyncCodec);
        } catch (Exception e) {
            Log.d(TAG, "HWEncoderWrapper.start: " + e.getMessage() + "and" + e.getLocalizedMessage());
            return ;
        }

        mVExecutor = new Thread(new VideoRunnable(eglContext, width, height));

        //启动线程
        mVExecutor.start();

        isStarted = true;
    }

    public void start(ShareDtCommonImpl shareDtCommon, int width, int height, int bitRate, int frameRate,
                      EGLContext eglContext) {

        try {
            mEncoder.init(shareDtCommon, width, height, mImageFormat, bitRate, frameRate, mAsyncCodec);
        } catch (Exception e) {
            Log.d(TAG, "HWEncoderWrapper.start: " + e.getMessage() + "and" + e.getLocalizedMessage());
            return ;
        }

        mVExecutor = new Thread(new VideoRunnable(eglContext, width, height));

        //启动线程
        mVExecutor.start();

        isStarted = true;
    }

    public void changeResolution(int encodeWidth, int encodeHeight) {

        try {
            mEncoder.changeResolution(encodeWidth, encodeHeight);
        } catch (Exception e) {
            Log.d(TAG, "HWEncoderWrapper.changeResolution: " + e.getMessage() + "and" + e.getLocalizedMessage());
            return ;
        }
    }

    public class VideoRunnable implements Runnable {

        int width;
        int height;
        EGLContext eglContext;

        public VideoRunnable(EGLContext eglContext, int width, int height) {

            this.eglContext = eglContext;
            this.width = width;
            this.height = height;
        }

        @Override
        public void run() {

            Looper.prepare();

            if (mVideoHandler == null)
                mVideoHandler = new Handler();

            prepareEGL(eglContext, width, height);

            Looper.loop();

        }
    }

    @SuppressLint("NewApi")
    private void prepareEGL(EGLContext eglContext, int width, int height) {

        mVideoHandler.post(() -> {

            Log.d(TAG, ">>>>>prepareEGL: " + width + "x" + height);

            mEglCore = new EglCore(eglContext, EglCore.FLAG_RECORDABLE);
            mInputWindowSurface = new WindowSurface(mEglCore, mEncoder.getInputSurface(), true);
            mInputWindowSurface.makeCurrent();

            mFullScreen = FilterFactory.createFilter(c, type);
            mFullScreen.createProgram();
            mFullScreen.onInputSizeChanged(width, height);

            isReady = true;

        });

    }


    /**
     * 开启视频录制，该方法随着帧数反复调用
     *
     * @param mTextureId
     * @param st
     */

    public void onFrameAvailable(int mTextureId, SurfaceTexture st, boolean mirror) {

        if (!isReady) {
            Log.d(TAG, "onFrameAvailable: not ready");
            return;
        }

        if (mVideoHandler == null) {
            Log.d(TAG, "onFrameAvailable: VideoHandler == null");
            return;
        }

        if (mEncoder == null) {
            Log.d(TAG, "onFrameAvailable: encoder null");
            return;
        }

        if (mFullScreen == null) {
            Log.d(TAG, "onFrameAvailable:filter null");
            return;
        }

        if (mInputWindowSurface == null) {
            Log.d(TAG, "onFrameAvailable:input surface null");
            return;
        }

        if (!isStarted) {
            Log.d(TAG, "onFrameAvailable: NOT started");
            return;
        }

        mVideoHandler.post(() -> {

            if (!isStarted) {
                Log.d(TAG, "onFrameAvailable(post): NOT started");
                return;
            }

            float[] transform = new float[16];
            st.getTransformMatrix(transform);

            //每帧间隔多少毫秒
            int loopingInterval = 1000/30;

            //每帧的时间估算。这里原来想用grafika里的算法，但是视频和音频总是不能同步。因此改了算法。
            long timestamp = (SystemClock.uptimeMillis() + loopingInterval) * 1000000;

            try {
                if (!mAsyncCodec) mEncoder.sendFrame();

                mFullScreen.setMirror(mirror);
                mFullScreen.draw(mTextureId, transform);
                mInputWindowSurface.setPresentationTime(timestamp);
                mInputWindowSurface.swapBuffers();
            } catch (Exception e) {
                e.printStackTrace();

            }

        });

    }

    public void stop() {

            releaseEncoder();

            isStarted = false;

            //清空handler
            //mVideoHandler.removeCallbacksAndMessages(null);
            //mVideoHandler = null;

    }

    public void stopAll() {

        releaseEncoder();

        isStarted = false;

        //清空handler
        mVideoHandler.removeCallbacksAndMessages(null);
        mVideoHandler = null;

    }

    public void updateFilter(FilterFactory.FilterType type) {

        this.type = type;

    }
    private void releaseEncoder() {

        mEncoder.stop();

        if (mVideoHandler == null) return;

        mVideoHandler.post(() -> {

            Log.d(TAG, ">>>>>releaseEncoder");

            if (mInputWindowSurface != null) {
                mInputWindowSurface.release();
                mInputWindowSurface = null;
            }
            if (mFullScreen != null) {
                mFullScreen.releaseProgram();
                mFullScreen = null;
            }
            if (mEglCore != null) {
                mEglCore.release();
                mEglCore = null;
            }
        });

    }
}
