package com.infowarelab.tvbox.view;


import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.domain.ConferenceBean;

public class ExitDialog extends AlertDialog {
    private boolean hideCancel = false;
    private int width = 0;
    private OnResultListener onResultListener;
    private TextView contextText;

    private String context;

    public void setContext(String context) {
        this.context = context;
    }

    public ExitDialog(Context context) {
        super(context, R.style.style_dialog_normal);
    }

    public ExitDialog(Context context, int width) {
        super(context, R.style.style_dialog_normal);
        this.width = width;
    }

    public ExitDialog(Context context, int width, boolean hideCancel) {
        super(context, R.style.style_dialog_normal);
        this.width = width;
        this.hideCancel = hideCancel;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.view_dialog_exit);
        setCanceledOnTouchOutside(true);

        contextText = (TextView)findViewById(R.id.dialog_exit_tv_join);

        Button btnYes = (Button) findViewById(R.id.btn_dialogexit_confirm);
        btnYes.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                doYes();
                cancel();
            }
        });
        Button btnNo = (Button) findViewById(R.id.btn_dialogexit_cancel);
        btnNo.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                doNo();
                cancel();
            }
        });

        if (!hideCancel) {
            btnNo.setPressed(true);
            btnNo.setFocusable(true);
            btnYes.setFocusable(true);
            btnNo.requestFocus();
        }
        else
        {
            btnYes.setPressed(true);
            btnYes.setFocusable(true);
            btnNo.setVisibility(View.GONE);
            btnYes.requestFocus();
        }
    }

    @Override
    public void show() {
        super.show();
        if (!TextUtils.isEmpty(context)){
            contextText.setText(context);
        }else {
            contextText.setText(getContext().getResources().getString(R.string.exit_hint));
        }
    }

    /**
     * 在AlertDialog的 onStart() 生命周期里面执行开始动画
     */
    @Override
    protected void onStart() {
        super.onStart();
    }

    /**
     * 在AlertDialog的onStop()生命周期里面执行停止动画
     */
    @Override
    protected void onStop() {
        super.onStop();

    }

    public void setClickListener(OnResultListener onResultListener) {
        this.onResultListener = onResultListener;
    }

    public interface OnResultListener {
        public void doYes();

        public void doNo();
    }

    private void doYes() {
        if (onResultListener != null) {
            onResultListener.doYes();
        }
    }

    private void doNo() {
        if (onResultListener != null) {
            onResultListener.doNo();
        }
    }

    public void animShow(){
        super.show();
        Window window = getWindow();
        assert window != null;
        window.setWindowAnimations(R.style.dialogWindowAnim);
    }
}
