package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.activity.ActHome;
import com.infowarelab.tvbox.adapter.GridAdapter;
import com.infowarelab.tvbox.base.ResInvite;
import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.DeviceIdFactory;
import com.infowarelab.tvbox.utils.IUDP;
import com.infowarelab.tvbox.utils.PublicWay;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelab.tvbox.utils.UDPHelper;
import com.infowarelab.tvbox.utils.XMLUtils;
import com.infowarelab.tvbox.view.GridViewTV;
import com.infowarelab.tvbox.view.InviteDialog;
import com.infowarelab.tvbox.view.InvitedDialog;
import com.infowarelab.tvbox.view.JoinDialog;
import com.infowarelab.tvbox.view.LoadingDialog;
import com.infowarelab.tvbox.view.MyEffectNoDrawBridge;
import com.infowarelab.tvbox.view.MyMainUpView;
import com.infowarelabsdk.conference.callback.CallbackManager;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.AudioCommonImpl;
import com.infowarelabsdk.conference.common.impl.ChatCommomImpl;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.confctrl.ConferenceCommon;
import com.infowarelabsdk.conference.domain.ConferenceBean;
import com.infowarelabsdk.conference.domain.ConfigBean;
import com.infowarelabsdk.conference.domain.LoginBean;
import com.infowarelabsdk.conference.domain.UserBean;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.MessageEvent;
import com.infowarelabsdk.conference.util.StringUtil;
import com.infowarelabsdk.conference.util.ToastUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

////import org.apache.log4j.Logger;

/**
 * Created by Always on 2017/8/7.
 */

@SuppressLint("ValidFragment")
public class FragMeetings extends BaseFragment implements View.OnClickListener, IUDP {

    protected static final int CHECK_SITE = -18;
    protected static final int SHOWALTER = 10;

    public ConferenceBean joinedConfBean = null;

    private final ConferenceBean conferenceBean = new ConferenceBean();

    private int duration = 120;

    //protected Logger log = Logger.getLogger(getClass());
    private View meetingView;
    private GridViewTV gridView;
    private MyMainUpView mainUpView1;
    private GridAdapter adapter;
    private LinearLayout llGv, llNone;
    private View mOldView = null;
    private MyEffectNoDrawBridge bridget;
    private int mCurrentRow = 0;
    private int offsetheight = 0;
    private List<ConferenceBean> conferences = new ArrayList<>();
    private UserBean userBean = new UserBean();
    private SharedPreferences preferences;
    Intent mConfIntent;

    //无数据
    private ImageView netImage;
    private TextView tv,tv1,tv2;
    private TextView tv_ip;

    public static final int RES_CONFERENCES = 1;
    public static final int DISMISS = 3;
    public static final int FINISH = 4;
    public static final int DOCONFIG = 5;
    public static final int LOGINFAILED = 6;
    public static final int MEETINGINVALIDATE = 7;
    public static final int MEETINGNOTJOINBEFORE = 8;
    public static final int HOSTERROR = 9;
    public static final int SPELLERROR = 10;
    public static final int GET_ERROR_MESSAGE = 11;
    public static final int JOIN_CONFERENCE = 12;
    public static final int CONNTIMEOUT = 13;
    public static final int ADDPASSWORDEDITOR = 14;
    public static final int CREATECONF_ERROR = 15;
    public static final int READY_JOINCONF = 16;
    public static final int CONFERENCE_LOCKED = 17;
    public static final int NEED_LOGIN = 1001;
    public static final int NO_CONFERENCE = 1002;
    public static final int TIME_EXPIRED = 40206;
    public static final int JOINER_OVER_LIMITATION = 40705;
    public static final int LOGIN_VALIDATE_ERRORTIP = 1003;
    protected final static int ENTERFROMITEM = 100;
    public static final int INIT_SDK = 101;
    protected static final int INIT_SDK_FAILED = 102;
    protected static final int CONF_CONFLICT = -5;
    private JoinDialog joinDialog;
    private CommonFactory commonFactory = CommonFactory.getInstance();
    private ConferenceCommonImpl conferenceCommon;
    private String result = "";
    private LoginBean loginBean;
    private Config config;
    private InviteDialog dialog = null;

    private boolean isFist = true;

    private LoadingDialog loadingDialog;
    //是否弹消息
    private boolean isShow = true;
    private Runnable curR;
    //是否弹窗
//    private boolean isPopup = true;

    private Handler mRejoinConfResultHandler = null;

    public Handler createHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case CHECK_SITE:
                    hideLoading();
                    ToastUtil.showMessage(getContext(),"请确认站点是否正确或检查网络是否给力",2 * 1000);
                    break;
                case SHOWALTER:
                    hideLoading();
                    break;
                default:
                    break;
            }
        }
    };;
    private String confId = null;
    private int mCurrentIndex = -1;
    private boolean mRejoining = false;
    //private String mConfId;

    public FragMeetings(ICallParentView iCallParentView) {
        super(iCallParentView);
        Log.d("InfowareLab.Debug","FragMeetings construction with parameters");
    }
    //无参构造器
    public FragMeetings(){
        super();
        Log.d("InfowareLab.Debug","FragMeetings null construction");
    }

    @Override
    public void onClick(View v) {

    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d("InfowareLab.Debug","FragMeetings.onCreateView");

        if (TextUtils.isEmpty(Config.Site_URL)) {
            Config.SiteName = FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES, Constants.SITE_NAME);
            Config.Site_URL = FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES, Constants.SITE);

            Log.d("InfowareLab.Debug",">>>>>>FragMeetings.config.Site_URL Reset!");
        }

        Log.d("InfowareLab.Debug",">>>>>>FragMeetings.config.Site_URL = " + Config.Site_URL);

        meetingView = inflater.inflate(R.layout.frag_home_meetings, container, false);
        //if (savedInstanceState == null)
        initView();
        //注册EventBus
        EventBus.getDefault().register(this);
        mConfIntent = null;
        return meetingView;
    }

    private void initView() {
        Log.d("InfowareLab.Debug","FragMeetings.initView");
        //show loading dialog to show joining meeting
        loadingDialog = new LoadingDialog(getContext());
        loadingDialog.setCancelable(false);
        loadingDialog.setCanceledOnTouchOutside(false);

        loadingDialog.setTitle("正在加会");

        dialog = new InviteDialog(getContext(), 0);

        preferences = getActivity().getSharedPreferences(Constants.SHARED_PREFERENCES,
                Context.MODE_WORLD_READABLE);

        userBean.setUid(preferences.getInt(Constants.USER_ID, 0));
        userBean.setNickname(preferences.getString(Constants.LOGIN_NAME, ""));

        conferenceCommon = (ConferenceCommonImpl) commonFactory.getConferenceCommon();
        if (confHandler != null && conferenceCommon != null) {
            if (conferenceCommon.getHandler() != confHandler) {
                conferenceCommon.setHandler(confHandler);
            }
        }

        llGv = (LinearLayout) meetingView.findViewById(R.id.llgv);
        llNone = (LinearLayout) meetingView.findViewById(R.id.llNone);
        tv_ip = (TextView) meetingView.findViewById(R.id.tv_ip);
        netImage = (ImageView)meetingView.findViewById(R.id.no_feeting_image);
        tv = (TextView)meetingView.findViewById(R.id.no_feeting_tv);
        tv1 = (TextView)meetingView.findViewById(R.id.tv1);
        tv2 = (TextView)meetingView.findViewById(R.id.tv2);
        final List<ConferenceBean> data = new ArrayList<ConferenceBean>();
        gridView = (GridViewTV) meetingView.findViewById(R.id.gv_frag_meetings);
        adapter = new GridAdapter(getActivity(), data, 10);
        //gridView.setAdapter(adapter);
        mainUpView1 = (MyMainUpView) meetingView.findViewById(R.id.fl_frag_meetings_mainUpView);
        // 建议使用 NoDraw.
        mainUpView1.setEffectBridge(new MyEffectNoDrawBridge());
        bridget = (MyEffectNoDrawBridge) mainUpView1.getEffectBridge();
        bridget.setTranDurAnimTime(200);
        // 设置移动边框的图片.
        //mainUpView1.setUpRectResource(R.drawable.white_light_10);
        // 移动方框缩小的距离.
        mainUpView1.setDrawUpRectPadding(new Rect(10, 10, 10, -55));
        gridView.setSelector(new ColorDrawable(Color.TRANSPARENT));
        //gridView.setSelection(0);
        //
        gridView.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Log.d("InfowareLab.Debug","FragMeetings.onItemSelected:"+position);

                if (getActivity() == null){
                    Log.d("InfowareLab.Debug","FragMeetings.onItemSelected: getActivity() == null");
                    return;
                }

                if (position > adapter.getCount() || getActivity() == null){
                    return;
                }

                int offset = 0;
                if (mCurrentRow == 0) {
                    if (gridView != null && view != null ){
                        offsetheight = (gridView.getHeight() - view.getHeight()) / 2;
                    }else {
                        offsetheight = DensityUtil.dip2px(getContext(),600);
                    }
                    mCurrentRow = getItemIndex(position);
                } else if (getItemIndex(position) != mCurrentRow) {
                    if (gridView != null && view != null ){
                        offsetheight = (gridView.getHeight() - view.getHeight()) / 2;
                    }else{
                        offsetheight = DensityUtil.dip2px(getActivity().getApplicationContext(),600);
                    }
                    gridView.smoothScrollToPositionFromTop(position, offsetheight, 200);
                    mCurrentRow = getItemIndex(position);
                    if (getItemIndex(position) != 1 && getItemIndex(position) != adapter.getNumRows()) {
                        int h = view.getHeight();
                        offset = offsetheight - (view.getTop() - gridView.getTop()) - (llGv.getHeight() - gridView.getHeight()) / 2;
                    }
                } else if (getItemIndex(position) < mCurrentRow) {
                    if (gridView != null && view != null ){
                        offsetheight = (gridView.getHeight() - view.getHeight()) / 2;
                    }else {
                        offsetheight = DensityUtil.dip2px(getContext(),600);
                    }
                    gridView.smoothScrollToPositionFromTop(position, offsetheight, 600);
                    mCurrentRow = getItemIndex(position);
                    if (getItemIndex(position) != 1 && getItemIndex(position) != adapter.getNumRows()) {
                        int h = view.getHeight();
                        offset = offsetheight + (gridView.getTop() - llGv.getTop());
                    }
                }
//                Toast.makeText(getActivity(), position + "; " + offset, Toast.LENGTH_SHORT).show();
                if (mOldView == null) {
                    bridget.setVisibleWidget(false);
                }
                /**
                 * 这里注意要加判断是否为NULL.keneng
                 * 因为在重新加载数据以后会出问题.
                 */
                if (view != null) {
                    Log.d("InfowareLab.Debug","mainUpView1.setFocusView");
                    mainUpView1.setFocusView(view, mOldView, 1.1f, offset);
                }
                mOldView = view;

                mCurrentIndex = position;
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                Log.d("InfowareLab.Debug", "onNothingSelected");
            }
        });
        gridView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (position == 0 && FileUtil.readSharedPreferences(getContext(), Constants.SHARED_PREFERENCES,
                        Constants.LOGIN_ROLE).contains("meeting.role.init.host;") /*&& PublicWay.isConnect*/){
                    adapter.doCreat();
                }else {
                    adapter.doSelect(adapter.getItem(position));
                }
            }
        });
        gridView.post(new Runnable() {
            @Override
            public void run() {
                if (adapter.getCount() > 1) {
//                    gridView.setDefaultSelect(0);
                }
                getConfListData();
            }
        });
    }

    Handler focusHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            gridView.setSelection(msg.arg1);
        }
    };


    public void onParentResume() {
        UDPHelper.getInstance(getActivity()).setIudp(this);
    }

    private int getItemIndex(int position) {
        int r = position % adapter.getNumColumns();
        if (r == 0) {
            if (position > 0) {
                return (position / adapter.getNumColumns()) + 1;
            } else {
                return 1;
            }
        } else {
            return (position / adapter.getNumColumns()) + 1;
        }
    }

    public void onLosefocus() {
        if (bridget != null)
            bridget.setVisibleWidget(true);
        if (mOldView != null) {
            mainUpView1.setUnFocusView(mOldView);
            mOldView = null;
        }
    }

    public boolean isGridViewFocused() { return gridView.isFocused(); }
    public int getConferenceCount() {
       if (conferences == null)
           return 0;

       return conferences.size();
    }
    public boolean isInLastRow() { return (mCurrentIndex + 1)/4 == conferences.size()/4;}

    public Handler confHandler = new Handler() {
        public void handleMessage(Message msg) {

            switch (msg.what) {
                case ConferenceCommon.RESULT_SUCCESS:

                    checkRejoinResult(msg.what);

                    Log.d("InfowareLab.Debug","conf.Handler: RESULT_SUCCESS");
                    SharedPreferencesUrls.getInstance().putBoolean("isPopup1",false);
//                    //通知是box

                    //Intent intent = null;

                    if (null != mConfIntent){
                        Log.d("InfowareLab.Debug","Ignored for ActConf is already launched.");
                        break;
                    }

                    Log.d("InfowareLab.Debug","conf.Handler: Launch ActConf");

                    if (ActHome.mActivity != null){
                        mConfIntent = new Intent(ActHome.mActivity, ActConf.class);
                    }else {
                        mConfIntent = new Intent(getActivity(), ActConf.class);
                    }
                    mConfIntent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                    if (confId != null)
                        mConfIntent.putExtra("confId", confId);
                    getActivity().startActivity(mConfIntent);

                    hideLoading();

                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }

                    getActivity().finish();

                    /*
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (loadingDialog != null && loadingDialog.isShowing()){
                                loadingDialog.dismiss();
                            }
                        }
                    }, 1 * 1000); */
                    break;
                case ConferenceCommon.BEYOUNGMAXCOUNT:

                    checkRejoinResult(msg.what);

                    hideLoading();
                    post(new Runnable() {
                        @Override
                        public void run() {
                            showShortToast(R.string.item_meetings_err_5);
                        }
                    });
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (loadingDialog != null && loadingDialog.isShowing()){
                                loadingDialog.dismiss();
                            }
                        }
                    }, 1 * 1000);
                    break;
                case ConferenceCommon.BEYOUNGJIAMI:

                    checkRejoinResult(msg.what);

                    hideLoading();
                    post(new Runnable() {
                        @Override
                        public void run() {
                            showShortToast(R.string.item_meetings_err_6);
                        }
                    });
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (loadingDialog != null && loadingDialog.isShowing()){
                                loadingDialog.dismiss();
                            }
                        }
                    }, 1 * 1000);
                    break;
                case ENTERFROMITEM:
//                    checkResultIntent((ConferenceBean) msg.obj);
//                    checkJoinConf();
                    break;
                case INIT_SDK:
                    Log.d("InfowareLab.Debug","FragMeetings.initSDK success");
                    ////log.info("initSDK success");
                    break;
                case INIT_SDK_FAILED:

                    checkRejoinResult(msg.what);

                    hideLoading();
                    showShortToast(R.string.item_meetings_err_15);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (loadingDialog != null && loadingDialog.isShowing()){
                                loadingDialog.dismiss();
                            }
                        }
                    }, 1 * 1000);
                    break;
                case CONF_CONFLICT:

                    checkRejoinResult(msg.what);

                    hideLoading();
                    showShortToast(R.string.item_meetings_err_9);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (loadingDialog != null && loadingDialog.isShowing()){
                                loadingDialog.dismiss();
                            }
                        }
                    }, 1 * 1000);
                    break;
                case ConferenceCommon.LEAVE:
                    break;
                case ConferenceCommon.CLOUDRECORD:
                    break;
                case ConferenceCommon.LICENSE_ERR:
                case JOINER_OVER_LIMITATION:

                    checkRejoinResult(msg.what);

                    hideLoading();
                    showShortToast("终端授权点数不足，请联系管理员处理!");
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (loadingDialog != null && loadingDialog.isShowing()){
                                loadingDialog.dismiss();
                            }
                        }
                    }, 1 * 1000);
                    break;
                default:
                    hideLoading();
                    ////log.info("Join Failed");
                    Log.d("InfowareLab.Debug", "FragMeetings.Join Failed: "+ msg.what);
                    //if (getContext() != null){
                        //ErrorMessage errorMessage = new ErrorMessage(getContext());
                        //String message = errorMessage.getErrorMessageByCode(msg.what);
                        ////log.info("false code = " + msg.what + " false = " + getActivity().getString(R.string.ConfSysErrCode_Conf_Area_Error));
                        //Log.d("InfowareLab.Debug", "FragMeetings.Error Code="+ msg.what);
                        //Toast.makeText(getActivity(), message, Toast.LENGTH_LONG).show();
                    //}
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (loadingDialog != null && loadingDialog.isShowing()){
                                loadingDialog.dismiss();
                            }
                        }
                    }, 1 * 1000);
                    break;
            }
        }

    };

    private void checkRejoinResult(int errorCode) {

        if (!mRejoining) return;

        if (mRejoinConfResultHandler != null){

            Message msg = new Message();
            msg.what = 2;  //消息(一个整型值)
            msg.obj = errorCode;

            Log.d("InfowareLab.Debug", ">>>>>>checkRejoinResult: errorCode = " + errorCode);

            mRejoinConfResultHandler.sendMessage(msg);
        }
    }

    private Handler handler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case Constants.ERROR_GET_SITENAME:
//                    preferences.edit().putBoolean(ISCONFIG, false).commit();
//                    showShortToast(mActivity.getString(R.string.error_prompt_siteName));
                    break;

                default:
                    break;
            }
        }

        ;
    };

    @Override
    public void onResume() {
        if (gridView != null){
            gridView.setFocusable(true);
            gridView.requestFocusFromTouch();
        }else if (ActHome.mActivity != null){
            ActHome.mActivity.applyFouse(0);
        }
        super.onResume();
        isFist = true;
        isShow = true;
        if (isNetConnect()){
            netImage.setImageResource(R.drawable.no_meeting);
            tv.setText(getResources().getString(R.string.no_meeting));
            tv1.setText(getResources().getString(R.string.please));

            tv2.setText(getResources().getString(R.string.create_meeting));
        }else {
            netImage.setImageResource(R.drawable.no_wifi);
            tv.setText(getResources().getString(R.string.no_wifi_title));
            tv1.setText(getResources().getString(R.string.qd_title));
            tv_ip.setText(getResources().getString(R.string.sett_title));
            tv2.setText(getResources().getString(R.string.check_net));
        }
    }
    private Handler listHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case RES_CONFERENCES:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    if (conferences == null) {
                        Log.d("InfowareLab.Debug","listHandler.NoMeetings(null)");
                        if (!TextUtils.isEmpty(Config.Site_URL)){
                            tv_ip.setText(Config.Site_URL);
                        }else {
                            tv_ip.setText(getResources().getString(R.string.ip));
                        }
                        mainUpView1.setVisibility(View.GONE);
                        llNone.setVisibility(View.VISIBLE);
//                        ActHome actHome = (ActHome) getActivity();
//                        if (!ActHome.isSetting){
//                            actHome.switchFrag(5);
//                        }
                        adapter.update(new ArrayList<ConferenceBean>());
                    } else if (conferences.isEmpty()) {
                        Log.d("InfowareLab.Debug","listHandler.NoMeetings(empty)");
                        if (!TextUtils.isEmpty(Config.Site_URL)){
                            tv_ip.setText(Config.Site_URL);
                        }else {
                            tv_ip.setText("http://meeting.hongshantong.cn");
                        }
                        mainUpView1.setVisibility(View.GONE);
                        llNone.setVisibility(View.VISIBLE);

//                        ActHome act = (ActHome) getActivity();
//                        if (!ActHome.isSetting){
//                            act.switchFrag(5);
//                        }
                        adapter.update(new ArrayList<ConferenceBean>());
                    } else {
                        Log.d("InfowareLab.Debug","listHandler: Got meeting list: " + conferences.size());
                        adapter.update(new ArrayList<ConferenceBean>());
                        gridView.setAdapter(null);
                        llNone.setVisibility(View.GONE);
                        adapter.update(conferences);
                        gridView.setAdapter(adapter);
                        mainUpView1.setVisibility(View.VISIBLE);

                        adapter.setOnSelectListener(new GridAdapter.OnSelectListener() {
                            @Override
                            public void doSelect(ConferenceBean conferenceBean) {

                                Log.d("InfowareLab.Debug","FragMeetings.adapter.doSelect: " + conferenceBean.getName());

                                conferenceCommon.saveMyVideoAudioSync(false, false);

                                if (loadingDialog != null && !loadingDialog.isShowing()){
                                    loadingDialog.setTitle("正在加会");
                                    loadingDialog.show();
                                }
                                XMLUtils.CONFIGID = conferenceBean.getId();
                                XMLUtils.CONFIGNAME = conferenceBean.getName();
                                XMLUtils.CONFERENCEPATTERN = conferenceBean.getConferencePattern();
                                String password = conferenceBean.getConfPassword();
                                handler.postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        if (loadingDialog != null && loadingDialog.isShowing()){
                                            loadingDialog.dismiss();
                                        }
                                    }
                                }, 10 * 1000);

                                CommonFactory commonFactory = CommonFactory.getInstance();
                                if (null != commonFactory)
                                {
                                    VideoCommonImpl vCommon = (VideoCommonImpl)commonFactory.getVideoCommon();
                                    if (null != vCommon)
                                        vCommon.clearMap();
                                }
                                //清空视频路数
                                //((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).clearMap();
//                                //通知是box
                                if (conferenceBean.getNeedLogin() == 1 && !isLogin()) {
                                    ToastUtil.showMessage(getContext(),getContext().getResources().getString(R.string.no_login_text),
                                            10 * 1000);
                                    if (loadingDialog != null && loadingDialog.isShowing()){
                                        loadingDialog.dismiss();
                                    }
                                    hideLoading();
                                } else {
                                    if (isFastClick()){
                                        ToastUtil.showMessage(getContext(),"请勿连续点击",2 * 1000);
                                        if (loadingDialog != null && loadingDialog.isShowing()){
                                            loadingDialog.dismiss();
                                        }
                                        return;
                                    }
                                    if (TextUtils.isEmpty(password) || "".equals(password)) {
                                        joinConf(conferenceBean, false, "");
                                    } else {
                                        showJoinDialog(conferenceBean);
                                        if (loadingDialog != null && loadingDialog.isShowing()){
                                            loadingDialog.dismiss();
                                        }
                                    }
                                }
                            }

                            @Override
                            public void doCreateMeeting() {
                                if (PublicWay.socketBinder != null && PublicWay.socketBinder.getService() != null){
                                    String RequestID = ""+ConferenceApplication.currentTimeMillis();
                                    String deviceId = DeviceIdFactory.getUUID1(getContext());
                                    String siteId = FileUtil.readSharedPreferences(getContext(),Constants.SHARED_PREFERENCES,
                                            Constants.SITE_ID);
                                    PublicWay.socketBinder.getService().sendOrderEx(XMLUtils.getXml_list(RequestID,deviceId,siteId));
                                    SharedPreferencesUrls.getInstance().putBoolean("isPopup",true);
                                    SharedPreferencesUrls.getInstance().putBoolean("isPopup1",false);
                                    if (loadingDialog != null && !loadingDialog.isShowing()){
                                        loadingDialog.setTitle("正在获取列表");
                                        loadingDialog.show();
                                    }
                                    Runnable r = new Runnable() {
                                        @Override
                                        public void run() {
                                            if(curR != this) return;
                                            hideLoading();
                                            if (loadingDialog != null && loadingDialog.isShowing()){
                                                loadingDialog.dismiss();
                                            }
                                            if (isShow){
                                                ToastUtil.showMessage(getContext(),"获取列表失败，请重试", 2 * 1000);
                                                isShow = false;
                                                SharedPreferencesUrls.getInstance().putBoolean("isPopup",false);
                                            }
                                        }
                                    };
                                    curR = r;
                                    handler.postDelayed(r, 10 * 1000);
                                    isShow = true;
                                }else {
                                    if (loadingDialog != null && !loadingDialog.isShowing()){
                                        loadingDialog.setTitle("正在连接服务");
                                        loadingDialog.show();
                                    }
                                    PublicWay.getIpAndPort(getActivity(),Config.Site_URL,FileUtil.readSharedPreferences(getContext(),
                                            Constants.SHARED_PREFERENCES, Constants.SITE_ID));
                                    handler.postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            hideLoading();
                                            if (loadingDialog != null && loadingDialog.isShowing()){
                                                loadingDialog.dismiss();
                                            }
                                        }
                                    }, 5 * 1000);
                                }
                            }
                        });

                        gridView.setDefaultSelect(0);
                        mainUpView1.invalidate();
                    }
                    break;
                case NEED_LOGIN:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_1);
                    break;
                case JOIN_CONFERENCE:
                    Log.d("InfowareLab.Debug","listHander.JOIN_CONFERENCE");
                    conferenceCommon.setLogPath(ConferenceApplication.getConferenceApp().getFilePath("hslog"));
                    Log.d("InfowareLab.Debug","getConferenceCommon.initSDK");
                    commonFactory.getConferenceCommon().initSDK();
                    joinConference();
                    break;
                case NO_CONFERENCE:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_3);
                    break;
                case  TIME_EXPIRED: //40206
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_3);
                    break;
                case JOINER_OVER_LIMITATION:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_5);
                    break;
                case GET_ERROR_MESSAGE:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(config.getConfigBean().getErrorMessage());
                    break;
                case MEETINGNOTJOINBEFORE:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_7);
                    break;
                case HOSTERROR:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    if (isAdded()){
                        showShortToast(R.string.item_meetings_err_13);
                    }
                    break;
                case SPELLERROR:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    if (isAdded()){
                        showShortToast(R.string.item_meetings_err_8);
                    }
                    break;
                case LOGINFAILED:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_9);
                    break;
                case CONFERENCE_LOCKED:
                    showShortToast(R.string.item_meetings_err_17);
                    break;
                case CREATECONF_ERROR:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_14);
                    break;
                case MEETINGINVALIDATE:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_2);
                    break;
                case READY_JOINCONF:
                    break;
                case FINISH:

                    break;
                default:
                    break;
            }
        }

        ;
    };

    /**
     * 开启子线程去获取会议列表
     */
    //此处调两次，故加个值过滤一下
    public void getConfListData() {
        Log.d("InfowareLab.Debug", "FragMeetings.getConfListData");

        if (!isVisible()) {
            Log.d("InfowareLab.Debug", "FragMeetings.getConfListData: Ignored as being hidden.");
        }
        //adapter.update(new ArrayList<ConferenceBean>());

        new Thread(new Runnable() {
            @Override
            public void run() {
                sortConference(userBean);
                //Log.d("InfowareLab.Debug", "username=" + userBean.getUsername() + "password=" + userBean.getPassword());
                listHandler.sendMessage(listHandler.obtainMessage(RES_CONFERENCES));
            }
        }).start();
    }

    private void sortConference(UserBean userBean) {

        Log.d("InfowareLab.Debug", "FragMeetings.sortConference");

        if (!isVisible()) {
            Log.d("InfowareLab.Debug", "FragMeetings.sortConference: Ignored as being hidden.");
        }

        Config.SiteName = FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES, Constants.SITE_NAME);
        Config.Site_URL = FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES, Constants.SITE);
        Config.HAS_LIVE_SERVER = FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES, Constants.HAS_LIVE_SERVER).equals("true");
        if (isLogin()) {
            conferences = new ArrayList<ConferenceBean>();
            List<ConferenceBean> pubConfs = Config.getConferenceList(userBean, handler, 1);
            List<ConferenceBean> myConfs = Config.getConferenceList(userBean, handler, 0);
            HashSet<String> ids = new HashSet<String>();
            if (conferences.size() != 0){
                conferences.clear();
            }
            //主持人角色
            if (FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES,
                    Constants.LOGIN_ROLE).contains("meeting.role.init.host;") /*&& PublicWay.isConnect*/) {
                ConferenceBean conferenceBean = new ConferenceBean();
                conferenceBean.setId("0");
                conferenceBean.setStatus("-10");
                conferences.add(conferenceBean);
            }
            for (ConferenceBean conferenceBean : myConfs) {
                if (conferenceBean.getStatus().equals("1")) {
                    conferences.add(conferenceBean);
                    ids.add(conferenceBean.getId());
                }
            }
            for (ConferenceBean conferenceBean : pubConfs) {
                if (conferenceBean.getStatus().equals("1") && !ids.contains(conferenceBean.getId())) {
                    conferences.add(conferenceBean);
                    ids.add(conferenceBean.getId());
                }
            }
            for (ConferenceBean conferenceBean : myConfs) {
                if (conferenceBean.getStatus().equals("0")) {
                    conferences.add(conferenceBean);
                    ids.add(conferenceBean.getId());
                }
            }
            for (ConferenceBean conferenceBean : pubConfs) {
                if (conferenceBean.getStatus().equals("0") && !ids.contains(conferenceBean.getId())) {
                    conferences.add(conferenceBean);
                    ids.add(conferenceBean.getId());
                }
            }
        } else {
            conferences = null;
            conferences = Config.getConferenceList(userBean, handler, 1);
        }
        if (commonFactory.getConferenceCommon() != null) {
            ((ConferenceCommonImpl) commonFactory.getConferenceCommon()).setConfList(conferences);
        }
    }

//    private boolean isLogin(){
//        mPreferences = getActivity().getSharedPreferences(Constants.SHARED_PREFERENCES, Context.MODE_WORLD_READABLE);
//        int uid = mPreferences.getInt(Constants.USER_ID,0);
//        String username = mPreferences.getString(Constants.LOGIN_NAME, "");
//        userBean.setUid(uid);
//        userBean.setUsername(username);
//        if(uid>0){
//            return true;
//        }else{
//            return false;
//        }
//    }

    public void showJoinDialog(final ConferenceBean confBean) {
        if (joinDialog == null) {
            joinDialog = new JoinDialog(getActivity(), 0);
            joinDialog.setClickListener(new JoinDialog.OnResultListener() {

                @Override
                public void doYes(String pwd) {
                    // TODO Auto-generated method stub
                    if (loadingDialog != null && !loadingDialog.isShowing()){
                        loadingDialog.setTitle("正在加会");
                        loadingDialog.show();
                    }


                    joinConf(confBean, true, pwd);
                }

                @Override
                public void doNo() {
                    // TODO Auto-generated method stub
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }

                    if (joinDialog != null) {
                        joinDialog.dismiss();
                        joinDialog = null;
                    }
                }
            });
        }
        if (joinDialog != null && !joinDialog.isShowing()) {
            joinDialog.show(confBean);
        }
    }

    private void joinConf(final ConferenceBean conferenceBean, boolean isNeedPwd, String pwd) {
//        showLoading();
        final String password = StringUtil.toSemiangle(pwd);
        getLoginBean(conferenceBean, password);

        //remember the conference bean
        conferenceCommon.setConfPwd(pwd);
        conferenceCommon.setConfBean(conferenceBean);

        new Thread() {
            @Override
            public void run() {
//                if (conferenceBean.getStatus().equals("1")) {
//                    joinConf(conferenceBean, getLoginBean(conferenceBean, pwd));
//                } else {
                String nickName = "";
                if (!TextUtils.isEmpty(FileUtil.readSharedPreferences(getActivity(),
                        Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME))){
                    nickName = FileUtil.readSharedPreferences(getActivity(),
                            Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME);
                }
//                    if (conferenceBean.getHostID().equals(String.valueOf(getUid()))) {
                if (nickName.equals(conferenceBean.getHostName()) || nickName.equals(conferenceBean.getCreatorName())) {
                    Log.d("InfowareLab.Debug","FragMeetings.joinConf(HOST) with nickName: " + nickName);
                    startConf(conferenceBean, getLoginBean(conferenceBean, password));
                }
//                    else if (!conferenceBean.getConfType().equals("2")) {//不能加入
//                        listHandler.sendEmptyMessage(MEETINGNOTJOINBEFORE);
//                    }
                else {
                    Log.d("InfowareLab.Debug","FragMeetings.joinConf(GUEST) with nickName  " + nickName);
                    joinConf(conferenceBean, getLoginBean(conferenceBean, password));
                }
//                }
            }

            ;
        }.start();
    }

    private void joinConf(final String confId) {
        showLoading();
        new Thread() {
            @Override
            public void run() {
                String showName = FileUtil.readSharedPreferences(getActivity(),
                        Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
                LoginBean login = new LoginBean(confId, showName, "");
                login.setUid(preferences.getInt(Constants.USER_ID, 0));
                ConferenceBean confBean = Config.getConferenceByNumber(confId);
                String password = confBean.getConfPassword();
                login.setPassword(password);

                if (confBean.getStatus().equals("1")) {
                    joinConf(confBean, login);
                } else {
                    if (confBean.getHostID().equals(String.valueOf(preferences.getInt(Constants.USER_ID, 0)))) {
                        startConf(confBean, login);
                    }
//                    else if (!confBean.getConfType().equals("2")) {//不能加入
//                        listHandler.sendEmptyMessage(MEETINGNOTJOINBEFORE);
//                    }
                    else {
                        joinConf(confBean, login);
                    }
                }
            }
        }.start();
    }

    private void startConf(ConferenceBean confBean, LoginBean loginBean) {

        if (null == conferenceCommon)
            conferenceCommon = (ConferenceCommonImpl) commonFactory.getConferenceCommon();

        if (null == conferenceCommon){
            Log.d("InfowareLab.Debug", "FragMeeting.startConf: conferenceCommon is NULL!");
            return;
        }

        config = conferenceCommon.initConfig();
        int uid = preferences.getInt(Constants.USER_ID, 0);
        String siteId = preferences.getString(Constants.SITE_ID, "");
        String showName = preferences.getString(Constants.LOGIN_JOINNAME, "");

        result = Config.startConf(uid, showName, siteId, confBean);

        Log.d("InfowareLab.Debug","FragMeetings.startConf(HOST) with result: " + result);

        confId = confBean.getId();

        if (result.equals("-1:error")) {
            listHandler.sendEmptyMessage(CREATECONF_ERROR);
        } else {
            if ((getActivity()!= null && (ConferenceApplication) getActivity().getApplication() != null)){
                conferenceCommon.setLogPath(((ConferenceApplication) getActivity().getApplication()).getFilePath("hslog"));
            }
            conferenceCommon.initSDK();
            Config config = conferenceCommon.getConfig();
            conferenceCommon.setMeetingBox();

            boolean existCamera = ConferenceApplication.existCamera();
            boolean existMicrophone = ConferenceApplication.existMicrophone(getActivity().getApplicationContext());

            Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
            commonFactory.getConferenceCommon().setDeviceStatus(existMicrophone, existCamera);

            conferenceCommon.joinConference(conferenceCommon.getParam(loginBean, true));
            //Log.d("InfowareLab.Debug","conferenceCommon.joinConference(HOST): " + conferenceCommon.getParam(loginBean, true));
            //Log.e("gggg","会议参数::"+conferenceCommon.getParam(loginBean, true));
            config.setMyConferenceBean(confBean);
            listHandler.sendEmptyMessage(READY_JOINCONF);
        }
    }

    private void joinConf(ConferenceBean confBean, LoginBean loginBean) {
        if (null == conferenceCommon)
            conferenceCommon = (ConferenceCommonImpl) commonFactory.getConferenceCommon();

        if (null == conferenceCommon){
            Log.d("InfowareLab.Debug", "conferenceCommon is NULL!");
            return;
        }

        if (TextUtils.isEmpty(Config.Site_URL)) {
            Config.SiteName = FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES, Constants.SITE_NAME);
            Config.Site_URL = FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES, Constants.SITE);

            Log.d("InfowareLab.Debug",">>>>>>FragMeetings.config.Site_URL Reset!");
        }

        //Log.d("InfowareLab.Debug", ">>>>>> Config.URL(Before) = " + Config.Site_URL);

        config = conferenceCommon.initConfig(loginBean);

        //Log.d("InfowareLab.Debug", ">>>>>> Config.URL(End) = " + Config.Site_URL);

        if (config.getConfigBean() == null) {
            Log.d("InfowareLab.Debug", "configbean is null");
        }
        if (config.getConfigBean().getErrorCode() == null) {
            Log.d("InfowareLab.Debug", "errorcode is null");
        }

        Log.d("InfowareLab.Debug","FragMeetings.joinConf: with config " + config.getConfigBean().getErrorCode() + ":" + config.getConfigBean().getErrorMessage());
        //Log.d("InfowareLab.Debug", "doLogin " + config.getConfigBean().getErrorCode() + ":" + config.getConfigBean().getErrorMessage());

        confId = confBean.getId();

        if ("0".equals(config.getConfigBean().getErrorCode())) {
            listHandler.sendEmptyMessage(JOIN_CONFERENCE);
        } else {
            ((ConferenceCommonImpl) commonFactory.getConferenceCommon()).setJoinStatus(
                    ConferenceCommon.NOTJOIN);
            if ("-1".equals(config.getConfigBean().getErrorCode())) {
                if (config.getConfigBean().getErrorMessage().startsWith("0x0604003")) {
                    if (config.getConfigBean().getErrorMessage().equals("0x0604003:you should login to meeting system! ")) {
                        loginSystem();
                    } else {
                        listHandler.sendEmptyMessage(FINISH);
                    }
                } else if (config.getConfigBean().getErrorMessage().startsWith("0x0604010")) {
                    String errorMessage = config.getConfigBean().getErrorMessage();
                    if (errorMessage.compareToIgnoreCase("0x0604010:conference lock! ") == 0) {
                        hideLoading();
                        if (loadingDialog != null && loadingDialog.isShowing()){
                            loadingDialog.dismiss();
                        }
                        listHandler.sendEmptyMessage(CONFERENCE_LOCKED);
                    }
                }
                else
                    listHandler.sendEmptyMessage(LOGINFAILED);
            } else if ("-2".equals(config.getConfigBean().getErrorCode())) {
                listHandler.sendEmptyMessage(FINISH);
            } else if ("-10".equals(config.getConfigBean().getErrorCode())) {
                if (Config.HAS_LIVE_SERVER) {
                    if (confBean.getType().equals(Config.MEETING)) {
                        confBean.setType(Config.LIVE);
                        joinConf(confBean, getLoginBean(confBean, loginBean.getPassword()));
                        return;
                    }
                }
                listHandler.sendEmptyMessage(MEETINGINVALIDATE);

            } else if ("-18".equals(config.getConfigBean().getErrorCode())) {
                listHandler.sendEmptyMessage(MEETINGNOTJOINBEFORE);
            } else if (ConferenceCommon.HOSt_ERROR.equals(config.getConfigBean().getErrorCode())) {
                listHandler.sendEmptyMessage(HOSTERROR);
            } else if (ConferenceCommon.SPELL_ERROR.equals(config.getConfigBean().getErrorCode())) {
                //log.info("spell error");
                listHandler.sendEmptyMessage(SPELLERROR);
            } else {
                listHandler.sendEmptyMessage(GET_ERROR_MESSAGE);
            }
        }
    }

    private LoginBean getLoginBean(ConferenceBean conferenceBean, String pwd) {
        String showName = FileUtil.readSharedPreferences(getActivity(),
                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
        int userid = preferences.getInt(Constants.USER_ID, 0);
        loginBean = new LoginBean(conferenceBean.getId(), showName, pwd);
        loginBean.setType(conferenceBean.getType());
        loginBean.setUid(userid);
        return loginBean;
    }

    private void loginSystem() {
        Message msg = listHandler.obtainMessage(NEED_LOGIN);
        msg.sendToTarget();
    }

    private void joinConference() {
        Config config = conferenceCommon.getConfig();
        if (config != null) {
            ConfigBean configBean = config.getConfigBean();
            if (configBean != null) {
                configBean.setUserInfo_m_dwStatus(ConferenceCommon.RT_STATE_RESOURCE_AUDIO);
            } //else
                //log.info("configBean is null");
        } //else
            //log.info("config is null");
        //Log.e("ldy","会议信息:"+conferenceCommon.getParam());

        Log.d("InfowareLab.Debug","FragMeetings.joinConference: " + conferenceCommon.getParam());

        commonFactory.getConferenceCommon().setMeetingBox();

        boolean existCamera = ConferenceApplication.existCamera();
        boolean existMicrophone = ConferenceApplication.existMicrophone(getActivity().getApplicationContext());

        Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
        commonFactory.getConferenceCommon().setDeviceStatus(existMicrophone, existCamera);

        commonFactory.getConferenceCommon().joinConference(conferenceCommon.getParam());
    }

    @Override
    public void onReceive(int code, Object object) {
        if (code == 6 && !isHidden) {
            if (!CallbackManager.IS_LEAVED) return;
            final ResInvite resInvite = (ResInvite) object;
            getActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (resInvite.getConfId() != null && !"".equals(resInvite.getConfId())) {
                        showInivteDialog(resInvite.getConfTitle(), resInvite.getFromTerminal().getName(), resInvite.getConfId());
                    }
                }
            });
            Log.d("UDP Demo", resInvite.toString());
        }
    }

    @Override
    public void onSend(int code) {

    }

    private InvitedDialog inviteDialog;

    private void showInivteDialog(String theme, String name, String confid) {
        if (inviteDialog == null) {
            inviteDialog = new InvitedDialog(getActivity(), 0);
            inviteDialog.setClickListener(new InvitedDialog.OnResultListener() {
                @Override
                public void doYes(String confid) {
                    joinConf(confid);
                }
                @Override
                public void doNo() {

                }
            });
        }
        if (inviteDialog != null && !inviteDialog.isShowing()) {
            inviteDialog.show(theme, name, confid);
        }
    }

    private boolean isHidden = false;

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (hidden) {
            isHidden = true;
        } else {
            isHidden = false;
        }
    }

    /**
     * 发送开启会议的申请获取会议ID
     */

    private void sendConfRequire() {
        config = conferenceCommon.initConfig();
        conferenceBean.setName(FileUtil.readSharedPreferences(getContext(),
                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME));
        conferenceBean.setConfPassword("");
        conferenceBean.setConfType("0");
        XMLUtils.CONFERENCEPATTERN = 1;
        conferenceBean.setConferencePattern(1);
        int uid = getActivity().getSharedPreferences(Constants.SHARED_PREFERENCES,
                getActivity().MODE_WORLD_READABLE).getInt(Constants.USER_ID, 0);
        String siteId = getActivity().getSharedPreferences(Constants.SHARED_PREFERENCES,
                getActivity().MODE_WORLD_READABLE).getString(Constants.SITE_ID, "");
        String userName = getActivity().getSharedPreferences(Constants.SHARED_PREFERENCES, getActivity().MODE_WORLD_READABLE)
                .getString(Constants.LOGIN_NAME, "");
        String realName = getActivity().getSharedPreferences(Constants.SHARED_PREFERENCES, getActivity().MODE_WORLD_READABLE)
                .getString(Constants.LOGIN_EXNAME, "");

        if (realName == null || realName.equals("")) realName = userName;

        conferenceBean.setName(realName+"的会议");
        XMLUtils.CONFIGNAME = realName+"的会议";
//        conferenceBean.setStartTime(StringUtil.dateToStrInGMT(new Date(ConferenceApplication.currentTimeMillis()+ 10 *60 * 1000), "yyyy-MM-dd HH:mm:ss"));
        conferenceBean.setStartTime("");
        conferenceBean.setDuration("" + duration);
        confId = Config.getFixedConfId(uid, userName, siteId, conferenceBean);
        if (confId.startsWith("0")) {
            confId = confId.substring(2);
            conferenceBean.setId(confId);
            XMLUtils.CONFIGID = confId;
            result = Config.startConf(uid, userName, siteId, conferenceBean);
            if (result.equals("-1:error")) {
                createHandler.sendEmptyMessage(CREATECONF_ERROR);
            } else {
                if (PublicWay.socketBinder != null){
                    String RequestID = ""+ConferenceApplication.currentTimeMillis();
                    String siteId1 = FileUtil.readSharedPreferences(getContext(),com.infowarelabsdk.conference.util.Constants.SHARED_PREFERENCES,
                            Constants.SITE_ID);
                    if (dialog.getSelDatas().size() != 0){
                        PublicWay.socketBinder.getService().sendOrderEx(XMLUtils.getInvite(getActivity(),RequestID,confId+conferenceBean.getName(),siteId1,dialog.getSelDatas()));
                    }
                }
                startConf(conferenceBean, getLoginBean(conferenceBean, ""));
            }
        }else {
            hideLoading();
            if (confId.length() > 0)
                createHandler.sendEmptyMessage(Integer.parseInt(confId));
            else
                createHandler.sendEmptyMessage(CREATECONF_ERROR);
        }
    }
    //隐藏邀请对话框
    public void disDialog(){
        if (dialog != null && !dialog.isShowing()){
            dialog.dismiss();
        }
    }
    private void showDialog() {
        if (dialog == null) {
            dialog = new InviteDialog(getContext(), 0);
        }
        if (!dialog.isShowing()) {
            dialog.show();
            dialog.setFouse();
        }
        dialog.setOnResultListener(new InviteDialog.OnResultListener() {
            @Override
            public void doYes() {
                SharedPreferencesUrls.getInstance().putBoolean("isPopup",false);
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
//                        showLoading();
                        if (loadingDialog != null && !loadingDialog.isShowing()){
                            loadingDialog.setTitle("正在加会");
                            loadingDialog.show();
                        }
                    }
                }, 800);
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        hideLoading();
                        if (loadingDialog != null && loadingDialog.isShowing()){
                            loadingDialog.dismiss();
                        }
                    }
                }, 10 * 1000);
                creatConfig();
            }
            @Override
            public void doNo() {
                SharedPreferencesUrls.getInstance().putBoolean("isPopup",false);
                // TODO Auto-generated method stub
            }
        });
        dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                hideLoading();
                SharedPreferencesUrls.getInstance().putBoolean("isPopup",false);
            }
        });
    }
    /**
     * 创建会议
     * */
    private void creatConfig(){
        new Thread() {
            @Override
            public void run() {


                String url = FileUtil.readSharedPreferences(getActivity(),
                        Constants.SHARED_PREFERENCES, Constants.SITE);

                Log.d("InfowareLab.Debug", "FragMeetings.createConfig(createConference)：" + url);

                Config.SiteName = Config.getSiteName(url);

                Log.d("InfowareLab.Debug", "FragMeetings.createConfig：Config.SiteName=" + Config.SiteName);

                if (Config.SiteName.equals("")
                        || Config.SiteName == null) {
                    createHandler.sendEmptyMessage(CHECK_SITE);
                    return;
                }
                loginBean = new LoginBean(null, FileUtil.readSharedPreferences(
                        getActivity(), Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME),
                        FileUtil.readSharedPreferences(getActivity(),
                                Constants.SHARED_PREFERENCES, Constants.LOGIN_PASS));
                loginBean = ((ConferenceCommonImpl) CommonFactory.getInstance().getConferenceCommon())
                        .checkUser(loginBean);//更新登录用户的信息
                if (loginBean == null) {
                    //当无网络或站点不可用时会出现login值为空的情况
                    createHandler.sendEmptyMessage(CHECK_SITE);
                    return;
                }
                //保存登录用户的角色权限信息，并通过匹配字符串检测是否有开会权限
                FileUtil.saveSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES,
                        Constants.LOGIN_ROLE, loginBean.getCreateConfRole());

                FileUtil.saveSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES,
                        Constants.LOGIN_EXNAME, loginBean.getRealname());

                if (FileUtil.readSharedPreferences(getActivity(), Constants.SHARED_PREFERENCES,
                        Constants.LOGIN_ROLE).contains("meeting.role.init.host;")) {
                    sendConfRequire();
                } else {
                    createHandler.sendEmptyMessage(SHOWALTER);
                }
            }
        }.start();
    }

    @Override
    public void onStop() {
        super.onStop();
        if(dialog.isShowing()){
            dialog.dismiss();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (loadingDialog != null && loadingDialog.isShowing()){
            loadingDialog.dismiss();
            loadingDialog = null;
        }
        config = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
    @Subscribe(threadMode = ThreadMode.MAIN,sticky = true)
    public void Event(MessageEvent messageEvent) {
        switch (messageEvent.getType()){
            case 6:
                if (loadingDialog != null && loadingDialog.isShowing()){
                    loadingDialog.dismiss();
                }
                PublicWay.isConnect = messageEvent.isSocketConnect();
                if (messageEvent.isSocketConnect()){
                    ToastUtil.showMessage(getContext(),"服务已连接",2 * 1000);
                }
                if (!isFist && messageEvent.isSocketConnect()){
                    isFist = false;
                    gridView.post(new Runnable() {
                        @Override
                        public void run() {
                           getConfListData();
                        }
                    });
                }
                break;
            case 8:
                if (ActHome.mActivity != null && SharedPreferencesUrls.getInstance().getBoolean("isPopup",false)){
                    isShow = false;
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showDialog();
                }
                break;
            default:
                break;
        }
    }
    /**
     * 防止连续点击
     * @date 2020-12-30
     * */
    private long lastClickTime;
    private boolean isFastClick() {
        boolean flag = false;
        long curClickTime = ConferenceApplication.currentTimeMillis();
        if ((curClickTime - lastClickTime)  <= 1500) {
            flag = true;
        }
        lastClickTime = curClickTime;
        return flag;
    }

    /** <AUTHOR>
     * @category 判断是否有外网连接（普通方法不能判断外网的网络是否连接，比如连接上局域网）
     * @return
     */
    public boolean isNetConnect() {
        try {
            ConnectivityManager cm = (ConnectivityManager)getActivity().getSystemService(getActivity().CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = cm.getActiveNetworkInfo();
            if (networkInfo != null){
                return true;
            }
        }catch (Exception e){
        }
        return false;
    }

    public void rejoinConf(Handler handler) {
        if (conferenceCommon.getConfBean() != null && conferenceCommon.getConfPwd() != null){
            mRejoinConfResultHandler = handler;
            mRejoining = true;
            joinConf(conferenceCommon.getConfBean(), false, conferenceCommon.getConfPwd());
        }
    }
}
