package com.infowarelab.tvbox.action;


import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.LinearLayout;
import android.widget.ToggleButton;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.domain.AnnotationType;
import com.infowarelabsdk.conference.domain.EraserBean;
import com.infowarelabsdk.conference.util.Constants;

////import org.apache.log4j.Logger;

/**
 * 注释操作控制
 *
 * <AUTHOR>
 *
 */
public class AnnotationAction implements OnClickListener {
	//private final Logger log = Logger.getLogger(getClass());

	protected CommonFactory commonFactory = CommonFactory.getInstance();
	private AnnotationType annotation = ((DocCommonImpl) commonFactory
			.getDocCommon()).getAnnotation();
	private EraserBean eraser = ((DocCommonImpl) commonFactory.getDocCommon())
			.getEraser();
	private View view;
	private boolean isAnnoDisplay = false;

	private ToggleButton colorBtn, redBtn, greenBtn, yellowBtn, eraserBtn,
			penBtn, pointerBtn;
	private LinearLayout colorMore;

	public AnnotationAction(View view) {
		this.view = view;
		colorBtn = (ToggleButton) view.findViewById(R.id.annotationColor);
		redBtn = (ToggleButton) view.findViewById(R.id.annotationColorRed);
		greenBtn = (ToggleButton) view.findViewById(R.id.annotationColorGreen);
		yellowBtn = (ToggleButton) view
				.findViewById(R.id.annotationColorYellow);
		eraserBtn = (ToggleButton) view.findViewById(R.id.annotationEraser);
		penBtn = (ToggleButton) view.findViewById(R.id.annotationPen);
		pointerBtn = (ToggleButton) view.findViewById(R.id.annotationPointer);
		colorMore = (LinearLayout) view.findViewById(R.id.annotationColorMore);
		initAnnotationPaintColor();
		initBtnStatus();
		if (annotation != null)
		annotation.setCurrentColor(R.id.annotationColorRed);
	}

	private void initAnnotationPaintColor() {
		colorMore.setVisibility(View.GONE);
		colorBtn.setVisibility(view.VISIBLE);
	}

	/**
	 * 设置图像背景图片
	 *
	 * @param id
	 * @param pressedRes
	 * @param shapeRes
	 */
	private void setShapeBackground(int id, int pressedRes, int shapeRes) {
		view.findViewById(id).setBackgroundResource(pressedRes);
		if (annotation.getAnnoPattern().equals(Constants.SHAPE_POLYGON)) {
			annotation.setPolygonPattern(Constants.SHAPE_CLOSE);
		}
	}

	/**
	 * 设置颜色背景图片
	 *
	 * @param id
	 * @param pressedRes
	 */
	private void setColorBackground(int id, int pressedRes) {
		ToggleButton tb = (ToggleButton) view
				.findViewById(R.id.annotationColor);
		tb.setVisibility(View.VISIBLE);
		tb.setBackgroundResource(pressedRes);
		colorMore.setVisibility(View.GONE);

		annotation.setCurrentColor(id);

		eraserBtn.setChecked(false);
		eraser.setEraserClean(false);
	}

	/**
	 * 显示路径
	 *
	 * @param path
	 */
	public void setPath(String path) {
		// saveDialog.setPath(path);
	}

	@Override
	public void onClick(View v) {
		Log.d("InfowareLab.Debug","annotation action btnView id = " + v.getId());
		int id = v.getId();
		boolean ischecked = false;
		switch (id) {
			case R.id.annotationEraser:
				ischecked = eraser.isEraserClean();
				initBtnStatus();
				if (!ischecked) {
					eraser.setEraserClean(true);
					eraserBtn.setChecked(true);
				}
				break;
			case R.id.annotationColor:
				if (colorMore.getVisibility() == View.VISIBLE){
					colorMore.setVisibility(View.GONE);
					setColorBackground(id,R.drawable.menu_icon);
				}else {
//				colorBtn.setVisibility(View.VISIBLE);
					setColorBackground(id,R.drawable.menu_sel);
					colorMore.setVisibility(View.VISIBLE);
				}
				eraserBtn.setChecked(false);
				eraser.setEraserClean(false);
				break;
			case R.id.annotationColorGreen:
				redBtn.setBackgroundResource(R.drawable.icon_color_red);
				yellowBtn.setBackgroundResource(R.drawable.icon_color_yellow);
				greenBtn.setBackgroundResource(R.drawable.icon_color_green_sel);
				annotation.setCurrentColor(id);

				eraserBtn.setChecked(false);
				eraser.setEraserClean(false);
				break;
			case R.id.annotationColorRed:
				redBtn.setBackgroundResource(R.drawable.icon_color_red_sel);
				yellowBtn.setBackgroundResource(R.drawable.icon_color_yellow);
				greenBtn.setBackgroundResource(R.drawable.icon_color_green);
				annotation.setCurrentColor(id);

				eraserBtn.setChecked(false);
				eraser.setEraserClean(false);
				break;
			case R.id.annotationColorYellow:
				redBtn.setBackgroundResource(R.drawable.icon_color_red);
				yellowBtn.setBackgroundResource(R.drawable.icon_color_yellow_sel);
				greenBtn.setBackgroundResource(R.drawable.icon_color_green);
				annotation.setCurrentColor(id);

				eraserBtn.setChecked(false);
				eraser.setEraserClean(false);
				break;
			case R.id.annotationPen:
				ischecked = annotation.isPainting();
				initBtnStatus();
				if (!ischecked) {
					annotation.setPaint(true);
					penBtn.setChecked(true);
					annotation.setPaintType(annotation.getJsonField(annotation
							.getCurrentPen()));
					annotation.setAnnoPattern(Constants.SHAPE_POLYGON);
					annotation.setPolygonPattern(Constants.SHAPE_LINE);
				}
				break;
			case R.id.annotationPointer:
				ischecked = DocCommonImpl.isStartPointer;
				initBtnStatus();
				if (!ischecked) {
					DocCommonImpl.isStartPointer = true;
					pointerBtn.setChecked(true);
				}
				break;
			case R.id.ibDrag:
				//log.info("ibDrag");
				LinearLayout llAnno = (LinearLayout) view
						.findViewById(R.id.llAnno);

				if (isAnnoDisplay) {
					isAnnoDisplay = false;
					llAnno.setVisibility(View.GONE);
					view.findViewById(R.id.ibDrag).setBackgroundResource(
							R.drawable.anno_pull_pressed);
				} else {
					isAnnoDisplay = true;
					llAnno.setVisibility(View.VISIBLE);
					view.findViewById(R.id.ibDrag).setBackgroundResource(
							R.drawable.anno_push_pressed);
				}
				break;
		}
	}

	/**
	 * 初始化所有标注的按钮状态
	 */
	public void initBtnStatus() {
		DocCommonImpl.isStartPointer = false;
		eraser.setEraserClean(false);

		if (annotation != null)
			annotation.setPaint(false);

		penBtn.setChecked(false);
		eraserBtn.setChecked(false);
		pointerBtn.setChecked(false);
		colorBtn.setVisibility(View.VISIBLE);
		colorMore.setVisibility(View.GONE);
	}
}
