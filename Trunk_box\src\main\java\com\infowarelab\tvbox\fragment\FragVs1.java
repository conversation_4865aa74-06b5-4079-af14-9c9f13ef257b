package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.modle.VideoBean;
import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelab.tvbox.utils.Utils;
import com.infowarelab.tvbox.view.UvcEnDecodeView;
import com.infowarelab.tvbox.view.UvcEnDecodeView1;
import com.infowarelab.tvbox.view.VideoDecodeView;
import com.infowarelabsdk.conference.callback.CallbackManager;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.AudioCommonImpl;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.confctrl.UserCommon;
import com.infowarelabsdk.conference.domain.UserBean;
import com.infowarelabsdk.conference.video.VideoCommon;
import com.mingri.uvc.Uvc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Always on 2018/11/6.
 */
@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
@SuppressLint("ValidFragment")
public class FragVs1 extends BaseFragment {

    private View vsView;
    private RelativeLayout rlRoot, rlEncoder;
    private UvcEnDecodeView localCamera;
    private UvcEnDecodeView1 localCamera1;

    private AudioCommonImpl audioCommon;
    private VideoCommonImpl videoCommon;
    private UserCommonImpl userCommon;
    private ConferenceCommonImpl conferenceCommon;

    private Set<Integer> existingVideos;
    private Set<ImageView> existingDefault;

    private int curMaxVoiceUser = 0;
    private int curSpeakerId = -1;

    private boolean isShare =false;
    //保存异步视频数据
    private List<VideoBean> syncList = new ArrayList<>();
    private List<VideoBean> totalSyncList = new ArrayList<>();
    private List<Integer> uids = new ArrayList<>();
    private int totalNum = 4;
    //轮循路数
    private int loopNum = 0;

    private boolean isSupportSvc = true;
    //保存非主讲模式的路数
    private List<VideoBean> noSpeakerList = new ArrayList<>();
//    private int mSampleRateInHZ = 32000;//采样率
//    //音频采集工具类
//    private AudioRecordUtils audioRecordUtils;

    //与Activity交互
    private FragmentInteraction listterner;
    private int localChannelID = -1;
    private TextView tvMyName = null;
    private int mainChannelId = -1;

    public void setShare(boolean share) {
        isShare = share;
    }

    public FragVs1(ICallParentView iCallParentView) {
        super(iCallParentView);
    }

    public FragVs1(){
        super();
    }
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        vsView = inflater.inflate(R.layout.frag_inconf_video1, container, false);
        initView();
        initData();
        initVideoHandler();
        return vsView;
    }
    private void initView() {

        Log.d("InfowareLab.Debug","FragVs1.initView");

//        audioRecordUtils = new AudioRecordUtils(MediaRecorder.AudioSource.UNPROCESSED);
        if (audioCommon == null){
            audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
        }
        existingVideos = new HashSet<Integer>();
        existingDefault = new HashSet<ImageView>();
        rlRoot = (RelativeLayout) vsView.findViewById(R.id.frag_video_rl_root);
        rlEncoder = (RelativeLayout) vsView.findViewById(R.id.frag_video_rl_camera);
        tvMyName = (TextView) vsView.findViewById(R.id.frag_video_tv_camera);

        localCamera = (UvcEnDecodeView) vsView.findViewById(R.id.frag_video_localCamera);
        localCamera.setCameraLandscape();

        localCamera1 = (UvcEnDecodeView1) vsView.findViewById(R.id.frag_video_localCamera1);
        localCamera1.setCameraLandscape();
    }
    private void initData() {
        videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
        conferenceCommon = (ConferenceCommonImpl) CommonFactory.getInstance().getConferenceCommon();
        if (conferenceCommon != null){
            isSupportSvc = conferenceCommon.isSupportSvc();
        }else {
            isSupportSvc = true;
        }
//        isSupportSvc = false;
        if (videoCommon == null){
            videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        }

        int localWidth = SharedPreferencesUrls.getInstance().getInt("width",1280);
        int localHeight = SharedPreferencesUrls.getInstance().getInt("height",720);

        //boolean remoteEmpty = isRemoteEmpty();

        //videoCommon.setResolution(localWidth,localHeight);

        Log.d("InfowareLab.Debug","FragVs1.local resolution: " + localWidth + "x" + localHeight);

        //Log.d("InfowareLab.Debug","FragVs1.isRemoteEmpty=" + remoteEmpty);

        if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST){

            if (videoCommon.LOCAL_VIDEO_CHANNEL_ID > 0) {
                if (videoCommon.LOCAL_VIDEO_CHANNEL_ID != localChannelID) {
                    localChannelID = videoCommon.LOCAL_VIDEO_CHANNEL_ID;
                }
            }

            if (localChannelID > 0) {
//                if (userCommon.getSelf().getRole() != UserCommon.ROLE_HOST) {
//                    Log.d("InfowareLab.Debug", "FragVs1.openLocalVideo(NOT HOST): " + localChannelID);
//                    openLocalVideo();
//                }
//                else {
                    videoCommon.openVideo(localChannelID, (SurfaceView) null);
                    videoCommon.setSyncVedio(localChannelID, true);
                    Log.d("InfowareLab.Debug", "FragVs1.videoCommon.openVideo(local HOST): " + localChannelID);
                //}
            }
        }

//        if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST){
//            videoCommon.openVideo(SharedPreferencesUrls.getInstance().getInt("localChannelID",0), null);
//            videoCommon.setSyncVedio(SharedPreferencesUrls.getInstance().getInt("localChannelID",0), true);
//        }

        if (tvMyName != null)
            tvMyName.setText(userCommon.getSelf().getUsername());
    }

    private boolean isRemoteEmpty() {
        if (videoCommon == null || videoCommon.getSyncMap() == null){
            return true;
        }
        Map<Integer, Integer> mm = videoCommon.getSyncMap();
        if (mm == null || mm.isEmpty()) {
            return true;
        } else if (mm.size() == 1 && mm.containsValue(userCommon.getOwnID())) {
            return true;
        } else {
            return false;
        }
    }

    byte action = -1;
    //本地视频通道
    boolean isSigle = false;
    public void initVideoHandler() {
        Handler videoHandler = new Handler() {
            @Override
            public void handleMessage(final Message msg) {
                if (getActivity() == null) return;
                super.handleMessage(msg);
                switch (msg.what) {
                    case VideoCommon.VIDEO_ADD_CHANNEL:
                        Log.d("InfowareLab.Debug","FragVs1.VideoCommon.VIDEO_ADD_CHANNEL: " + videoCommon.getSyncMap().size());
                        /*if ( userCommon.getSelf().getRole() == UserCommon.ROLE_HOST || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                            if (videoCommon.getSyncMap().size() == 1)
                            {
                                if (ActConf.mActivity != null){
                                    if (!ActConf.mActivity.isAcLeave){
                                        isSigle = true;
                                        openLocalVideo();
                                        if (ActConf.mActivity.rbMicSet != null){
                                            ActConf.mActivity.rbMicSet.setChecked(true);
                                        }
                                        changeRatio(videoCommon.getSyncMap().size());
                                    }
                                }
                            }
                        }*/

                        callParentView(ACTION_VSSHARED, null);
                        updateVideoShowEnter();
                        if (!isSigle){
//                            changeRatio(videoCommon.getSyncMap().size());
                        }
                        localCamera.AnalyzeEncodeSizeEx();
                        break;
                    case VideoCommon.VIDEO_REMOVE_CHANNEL:
                        Log.d("InfowareLab.Debug","FragVs1.VideoCommon.VIDEO_REMOVE_CHANNEL: " + videoCommon.getSyncMap().size());
                        if (!CallbackManager.IS_LEAVED) {
                            callParentView(ACTION_VSSHARED, null);
                            updateVideoShowEnter();
                            localCamera.AnalyzeEncodeSizeEx();
                        }
                        break;
                    case VideoCommon.VIDEO_LOCAL_CHANNELID:
                        //localChannelID = msg.arg1;
//                      //videoCommon.removeLocalChannel(msg.arg1);
                        Log.d("InfowareLab.Debug", "FragVs1.VideoCommon.VIDEO_LOCAL_CHANNELID");
                        //Log.d("InfowareLab.Debug", "FragVs1.VideoCommon.VIDEO_LOCAL_CHANNELID");
                        /*Log.d("InfowareLab.Debug", "FragVs1.isRemoteEmpty() = "+isRemoteEmpty());*/
                        if (userCommon != null && userCommon.getSelf().getRole() == UserCommon.ROLE_HOST) {
                            if (localChannelID != msg.arg1) {
                                localChannelID = msg.arg1;
                                if (localChannelID > 0) {
                                    videoCommon.openVideo(localChannelID, (SurfaceView)null);
                                    videoCommon.setSyncVedio(localChannelID, true);
                                    Log.d("InfowareLab.Debug", "FragVs1.videoCommon.openVideo(local HOST): " + localChannelID);
                                }
                            }
                        }
                        break;
                    case VideoCommon.VIDEO_REMOVE_DEVICE:
                        break;
                    case VideoCommon.VIDEO_RESET_SIZE:

                        break;
                    case VideoCommon.VIDEO_DECODER_SIZE:
                        int[] param1 = (int[]) msg.obj;
                        int channelid = msg.arg1;
                        VideoDecodeView videoDecoderView = findViewByChannelid(channelid);
                        if (null == videoDecoderView) {
                        } else if (!videoCommon.isHardDecode() && param1[0] * param1[1] > 1024 * 768) {
                            videoDecoderView.showSupport(false);
                        } else {
                            videoDecoderView.resetSize(param1[0], param1[1]);
                            videoDecoderView.showSupport(true);
                        }
                        break;
                    case VideoCommon.VIDEO_LOCAL_CHANNEL:
                        Log.d("InfowareLab.Debug","FragVs1.VideoCommon.VIDEO_LOCAL_CHANNEL: "+((Boolean) msg.obj));
                        if ((Boolean) msg.obj) {
                            if (ActConf.mActivity != null){
                                if (!ActConf.mActivity.isAcLeave){
                                    isSigle = true;
                                    openLocalVideo();
                                    if (ActConf.mActivity.rbMicSet != null){
                                        ActConf.mActivity.rbMicSet.setChecked(true);
                                    }
                                    changeRatio(videoCommon.getSyncMap().size());
                                }
                            }
                        } else{
                            closeLocalVideo();
                            if (ActConf.mActivity != null && ActConf.mActivity.rbMicSet != null){
                                ActConf.mActivity.rbMicSet.setChecked(false);
                            }
                            if (ActConf.isShare){
                                localCamera1.reStartLocalView();
                            }
                        }
                        break;
                    case VideoCommon.VIDEO_KEYFRAME:
                        if (localCamera != null) localCamera.flushEncoder();
                        break;
                    case VideoCommon.VIDEO_LOCAL_RESTART:
                        Log.d("InfowareLab.Debug", "FragVs1.getMessage: VIDEO_LOCAL_RESTART");
                        localCamera.reStartLocalView();
                        if (msg.arg2 > 0){
                            if (240 >= msg.arg2 && 480 > msg.arg2){
                                SharedPreferencesUrls.getInstance().putInt("position",0);
                                SharedPreferencesUrls.getInstance().putInt("width",320);
                                SharedPreferencesUrls.getInstance().putInt("height",240);
                            }else if (480 >= msg.arg2 && 720 > msg.arg2){
                                SharedPreferencesUrls.getInstance().putInt("position",1);
                                SharedPreferencesUrls.getInstance().putInt("width",640);
                                SharedPreferencesUrls.getInstance().putInt("height",480);
                            }else if (720 >= msg.arg2 && 1080 > msg.arg2){
                                SharedPreferencesUrls.getInstance().putInt("position",2);
                                SharedPreferencesUrls.getInstance().putInt("width",1280);
                                SharedPreferencesUrls.getInstance().putInt("height",720);
                            }else if (1080 >= msg.arg2){
                                SharedPreferencesUrls.getInstance().putInt("position",3);
                                SharedPreferencesUrls.getInstance().putInt("width",1920);
                                SharedPreferencesUrls.getInstance().putInt("height",1080);
                            }
                        }
                        break;
                    case VideoCommon.LOCAL_RESOLUTION_CHANGE:
                        Log.d("InfowareLab.Debug", "FragVs1.getMessage: LOCAL_RESOLUTION_CHANGE");

                        localCamera.AnalyzeEncodeSize(msg.arg1, msg.arg2);

//                        localCamera.reStartLocalView();
//                        if (msg.arg2 > 0){
//                            if (240 >= msg.arg2 && 480 > msg.arg2){
//                                SharedPreferencesUrls.getInstance().putInt("position",0);
//                                SharedPreferencesUrls.getInstance().putInt("width",320);
//                                SharedPreferencesUrls.getInstance().putInt("height",240);
//                            }else if (480 >= msg.arg2 && 720 > msg.arg2){
//                                SharedPreferencesUrls.getInstance().putInt("position",1);
//                                SharedPreferencesUrls.getInstance().putInt("width",640);
//                                SharedPreferencesUrls.getInstance().putInt("height",480);
//                            }else if (720 >= msg.arg2 && 1080 > msg.arg2){
//                                SharedPreferencesUrls.getInstance().putInt("position",2);
//                                SharedPreferencesUrls.getInstance().putInt("width",1280);
//                                SharedPreferencesUrls.getInstance().putInt("height",720);
//                            }else if (1080 >= msg.arg2){
//                                SharedPreferencesUrls.getInstance().putInt("position",3);
//                                SharedPreferencesUrls.getInstance().putInt("width",1920);
//                                SharedPreferencesUrls.getInstance().putInt("height",1080);
//                            }
//                        }
                        break;
                    case VideoCommon.VIDEO_READY:
                        VideoDecodeView videoMainView = findViewByChannelid(msg.arg1);
                        if (null != videoMainView) {
                            videoMainView.showSupportReady();
                        }
                        break;
                    case VideoCommon.VIDEO_NOPERMISSION:
                        break;
                    case VideoCommon.VIDEO_PERMISSION:
                        break;
                    case VideoCommon.VIDEO_CHANGE_SVC:
                        break;
                    case VideoCommon.VIDEO_PREVIEW_PRIVILEDGE:
                        break;
                    case VideoCommon.VIDEO_MODE_LAYOUT:
                        Log.d("GLVideoEncodeViewEx","VIDEO_MODE_LAYOUT: n = " + msg.arg1);
//                        Log.e("UUUUUU","88888888888::::"+msg.arg1);
//                        curSpeakerId = msg.arg1;
//                        updateVideoShowEnter();
                        localCamera.AnalyzeEncodeSizeEx();
                        break;
                    case VideoCommon.VIDEO_COUNT_CHANGE:
                        Log.d("GLVideoEncodeViewEx","VIDEO_COUNT_CHANGE: count = " + msg.arg1);
//                        curSpeakerId = msg.arg1;
//                        updateVideoShowEnter();
                        localCamera.AnalyzeEncodeSizeEx();
                        break;
                    case VideoCommon.VIDEO_VIDEO_SINGLE:
                        callParentView(ACTION_VSSHARED, null);
                        updateVideoShowEnter();
                        if ((Boolean)msg.obj){
                            for (Integer key : videoCommon.getSyncMap().keySet()){
                                if (userCommon.getSelf().getUid() == videoCommon.getSyncMap().get(key)){
                                    changeRatio(1);
                                }
                            }
                        }else {
                            changeRatio(videoCommon.getSyncMap().size());
                        }
                        break;
                    //云台控制
                    case VideoCommon.VIDEO_VIDEO_PTZ:
                        for (Integer key : videoCommon.getDeviceMap().keySet()){
                            if (key == msg.arg1){
                                control(msg);
                            }
                        }
                        break;
                    case VideoCommon.VIDEO_LOCAL_RESTART_SWITCHENCODE:
//                        localCamera.switchSoftEncode(msg.arg1 == 1);
//                        localCamera1.switchSoftEncode(msg.arg1 == 1);
                        break;
                    default:
                        break;
                }
            }
        };
        if (videoCommon == null) {
            videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        }
        videoCommon.setHandler(videoHandler);
    }
    //云台控制
    private void control(Message msg){
        if (null != localCamera){
            switch (msg.arg2){
                //向左
                case 1:
                    action = Uvc.PTZ_LEFT;
                    break;
                //向右
                case 2:
                    action = Uvc.PTZ_RIGHT;
                    break;
                //向上
                case 3:
                    action = Uvc.PTZ_UP;
                    break;
                //向下
                case 4:
                    action = Uvc.PTZ_DOWN;
                    break;
                //缩小
                case 5:
                    action = Uvc.ZOOM_DEC;
                    break;
                //放大
                case 6:
                    action = Uvc.ZOOM_ADD;
                    break;
                default:
                    break;
            }
            //开始云台控制
            if (-1 != action){
                localCamera.startPtz(action);
            }
            if (msg.arg2 == 7 || msg.arg2 == 8){
                //停止云台控制
                if (-1 != action){
                    localCamera.stopPtz(action);
                }
            }
        }
    }
    //主讲模式
    public void setSpeak(int curSpeakerId){
        this.curSpeakerId = curSpeakerId;
        updateVideoShowEnter();
    }
    public boolean getMax() {
        if (videoCommon != null && videoCommon.getSingleChannel() > 0) {
            return true;
        } else {
            return false;
        }
    }
    public void retCamera(){
        if (localCamera != null){
            updateVideoShowEnter();
        }
    }

    private void updateVideoShowEnter() {

        if (!isShare){
//            audioRecordUtils.pauseRecord();
            if (audioCommon == null){
                audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
            }
            if (!SharedPreferencesUrls.getInstance().getBoolean("isOpenMic",false)){
                if (audioCommon != null)
                    audioCommon.stopSend();
            }else{
                if (audioCommon != null)
                    audioCommon.startReceive();
                audioCommon.startSend();
            }
            if (localCamera1 != null){
                localCamera1.setParams(1,1);

                if (localCamera1.isSharing()) {
                    localCamera1.setSharing(false);
                    localCamera1.changeStatus(false);
                    if (localCamera.isStarted())
                        localCamera.reStartLocalView();
                }
            }
            if (videoCommon == null){
                videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
            }
            if (videoCommon.getSingleChannel() > 0) {
                if (rlRoot.getWidth() < ConferenceApplication.Screen_W / 2) {
                    callParentView(ACTION_VSSHARED, null);
                    return;
                }
            } else if (isEmpty()) {
                if (rlRoot.getWidth() > 10) {
                    callParentView(ACTION_VSSHARED, null);
                    updateVideoShow(false);
                    return;
                }
            } else {
                if (rlRoot.getWidth() < 10) {
                    callParentView(ACTION_VSSHARED, null);
                    return;
                }
            }
            if (isHidden()) return;
            updateVideoShow(true);
        }else {
            //设置线程优先级，android.os.Process.THREAD_PRIORITY_AUDIO-标准音乐播放使用的线程优先级
            android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_AUDIO);
            if (localCamera1 != null){
                localCamera1.reStartLocalView();
                localCamera1.setStreamVideoSize(SharedPreferencesUrls.getInstance().getInt("width1", 1280),
                        SharedPreferencesUrls.getInstance().getInt("height1", 720));
            }
            if (audioCommon == null){
                audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
            }
            if (!SharedPreferencesUrls.getInstance().getBoolean("isOpenMic",false)){
                if (audioCommon != null)
                    audioCommon.stopSend();
            }else{
                if (audioCommon != null)
                    audioCommon.startReceive();
                audioCommon.startSend();
            }
            //修改分辨率
//            if (localCamera != null){
//                if (Camera.getNumberOfCameras() > 1 && Build.MODEL.indexOf("YT-500") != -1){
//                    localCamera.setStreamVideoSize(640,480);
//                }
//            }
            //开始录制
//            audioRecordUtils.startRecord();
            removeUselessView(videoCommon.getSyncMap());
            removeDefault();
            syncList.clear();
            uids.clear();
            totalSyncList.clear();
            for (Integer key : videoCommon.getSyncMap().keySet()){
                uids.add(videoCommon.getSyncMap().get(key));
                VideoBean bean = new VideoBean();
                bean.setChannelid(key);
                bean.setUid(videoCommon.getSyncMap().get(key));
                totalSyncList.add(bean);
            }
            if (uids.contains(userCommon.getSelf().getUid())){
                totalNum = 3;
            }else {
                if (totalSyncList.size() != 0){
                    totalNum = 4;
                }else {
                    totalNum = 0;
                }
            }
            for (VideoBean bean : totalSyncList){
                if (userCommon.getSelf().getUid() == bean.getUid()){
                    continue;
                }
//                if (syncList.size() > totalNum){
//                    return;
//                }
                syncList.add(bean);
            }
            addVideo();
        }
    }

    private void updateVideoShow(boolean isShow){
        if (!isAdded()) return;
        if (isShow) {
            boolean isLocalSync = false;
            mainChannelId = -1;
            int num = 0;
            boolean existSpeakerId = true;
            boolean isFull = !(rlRoot.getWidth() < ConferenceApplication.Root_W / 2);
            HashMap<Integer, Integer> mm = new HashMap<Integer, Integer>();
            mm.putAll(videoCommon.getSyncMap());
            int mainUid = 0;
            if (videoCommon.getSingleChannel() > 0) {
                int c = videoCommon.getSingleChannel();
                if (!mm.containsKey(c)){
                    SharedPreferencesUrls.getInstance().putBoolean("flag",true);
                    mainChannelId = 0;
                    updateSyncVideos1(mm, false, mainChannelId, true);
                }else if (mm.get(c) == userCommon.getSelf().getUid()){
                    SharedPreferencesUrls.getInstance().putBoolean("flag",false);
                    mainChannelId = 0;
                    mm.clear();
                    updateSyncVideos(mm, false, mainChannelId, true);
                }else {
                    SharedPreferencesUrls.getInstance().putBoolean("flag",false);
                    mainChannelId = c;
                    int uid = mm.get(c);
                    mm.clear();
                    mm.put(c, uid);
                    updateSyncVideos(mm, false, mainChannelId, false);
                }
                return;
            } else if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_PLAIN) {
                mainUid = 0;
                curMaxVoiceUser = 0;
                curSpeakerId = -1;
                SharedPreferencesUrls.getInstance().putInt("curSpeakerId",-1);
            } else if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_VOICE) {
                validateCurMaxVoiceUser();
                mainUid = curMaxVoiceUser;
                curSpeakerId = -1;
                //listterner.setBuJuType(3);
                SharedPreferencesUrls.getInstance().putInt("curSpeakerId",-1);
            } else if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_SPEAKER) {
                curMaxVoiceUser = 0;
                if (curSpeakerId < 0) {
                    if (-1 != SharedPreferencesUrls.getInstance().getInt("curSpeakerId", -1)) {
                        curSpeakerId = SharedPreferencesUrls.getInstance().getInt("curSpeakerId", -1);
                    }
                }
                if (mm.containsKey(curSpeakerId)) {
                    mainUid = mm.get(curSpeakerId);
                    existSpeakerId = true;
                } else {
                    existSpeakerId = false;
                    mainUid = 0;
                    Iterator<Map.Entry<Integer, Integer>> it = mm.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry<Integer, Integer> item = it.next();
                        int cid = item.getKey();
                        int uid = item.getValue();

                        mainUid = uid;
                    }
                }
            }
            if (mm.containsValue(userCommon.getSelf().getUid())) {
                isLocalSync = true;
                num++;
            }
            int limit = 0;
            boolean isSpeakerMode = mainUid != 0 ? true : false;
            if (videoCommon.getSyncMap().size() > 6){
                if (isSpeakerMode){
                    //主讲模式最大8路
                    limit = isFull ? 8 : 4;
                }else {
                    limit = isFull ? 9 : 4;
                }
            }else {
                limit = isFull ? 6 : 4;
            }
            listterner.getLimit1(limit);
            if (mm.containsValue(mainUid)) {
                if (mainUid == userCommon.getOwnID()) {
                    Iterator<Map.Entry<Integer, Integer>> it = mm.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry<Integer, Integer> item = it.next();
                        int cid = item.getKey();
                        int uid = item.getValue();
                        if (uid == userCommon.getOwnID()) {
                            mainChannelId = cid;
                            if (limit < 7){
                                mainChannelId = 0;
                                it.remove();
                            }
                        } else if (num >= limit) {
                            it.remove();
                        } else {
                            num++;
                        }
                    }
                } else {
                    int backChannelId = -1;
                    mainChannelId = -1;

                    Iterator<Map.Entry<Integer, Integer>> it = mm.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry<Integer, Integer> item = it.next();
                        int cid = item.getKey();
                        int uid = item.getValue();
                        if (uid == mainUid) {

                            if (curSpeakerId == cid)
                                mainChannelId = cid;
                            else if (mainChannelId < 0)
                                mainChannelId = cid;
                            else
                                backChannelId = cid;

                            num++;
                        } else if (uid == userCommon.getOwnID()) {
                            if (limit < 7){
                                it.remove();
                            }
                        } else if (num >= (limit - 1) && mainChannelId == -1) {
                            it.remove();
                        } else if (num >= limit && mainChannelId != -1) {
                            it.remove();
                        } else {
                            num++;
                        }
                    }

                    if (mainChannelId < 0) {
                        mainChannelId = backChannelId;
                        if (!existSpeakerId) curSpeakerId = backChannelId;
                    }
                }
            } else {
                mainChannelId = -1;

                Iterator<Map.Entry<Integer, Integer>> it = mm.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<Integer, Integer> item = it.next();
                    int cid = item.getKey();
                    int uid = item.getValue();

                    if (isSpeakerMode) {
                        if (mainChannelId < 0) {
                            curMaxVoiceUser = uid;
                            mainChannelId = cid;
                        }
                    }

                    if (uid == userCommon.getOwnID()) {
                        if (limit < 7){
                            it.remove();
                        }

                    } else if (num >= limit) {
                        it.remove();
                    } else {
                        num++;
                    }
                }
            }
            updateSyncVideos(mm, false, mainChannelId, isLocalSync);
        } else {
            updateSyncVideos(new HashMap<Integer, Integer>(), true, 0, false);
        }
    }

    private void validateCurMaxVoiceUser() {
        if (curMaxVoiceUser > 0 || videoCommon == null) return;

        for (Integer key : videoCommon.getSyncMap().keySet()) {
            curMaxVoiceUser = videoCommon.getSyncMap().get(key);
            return;
        }

        Log.d("InfowareLab.Debug", ">>>>>> fragVs1.validateCurMaxVoiceUser: curMaxVoiceUser = " + curMaxVoiceUser);
    }

    private void updateSyncVideos(Map<Integer, Integer> syncMap, boolean isHidden, int hostChannelId, boolean isLocalSync) {
        int rootW = rlRoot.getWidth();
        removeUselessView(syncMap);
        removeDefault();
        int count = 1;
        int num = syncMap.size() + (isLocalSync ? 1 : 0);
        boolean isFull = !(rootW < ConferenceApplication.Root_W / 2);
        boolean isSpeakerMode = hostChannelId != -1 ? true : false;
        if (num > 6){
            //6路视频以上
            updateVideos(syncMap);
        }else {
            //6路视频以下
            if (num == 0 && isFull) {
                callLocalCamera(1, 1, isFull, isSpeakerMode, true);
                changeRatio(1);
            } else if (hostChannelId > 0) {
                callLocalCamera(num, 2, isFull, isSpeakerMode, isLocalSync);
                if (isLocalSync){
                    changeRatio(num);
                }
            } else {
                callLocalCamera(num, 1, isFull, isSpeakerMode, isLocalSync);
                if (isLocalSync){
                    changeRatio(num);
                }
            }
            for (Integer key : syncMap.keySet()) {
                if (userCommon.getSelf().getUid() == syncMap.get(key)) {
                    continue;
                }
                int uid = syncMap.get(key);
                if (isLocalSync) {
                    if (isSpeakerMode) {
                        if (key == hostChannelId) {
                            addVideo(num, 1, key, uid, isFull, isSpeakerMode);
                        } else if (hostChannelId == 0) {
                            addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                            count++;
                        } else {
                            addVideo(num, count + 2, key, uid, isFull, isSpeakerMode);
                            count++;
                        }
                    } else {
                        addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                        count++;
                    }
                } else {
                    if (isSpeakerMode) {
                        if (key == hostChannelId) {
                            addVideo(num, 1, key, uid, isFull, isSpeakerMode);
                        } else {
                            addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                            count++;
                        }
                    } else {
                        addVideo(num, count, key, uid, isFull, isSpeakerMode);
                        count++;
                    }
                }
            }
            if(num>0){
                addDefault(num,num+1,isFull,isSpeakerMode);
            }
        }
    }
    private void updateSyncVideos1(Map<Integer, Integer> syncMap, boolean isHidden, int hostChannelId, boolean isLocalSync) {
        int rootW = rlRoot.getWidth();

        removeUselessView(syncMap);
        removeDefault();

        int count = 1;
        int num = syncMap.size();
        boolean isFull = !(rootW < ConferenceApplication.Root_W / 2);
        boolean isSpeakerMode = hostChannelId != -1 ? true : false;
        if (num == 0 && isFull) {
            callLocalCamera(1, 1, isFull, isSpeakerMode, true);
        } else if (hostChannelId > 0) {
            callLocalCamera(num, 2, isFull, isSpeakerMode, isLocalSync);
        } else {
            callLocalCamera(num, 1, isFull, isSpeakerMode, isLocalSync);
        }

        for (Integer key : syncMap.keySet()) {
            if (userCommon.getSelf().getUid() == syncMap.get(key)) {
                continue;
            }
            int uid = syncMap.get(key);
            if (isLocalSync) {
                if (isSpeakerMode) {
                    if (key == hostChannelId) {
                        addVideo(num, 1, key, uid, isFull, isSpeakerMode);
                    } else if (hostChannelId == 0) {
                        addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                        count++;
                    } else {
                        addVideo(num, count + 2, key, uid, isFull, isSpeakerMode);
                        count++;
                    }
                } else {
                    addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                    count++;
                }
            } else {
                if (isSpeakerMode) {
                    if (key == hostChannelId) {
                        addVideo(num, 1, key, uid, isFull, isSpeakerMode);
                    } else {
                        addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                        count++;
                    }
                } else {
                    addVideo(num, count, key, uid, isFull, isSpeakerMode);
                    count++;
                }
            }
        }
        if(num>0){
            addDefault(num,num+1,isFull,isSpeakerMode);
        }
    }
    private void callLocalCamera(int total, int count, boolean isFull, boolean isSpeakerMode, boolean isShow) {
        int left = 0, right = 0, top = 0, bottom = 0, w = 0, h = 0;
        //int p = getResources().getDimensionPixelOffset(R.dimen.dp_2);
//        int rootW = rlRoot.getWidth();
//        int rootH = rlRoot.getHeight() +Utils.getNavigationBarHeight(getActivity()) ;
        int rootW = DensityUtil.getWindowWidth(getActivity());
        int rootH = DensityUtil.getWindowHeight(getActivity())+Utils.getNavigationBarHeight(getActivity());
        if (isShow) {
            if (isFull) {
                if (isSpeakerMode) {
                    if (total == 1) {
                        left = 0;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW;
                        h = rootH;
                    } else if (total > 4) {
                        if (count == 1) {
                            left = 0;
                            top = 0;
                            right = 0;
                            bottom = 0;
                            w = rootW * 2 / 3;
                            h = rootH * 2 / 3;
                        } else {
                            left = rootW * 2 / 3 - (count / 5 + count / 6) * rootW / 3;
                            top = ((count - 1) / 3 + (count + 1) / 4) * rootH / 3;
                            right = 0;
                            bottom = 0;
                            w = rootW / 3;
                            h = rootH / 3;
                        }
                    } else {
                        if (count == 1) {
                            left = rootW / 6;
                            top = 0;
                            right = 0;
                            bottom = 0;
                            w = rootW * 2 / 3;
                            h = rootH * 2 / 3;
                        } else {
                            left = (count - 2) * rootW / 3;
                            top = rootH * 2 / 3;
                            right = 0;
                            bottom = 0;
                            w = rootW / 3;
                            h = rootH / 3;
                        }
                    }
                } else {
                    if (total == 1) {
                        left = 0;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW;
                        h = rootH;
                        changeRatio(1);
                    } else if (total == 2) {
                        left = (count - 1) * rootW / 2;
//                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW / 2;
//                        h = rootH;
                        //h = w * 9/15+DensityUtil.dip2px(getActivity(),8);
                        h = w * 9/16;
                        top = (rootH - h)/2;
                    } else if (total == 3) {
                        if (count == 1) {
                            left = rootW / 4;
                            top = 0;
                            right = 0;
                            bottom = 0;
                            w = rootW / 2;
                            h = rootH / 2;
                        } else {
                            left = (count - 2) * rootW / 2;
                            top = rootH / 2;
                            right = 0;
                            bottom = 0;
                            w = rootW / 2;
                            h = rootH / 2;
                        }
                    } else if (total == 4) {
                        left = (count - 1) % 2 * rootW / 2 + ((count - 1) / 4) * rootW;
                        top = ((count - 1) % 4 / 2) * (rootH / 2);
                        right = 0;
                        bottom = 0;
                        w = rootW / 2;
                        h = rootH / 2;
                    } else {
                        left = (count - 1) % 3 * rootW / 3 + ((count - 1) / 6) * rootW;
                        top = rootH / 6 + (count / 4) * rootH / 3;
                        right = 0;
                        bottom = 0;
                        w = rootW / 3;
                        h = rootH / 3;
                    }
                }
            } else {
                left = 0;
                top = (count - 1) * rootH / 4;
                right = 0;
                bottom = 0;
                w = rootW;
                h = rootH / 4;
            }
        } else {
            left = 0;
            top = 0;
            right = 0;
            bottom = 0;
            w = 1;
            h = 1;
        }
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rlEncoder.getLayoutParams();
        params.setMargins(left, top, right, bottom);
        params.width = w;
        params.height = h;
        rlEncoder.setLayoutParams(params);
        localCamera.setParams(w,h);
    }
    //修改分辨率
    public void changeRatio(int total){
        if (userCommon == null || userCommon.getSelf() == null || videoCommon.getSyncMap() == null){
            return;
        }

        for (Integer key : videoCommon.getSyncMap().keySet()){
            Integer value = videoCommon.getSyncMap().get(key);
            if (value == null) continue;
            if (userCommon.getSelf().getUid() == value.intValue()){
                int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
                int height = SharedPreferencesUrls.getInstance().getInt("height", 720);
                switch (total){
                    case 0:
                    case 1:
                        //localCamera.setStreamVideoSize(width,height);
                        break;
                    case 2:
//                        if (width * height >= 1280 * 720){
//                            localCamera.setStreamVideoSize(1280,720);
//                        }
                        break;
                    case 3:
                    case 4:
//                        if (width * height >= 960 * 540){
//                            localCamera.setStreamVideoSize(960,540);
//                        }
                        break;
                    case 5:
                    case 6:
//                        if (key == curSpeakerId){
//                            if (width * height >= 1280 * 720){
//                                localCamera.setStreamVideoSize(1280,720);
//                            }
//                        }else {
//                            if (width * height >= 640 * 360){
//                                localCamera.setStreamVideoSize(640,360);
//                            }
//                        }
//                        if (userCommon.getSelf().getUid() == curMaxVoiceUser){
//                            if (width * height >= 1280 * 720){
//                                localCamera.setStreamVideoSize(1280,720);
//                            }
//                        }else {
//                            if (width * height >= 640 * 360){
//                                localCamera.setStreamVideoSize(640,360);
//                            }
//                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }
    public void changeRatio(int total,int model){
        for (Integer key : videoCommon.getSyncMap().keySet()){
            if (userCommon.getSelf().getUid() == videoCommon.getSyncMap().get(key)){
                int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
                int height = SharedPreferencesUrls.getInstance().getInt("height", 720);
                if (model != ActConf.MODEL_AS_VS && model != ActConf.MODEL_DS_VS){
                    switch (total){
                        case 0:
                        case 1:
                            //localCamera.setStreamVideoSize(width,height);
                            break;
                        case 2:
//                            if (width * height >= 1280 * 720){
//                                localCamera.setStreamVideoSize(1280,720);
//                            }
                            break;
                        case 3:
                        case 4:
//                            if (width * height >= 960 * 540){
//                                localCamera.setStreamVideoSize(960,540);
//                            }
                            break;
                        case 5:
                        case 6:
//                            if (key == curSpeakerId){
//                                if (width * height >= 1280 * 720){
//                                    localCamera.setStreamVideoSize(1280,720);
//                                }
//                            }else {
//                                if (width * height >= 640 * 360){
//                                    localCamera.setStreamVideoSize(640,360);
//                                }
//                            }
//                            if (userCommon.getSelf().getUid() == curMaxVoiceUser){
//                                if (width * height >= 1280 * 720){
//                                    localCamera.setStreamVideoSize(1280,720);
//                                }
//                            }else {
//                                if (width * height >= 640 * 360){
//                                    localCamera.setStreamVideoSize(640,360);
//                                }
//                            }
                            break;
                        default:
                            break;
                    }
                }else {
                    //localCamera.setStreamVideoSize(640,360);
                }
            }
        }
    }
    private void addVideo(int total, int count, int channelid, int uid, boolean isFull, boolean isSpeakerMode) {
        if (count > total) return;
        int p = getResources().getDimensionPixelOffset(R.dimen.dp_2);
//        int rootW = rlRoot.getWidth();
//        int rootH = rlRoot.getHeight() +Utils.getNavigationBarHeight(getActivity()) ;
        int rootW = DensityUtil.getWindowWidth(getActivity());
        int rootH = DensityUtil.getWindowHeight(getActivity())+Utils.getNavigationBarHeight(getActivity());
        int left = 0, right = 0, top = 0, bottom = 0, w = 0, h = 0;
        if (isFull) {
            if (isSpeakerMode) {
                if (total == 1) {
                    left = 0;
                    top = 0;
                    right = 0;
                    bottom = 0;
                    w = rootW;
                    h = rootH;
                } else if (total > 4) {
                    if (count == 1) {
                        left = 0;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW * 2 / 3;
                        h = rootH * 2 / 3;
                    } else {
                        left = rootW * 2 / 3 - (count / 5 + count / 6) * rootW / 3;
                        top = ((count - 1) / 3 + (count + 1) / 4) * rootH / 3;
                        right = 0;
                        bottom = 0;
                        w = rootW / 3;
                        h = rootH / 3;
                    }
                } else {
                    if (count == 1) {
                        left = rootW / 6;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW * 2 / 3;
                        h = rootH * 2 / 3;
                    } else {
                        left = (count - 2) * rootW / 3;
                        top = rootH * 2 / 3;
                        right = 0;
                        bottom = 0;
                        w = rootW / 3;
                        h = rootH / 3;
                    }
                }
            } else {
                if (total == 1) {
                    left = 0;
                    top = 0;
                    right = 0;
                    bottom = 0;
                    w = rootW;
                    h = rootH;
                } else
                if (total == 2) {
                    left = (count - 1) * rootW / 2;
//                    top = 0;
                    right = 0;
                    bottom = 0;
                    w = rootW / 2;
//                    h = rootH;
                    h = w*9/16;
                    top = (rootH - h)/2;//+DensityUtil.dip2px(getActivity(),20);
                } else if (total == 3) {
                    if (count == 1) {
                        left = rootW / 4;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW / 2;
                        h = rootH / 2;
                    } else {
                        left = (count - 2) * rootW / 2;
                        top = rootH / 2;
                        right = 0;
                        bottom = 0;
                        w = rootW / 2;
                        h = rootH / 2;
                    }

                } else if (total == 4) {
                    left = (count - 1) % 2 * rootW / 2 + ((count - 1) / 4) * rootW;
                    top = ((count - 1) % 4 / 2) * (rootH / 2);
                    right = 0;
                    bottom = 0;
                    w = rootW / 2;
                    h = rootH / 2;
                } else {
                    left = (count - 1) % 3 * rootW / 3 + ((count - 1) / 6) * rootW;
                    top = rootH / 6 + (count / 4) * rootH / 3;
                    right = 0;
                    bottom = 0;
                    w = rootW / 3;
                    h = rootH / 3;
                }
            }

        } else {
            left = 0;
            top = (count - 1) * rootH / 4;
            right = 0;
            bottom = 0;
            w = rootW;
            h = rootH / 4;
        }
        UserBean user = userCommon.getUser(uid);
        String name = user.getUsername();
        boolean isHost = false;
        if (user.getRole() == UserCommon.ROLE_SPEAKER || user.getRole() == UserCommon.ROLE_HOST) {
            isHost = true;
        } else {
            isHost = false;
        }
        View v = rlRoot.findViewWithTag(channelid);
        if (v != null) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) v.getLayoutParams();
            if (isFull && !isSpeakerMode && total == 2){
                //lp.setMargins(left, top -h/10, right, h/10);
                lp.setMargins(left, top, right, bottom);
                lp.width = w;
                lp.height = h;// + h/11;
            }else {
                lp.setMargins(left, top, right, bottom);
                lp.width = w;
                lp.height = h;
            }
            v.setLayoutParams(lp);
//            if (isHost) {
//                v.setBackgroundResource(R.drawable.bg_item_video_host);
//            } else {
//                v.setBackgroundResource(R.drawable.bg_item_video_nor);
//            }
            VideoDecodeView video = (VideoDecodeView) v.findViewById(R.id.item_video_video);
            video.setFitsSystemWindows(true);
            video.setClipToPadding(true);
            UserBean bean = userCommon.getUser(uid);
            if (bean.getDevice() == UserCommon.DEVICE_MOBILE){
                video.setMobile(true);
            }else {
                video.setMobile(false);
            }
            video.setCurSVCLvl(lp.width/* - 2 * p*/, lp.height/* - 2 * p*/);
            TextView tv = (TextView) v.findViewById(R.id.item_video_tv);
            if (isHost)
                tv.setText(name + "(主持人)");
            else
                tv.setText(name);
        } else {
            if (isFull && !isSpeakerMode && 2 == total) {
                //h = w*9/15+DensityUtil.dip2px(getContext(),8f);
                h = w*9/16/*+DensityUtil.dip2px(getContext(),8f)*/;
                top = (rootH - h)/2;
            }
            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            lp.setMargins(left, top, right, bottom);
            lp.width = w;
            lp.height = h;
            LayoutInflater inflater3 = LayoutInflater.from(getActivity());
            FrameLayout fl = (FrameLayout) inflater3.inflate(R.layout.item_inconf_video, null);
            fl.setLayoutParams(lp);
            fl.setTag(channelid);

//            if (isHost) {
//                fl.setBackgroundResource(R.drawable.bg_item_video_host);
//            } else {
//                fl.setBackgroundResource(R.drawable.bg_item_video_nor);
//            }

            TextView tv = (TextView) fl.findViewById(R.id.item_video_tv);
            if (isHost)
                tv.setText(name + "(主持人)");
            else
                tv.setText(name);
            rlRoot.addView(fl);
            VideoDecodeView video = (VideoDecodeView) fl.findViewById(R.id.item_video_video);
            video.setFitsSystemWindows(true);
            video.setClipToPadding(true);
            UserBean bean = userCommon.getUser(uid);
            if (bean.getDevice() == UserCommon.DEVICE_MOBILE){
                video.setMobile(true);
            }else {
                video.setMobile(false);
            }
            video.setSvc(isSupportSvc);
            video.changeStatus(channelid, true);
            existingVideos.add(channelid);
        }
    }
    private void addVideo(){
        int p = getResources().getDimensionPixelOffset(R.dimen.dp_2);
//        int w = rlRoot.getWidth();
//        int h = rlRoot.getHeight() +Utils.getNavigationBarHeight(getActivity()) ;
        int w = DensityUtil.getWindowWidth(getActivity())/4;
        int h = (DensityUtil.getWindowHeight(getActivity())+Utils.getNavigationBarHeight(getActivity()))/4;
        int top = 0;
        int left = DensityUtil.getWindowWidth(getActivity()) * 3/4;
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rlEncoder.getLayoutParams();
        RelativeLayout.LayoutParams params1 = (RelativeLayout.LayoutParams)localCamera1.getLayoutParams();
        if (totalSyncList.size() == 0){
            closeLocalVideo();
            params.width = 1;
            params.height = 1;
            rlEncoder.setLayoutParams(params);
            localCamera.setParams(1,1);
            localCamera1.setParams(ConferenceApplication.Root_W,ConferenceApplication.Root_H);
            localCamera1.setSharing(true);
        }else {
            if (3 == totalNum){
                params.width= w;
                params.height = h;
                params.setMargins(left,top,0,0);
                //localCamera.setStreamVideoSize(640,480);
            }else {
                params.width= 0;
                params.height = 0;
                params.setMargins(0,0,0,0);
            }
            params1.width = ConferenceApplication.Root_W * 3/4;
            params1.height = ConferenceApplication.Root_H;
            rlEncoder.setLayoutParams(params);
            localCamera.setParams(params.width,params.height);
            localCamera1.setParams(params1.width,params1.height);
            if (3 == totalNum){
                localCamera.setSharing(true);
            }
            localCamera1.setSharing(true);
        }
        for (int i = 0; i < syncList.size(); i++) {
            VideoBean bean = syncList.get(i);
            UserBean user = userCommon.getUser(bean.getUid());
            String name = user.getUsername();
            if (3 == totalNum){
                top = (i+1)* h;
            }else {
                top = i * h;
            }
            boolean isHost = false;
            if (user.getRole() == UserCommon.ROLE_SPEAKER || user.getRole() == UserCommon.ROLE_HOST) {
                isHost = true;
            } else {
                isHost = false;
            }
            View v = rlRoot.findViewWithTag(bean.getChannelid());
            if (v != null) {
                RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) v.getLayoutParams();
                lp.setMargins(left, top, 0, 0);
                lp.width = w;
                lp.height = h;
                v.setLayoutParams(lp);
//                if (isHost) {
//                    v.setBackgroundResource(R.drawable.bg_item_video_host);
//                } else {
//                    v.setBackgroundResource(R.drawable.bg_item_video_nor);
//                }
                VideoDecodeView video = (VideoDecodeView) v.findViewById(R.id.item_video_video);
                video.setFitsSystemWindows(true);
                video.setClipToPadding(true);
                if (user.getDevice() == UserCommon.DEVICE_MOBILE){
                    video.setMobile(true);
                }else {
                    video.setMobile(false);
                }
                video.setCurSVCLvl(lp.width/* - 2 * p*/, lp.height/* - 2 * p*/);
                video.setFitsSystemWindows(true);
                video.setClipToPadding(true);
                TextView tv = (TextView) v.findViewById(R.id.item_video_tv);
                if (isHost)
                    tv.setText(name + "(主持人)");
                else
                    tv.setText(name);
            } else {
                RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                lp.setMargins(left, top, 0, 0);
                lp.width = w;
                lp.height = h;
                LayoutInflater inflater3 = LayoutInflater.from(getActivity());
                FrameLayout fl = (FrameLayout) inflater3.inflate(R.layout.item_inconf_video, null);
                fl.setLayoutParams(lp);
                fl.setTag(bean.getChannelid());

//                if (isHost) {
//                    fl.setBackgroundResource(R.drawable.bg_item_video_host);
//                } else {
//                    fl.setBackgroundResource(R.drawable.bg_item_video_nor);
//                }

                TextView tv = (TextView) fl.findViewById(R.id.item_video_tv);
                if (isHost)
                    tv.setText(name + "(主持人)");
                else
                    tv.setText(name);
                rlRoot.addView(fl);
                VideoDecodeView video = (VideoDecodeView) fl.findViewById(R.id.item_video_video);
                video.setFitsSystemWindows(true);
                video.setClipToPadding(true);
                if (user.getDevice() == UserCommon.DEVICE_MOBILE){
                    video.setMobile(true);
                }else {
                    video.setMobile(false);
                }
                video.setSvc(isSupportSvc);
                video.changeStatus(bean.getChannelid(), true);
                video.setFitsSystemWindows(true);
                video.setClipToPadding(true);
                existingVideos.add(bean.getChannelid());
            }
        }
        //添加默认图
        if (syncList.size() > 0 || 3 == totalNum){
            for (int i = 0; i < totalNum - syncList.size(); i++){
                if (3 == totalNum){
                    top = (i+syncList.size()+1) * h;
                }else {
                    top = (i+syncList.size()) * h;
                }
                ImageView iv = new ImageView(getActivity());
                RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                lp.setMargins(left, top, 0, 0);
                lp.width = w;
                lp.height = h;
                iv.setLayoutParams(lp);
                iv.setScaleType(ImageView.ScaleType.FIT_XY);
                iv.setImageResource(R.drawable.video_default);
                rlRoot.addView(iv);
                existingDefault.add(iv);
            }
        }
    }
    private void addDefault(int total, int count, boolean isFull,boolean isSpeakerMode) {
        if (isFull) {
            if (total == 1) {
                return;
            }else if(total>4){
                int sum  = 0;
                if (total < 6){
                    sum = (total/6 + 1)*6;
                }
                while (count<=sum){
                    addDefault1(sum,count,isFull,isSpeakerMode);
                    count++;
                }
            }else if(isSpeakerMode){
                int sum = 0;
                if (total < 4){
                    sum = (total/4 + 1)*4;
                }
                while (count<=sum){
                    addDefault1(sum,count,isFull,isSpeakerMode);
                    count++;
                }
            }
        } else {
            int sum = 4;
            while (count<=sum){
                addDefault1(sum,count,isFull,isSpeakerMode);
                count++;
            }
        }
    }

    private void addDefault1(int total, int count, boolean isFull,boolean isSpeakerMode) {
        if(count>total)return;
//        int rootW = rlRoot.getWidth();
//        int rootH = rlRoot.getHeight() +Utils.getNavigationBarHeight(getActivity()) ;
        int rootW = DensityUtil.getWindowWidth(getActivity());
        int rootH = DensityUtil.getWindowHeight(getActivity())+ Utils.getNavigationBarHeight(getActivity());
        int left = 0, right = 0, top = 0, bottom = 0, w = 0, h = 0;
        if (isFull) {
            if(isSpeakerMode){
                if (total == 1) {
                    return;
                } else if (total > 4) {
                    left = rootW * 2 / 3 - (count / 5 + count / 6) * rootW / 3;
                    top = ((count - 1) / 3 + (count + 1) / 4) * rootH / 3;
                    right = 0;
                    bottom = 0;
                    w = rootW / 3;
                    h = rootH / 3;
                } else {
                    left = (count - 2) * rootW / 3;
                    top = rootH * 2 / 3;
                    right = 0;
                    bottom = 0;
                    w = rootW / 3;
                    h = rootH / 3;
                }
            }else{
                if (total == 1) {
                    return;
                } else if (total == 2) {
                    return;
                } else if (total == 3) {
                    return;
                } else if (total == 4) {
                    left = (count - 1) % 2 * rootW / 2 + ((count - 1) / 4) * rootW;
                    top = ((count - 1) % 4 / 2) * (rootH / 2);
                    right = 0;
                    bottom = 0;
                    w = rootW / 2;
                    h = rootH / 2;
                } else {
                    left = (count - 1) % 3 * rootW / 3 + ((count - 1) / 6) * rootW;
                    top = rootH / 6 + (count / 4) * rootH / 3;
                    right = 0;
                    bottom = 0;
                    w = rootW / 3;
                    h = rootH / 3;
                }
            }
        } else {
            left = 0;
            top = (count - 1) * rootH / 4;
            right = 0;
            bottom = 0;
            w = rootW;
            h = rootH / 4;
        }
        ImageView iv = new ImageView(getActivity());
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        lp.setMargins(left, top, right, bottom);
        lp.width = w;
        lp.height = h;
        iv.setLayoutParams(lp);
        iv.setScaleType(ImageView.ScaleType.FIT_XY);
        iv.setImageResource(R.drawable.video_default);
        rlRoot.addView(iv);
        existingDefault.add(iv);
    }

    private void removeDefault() {
        if (existingDefault != null && !existingDefault.isEmpty()) {
            Iterator<ImageView> it = existingDefault.iterator();
            while (it.hasNext()) {
                rlRoot.removeView(it.next());
            }
        }
    }

    private VideoDecodeView findViewByChannelid(int channelid) {
        View v = rlRoot.findViewWithTag(channelid);
        if (v != null) {
            VideoDecodeView video = (VideoDecodeView) v.findViewById(R.id.item_video_video);
            return video;
        } else {
            return null;
        }

    }

    private void removeUselessView(Map<Integer, Integer> mm) {
        if (existingVideos != null && !existingVideos.isEmpty()) {
            Iterator<Integer> it = existingVideos.iterator();
            while (it.hasNext()) {
                int channelid = it.next();
                if (!mm.containsKey(channelid)) {
                    View v = rlRoot.findViewWithTag(channelid);
                    if (v != null) {
                        VideoDecodeView video = (VideoDecodeView) v.findViewById(R.id.item_video_video);
                        video.changeStatus(0, false);
                        rlRoot.removeView(v);
                    }
                    it.remove();
                }
            }
        }
    }

    protected void closeLocalVideo() {
        Log.d("InfowareLab.Debug","FragVs1.closeLocalVideo(setSharing=false)");
        //隐藏本地预览窗口
        localCamera.setSharing(false);
    }

    public void destroyCamera() {
        Log.d("InfowareLab.Debug","FragVs1.destroyCamera(main+second)");
        localCamera.destroyCamera();
        localCamera1.destroyCamera();
    }
    protected void openLocalVideo() {
        Log.d("InfowareLab.Debug","FragVs1.openLocalVideo(main)");
        localCamera.changeStatus(true);
        localCamera.setSharing(true);
    }

    public boolean isEmpty() {
        if (videoCommon == null || videoCommon.getSyncMap() == null){
            return true;
        }
        Map<Integer, Integer> mm = videoCommon.getSyncMap();
        if (mm == null || mm.isEmpty()) {
            return true;
        } else if (mm.size() == 1 && mm.containsValue(userCommon.getOwnID())) {
            return false;
        } else {
            return false;
        }
    }

    public void doSetView() {
        setViewHandler.sendEmptyMessage(0);
    }

    Handler setViewHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {
            Map<Integer, Integer> mm = videoCommon.getSyncMap();
            if (rlRoot.getWidth() < 10 || mm == null || mm.isEmpty()) {
                updateVideoShow(false);
            } else {
                updateVideoShow(true);
            }
        }
    };

    public void preLeave() {
        Log.d("InfowareLab.Debug","FragVs1.preLeave");
        updateVideoShow(false);
        localChannelID = -1;
    }

    public void setMaxVoice(int uid) {
        if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_VOICE
                && curMaxVoiceUser != uid){
            this.curMaxVoiceUser = uid;
            updateVideoShowEnter();
        }
    }
    public void restartCam() {
        if (localCamera != null){
            localCamera.destroyCamera();
            localCamera.startCamera();
        }
//        stopCollect();
    }

    @Override
    public void onDestroyView() {
        localCamera.destroyCamera();
        super.onDestroyView();
    }
//    //停止采集
//    public void stopCollect(){
//        if (audioRecordUtils != null){
//            audioRecordUtils.pauseRecord();
//        }
//    }

    /**
     * 6路视频以上算法
     * <AUTHOR>
     * @param syncMap 同步视频路数
     * */
    private void updateVideos(Map<Integer, Integer> syncMap){
        if (totalSyncList.size() != 0){
            totalSyncList.clear();
        }
        if (noSpeakerList.size() != 0){
            noSpeakerList.clear();
        }
        for (Integer key : syncMap.keySet()){
            VideoBean bean = new VideoBean();
            bean.setChannelid(key);
            bean.setUid(syncMap.get(key));
            totalSyncList.add(bean);
        }
        int column = 3;
        //单元格的宽度
        int cell_width = 0;
        //单元的高度
        int cell_height = 0;
        //同步视频路数
        int size = syncMap.size();
        if (-1 == curSpeakerId && 0 == curMaxVoiceUser){
            /***
             * 非主讲模式
             * 平铺
             * */
            if (size > 6 && size < 10){
                column = 3;
            }else{
                column = 4;
            }
            cell_width = DensityUtil.getWindowWidth(getActivity())/column;
            cell_height = DensityUtil.getWindowHeight(getActivity())/column;
            for (int i = 0; i < size; i++){
                VideoBean bean = totalSyncList.get(i);
                if (userCommon.getSelf().getUid() == bean.getUid()) {
                    //重置分辨率
                    if (size > 6){
                        ;//videoCommon.setResolution(cell_width,cell_height);
                    }
                    //本地视频
                    addLocalVideo(cell_width, cell_height,(i/column) * cell_height,(i % column) * cell_width);
                }else {
                    //远端视频
                    addFarVideo(cell_width, cell_height,(i/column) * cell_height,(i % column) * cell_width,bean);
                }
            }
            //添加默认图
            for (int i = 0; i < column * column - size; i++){
                addImage(cell_width,cell_height,((i+size)/column) * cell_height,((i+size) % column) * cell_width);
            }
        }else {
            /***
             * 主讲模式
             * 非平铺
             * */
            for (VideoBean bean : totalSyncList){
                if (bean.getChannelid() != curSpeakerId && (bean.getUid() != curMaxVoiceUser || bean.getChannelid() != mainChannelId)){
                    noSpeakerList.add(bean);
                }
            }
            if (size < 14){
                column = 4;
            }else {
                column = 5;
            }
            cell_width = DensityUtil.getWindowWidth(getActivity())/column;
            cell_height = DensityUtil.getWindowHeight(getActivity())/column;
            for (int i = 0; i < size; i++){
                VideoBean bean = totalSyncList.get(i);
                if (userCommon.getSelf().getUid() == bean.getUid()){
                    if (curMaxVoiceUser == bean.getUid() || (-1 != curSpeakerId && syncMap.containsKey(curSpeakerId) &&
                            syncMap.get(curSpeakerId) == bean.getUid())){
                        if (size < 9 || size > 13){
                            //重置分辨率
                            if (size > 6){
                                ;//videoCommon.setResolution(3 * cell_width,3 * cell_height);
                            }
                            addLocalVideo(3 * cell_width, 3 * cell_height,0,0);
                        }else{
                            if (size > 6){
                                ;//videoCommon.setResolution(2 * cell_width,2 * cell_height);
                            }
                            addLocalVideo(2 * cell_width, 2 * cell_height,0,0);
                        }
                    }
                }else {
                    if (curMaxVoiceUser == bean.getUid() || (-1 != curSpeakerId && syncMap.containsKey(curSpeakerId) &&
                            syncMap.get(curSpeakerId) == bean.getUid())){
                        if (size < 9 || size > 13){
                            addFarVideo(3 * cell_width, 3 * cell_height,0,0,bean);
                        }else{
                            addFarVideo(2 * cell_width, 2 * cell_height,0,0,bean);
                        }
                    }
                }
            }
            //遍历非主讲人员
            for (int i = 0; i < noSpeakerList.size(); i++){
                VideoBean bean = noSpeakerList.get(i);
                if (column == 4){
                    if (size < 9){
                        //7+1
                        if (i < 3){
                            if (userCommon.getSelf().getUid() == bean.getUid()){
                                if (size > 6){
                                    ;//videoCommon.setResolution(cell_width,cell_height);
                                }
                                addLocalVideo(cell_width,cell_height,(i % column) * cell_height,3 * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,(i % column) * cell_height,3 * cell_width,bean);
                            }
                        }else {
                            if (userCommon.getSelf().getUid() == bean.getUid()){
                                if (size > 6){
                                    ;//videoCommon.setResolution(cell_width,cell_height);
                                }
                                addLocalVideo(cell_width,cell_height,3 * cell_height,(i%column) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,3 * cell_height,(i%column) * cell_width,bean);
                            }
                        }
                    }else {
                        //12+1
                        if (i < 4){
                            if (i < 2){
                                if (userCommon.getSelf().getUid() == bean.getUid()){
                                    if (size > 6){
                                        ;//videoCommon.setResolution(cell_width,cell_height);
                                    }
                                    addLocalVideo(cell_width,cell_height,0,((i % column)+2) * cell_width);
                                }else {
                                    addFarVideo(cell_width,cell_height,0,((i % column)+2) * cell_width,bean);
                                }
                            }else {
                                if (userCommon.getSelf().getUid() == bean.getUid()){
                                    if (size > 6){
                                        ;//videoCommon.setResolution(cell_width,cell_height);
                                    }
                                    addLocalVideo(cell_width,cell_height,cell_height,(i % column) * cell_width);
                                }else {
                                    addFarVideo(cell_width,cell_height,cell_height,(i % column) * cell_width,bean);
                                }
                            }
                        }else {
                            if (userCommon.getSelf().getUid() == bean.getUid()){
                                if (size > 6){
                                    ;//videoCommon.setResolution(cell_width,cell_height);
                                }
                                addLocalVideo(cell_width,cell_height,(i/column+1) * cell_height,(i % column) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,(i/column+1) * cell_height,(i % column) * cell_width,bean);
                            }
                        }
                    }
                }else {
                    //16+1
                    if (i < 6){
                        if (i < 2){
                            if (userCommon.getSelf().getUid() == bean.getUid()){
                                if (size > 6){
                                    ;//videoCommon.setResolution(cell_width,cell_height);
                                }
                                addLocalVideo(cell_width,cell_height,0,((i % column)+3) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,0,((i % column)+3) * cell_width,bean);
                            }
                        }else if (i < 4){
                            if (userCommon.getSelf().getUid() == bean.getUid()){
                                if (size > 6){
                                    ;//videoCommon.setResolution(cell_width,cell_height);
                                }
                                addLocalVideo(cell_width,cell_height,cell_height,((i % column)+3) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,cell_height,((i % column)+3) * cell_width,bean);
                            }
                        }else {
                            if (userCommon.getSelf().getUid() == bean.getUid()){
                                if (size > 6){
                                    ;//videoCommon.setResolution(cell_width,cell_height);
                                }
                                addLocalVideo(cell_width,cell_height,0,((i % column)+3) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,0,((i % column)+3) * cell_width,bean);
                            }
                        }
                    }else {
                        if (userCommon.getSelf().getUid() == bean.getUid()){
                            if (size > 6){
                                ;//videoCommon.setResolution(cell_width,cell_height);
                            }
                            addLocalVideo(cell_width,cell_height,(i/column+2) * cell_height,(i % column - 1) * cell_width);
                        }else {
                            addFarVideo(cell_width,cell_height,(i/column+2) * cell_height,(i % column - 1) * cell_width,bean);
                        }
                    }
                }
            }
            //添加默认图
            if (size < 9){
                int cellNum = column * column - 9 - noSpeakerList.size();
                for (int i = 0; i < cellNum; i++){
                    addImage(cell_width,cell_height,(column -1) * cell_height,((i+size-1)%column) * cell_width);
                }
            }else if (size > 9 && size < 13){
                //剩余的单元格
                int cellNum = column * column - 4 - noSpeakerList.size();
                for (int i = 0; i < cellNum; i++){
                    addImage(cell_width,cell_height,(column -1) * cell_height,((i+size-1) % column) * cell_width);
                }
            }else if (size > 13 && size < 17){
                int cellNum = column * column - 9 - noSpeakerList.size();
                for (int i = 0; i < cellNum; i++){
                    addImage(cell_width,cell_height,(column -1) * cell_height,((i+size-1) % column -1) * cell_width);
                }
            }
        }
    }
    //修改本地视频
    private void addLocalVideo(int w, int h, int top, int left){
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rlEncoder.getLayoutParams();
        params.setMargins(left, top, 0, 0);
        params.width = w;
        params.height = h;
        rlEncoder.setLayoutParams(params);
        localCamera.setParams(w,h);
//        localCamera.reStartLocalView();
    }
    //添加远端视频
    private void addFarVideo(int w, int h, int top, int left,VideoBean bean){
        //int p = getResources().getDimensionPixelOffset(R.dimen.dp_2);
        UserBean user = userCommon.getUser(bean.getUid());
        String name = user.getUsername();
        View v = rlRoot.findViewWithTag(bean.getChannelid());

        boolean isHost = false;
        if (user.getRole() == UserCommon.ROLE_SPEAKER || user.getRole() == UserCommon.ROLE_HOST) {
            isHost = true;
        } else {
            isHost = false;
        }

        if (v != null) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) v.getLayoutParams();
            lp.setMargins(left, top, 0, 0);
            lp.width = w;
            lp.height = h;
            v.setLayoutParams(lp);
            VideoDecodeView video = (VideoDecodeView) v.findViewById(R.id.item_video_video);

            if (user.getDevice() == UserCommon.DEVICE_MOBILE){
                video.setMobile(true);
            }else {
                video.setMobile(false);
            }

            video.setCurSVCLvl(lp.width/* - 2 * p*/, lp.height/* - 2 * p*/);
            TextView tv = (TextView) v.findViewById(R.id.item_video_tv);
            if (isHost)
                tv.setText(name + "(主持人)");
            else
                tv.setText(name);
        } else {
            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            lp.setMargins(left, top, 0, 0);
            lp.width = w;
            lp.height = h;
            LayoutInflater inflater3 = LayoutInflater.from(getActivity());
            FrameLayout fl = (FrameLayout) inflater3.inflate(R.layout.item_inconf_video, null);
            fl.setLayoutParams(lp);
            fl.setTag(bean.getChannelid());
            TextView tv = (TextView) fl.findViewById(R.id.item_video_tv);
            if (!isHost)
                tv.setText(name);
            else
                tv.setText(name + "(主持人)");
            rlRoot.addView(fl);
            VideoDecodeView video = (VideoDecodeView) fl.findViewById(R.id.item_video_video);

            if (user.getDevice() == UserCommon.DEVICE_MOBILE){
                video.setMobile(true);
            }else {
                video.setMobile(false);
            }

            video.setSvc(isSupportSvc);
            video.changeStatus(bean.getChannelid(), true);
            existingVideos.add(bean.getChannelid());
        }
    }
    //添加默认图
    private void addImage(int w, int h, int top, int left){

        ImageView iv = new ImageView(getActivity());
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        lp.setMargins(left, top, 0, 0);
        lp.width = w;
        lp.height = h;
        iv.setLayoutParams(lp);
        iv.setScaleType(ImageView.ScaleType.FIT_XY);
        iv.setImageResource(R.drawable.video_default);
        rlRoot.addView(iv);
        existingDefault.add(iv);
    }

    /**
     * 定义了所有activity必须实现的接口
     * 布局切换
     */
    public interface FragmentInteraction {
        /**
         * Fragment 向Activity传递指令，这个方法可以根据需求来定义
         *
         */
        void getLimit1(int limit);
    }

    /**
     * 当FRagmen被加载到activity的时候会被回调
     *
     * @param activity
     */
    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity instanceof FragMenu.FragmentInteraction) {
            listterner = (FragmentInteraction) activity;
        } else {
            throw new IllegalArgumentException("activity must implements FragmentInteraction");
        }
    }
    //隐藏本地预览窗口
    public void setCameraSize(){
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rlEncoder.getLayoutParams();
        params.setMargins(0, 0, 0, 0);
        params.width = 1;
        params.height = 1;
        rlEncoder.setLayoutParams(params);
        localCamera.setParams(1,1);
    }

    //恢复分辨率
    public void recoverRatio(){
//        if (localCamera != null){
//            //恢复分辨率
//            localCamera.setStreamVideoSize(SharedPreferencesUrls.getInstance().getInt("width",1280),
//                    SharedPreferencesUrls.getInstance().getInt("height",720));
//        }
    }

    public void onSelfRoleChange(int newRole) {

        Log.d("GLVideoEncodeViewEx","FragVs onSelfRoleChange: " + newRole);
        if (localCamera != null) localCamera.AnalyzeEncodeSizeEx();
    }
}