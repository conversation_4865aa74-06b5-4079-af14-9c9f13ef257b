package com.infowarelab.tvbox.utils;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Paint;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;

import java.lang.reflect.Method;

/**
 * dp、sp 转换为 px 的工具类
 *
 * <AUTHOR> 2012.11.12
 */
public class DisplayUtil {

    private static final String TAG = DisplayUtil.class.getSimpleName();

      /**
         * 禁用7.0（23）以上显示大小改变和文字大小
         */
      public static Resources disabledDisplayDpiChange(Resources res) {
          Configuration newConfig = res.getConfiguration();
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
              //字体非默认值
              if (res.getConfiguration().fontScale != 1) {
                  newConfig.fontScale = 1;
              }
              newConfig.densityDpi = getDefaultDisplayDensity();
              res.updateConfiguration(newConfig, res.getDisplayMetrics());
          } else {
              //字体非默认值
              if (res.getConfiguration().fontScale != 1) {
                  newConfig.fontScale = 1;//设置默认
                  res.updateConfiguration(newConfig, res.getDisplayMetrics());
              }
          }
          return res;
      }

    /**
     * 系统设置"显示大小"时原有UI样式保持不变：
     * <p>
     * 1、当调节手机系统"显示大小"【调大】的时候，相应的dpi会变大【dp = (dpi/160) * px】,此时dp就会变大，所以相应的UI布局就会变大。
     * 2、当调节手机系统"分辨率"【调小】的时候，相应的dpi会变小【比如由480-->320】。如果此时使用技术手段使dpi保持不变，那么相同的dp就会占用更多的px，所以UI布局就会变大。
     *
     * @param context
     */
    public static void setDefaultDisplay(Context context) {

        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M){
            Configuration origConfig = context.getResources().getConfiguration();

            //获取手机出厂时默认的densityDpi【注释1】
            origConfig.densityDpi = getDefaultDisplayDensity();

            Log.d(TAG, "densityDpi: " + origConfig.densityDpi);
            context.getResources().updateConfiguration(origConfig, context.getResources().getDisplayMetrics());
        }

    }

    public static int getDefaultDisplayDensity() {
        try {
            Class clazz = Class.forName("android.view.WindowManagerGlobal");
            Method method = clazz.getMethod("getWindowManagerService");
            method.setAccessible(true);
            Object iwm = method.invoke(clazz);
            Method getInitialDisplayDensity = iwm.getClass().getMethod("getInitialDisplayDensity", int.class);
            getInitialDisplayDensity.setAccessible(true);
            Object densityDpi = getInitialDisplayDensity.invoke(iwm, Display.DEFAULT_DISPLAY);
            return (int) densityDpi;
        }
        catch(Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    public static void setDefaultDisplayEx(Context context) {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
            Resources res = context.getResources();
            Configuration configuration = res.getConfiguration();
            if (res.getDisplayMetrics().densityDpi != DisplayMetrics.DENSITY_DEVICE_STABLE) {
                configuration.densityDpi = DisplayMetrics.DENSITY_DEVICE_STABLE;
                res.updateConfiguration(configuration, res.getDisplayMetrics());
            }
        }
    }

    /**
     * 将px值转换为dip或dp值，保证尺寸大小不变
     *
     * @param pxValue
     * @param scale   （DisplayMetrics类中属性density）
     * @return
     */
    public static int px2dip(Context context, float pxValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }

    /**
     * 将dip或dp值转换为px值，保证尺寸大小不变
     *
     * @param dipValue
     * @param scale    （DisplayMetrics类中属性density）
     * @return
     */
    public static int dip2px(Context context, float dipValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dipValue * scale + 0.5f);
    }

    /**
     * 将px值转换为sp值，保证文字大小不变
     *
     * @param pxValue
     * @param fontScale （DisplayMetrics类中属性scaledDensity）
     * @return
     */
    public static int px2sp(Context context, float pxValue) {
        final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
        return (int) (pxValue / fontScale + 0.5f);
    }

    /**
     * 将sp值转换为px值，保证文字大小不变
     *
     * @param spValue
     * @param fontScale （DisplayMetrics类中属性scaledDensity）
     * @return
     */
    public static int sp2px(Context context, float spValue) {
        final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
        return (int) (spValue * fontScale + 0.5f);
    }

    public static int getTextViewHeight(float fontSize) {
        Paint paint = new Paint();
        paint.setTextSize(fontSize);
        Paint.FontMetrics fm = paint.getFontMetrics();
        return (int) Math.ceil(fm.bottom - fm.top);
    }

}