package com.infowarelab.tvbox.render;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.ImageFormat;
import android.graphics.SurfaceTexture;
import android.hardware.Camera;
import android.opengl.EGL14;
import android.opengl.GLES20;
import android.opengl.GLSurfaceView;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.widget.RelativeLayout;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelabsdk.conference.callback.CallbackManager;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.video.AvcHardEncoder;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.List;

import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;


public class GLVideoEncodeView extends GLSurfaceView implements Camera.PreviewCallback {

    private static final String TAG = "InfowareLab.GLVideoEncodeView";
    private boolean inited = false;
    public GLVideoEncodeView.GLRenderer renderer = null;
    public BaseFilter mCurrentFilter = null;
    //private HWEncoderWrapper hwEncoderWrapper = null;
    private HWEncoderWrapperEx hwEncoderWrapperEx = null;
    private int mTextureId;
    private SurfaceTexture mSurfaceTexture = null;
    private float[] mSTMatrix = new float[16];
    private boolean mRenderEnabled = true;
    private  FilterFactory.FilterType type;

    public static int cameraFPS = 30;
    public static int cameraWidth = 640;
    public static int cameraHeight = 480;

    public static int encodeWidth = 640;
    public static int encodeHeight = 360;

    private Camera camera;
    private int currentCamera = Camera.CameraInfo.CAMERA_FACING_FRONT;// 代表摄像头的方位，目前有定义值两个分别为CAMERA_FACING_FRONT前置和CAMERA_FACING_BACK后置
    private int numOfCamera = -1;
    File file = new File("/sdcard/testView.yuv");
    private FileOutputStream fos;
    // private Handler handler;
    private static boolean initVideo = true;
    private static boolean isSharing = false;
    private boolean isPortrait = false;
    private int frameSize;
    private int[] rgba;
    private VideoCommonImpl videoCommon = (VideoCommonImpl) CommonFactory
            .getInstance().getVideoCommon();
    private boolean isPreview = false;
    private boolean isOpen = false;

    public static boolean isDestroyed = false;
    public static boolean isInited = false;

    // Video Hard Encoder
    private AvcHardEncoder h264HwEncoderImpl = null;
    private static final int FRAME_RATE = 25;
    private boolean isHardCodec = true; // true
    private boolean isWriteFile = false;
    private boolean isEnabled = true;

    byte[] yv12buf;

    private int degrees = 90;
    public boolean isSmall = false;

    private File _fr = null;
    private FileOutputStream _out = null;
    private Context activity;
    private int mFrameLength = 0;
    private static int STATE_ON = 1;
    private static int STATE_OFF = 2;
    private int mEncoderState = 2;
    private int mBitRate = 1000000;
    private boolean isFlow = false;

    private int mViewWidth = 0;
    private int mViewHeight = 0;

    private int mSurfaceWidth = 0;
    private int mSurfaceHeight = 0;

    private boolean isCreated = false;
    private boolean mMirror = false;
    private boolean needToInitMyVideo = true;

    private long lastCheckTime = -1;
    private int realFrameRate = 0;
    private boolean needToChangeEncodeSize = false;

    private UserCommonImpl userCommon;
    private int maxWidthForJoin = 3840;//1280;
    private int maxHeightForJoin = 2160;//720
    private boolean resolutionSpecified = false;

    private boolean encodeByCallback = false;

    public GLVideoEncodeView(Context context) {
        super(context);
        activity = context;
        init();

    }

    public GLVideoEncodeView(Context context, AttributeSet attrs) {
        super(context, attrs);
        activity = context;
        init();
    }

    public void init() {

        if (inited) return;

        setEGLContextClientVersion(2);

        type = FilterFactory.FilterType.Original;
        renderer = new GLVideoEncodeView.GLRenderer(this, type);

        setRenderer(renderer);
        setRenderMode(RENDERMODE_WHEN_DIRTY);

        userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();

        inited = true;
    }

    public boolean rendererCreated(){return renderer != null;}
    public boolean rendererEnabled(){return mRenderEnabled;}
    public void setRenderEnabled(boolean renderEnabled) {
        Log.d(TAG, "GLVideoEncoderView.setRenderEnabled: "+ renderEnabled);
        mRenderEnabled = renderEnabled;
    }

    private void checkOpenGLCreated(int width, int height) {

        if (!isCreated){

            mCurrentFilter.createProgram();
            mTextureId = BaseFilter.bindTexture();
            mSurfaceTexture = new SurfaceTexture(mTextureId);
            mSurfaceTexture.setOnFrameAvailableListener(renderer);

            if (camera != null && mSurfaceTexture != null && isEnabled) {
                try {
                    Log.d(TAG,"GLVideoEncodeView.camera.setPreviewTexture");
                    camera.setPreviewTexture(mSurfaceTexture);
                } catch (IOException e) {
                    Log.e(TAG, e.getMessage());
                    e.printStackTrace();
                }

                if (!isPreviewStarted() && isEnabled()){
                    Log.d(TAG, "GLVideoEncodeView.startPreview in onSurfaceChanged");
                    changePreview(true);
                }
            }
            isCreated = true;
        }

    }

    public class GLRenderer implements Renderer, SurfaceTexture.OnFrameAvailableListener {

        GLSurfaceView surfaceView;

        public GLRenderer(GLSurfaceView surfaceView, FilterFactory.FilterType type) {

            this.surfaceView = surfaceView;

            if (encodeByCallback){
                if (null == h264HwEncoderImpl)
                    h264HwEncoderImpl = new AvcHardEncoder();
            }
            else {
                if (null == hwEncoderWrapperEx) {
                    hwEncoderWrapperEx = new HWEncoderWrapperEx(surfaceView.getContext());
                }
            }

            mCurrentFilter = FilterFactory.createFilter(activity,type);
        }

        @Override
        public void onSurfaceCreated(GL10 gl, EGLConfig config) {

            Log.d(TAG,"GLVideoEncodeView.onSurfaceCreated");

        }

        @Override
        public void onSurfaceChanged(GL10 gl, int width, int height) {

            Log.d(TAG,"GLVideoEncodeView.onSurfaceChanged: " + width + "x" + height);

            mSurfaceWidth = width;
            mSurfaceHeight = height;

            if (!resolutionSpecified) {
                AnalyzeEncodeSize();
                resolutionSpecified = false;
            }

            if (width <= 1 || height <= 1) {
                return;
            }

            checkOpenGLCreated(width, height);

            if (isCreated)
                GLES20.glViewport(0, 0, width, height);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                mCurrentFilter.onInputSizeChanged(width,height);
            }

        }

        /**
         * 关于预览出现镜像，旋转等问题，有两种方案:
         * 1.在相机预览的地方进行调整
         * 2.通过opengl的矩阵变换在绘制的时候进行调整
         * 这里我采用了前者
         */

        @Override
        public void onDrawFrame(GL10 gl) {

            //if (mSurfaceWidth <= 1 || mSurfaceHeight <= 1) return;

            //if (mSurfaceTexture == null) return;

            //if (!isPreviewStarted()) return;

            checkOpenGLCreated(mSurfaceWidth, mSurfaceHeight);

            //Log.d(TAG,"GLVideoEncodeView.onDrawFrame: isSharing=" + isSharing);

            if (isSharing && !encodeByCallback) {

                if (mEncoderState == STATE_OFF && !hwEncoderWrapperEx.isRecording()) {

                    //Log.d(TAG,">>>>> Start HW encoder wrapper...");

                    mEncoderState = STATE_ON;

                    if (needToInitMyVideo){

                        Log.d(TAG,">>>>> initializeMyVideo: " + encodeWidth + "x" + encodeHeight);

                        videoCommon.initializeMyVideo(encodeWidth, encodeHeight, cameraFPS);
                        needToInitMyVideo = false;
                    }

                    AnalyzeBitRateByEncodeSize();

                    if (!resolutionSpecified)
                        AnalyzeEncodeSize();

                    resolutionSpecified = false;

                    Log.d(TAG,">>>>> start hwEncoderWrapper: " + encodeWidth + "x" + encodeHeight);
                    Log.d(TAG,">>>>> mBitRate: " + mBitRate);
                    Log.d(TAG,">>>>> cameraFPS: " + cameraFPS);

//                    hwEncoderWrapper.start(
//                            videoCommon,
//                            encodeWidth,
//                            encodeHeight,
//                            mBitRate,
//                            cameraFPS,
//                            EGL14.eglGetCurrentContext());

                    hwEncoderWrapperEx.startRecording(new HWEncoderWrapperEx.EncoderConfig(
                            videoCommon, null, encodeWidth, encodeHeight,
                            mBitRate, cameraFPS, EGL14.eglGetCurrentContext()
                    ));


                }
                else if (needToChangeEncodeSize){
                    needToChangeEncodeSize = false;
                    needToInitMyVideo = true;

//                    if (mEncoderState == STATE_ON) {
//                        Log.d(TAG,">>>>> Stop HW encoder wrapper...");
//                        mEncoderState = STATE_OFF;
//                        hwEncoderWrapper.stop();
//                    }

                    if (hwEncoderWrapperEx!=null&&mEncoderState==STATE_ON&&hwEncoderWrapperEx.isRecording()){
                        Log.d(TAG,">>>>> Stop HW encoder wrapper...");
                        hwEncoderWrapperEx.stopRecording();
                        mEncoderState = STATE_OFF;
                    }

//                    Log.d(TAG,">>>>> initializeMyVideo: " + encodeWidth + "x" + encodeHeight);
//                    videoCommon.initializeMyVideo(encodeWidth, encodeHeight, cameraFPS);
//
//                    hwEncoderWrapper.changeResolution(encodeWidth, encodeHeight);

                }

            }
            else {

//                if (mEncoderState == STATE_ON) {
//                    Log.d(TAG,">>>>> Stop HW encoder wrapper...");
//                    mEncoderState = STATE_OFF;
//                    hwEncoderWrapper.stop();
//                }
                if (!encodeByCallback && hwEncoderWrapperEx!=null&&mEncoderState==STATE_ON){
                    Log.d(TAG,">>>>> Stop HW encoder wrapper...");
                    hwEncoderWrapperEx.stopRecording();
                    mEncoderState = STATE_OFF;
                }
            }

            //if (!mRenderEnabled) return;

            mSurfaceTexture.updateTexImage();

            if (!encodeByCallback && isSharing && mEncoderState == STATE_ON){
                hwEncoderWrapperEx.setMirror(mMirror);
                hwEncoderWrapperEx.setTextureId(mTextureId);
                hwEncoderWrapperEx.frameAvailable(mSurfaceTexture);
            }

            //hwEncoderWrapperEx.onFrameAvailable(mTextureId, mSurfaceTexture, mMirror);

            mSurfaceTexture.getTransformMatrix(mSTMatrix);

            mCurrentFilter.draw(mTextureId,mSTMatrix);

            /*if (lastCheckTime < 0) {
                lastCheckTime = System.currentTimeMillis();
                realFrameRate = 0;
                return;
            }
            else
            {
                if (System.currentTimeMillis() - lastCheckTime < 1000){
                    realFrameRate++;
                }
                else
                {
                    realFrameRate++;
                    Log.d(TAG,">>>>> Camera Real Frame Rate = " + realFrameRate);
                    realFrameRate = 0;
                    lastCheckTime = System.currentTimeMillis();
                }
            }*/

        }

        @Override
        public void onFrameAvailable(SurfaceTexture surfaceTexture) {
            //Log.d(TAG,"GLVideoEncodeView.onFrameAvailable");
            surfaceView.requestRender();

        }

    }

    public void AnalyzeEncodeSize() {

        int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
        int height = SharedPreferencesUrls.getInstance().getInt("height", 720);

        Log.d(TAG, "AnalyzeEncodeSize: setting resolution = " + width + "x" + height);

        float ratio = (float) mSurfaceWidth / (float) ConferenceApplication.SCREEN_WIDTH;
        int resultWidth = width;
        int resultHeight = height;

        Log.d(TAG, "AnalyzeEncodeSize: ratio = " + ratio);

        if (mSurfaceWidth <= 1 || mSurfaceHeight <= 1) {
            if (videoCommon.isSpeakerMyself()) {

                Log.d(TAG, "AnalyzeEncodeSize: isSpeakerMyself = " + videoCommon.isSpeakerMyself());

                resultWidth = width;
                resultHeight = height;

                if (resultWidth > 1280 || resultHeight > 720) {
                    if (userCommon != null) {
                        if (!userCommon.isHost()) {
                            Log.d(TAG, "AnalyzeEncodeSize: Limited as NOT host and speaker");
                            resultWidth = maxWidthForJoin;
                            resultHeight = maxHeightForJoin;
                        }
                    }
                }
            }
            else if (userCommon != null)
            {
                int count = userCommon.getUserNumber();

                Log.d(TAG, "AnalyzeEncodeSize: user count = " + count);

                if (count <= 9){
                    resultWidth = 640;
                    resultHeight = 360;
                }
                else{
                    resultWidth = 320;
                    resultHeight = 180;
                }
            }

        } else {
            if (ratio > 0.5){
                resultWidth = width;
                resultHeight = height;

                if (resultWidth > 1280 || resultHeight > 720) {
                    if (userCommon != null) {
                        if (!userCommon.isHost()) {
                            Log.d(TAG, "AnalyzeEncodeSize: Limited as NOT host and speaker");
                            resultWidth = maxWidthForJoin;
                            resultHeight = maxHeightForJoin;
                        }
                    }
                }
            }
            else if (ratio <= 0.5 && ratio > 0.34){
                resultWidth = 640;
                resultHeight = 360;
            }
            else if (ratio <= 0.34){
                resultWidth = 320;
                resultHeight = 180;
            }
        }

        if (resultWidth > width || resultHeight > height){
            resultWidth = width;
            resultHeight = height;

            Log.d(TAG, "AnalyzeEncodeSize: adjusted to: " + width + "x" + height);
        }

        Log.d(TAG, "AnalyzeEncodeSize: result size =" + resultWidth + "x" + resultHeight);

        if (encodeWidth != resultWidth || encodeHeight != resultHeight){

            encodeWidth = resultWidth;
            encodeHeight = resultHeight;

            if (mEncoderState == STATE_ON)
                needToChangeEncodeSize = true;
        }
    }

    public void AnalyzeEncodeSize(int width, int height) {

        int resultWidth = width;
        int resultHeight = height;

        Log.d(TAG, "AnalyzeEncodeSize（2）: width = " + width + "; height = " + height);

        if (resultWidth > 1280 || resultHeight > 720) {
            if (userCommon != null) {
                if (!userCommon.isHost()) {
                    Log.d(TAG, "AnalyzeEncodeSize: Limited as NOT host and speaker");
                    resultWidth = maxWidthForJoin;
                    resultHeight = maxHeightForJoin;
                }
            }
        }

        Log.d(TAG, "AnalyzeEncodeSize(2): result size =" + resultWidth + "x" + resultHeight);

        if (encodeWidth != resultWidth || encodeHeight != resultHeight){

            encodeWidth = resultWidth;
            encodeHeight = resultHeight;
            resolutionSpecified = true;

            if (mEncoderState == STATE_ON)
                needToChangeEncodeSize = true;
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void updateFilter(final FilterFactory.FilterType type){

        this.type = type;

        //renderer.runOnDraw(() -> {


            mCurrentFilter.releaseProgram();
            mCurrentFilter = FilterFactory.createFilter(activity,type);

            //调整预览画面
            mCurrentFilter.createProgram();
            mCurrentFilter.onInputSizeChanged(getWidth(),getHeight());
            //调整录像画面
            //hwRecorderWrapper.updateFilter(type);

            //Log.v("aaaaa","updateFilter:"+ Thread.currentThread());

        //});

    }

    public void enableBeauty(boolean enableBeauty){

        if (enableBeauty){

            type = FilterFactory.FilterType.Beauty;

        }else{

            type = FilterFactory.FilterType.Original;
        }

        updateFilter(type);
    }

    public void enableCamera(boolean enabled){isEnabled=enabled;}
    public boolean isEnabled(){return isEnabled;}

    /**
     * 设置相机显示方向的详细解读
     **/
    public void setCameraDisplayOrientation(Activity activity,
                                            int cameraId, android.hardware.Camera camera) {
        // 1.获取屏幕切换角度值。
        int rotation = activity.getWindowManager().getDefaultDisplay()
                .getRotation();

        int degree = 0;
        switch (rotation) {
            case Surface.ROTATION_0: degree = 0; break;
            case Surface.ROTATION_90: degree = 90; break;
            case Surface.ROTATION_180: degree = 180; break;
            case Surface.ROTATION_270: degree = 270; break;
        }
        // 2.获取摄像头方向。
        android.hardware.Camera.CameraInfo info =
                new android.hardware.Camera.CameraInfo();
        android.hardware.Camera.getCameraInfo(cameraId, info);
        // 3.设置相机显示方向。
        int result;
        if (info.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {

            Log.d(TAG, "Camera.CameraInfo.CAMERA_FACING_FRONT: info.orientation=" + info.orientation);
            result = (info.orientation + degree) % 360;
            result = (360 - result) % 360;  // compensate the mirror
        } else {  // back-facing
            Log.d(TAG, "Camera.CameraInfo.CAMERA_FACING_BACK: info.orientation=" + info.orientation);
            result = (info.orientation - degree + 360) % 360;
        }

        degrees = result;

        Log.d(TAG, "setCameraDisplayOrientation: degrees=" + result);

        camera.setDisplayOrientation(result);
    }

    @SuppressLint("NewApi")
    public void startCamera() {

        openCamera();

        if (camera == null) {
            return;
        }

        setCameraParameters(degrees);

        if (isPortrait)
            mCurrentFilter.setTextureSize(new Size(cameraHeight, cameraWidth));
        else
            mCurrentFilter.setTextureSize(new Size(cameraWidth, cameraHeight));

        if (encodeByCallback) {
            mFrameLength = ((cameraWidth * cameraHeight) * ImageFormat.getBitsPerPixel(ImageFormat.NV21)) / 8;

            camera.addCallbackBuffer(new byte[mFrameLength]);
            camera.setPreviewCallbackWithBuffer(this);
        }
        else
        {
            camera.setPreviewCallbackWithBuffer(null);
        }

		if (mSurfaceTexture != null && !isPreviewStarted()) {
            try {
                Log.d(TAG,"camera.setPreviewTexture in startCamera()");
                camera.setPreviewTexture(mSurfaceTexture);

            } catch (IOException e) {
                Log.e(TAG, e.getMessage());
                e.printStackTrace();
            }

            Log.d(TAG,"camera.startPreview in startCamera()");
            changePreview(true);
        }

        setBackgroundColor(0);
    }

    public void openCamera(){

        if (camera != null) return;

        if (Integer.parseInt(Build.VERSION.SDK) > 8) {
            numOfCamera = Camera.getNumberOfCameras();

            Log.d(TAG,"GLVideoEncodeView.openCamera: numOfCamera = " + numOfCamera);

            if (numOfCamera == 1) {
                currentCamera = Camera.CameraInfo.CAMERA_FACING_BACK;

                if (Build.MODEL.indexOf("HDX2") != -1){
                    if (mCurrentFilter != null) mCurrentFilter.setMirror(true);
                    mMirror = true;
                }

                try {
                    camera = Camera.open(currentCamera);
                }catch (Exception e){
                    numOfCamera = 0;
                    Log.d(TAG,"GLVideoEncodeView.openCamera Error: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                if (isFlow){
                    currentCamera = Camera.CameraInfo.CAMERA_FACING_BACK;
                }else {
                    currentCamera = Camera.CameraInfo.CAMERA_FACING_FRONT;
                    if (mCurrentFilter != null) mCurrentFilter.setMirror(true);
                    mMirror = true;
                }
                try {
                    camera = Camera.open(currentCamera);
                } catch (Exception e) {
                    numOfCamera = 0;
                    Log.d(TAG,"GLVideoEncodeView.openCamera Error(2): " + e.getMessage());
                    e.printStackTrace();
                }
            }
        } else {
            try {
                camera = Camera.open(currentCamera);
            }catch (Exception e){
                e.printStackTrace();
                numOfCamera = 0;
                Log.d(TAG,"GLVideoEncodeView.openCamera Error(3): " + e.getMessage());
            }
        }
        if (Build.MODEL.toUpperCase().startsWith("Lenovo".toUpperCase())){
            camera.setDisplayOrientation(0);
        }
    }
    /*
     * 设置相机属性
     */
    private void setCameraParameters(int degrees) {
        Camera.Parameters parameters = camera.getParameters();
        List<Camera.Size> previewSizes = parameters.getSupportedPreviewSizes();
        List<int[]> rates = parameters.getSupportedPreviewFpsRange();
        cameraWidth = 0;
        cameraHeight = 0;

//        if (videoCommon.getWidth() <= 0 || videoCommon.getHeight() <= 0) {
//
//            int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
//            int height = SharedPreferencesUrls.getInstance().getInt("height", 720);
//
//            if (videoCommon != null) {
//                videoCommon.setWidth(width);
//                videoCommon.setHeight(height);
//                //Log.d("InfowareLab.Debug", ">>>>>>FragVs.local resolution: " + width + "x" + height);
//            }
//        }

//        int perferWidth = videoCommon.getWidth();
//        int perferHeight = videoCommon.getHeight();

        int perferWidth = SharedPreferencesUrls.getInstance().getInt("width", 1280);
        int perferHeight = SharedPreferencesUrls.getInstance().getInt("height", 720);

        Log.d(TAG,"GLVideoEncodeView: Setting Size: " + perferWidth + "x" + perferHeight);

        if (perferWidth < 1920 || perferHeight < 1080)
        {
            perferWidth = 1280;
            perferHeight = 720;
        }

        Log.d(TAG,"GLVideoEncodeView: Preferred Size: " + perferWidth + "x" + perferHeight);

        int defaultWidth = 0;
        int defaultHeight = 0;

        // 取比设定值小的像素中最大的
        for (Camera.Size size : previewSizes) {

            Log.d(TAG,"GLVideoEncodeView: Support resolution: " + size.width + "x" + size.height);

//            if (size.width * size.height <= videoCommon.getWidth()
//                    * videoCommon.getHeight()
//                    && size.height >= 0) {
////                if (cameraWidth == 0) {
////                    cameraWidth = size.width;
////                    cameraHeight = size.height;
////                }
//                if (size.width * size.height >= cameraWidth * cameraHeight) {
//                    cameraWidth = size.width;
//                    cameraHeight = size.height;
//                }
//            }

            if (size.width == perferWidth && size.height == perferHeight) {
                cameraWidth = size.width;
                cameraHeight = size.height;
            }

//            if (defaultWidth <= 0){
//                defaultWidth = size.width;
//                defaultHeight = size.height;
//            }

            if (size.width == perferWidth || size.height == perferHeight){
                defaultWidth = size.width;
                defaultHeight = size.height;
            }
        }
        // 如果设定值实在太小，取所支持的最小像素
        if (cameraWidth <= 0 || cameraHeight <= 0) {

            cameraWidth = defaultWidth;
            cameraHeight = defaultHeight;

            if (cameraWidth <= 0 || cameraHeight <= 0) {
                for (Camera.Size size : previewSizes) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                        continue;
                    }
                    if (size.width * size.height > cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
        }

        Log.d(TAG,"Camera.setPreviewSize = " + cameraWidth + "x" + cameraHeight);

        frameSize = cameraWidth * cameraHeight;
        encodeWidth = cameraWidth;
        encodeHeight = cameraHeight;

        int minimum = 0;
        int maximum = 0;
        int retMininum = 0;
        int retMaximum = 0;
        if (rates.size() > 0) {
            minimum = rates.get(0)[0];
            maximum = rates.get(0)[1];
            retMininum = rates.get(0)[0];
            retMaximum = rates.get(0)[1];
            for (int[] fps : rates){
                if (minimum < fps[0]){
                    minimum = fps[0];
                }
                if (maximum < fps[1]){
                    maximum = fps[1];
                }
            }
        }

        //setCameraOrientation(degrees,parameters);

        setCameraDisplayOrientation((ActConf) activity, currentCamera, camera);

        //minimum = 20000;

        parameters.setPreviewSize(cameraWidth, cameraHeight);//设置预览的高度和宽度,单位为像素
//	    parameters.setPreviewFrameRate(FRAME_RATE);//设置图片预览的帧速。
//	    parameters.setPreviewFpsRange(minimum, maximum);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){
                if (minimum > 0 && maximum > 0){

                    Log.d(TAG,"Camera.setPreviewFpsRange(1) = " + minimum + "=>" + maximum);
                    parameters.setPreviewFpsRange(minimum,maximum);
                }
            }else {
                if (retMininum > 0 && retMaximum > 0){
                    Log.d(TAG,"Camera.setPreviewFpsRange(2) = " + retMininum + "=>" + retMaximum);
                    parameters.setPreviewFpsRange(retMininum,retMaximum);
                }
            }
        }catch (RuntimeException e){
            if (retMininum > 0 && retMaximum > 0){
                Log.d(TAG,"Camera.setPreviewFpsRange(3) = " + retMininum + "=>" + retMaximum);
                parameters.setPreviewFpsRange(retMininum,retMaximum);
            }
        }
        List<String> focusModes = parameters.getSupportedFocusModes();
        if (focusModes.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE)){
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);// 1连续对焦
            camera.setParameters(parameters);
            camera.cancelAutoFocus();
        }else{
            try {
                camera.setParameters(parameters);
            }
            catch (RuntimeException e)
            {
                Camera.Parameters appliedParam = camera.getParameters();
                cameraWidth = appliedParam.getPreviewSize().width;
                cameraHeight = appliedParam.getPreviewSize().height;

                Log.d(TAG,"Camera.CameraParameters(FAILED) = " + cameraWidth + ", " + cameraHeight);

            }
        }

//        if(initVideo){
//            Log.d(TAG,"videoCommon.initializeMyVideo: " + encodeWidth + "x" + encodeHeight);
//
//            videoCommon.initializeMyVideo(encodeWidth, encodeHeight, cameraFPS);
//            //initVideo = false;
//        }

        AnalyzeBitRateByEncodeSize();

        needToInitMyVideo = true;
    }

    private void AnalyzeBitRateByEncodeSize() {

        //2022/8/1 编码：默认帧率25帧、码流1080P -1.5M，720P-800K， 640-400K，320-200K

        int bitrate = 0;
        int picSize = encodeWidth * encodeHeight;
        if (picSize <= 320 * 240) {
            //bitrate = 256*1024;
            bitrate = 200*1024;
//        } else if (picSize <= 352 * 288) {
//            bitrate = 240000;
        } else if (picSize <= 640 * 480) {
            //bitrate = 512*1024;
            bitrate = 400*1024;
//        } else if (picSize <= 960 * 720) {
//            bitrate = 700000;
        } else if (picSize <= 1280 * 720) {
            //bitrate = 1024*1024;
            bitrate = 800*1024;
//            bitrate = 700000;
        } else if (picSize <= 1920 * 1080) {
            //bitrate = 2048*1024;
            bitrate = 1536*1024;
        } else {
            bitrate = 2048*1024;
        }

        mBitRate = bitrate;

        Log.d(TAG,">>>>> AnalyzeBitRateByEncodeSize: mBitRate = " + mBitRate);
    }

    private void setCameraOrientation(int degrees, Camera.Parameters p){
        if (Integer.parseInt(Build.VERSION.SDK) >= 8)
            setDisplayOrientation(camera, degrees);
        else
        {
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT)
            {
                p.set("orientation", "portrait");
                p.set("rotation", degrees);
            }
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE)
            {
                p.set("orientation", "landscape");
                p.set("rotation", degrees);
            }
        }
    }
    private void setDisplayOrientation(Camera mCamera, int angle) {
        Method downPolymorphic;
        try
        {
            downPolymorphic = mCamera.getClass().getMethod("setDisplayOrientation", new Class[] { int.class });
            if (downPolymorphic != null)
                downPolymorphic.invoke(mCamera, new Object[] { angle });
        }
        catch (Exception e1)
        {
        }
    }
    /**
     * 相应地，在surfaceDestroyed中也需要释放该Camera对象。 我们将首先调用stopPreview，以确保应该释放的资源都被清理。
     */

    @Override
    public void onPreviewFrame(byte[] data, Camera camera) {


        if (data == null) {
            return;
        }

        if (!encodeByCallback)
            return;


//        if (isWriteFile) {
//            try {
//                _out.write(data);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }

        //防止花屏

//        if (data[4] == 0x61){
//            data[4] = 0x41;
//        }

        //Log.d(TAG, "GLVideoEncodeView.onPreviewFrame: data length: " + data.length + "<" + mFrameLength);

        if (data.length != mFrameLength)
        {
            Log.d(TAG, "GLVideoEncodeView.onPreviewFrame: wrong data length: " + data.length + "<" + mFrameLength);
            camera.addCallbackBuffer(data);
            return;
        }

        if (false == isSharing)
        {
            if (h264HwEncoderImpl.GetMediaEncoder() != null && mEncoderState == STATE_ON) {

                h264HwEncoderImpl.releaseEncoder();

                mEncoderState = STATE_OFF;
            }

            needToInitMyVideo = true;

            camera.addCallbackBuffer(data);

            return;
        }

        if (isSharing) {

                if (isPortrait) {//竖屏

                    if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16) {
                        if (h264HwEncoderImpl.GetMediaEncoder() == null && mEncoderState == STATE_OFF) {

                            if (needToInitMyVideo){

                                Log.d(TAG,">>>>> initializeMyVideo: " + encodeWidth + "x" + encodeHeight);

                                videoCommon.initializeMyVideo(encodeWidth, encodeHeight, cameraFPS);
                                needToInitMyVideo = false;
                            }

                            AnalyzeBitRateByEncodeSize();

                            if (!resolutionSpecified)
                                AnalyzeEncodeSize();

                            resolutionSpecified = false;

                            h264HwEncoderImpl.initEncoder(encodeHeight, encodeWidth);

                            mEncoderState = STATE_ON;
                        }
                        else
                        {
                            if (needToChangeEncodeSize) {
                                needToChangeEncodeSize = false;
                                needToInitMyVideo = true;

                                h264HwEncoderImpl.releaseEncoder();
                                mEncoderState = STATE_OFF;

                                camera.addCallbackBuffer(data);
                                return;
                            }
                        }

                        yv12buf = h264HwEncoderImpl.getAdapterYv12bufPortrait(data, cameraWidth, cameraHeight, currentCamera);
                        h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
                    } else //soft ware encoding
                    {
                        if (currentCamera == Camera.CameraInfo.CAMERA_FACING_BACK) {
                            yv12buf = rotateYUV420SPBackfacing(data, cameraWidth, cameraHeight);
                        } else {
                            yv12buf = rotateYUV420SPFrontfacing(data, cameraWidth, cameraHeight);
                        }

                        videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraHeight, cameraWidth, false);
                    }
                    //end
                } else {//横屏
                    if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16)//hardware encode.....
                    {
                        if (h264HwEncoderImpl.GetMediaEncoder() == null && mEncoderState == STATE_OFF) {

                            if (needToInitMyVideo){

                                Log.d(TAG,">>>>> initializeMyVideo: " + encodeWidth + "x" + encodeHeight);

                                videoCommon.initializeMyVideo(encodeWidth, encodeHeight, cameraFPS);
                                needToInitMyVideo = false;
                            }

                            AnalyzeBitRateByEncodeSize();

                            if (!resolutionSpecified)
                                AnalyzeEncodeSize();

                            resolutionSpecified = false;

                            h264HwEncoderImpl.initEncoder(encodeHeight, encodeWidth);

                            mEncoderState = STATE_ON;
                        }
                        else
                        {
                            if (needToChangeEncodeSize) {
                                needToChangeEncodeSize = false;
                                needToInitMyVideo = true;

                                h264HwEncoderImpl.releaseEncoder();
                                mEncoderState = STATE_OFF;

                                camera.addCallbackBuffer(data);
                                return;
                            }
                        }

                        yv12buf = h264HwEncoderImpl.getAdapterYv12bufLandscape(data, cameraWidth, cameraHeight, degrees);
                        h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
                    } else  //software encode
                    {
                        if (degrees == 0) {
                            yv12buf = changeYUV420SP2P(data, cameraWidth, cameraHeight);
                        } else {
                            yv12buf = Rotate180YUV420SP2P(data, cameraWidth, cameraHeight);
                        }
                        videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraWidth, cameraHeight, false);
                    }
                }
        }

        camera.addCallbackBuffer(data);

    }

    private byte[] rotateYUV420SPFrontfacing(byte[] src, int width, int height) {
        byte[] des = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;
        // 旋转Y
        int k = 0;
        for (int i = width - 1; i >= 0; i--) {
            for (int j = 0; j < height; j++) {
                des[k++] = src[width * j + i];
            }
        }
        for (int i = width - 2; i >= 0; i -= 2) {
            for (int j = 0; j < height / 2; j++) {
                des[k] = src[wh + width * j + i + 1];
                des[k + uv] = src[wh + width * j + i];
                k++;
            }
        }
        return des;
    }

    public static byte[] rotateYUV420SPBackfacing(byte[] src, int width,
                                                  int height) {
        byte[] des = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;
        // 旋转Y
        int k = 0;
        for (int i = 0; i < width; i++) {
            for (int j = height - 1; j >= 0; j--) {
                des[k] = src[width * j + i];
                k++;
            }
        }

        for (int i = 0; i < width; i += 2) {
            for (int j = height / 2 - 1; j >= 0; j--) {
                des[k] = src[wh + width * j + i + 1];
                des[k + uv] = src[wh + width * j + i];
                k++;
            }
        }

        return des;

    }

    private byte[] changeYUV420SP2P(byte[] src, int width, int height) {
        System.gc();
        byte[] yv12buf = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;

        System.arraycopy(src, 0, yv12buf, 0, wh);

        int k = 0;
        for (int i = 0; i < wh / 2; i += 2) {
            yv12buf[wh + k] = src[wh + i + 1];
            yv12buf[wh + uv + k] = src[wh + i];
            k++;
        }
        return yv12buf;
    }

    private byte[] Rotate180YUV420SP2P(byte[] src, int width, int height) {
        byte[] yv12buf = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;
        int k = 0;

        for (int i = 0; i < wh; i++) {
            yv12buf[k++] = src[wh - i];
        }

        for (int i = wh * 3 / 2 - 1; i >= wh; i -= 2) {
            yv12buf[k] = src[i];
            yv12buf[uv + k] = src[i - 1];
            k++;
        }
        return yv12buf;
    }


    public void flushEncoder(){
        //if(isHardCodec&&h264HwEncoderImpl.GetMediaEncoder()!= null){
            //h264HwEncoderImpl.flushEncoder();
        //}
    }


    public void destroyCamera() {

        if (camera != null) {
            //Log.d(TAG,"destroyCamera");
            Log.d(TAG,"stopPreview and releaseCamera");

            camera.setPreviewCallback(null);
            changePreview(false);
            camera.stopPreview();
            // 停止更新预览
            camera.release();// 释放资源

            camera = null;
            isOpen = false;
        }
    }

    public void preLeave() {

        if (camera != null) {
            Log.d(TAG,"GLVideoEncodeView.preLeave: stop and destroy camera");
            //Log.d(TAG,"stopPreview and releaseCamera");

            camera.setPreviewCallback(null);
            changePreview(false);
            camera.stopPreview();
            // 停止更新预览
            camera.release();// 释放资源

            if (!encodeByCallback) {
                if (hwEncoderWrapperEx != null && mEncoderState == STATE_ON) {
                    hwEncoderWrapperEx.stopRecording();
                    mEncoderState = STATE_OFF;
                }
            }
            else
            {
                if (h264HwEncoderImpl.GetMediaEncoder() != null && mEncoderState == STATE_ON) {
                    h264HwEncoderImpl.releaseEncoder();
                    mEncoderState = STATE_OFF;
                }
            }

            camera = null;
            isOpen = false;
            isSharing = false;
        }
    }

    public void reStartLocalView() {
        // if(camera != null){

        if (CallbackManager.IS_LEAVED) return;

        Log.d(TAG,"GLVideoEncoderView.reStartLocalView");
        if (camera == null) {
            changeStatus(true);
        } else {
            if (isDestroyed) {
                destroyCamera();
                if (!encodeByCallback) {
                    if (hwEncoderWrapperEx != null && mEncoderState == STATE_ON) {
                        hwEncoderWrapperEx.stopRecording();
                        mEncoderState = STATE_OFF;
                    }
                }
                else
                {
                    if (h264HwEncoderImpl.GetMediaEncoder() != null && mEncoderState == STATE_ON) {
                        h264HwEncoderImpl.releaseEncoder();
                        mEncoderState = STATE_OFF;
                    }
                }
                currentCamera = (currentCamera + 2) % numOfCamera;
                startCamera();
            }else {
                destroyCamera();
                if (!encodeByCallback) {
                    if (hwEncoderWrapperEx != null && mEncoderState == STATE_ON) {
                        hwEncoderWrapperEx.stopRecording();
                        mEncoderState = STATE_OFF;
                    }
                }
                else
                {
                    if (h264HwEncoderImpl.GetMediaEncoder() != null && mEncoderState == STATE_ON) {
                        h264HwEncoderImpl.releaseEncoder();
                        mEncoderState = STATE_OFF;
                    }
                }
                startCamera();
//				videoCommon.exChange(cameraHeight, cameraWidth);
            }
        }
    }

    public void setStatus(boolean isMove) {
        if (isMove) {
            camera.setPreviewCallback(null);
            changePreview(false);
        } else {
            camera.setPreviewCallback(this);
            changePreview(true);
        }
    }

    public void changeStatus(boolean isOpenCamera) {

        Log.d(TAG,"GLVideoEncoderView.changeStatus = " + isOpenCamera);

        if (isOpenCamera) {
            if (camera == null) {
                //invalidate();
                //init(activity);
                startCamera();
            }
        } else {
            if (camera != null) {
                destroyCamera();
            }
        }
    }

    private void changePreview(boolean state) {
        try {
            if (state) {
                Log.d(TAG,"GLVideoEncoderView.startPreview:" + state );

                camera.startPreview();
                isPreview = true;
            } else {
                Log.d(TAG,"GLVideoEncoderView.stopPreview:" + state );

                camera.stopPreview();
                isPreview = false;
            }
        } catch (Exception e) {
            Log.e(TAG,e.getMessage());
        }
    }

    public boolean getCamera() {
        return camera != null;
    }

    public boolean isSharing() {
        return isSharing;
    }

    public void setSharing(boolean sharing) {
        
        if (isSharing == sharing) return;
        
        isSharing = sharing;

        Log.d(TAG, "GLVideoEncoderView.setSharing: "+ isSharing);

        if (!isSharing) needToInitMyVideo = true;

    }

    public void setCameraLandscape() {
        degrees = 0;
        reStartLocalView();
    }

    public void setParams(int width,int height) {

        if (mViewWidth == width && mViewHeight == height)
            return;

        if (CallbackManager.IS_LEAVED) return;

        Log.d(TAG, "GLVideoEncoderView.setParams: " + width + "x" + height);

        mViewWidth = width;
        mViewHeight = height;

        if(width>1&&camera==null&&isEnabled()){
            reStartLocalView();
        }
        if(width <= 1 || height <= 1){
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = 1;
            params.height = 1;
            setLayoutParams(params);
            setRenderEnabled(false);
        }else {
            /*if(degrees%180==0){
                if((1.0f*cameraWidth/cameraHeight)>(1.0f*width/height)){
                    height = (int) ((1.0f*cameraHeight/cameraWidth)*width);
                }else{
                    width = (int) ((1.0f*cameraWidth/cameraHeight)*height);
                }
            }else {
                if((1.0f*cameraHeight/cameraWidth)>(1.0f*width/height)){
                    height = (int) ((1.0f*cameraWidth/cameraHeight)*width);
                }else{
                    width = (int) ((1.0f*cameraHeight/cameraWidth)*height);
                }
            }*/
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();

            params.setMargins(0,0,0,0);

            params.width = width;//RelativeLayout.LayoutParams.MATCH_PARENT;
            params.height = height;//RelativeLayout.LayoutParams.MATCH_PARENT;
            setLayoutParams(params);

            setRenderEnabled(true);
        }
    }

    public boolean isPreviewStarted() {
        return isPreview;
    }

    public void switchSoftEncode(boolean isSoft){
        isHardCodec = videoCommon.isHardCodec();
        reStartLocalView();
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        super.surfaceDestroyed(holder);

        Log.d(TAG,"GLVideoEncodeView.surfaceDestroyed");

        if (!encodeByCallback) {
            if (hwEncoderWrapperEx != null && mEncoderState == STATE_ON) {
                hwEncoderWrapperEx.stopRecording();
                mEncoderState = STATE_OFF;
            }
        }
        else
        {
            if (h264HwEncoderImpl.GetMediaEncoder() != null && mEncoderState == STATE_ON) {
                h264HwEncoderImpl.releaseEncoder();
                mEncoderState = STATE_OFF;
            }
        }

        if (mCurrentFilter != null) mCurrentFilter.releaseProgram();
    }

    public boolean existCamera(){
        if (numOfCamera < 0)
            numOfCamera = numOfCamera = Camera.getNumberOfCameras();
        return (numOfCamera > 0);
    }
}
