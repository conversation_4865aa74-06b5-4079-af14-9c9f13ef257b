package com.infowarelab.tvbox;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Looper;
import android.widget.Toast;


import com.infowarelab.tvbox.activity.ActLogo;

////import org.apache.log4j.Logger;

/**
 * 接收系统不能处理的异常并提示
 * <AUTHOR>
 * @Date 2013-9-6下午4:27:03
 * @Email <EMAIL>
 */
public class CrashHandler implements Thread.UncaughtExceptionHandler {

	private static CrashHandler instance;

	private Thread.UncaughtExceptionHandler unCaughException;

	private Context mContext;

	//private static final Logger log = Logger.getLogger(CrashHandler.class);

	private CrashHandler() {
		unCaughException = Thread.getDefaultUncaughtExceptionHandler();
	}

	public static CrashHandler getInstance() {
		if (instance == null) {
			instance = new CrashHandler();
		}
		return instance;
	}

	public void init(Context context) {
		this.mContext = context;
		Thread.setDefaultUncaughtExceptionHandler(this);
	}

	@Override
	public void uncaughtException(Thread thread, Throwable ex) {
		//log.error(thread.getName(), ex);
//		showMessage();
//		SystemClock.sleep(2000);
		
//		Intent intent = new Intent(mContext.getApplicationContext(), ActLogo.class);
//        PendingIntent restartIntent = PendingIntent.getActivity(mContext.getApplicationContext(), 0, intent,Intent.FLAG_ACTIVITY_NEW_TASK);
//
//        //退出程序
//        AlarmManager mgr = (AlarmManager)mContext.getSystemService(Context.ALARM_SERVICE);
//        mgr.set(AlarmManager.RTC, ConferenceApplication.currentTimeMillis() + 1000,
//                restartIntent);
//
//		System.exit(0);
//
//		android.os.Process.killProcess(android.os.Process.myPid());

	}

	private void showMessage() {
		new Thread(new Runnable() {
			@Override
			public void run() {
				Looper.prepare();
				Toast.makeText(mContext, mContext.getString(R.string.comm_crashMeessage), Toast.LENGTH_LONG).show();
				Looper.loop();
			}
		}).start();
	}

}
