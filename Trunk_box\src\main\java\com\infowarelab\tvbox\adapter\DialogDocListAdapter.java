package com.infowarelab.tvbox.adapter;

import android.content.Context;
import android.os.Build;
import androidx.annotation.RequiresApi;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.base.BaseViewAdapter;
import com.infowarelab.tvbox.base.BaseViewHolder;
import com.infowarelabsdk.conference.domain.DocBean;

import java.util.List;

/**
 * Created by xiaor on 2019/12/28.
 * <AUTHOR>
 * 分享文档列表适配器
 */

public class DialogDocListAdapter extends BaseViewAdapter<DocBean>{

    private OnSelectListener onSelectListener;

    public DialogDocListAdapter(Context context) {
        super(context);
    }

    @Override
    public void setDatas(List<DocBean> datas) {
        super.setDatas(datas);
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        if (null == convertView) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_dilog_doc, null);
        }
        LinearLayout itemLayout = BaseViewHolder.get(convertView,R.id.item_dialog_list_itemLayout);
        TextView nameText = BaseViewHolder.get(convertView,R.id.item_dialog_list_nameText);
        DocBean bean = getDatas().get(position);
        if (bean.isFouse()){
            itemLayout.setBackgroundResource(R.drawable.invate_list_bg);
            nameText.setTextColor(getContext().getColor(R.color.white));
        }else {
            itemLayout.setBackgroundResource(R.drawable.selected);
            nameText.setTextColor(getContext().getColor(R.color.black3));
        }
        nameText.setText(bean.getTitle());

        itemLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onSelectListener.doSelect(position);
            }
        });
        return convertView;
    }

    public interface OnSelectListener {
        void doSelect(int position);
    }
    public void setOnSelectListener(OnSelectListener onSelectListener) {
        this.onSelectListener = onSelectListener;
    }
    public void doSelect(int position) {
        if (onSelectListener != null) {
            onSelectListener.doSelect(position);
        }
    }
    public void update(List<DocBean> data){
        setDatas(data);
        notifyDataSetChanged();
    }
}
