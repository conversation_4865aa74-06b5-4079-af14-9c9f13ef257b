package com.infowarelab.tvbox.view;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.adapter.DialogDocListAdapter;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.domain.DocBean;

public class DocListDialog extends AlertDialog implements View.OnClickListener,
        AdapterView.OnItemSelectedListener, AdapterView.OnItemClickListener {

    private TextView titleText;
    private TextView sumText;
    private ImageView backImage;
    private ListView listView;

    private DialogDocListAdapter adapter;

    private DocCommonImpl docCommon;

    //获取焦点的下标
    private int fousePos = 0;

    private OnSelectListener onSelectListener;

    public DocListDialog(@NonNull Context context) {
        super(context, R.style.dialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_person_list);
        setCanceledOnTouchOutside(true);

        titleText = (TextView)findViewById(R.id.frag_att_tv_atts_titleText);
        sumText = (TextView)findViewById(R.id.frag_att_tv_atts_sum);
        backImage = (ImageView) findViewById(R.id.dialog_personList_back);
        listView = (ListView)findViewById(R.id.dialog_personList_listview);

        titleText.setText("文档列表");

        docCommon = (DocCommonImpl) CommonFactory.getInstance().getDocCommon();

        adapter = new DialogDocListAdapter(getContext());
        if (docCommon.getDocMapList() != null && docCommon.getDocMapList().size() != 0){
            refreshDocList();
        }
        listView.setAdapter(adapter);
        backImage.setOnClickListener(this);
        listView.setOnItemSelectedListener(this);
        listView.setOnItemClickListener(this);
        listView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {

                Log.d("InfowareLab.Debug", "onFocusChange:" + fousePos);
//                if (adapter.getDatas() != null && adapter.getDatas().size() > fousePos){
//                    adapter.getDatas().get(fousePos).setFouse(hasFocus);
//                }

//                adapter.update(adapter.getDatas() );

                adapter.notifyDataSetChanged();
            }
        });
        adapter.setOnSelectListener(new DialogDocListAdapter.OnSelectListener() {
            @Override
            public void doSelect(int position) {

                Log.d("InfowareLab.Debug", "doSelect:" + fousePos);

                DocBean docBean = adapter.getDatas().get(position);

                if (fousePos != position){
                    adapter.getDatas().get(fousePos).setFouse(false);
                    docBean.setFouse(true);
                    fousePos = position;
                    adapter.notifyDataSetChanged();
                }

                if (adapter.getDatas().size() == 1){
                    onSelectListener.onDocItemListener(docBean.getDocID());
                    docCommon.setCurrentDoc(docBean);
                }else {
                    if (docBean.getDocID() != docCommon.getCurrentDoc().getDocID()){
                        onSelectListener.onDocItemListener(docBean.getDocID());
                        docCommon.setCurrentDoc(docBean);
                        //adapter.getDatas().get(position).setFouse(true);
                        //adapter.notifyDataSetChanged();
                    }else {
                        onSelectListener.onCloseListener();
                    }
                }
                if (isShowing()){
                    dismiss();
                }
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.dialog_personList_back:
                if (isShowing()){
                    dismiss();
                }
                break;
            default:
                break;
        }
    }

    public void refreshDocList() {
        if (adapter != null){
            adapter.update(docCommon.getDocMapList());
        }
        if (sumText != null){
            sumText.setText(""+docCommon.getDocMapList().size());
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        backImage.setFocusable(false);
        if (docCommon.getDocMapList() != null && docCommon.getDocMapList().size() != 0){
            adapter.setDatas(docCommon.getDocMapList());
            for (int i = 0; i < adapter.getDatas().size(); i++){
                DocBean bean = adapter.getDatas().get(i);
                if (bean.getDocID() == docCommon.getCurrentDoc().getDocID()){
                    fousePos = i;
                    bean.setFouse(true);
                }
                else
                    bean.setFouse(false);
            }
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {

        Log.d("InfowareLab.Debug", "onItemSelected:" + position + "; old=" + fousePos);

        DocBean bean = adapter.getDatas().get(position);
        if (fousePos != position){
            adapter.getDatas().get(fousePos).setFouse(false);
            bean.setFouse(true);
            fousePos = position;
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        adapter.doSelect(position);
    }

    public interface OnSelectListener {
        void onDocItemListener(int docId);
        void onCloseListener();
    }
    public void setOnSelectListener(OnSelectListener onSelectListener) {
        this.onSelectListener = onSelectListener;
    }
}
