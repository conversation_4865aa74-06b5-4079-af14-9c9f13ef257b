package com.infowarelab.tvbox.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.util.AttributeSet;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceHolder.Callback;
import android.view.SurfaceView;
import android.widget.RelativeLayout;

import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.mingri.uvc.MrVideoDecoder;
import com.mingri.uvc.MrVideoDecoder1;
import com.mingri.uvc.Uvc;

////import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * PreviewCallback回到接口，用于显示预览框
 */
@SuppressLint("NewApi")
public class UvcEnDecodeView1 extends SurfaceView implements Uvc.PreviewCallback, Callback {
    public static final String TAG = "UvcEnDecoderView1";
    public static final String H264_MIME = "video/avc";
    private Context mContext;
    private ShareDtCommonImpl shareDtCommon;
    private int cameraWidth1 = 1920;
    private int cameraHeight1 = 1080;
    private int degrees = 0;
    private static boolean isSharing = false;
    private Uvc mCamera;
    private SurfaceHolder holder;
    private Uvc.Size curSize;
    private byte[] mMediaHead = null;
    private byte[] mOutput = new byte[10240 * 8];
    private File _fr = null;
    private FileOutputStream _out = null;
    private boolean isWriteFile = false;
    //private Logger logger = Logger.getLogger("UvcEncodeView");
    private MrVideoDecoder1 mVideoDecoder;
    private int nCameraID = Uvc.ID_HDMI;

    SharedPreferences sharedPreferences;

    private byte g_streamType = -1;

    public UvcEnDecodeView1(Context context) {
        super(context);
        this.mContext = context;
        init();
    }
    public UvcEnDecodeView1(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        this.mContext = context;
        init();
    }

    public UvcEnDecodeView1(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    public void init() {
        //logger.info("initLog");
        Log.d(TAG, "UvcEnDecoderView1.init()");
        shareDtCommon = (ShareDtCommonImpl)CommonFactory.getInstance().getSdCommon();
        sharedPreferences =mContext.getSharedPreferences("main", Context.MODE_PRIVATE);
        cameraWidth1 = sharedPreferences.getInt("width1", 0);
        cameraHeight1 = sharedPreferences.getInt("height1", 0);
        if (holder == null) holder = this.getHolder();
        holder.addCallback(this);
        holder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        if (isWriteFile) {
            _fr = new File("/sdcard/yv12buf.src");
            try {
                _out = new FileOutputStream(_fr);
            } catch (FileNotFoundException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }
    public void startCamera() {
//        int nCameraID = Uvc.ID_HDMI;
        // Open Camera
        Log.d(TAG, "UvcEnDecoderView1.startCamera(): " + nCameraID);

        if (mCamera == null) {
            mCamera = Uvc.open(nCameraID);
        }
        if (mCamera == null) {
            return;
        }
        mCamera.setPreset(0);
        // set Stream parames
        initStream(Uvc.MAIN_STREAM);
//        initStream(Uvc.SUB_STREAM);
//        initStream(Uvc.THIRD_STREAM);
        //create decoder
        mCamera.setPreviewCallback(this);
        mVideoDecoder = new MrVideoDecoder1();
//        mVideoDecoder.createDecoder(cameraWidth, cameraHeight, MrVideoDecoder.H264_MIME, holder.getSurface());
        mVideoDecoder.createDecoder(1920, 1080, MrVideoDecoder.H264_MIME, holder.getSurface());
        mCamera.startPreview();
        setCmd(Uvc.MAIN_STREAM);
        holder.setFixedSize(cameraWidth1,cameraHeight1);
        mCamera.setPreviewSize(getSetVideoSize());
        setBackgroundColor(0);

    }

    public void stopPreview() {

        Log.d(TAG, "UvcEnDecoderView1.stopPreview");

        if (null != mCamera){
            mCamera.setPreviewCallback(null);
            mCamera.stopPreview();
        }
    }

    private void releaseCamera() {

        Log.d(TAG, "UvcEnDecoderView1.releaseCamera(): " + nCameraID);

        g_streamType = -1;
        if (mCamera != null) {
            mCamera.release();
            mCamera = null;
        }
        if (mVideoDecoder != null) {
            mVideoDecoder.release();
            mVideoDecoder = null;
        }
    }

    public static boolean isKeyFrame(byte[] buffer) {

        if (buffer.length < 5) {
            return false;
        }

        //00 00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 0
                && buffer[3] == 1) {
            int nalType = buffer[4] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                return true;
            }
        }

        //00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 1) {
            int nalType = buffer[3] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                return true;
            }
        }

        return false;
    }

    @Override
    public void onPreviewFrame(byte[] data, Uvc uvc) {
        if (isWriteFile) {
            try {
                _out.write(data);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
//        //logger.info("onPreviewFrame dataLength = " + data.length);
//        if (!isSharing) return;
        byte nData1, nData2;
        byte nStreamInx, nFrameRefType;
        int frameSize;

        frameSize = data.length;

        nData1 = data[0];
        nData2 = data[1];
        data[0] = nData2 == 0 ? (byte) 0x00 : (byte) 0xFF;
        //Is main Stream
        nStreamInx = (byte) (nData1 & 0x03);
        //get svc information
        nFrameRefType = (byte) ((nData1 & 0x7C) >> 2);
        if (nStreamInx == 0) {
            if (mVideoDecoder != null && mVideoDecoder.getStart()&&getWidth()>0&&getHeight()>0) {
                try {
                    mVideoDecoder.decodeFrame(data, frameSize);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (!isSharing) return;
            int nalUnitType = data[4] & 0x1F;
            /**
             * 防止花屏
             * */
            if (data[4] == 0x61){
                data[4] = 0x41;
            }
            if (isKeyFrame(data)) { //IDR frame
                data[4] = (byte) (data[4] | (3 << 5));
                shareDtCommon.sendScreenData(cameraWidth1,cameraHeight1,24,data,data.length,true,true);
            } else { //P frame
                shareDtCommon.sendScreenData(cameraWidth1,cameraHeight1,24,data,data.length,true,false);
            }
        }
    }

    private boolean initStream(byte streamType) {
        int ret;
        // open stream
        ret = mCamera.openStream(streamType);
        if (ret != 0) {
            Log.e(TAG, "open mCamera failed");
            return false;
        }
        setCmd(streamType);
        return true;
    }

    private boolean setCmd(byte streamType) {

        if(g_streamType == streamType)
        {
            return true;
        }
        g_streamType = streamType;

        int ret;
        Uvc.Size size;
        // setStreamencodeType
        ret = mCamera.setStreamEncodeType(streamType, Uvc.CODEC_H264);
        if (ret != 0) {
            Log.e(TAG, "set mCamera encode type failed");
            return false;
        }

        size = getSetVideoSize();
        ret = mCamera.setStreamVideoSize(streamType, size);
        if (ret != 0) {
            Log.e(TAG, "set mCamera video size failed");
            return false;
        }

        ret = mCamera.setStreamFrameRate(streamType, (byte) 30);
        if (ret != 0) {
            Log.e(TAG, "set mCamera frame rate failed");
            return false;
        }

        ret = mCamera.setStreamIDR(streamType, 200);
        if (ret != 0) {
            Log.e(TAG, "set mCamera I Frame Interval failed");
            return false;
        }
        int bitrate = 0;
        int picSize = size.width * size.height;
        if (picSize < 320 * 240) {
            bitrate = 200;
        } else if (picSize <= 352 * 288) {
            bitrate = 240;
        } else if (picSize <= 720 * 576) {
            bitrate = 700;
        } else if (picSize <= 960 * 720) {
            bitrate = 900;
        } else if (picSize <= 1280 * 960) {
            bitrate = 1000;
        } else if (picSize <= 1600 * 1200) {
            bitrate = 2000;
        } else {
            bitrate = 2000;
        }
        ret = mCamera.setStreamBitRate(streamType, Uvc.CODEC_H264, (byte) 0, (byte) 5, (byte) 51, bitrate);
        if (ret != 0) {
            Log.e(TAG, "set mCamera bit rate failed");
            return false;
        }
        //去掉标题
        mCamera.setStringOSD(streamType, 0, 255, 0, 0, 5, 0, "去掉标题");
        return true;
    }
    private Uvc.Size getSetVideoSize() {
        List<Uvc.Size> mSizes = mCamera.getSupportedFrameSizes();
        // 取比设定值小的像素中最大的
        for (Uvc.Size size : mSizes) {
            if (size.height >= 0) {
                if (size.width * size.height <= cameraWidth1 * cameraHeight1) {
                    cameraWidth1 = size.width;
                    cameraHeight1 = size.height;
                    break;
                }
            }
        }
        // 如果设定值实在太小，取所支持的最小像素
        if (cameraWidth1 == 0) {
            for (Uvc.Size size : mSizes){
                if (size.height >= 0) {
                    if (cameraWidth1 == 0){
                        cameraWidth1 = size.width;
                        cameraHeight1 = size.height;
                    }
                    if (size.width * size.height <= cameraWidth1 * cameraHeight1) {
                        cameraWidth1 = size.width;
                        cameraHeight1 = size.height;
                        break;
                    }
                }
            }
        }
        Uvc.Size currentSize = new Uvc.Size(cameraWidth1, cameraHeight1);
        return currentSize;
    }
    public void reStartLocalView() {

        Log.d(TAG, "UvcEnDecoderView1.reStartLocalView()" + nCameraID);

        if(mCamera!=null){
            Uvc.Size size = getSetVideoSize();
            mCamera.setStreamVideoSize(Uvc.MAIN_STREAM, size);
            //设置码流
            setCmd(Uvc.MAIN_STREAM);
            mCamera.setPreviewSize(size);
        }else{
            stopPreview();
            releaseCamera();
            startCamera();
        }
    }
    public void changeStatus(boolean isOpenCamera) {
        if (isOpenCamera) {
            if (mCamera == null) {
                invalidate();
                init();
                startCamera();
            }
        } else {
            if (mCamera != null) {
                stopPreview();
                releaseCamera();
            }
        }
    }

    public void destroyCamera() {
        stopPreview();
        releaseCamera();
    }
    public void setCameraLandscape() {

    }

    public void setSharing(boolean isSharing) {
        UvcEnDecodeView1.isSharing = isSharing;
    }

    public boolean isSharing() {
        return UvcEnDecodeView1.isSharing;
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
    }
    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width,
                               int height) {
    }
    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        releaseCamera();
    }
    public void setParams(int width, int height) {
//        if (width > 1 && mCamera == null) {
//            reStartLocalView();
//        }
        if (width <= 1) {
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = 1;
            params.height = 1;
            setLayoutParams(params);
        } else {
            int h = height;
            int w = width;
            if (degrees % 180 == 0) {
                if ((1.0f * cameraWidth1 / cameraHeight1) > (1.0f * width / height)) {
                    h = (int) ((1.0f * cameraHeight1 / cameraWidth1) * width);
                } else {
                    w = (int) ((1.0f * cameraWidth1 / cameraHeight1) * height);
                }
            } else {
                if ((1.0f * cameraHeight1 / cameraWidth1) > (1.0f * width / height)) {
                    h = (int) ((1.0f * cameraWidth1 / cameraHeight1) * width);
                } else {
                    w = (int) ((1.0f * cameraHeight1 / cameraWidth1) * height);
                }
            }
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = w;
            params.height = h;
            params.setMargins((width - w) / 2, (height - h) / 2, 0, 0);
            setLayoutParams(params);
        }
    }
    public void switchSoftEncode(boolean isSoft){
        reStartLocalView();
    }

    public void setStreamVideoSize(int w, int h) {
        if(mCamera!=null){

            List<Uvc.Size> mSizes = mCamera.getSupportedFrameSizes();
            cameraWidth1 = 0;
            cameraHeight1 = 0;
            // 取比设定值小的像素中最大的
            for (Uvc.Size size : mSizes) {
                if (size.width * size.height <= w
                        * h
                        && size.height >= 0) {
                    if (cameraWidth1 == 0) {
                        cameraWidth1 = size.width;
                        cameraHeight1 = size.height;
                    }
                    if (size.width * size.height >= cameraWidth1 * cameraHeight1) {
                        cameraWidth1 = size.width;
                        cameraHeight1 = size.height;
                    }
                }
            }
            // 如果设定值实在太小，取所支持的最小像素
            if (cameraWidth1 == 0) {
                for (Uvc.Size size : mSizes) {
                    if (size.height >= 0) {
                        if (cameraWidth1 == 0) {
                            cameraWidth1 = size.width;
                            cameraHeight1 = size.height;
                        }
                        if (size.width * size.height <= cameraWidth1 * cameraHeight1) {
                            cameraWidth1 = size.width;
                            cameraHeight1 = size.height;
                        }
                    }
                }
            }
            Uvc.Size currentSize = new Uvc.Size(cameraWidth1, cameraHeight1);
            mCamera.setStreamVideoSize(Uvc.MAIN_STREAM, currentSize);
            int bitrate = 0;
            int picSize = currentSize.width * currentSize.height;
            if (picSize < 320 * 240) {
                bitrate = 200;
            } else if (picSize <= 352 * 288) {
                bitrate = 240;
            } else if (picSize <= 720 * 576) {
                bitrate = 700;
            } else if (picSize <= 960 * 720) {
                bitrate = 900;
            } else if (picSize <= 1280 * 960) {
                bitrate = 1000;
            } else if (picSize <= 1600 * 1200) {
                bitrate = 2000;
            } else {
                bitrate = 2000;
            }
            mCamera.setStreamBitRate(Uvc.MAIN_STREAM, Uvc.CODEC_H264, (byte) 0, (byte) 5, (byte) 51, bitrate);
        }else{
            releaseCamera();
            startCamera();
        }
    }
}