package com.infowarelab.tvbox.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.Button;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.util.NetUtil;

/**
 * Created by sdvye on 2019/6/12.
 */

public class ActNet extends Activity {
    public static ActNet mActivity = null;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.frag_net_connect);
        Button button = (Button) findViewById(R.id.btn_net_set);
        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(Settings.ACTION_SETTINGS));
            }
        });
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        if (checkNetwork()) {
            Intent intent = new Intent(this, ActHome.class);
            startActivity(intent);
            finish();
        }
    }

    private boolean checkNetwork() {
        if (NetUtil.isNetworkConnected(this)) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    protected void onResume() {
        mActivity = ActNet.this;
        super.onResume();
    }
    @Override
    protected void onDestroy() {
        mActivity = null;
        super.onDestroy();
    }
}
