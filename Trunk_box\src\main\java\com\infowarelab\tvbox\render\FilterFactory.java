package com.infowarelab.tvbox.render;

import android.content.Context;


public class FilterFactory {

    public enum FilterType {

        Encode,
        Decode,
        SkinWhiten,
        BlackWhite,
        BlackCat,
        WhiteCat,
        Healthy,
        Romance,
        Original,
        Sunrise,
        Sunset,
        Sakura,
        Latte,
        Warm,
        Calm,
        Brooklyn,
        Cool,
        Sweets,
        Amar<PERSON>,
        Antique,
        Brannan,
        Beauty
    }

    public static BaseFilter createFilter(Context c, FilterType filterType) {

        BaseFilter baseFilter = null;

        switch (filterType) {
            case Decode:
                baseFilter = new DecodeFilter(c);
                break;

            case Original:
            default:
                baseFilter = new OriginalFilter(c);
                break;

        }

        return baseFilter;
    }


}
