package com.infowarelab.tvbox.fragment;

import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.view.ASDecodeViewTV;
import com.infowarelab.tvbox.view.ASDecodeViewTV;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.shareDt.ShareDtCommon;

/**
 * Created by Always on 2018/11/6.
 */
//TODO 分享桌面
public class FragAs extends BaseFragment {
    private View asView;
    private AudioTrack audioTrack;
    private boolean opened = false;
    private ShareDtCommonImpl asCommon;

    private ASDecodeViewTV asDecodeView;
    private RelativeLayout flAs;
    private LinearLayout llNone, llNosupport, llWait, llClosed;
    private boolean mLeaved = false;
    private Handler asHandler = null;

    public FragAs(){
        super();
    }

    public FragAs(ICallParentView iCallParentView) {
        super(iCallParentView);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        asView = inflater.inflate(R.layout.frag_inconf_as_tv, container, false);
        initView();
        initData();
        initHandler();
        return asView;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        destroyAudio();
    }

    private void initView() {
        flAs = (RelativeLayout) asView.findViewById(R.id.frag_as_rl);
        asDecodeView = (ASDecodeViewTV) asView.findViewById(R.id.frag_as_as);
        llNone = (LinearLayout) asView.findViewById(R.id.frag_as_ll_none);
        llNosupport = (LinearLayout) asView.findViewById(R.id.frag_as_ll_nosupport);
        llWait = (LinearLayout) asView.findViewById(R.id.frag_as_ll_wait);
        llClosed = (LinearLayout) asView.findViewById(R.id.frag_as_ll_close);

        asDecodeView.post(new Runnable() {

            @Override
            public void run() {
                // TODO Auto-generated method stub
                initDecoder();
            }
        });
    }

    public void initConfData() {
        initData();
        initHandler();
    }

    private void initData() {
        asCommon = (ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon();
    }

    public void initHandler() {
        if (asHandler == null)
            asHandler = new Handler() {
                @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
                @Override
                public void handleMessage(final Message msg) {
                    if (!isAdded()) return;
                    super.handleMessage(msg);
                    switch (msg.what) {
                        case ShareDtCommon.START_SHARE_DT:
                            //Log.d("InfowareLab.Debug", "Start sharing desktop");
                            if (isAdded()) {
    //                            if (!isHidden() && flAs.getWidth() > 10) {
    //                                open();
    //                            } else {
                                callParentView(ACTION_ASSHARED, null);
    //                            }

                            }
                            initDecoder();
                            break;
                        case ShareDtCommon.STOP_SHARE_DT:
                            //Log.d("InfowareLab.AS", "Stop sharing desktop");
                            if (null != asDecodeView)
                                asDecodeView.changeStatus(false);
                            close();
                            break;
                        case ShareDtCommon.INIT_BROWSER:
                            //Log.d("InfowareLab.AS", "Init desktop browser");
                            asCommon.setShow(true);
                            setPM();
                            break;
                        case ShareDtCommon.INIT_DECODE_FAILED:
                            Log.d("InfowareLab.AS", "Init desktop decoder failed");
                            showNosupport();
                            break;
                        case ShareDtCommon.DT_READY:
                            Log.d("InfowareLab.AS", "Desktop frame data is ready.");
                            if (isAdded() && !isHidden()) {
                                showAs();
                                asCommon.setShow(true);
                            }
                            break;
                        case ShareDtCommon.CHECK_SWITCH:
                            break;
                        case ShareDtCommon.AUDIO:
                            if (audioTrack == null) {
                                int bufSize = AudioTrack.getMinBufferSize(32000, AudioFormat.CHANNEL_CONFIGURATION_STEREO, AudioFormat.ENCODING_PCM_16BIT);
                                audioTrack = new AudioTrack(AudioManager.STREAM_MUSIC, 32000, AudioFormat.CHANNEL_CONFIGURATION_STEREO, AudioFormat.ENCODING_PCM_16BIT, bufSize*4, AudioTrack.MODE_STREAM);
                                audioTrack.setVolume(100f);
                                audioTrack.play();
                            }
                            byte[] data = (byte[]) msg.obj;
                            int length = msg.arg1;

                            //Log.d("InfowareLab.AS", ">>>>>> ShareDtCommon.AUDIO: " + data + "; " + length);
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                                audioTrack.write(data, 0, length, AudioTrack.WRITE_NON_BLOCKING);
                            }
                            else
                                audioTrack.write(data, 0, length);
                            break;
                        default:
                            break;
                    }
                }
            };

        if (asCommon != null) {
            asCommon.setHandler(asHandler);
        }
    }

    private void open() {
        if (true == opened)
            return;

        Log.d("InfowareLab.AS", "FragAs.open");
        if (asCommon.getDecodeState() != 1) {
            showAsWait();
        } else {
            showAs();
        }
        asCommon.setShow(true);
        setPM();
        sub(true);
        opened = true;
    }
    private void close() {

        if (false == opened)
            return;

        Log.d("InfowareLab.AS", "FragAs.close");

        setP1();
        asCommon.setShow(false);
        sub(false);
        showNone();

        if (isAdded() && (!isHidden() || flAs.getWidth() > 10)) {
            callParentView(ACTION_ASSHARED, null);
        }

        opened = false;
    }
    private void sub(boolean sub) {

        Log.d("InfowareLab.Debug", "FragAs.Sub: " + sub);
        if (!sub) {
            subHandler.removeCallbacksAndMessages(null);
            //asCommon.subscribeWithMode(0);
            return;
        }
        if (CommonFactory.getInstance().getConferenceCommon().isSupportSvc()) {
            subHandler.removeCallbacksAndMessages(null);
            //subHandler.sendEmptyMessageDelayed(0, 10);
        } else
            {
            subHandler.removeCallbacksAndMessages(null);
            //subHandler.sendEmptyMessageDelayed(0, 10);
        }

        asCommon.subscribeWithMode(1);
    }

    Handler subHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {

            //asCommon.subscribeWithMode(0);
            asCommon.subscribeWithMode(1);
        }
    };

    private void initDecoder() {

        Log.d("InfowareLab.AS", "FragAs.initDecoder");

        int parentH = flAs.getHeight();
        int parentW = flAs.getWidth();
        if (parentH * 1.0 / asCommon.getDT_HEIGHT() > parentW * 1.0 / asCommon.getDT_WIDTH()) {
            asDecodeView.initSize(parentW, (int) ((parentW * 1.0 / asCommon.getDT_WIDTH()) * asCommon.getDT_HEIGHT()), parentW, parentH);
        } else {
            asDecodeView.initSize((int) ((parentH * 1.0 / asCommon.getDT_HEIGHT()) * asCommon.getDT_WIDTH()), parentH, parentW, parentH);
        }
//        asDecodeView.initSize(1, 1, 1, 1);
        asDecodeView.changeStatus(true);
    }


    private void setP1() {
        asDecodeView.setP1();
    }

    public void setPM() {
        int parentH = flAs.getHeight();
        int parentW = flAs.getWidth();
        if (parentH * 1.0 / asCommon.getDT_HEIGHT() > parentW * 1.0 / asCommon.getDT_WIDTH()) {
            asDecodeView.initSize(parentW, (int) ((parentW * 1.0 / asCommon.getDT_WIDTH()) * asCommon.getDT_HEIGHT()), parentW, parentH);
        } else {
            asDecodeView.initSize((int) ((parentH * 1.0 / asCommon.getDT_HEIGHT()) * asCommon.getDT_WIDTH()), parentH, parentW, parentH);
        }
    }

    /**
     * 无共享
     */
    private void showNone() {
        asDecodeView.setVisibility(View.VISIBLE);
        llNosupport.setVisibility(View.GONE);
        llClosed.setVisibility(View.GONE);
        llWait.setVisibility(View.GONE);
        llNone.setVisibility(View.VISIBLE);
    }

    /**
     * 不支持
     */
    private void showNosupport() {
        asDecodeView.setVisibility(View.GONE);
        llNone.setVisibility(View.GONE);
        llClosed.setVisibility(View.GONE);
        llWait.setVisibility(View.GONE);
        llNosupport.setVisibility(View.VISIBLE);
    }

    /**
     * 请稍候
     */
    private void showAsWait() {
        asDecodeView.setVisibility(View.VISIBLE);
        llNosupport.setVisibility(View.GONE);
        llNone.setVisibility(View.GONE);
        llClosed.setVisibility(View.GONE);
        llWait.setVisibility(View.VISIBLE);
    }

    private void showAs() {
        asDecodeView.setVisibility(View.VISIBLE);
        llNosupport.setVisibility(View.GONE);
        llNone.setVisibility(View.GONE);
        llClosed.setVisibility(View.GONE);
        llWait.setVisibility(View.GONE);

    }

    public boolean isEmpty() {

        if (asCommon == null) initData();

        if (asCommon != null){
            return !asCommon.isShared();
        }else {
            return true;
        }
    }

    public void doSetView() {
        setViewHandler.sendEmptyMessage(0);
    }

    Handler setViewHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {
            if (true == mLeaved)
                return;
            if (!isHidden() && asCommon.isShared() && flAs.getWidth() > 10) {
                open();
            } else {
//                close();
                setP1();
                asCommon.setShow(false);
                showNone();
            }
        }
    };


    public void preLeave() {

        Log.d("InfowareLab.AS","FragAs.preLeave");

        mLeaved = true;

        if (asDecodeView != null)
            asDecodeView.changeStatus(false);
        if (!isHidden() && asCommon.isShared() && flAs.getWidth() > 10) {
            close();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        stopAudio();
    }

    //销毁音频
    public void destroyAudio(){
        if (audioTrack != null){
            audioTrack.stop();
            audioTrack.release();
            audioTrack = null;
        }
    }

    public void stopAudio(){
        if (audioTrack != null){
            audioTrack.stop();
        }
    }

    public void startAudio(){
        if (audioTrack != null){
            audioTrack.play();
        }
    }
}
