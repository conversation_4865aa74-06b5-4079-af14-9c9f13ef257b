apply plugin: 'com.android.library'

//apply plugin: 'com.github.dcendents.android-maven'
//apply plugin: 'com.jfrog.bintray'
//def siteUrl = 'https://github.com/HelloHuDi/ScreenCapture'
//def gitUrl = '**************:HelloHuDi/ScreenCapture.git'
//Properties properties = new Properties()
//properties.load(project.rootProject.file('local.properties').newDataInputStream())
//version = "2.4"
//group = "com.hd"

android {
    compileSdkVersion 29
    buildToolsVersion "29.0.2"

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 29

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    android {
        lintOptions {
            abortOnError false
        }
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    def lifecycle_version = "1.1.0"
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.0.0'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.0.0'
    implementation 'androidx.lifecycle:lifecycle-runtime:2.0.0'
}


//bintray {
//    user = properties.getProperty("bintray.user")
//    key = properties.getProperty("bintray.apikey")
//    configurations = ['archives']
//    pkg {
//        repo = "maven"
//        name = "ScreenCapture"
//        websiteUrl = siteUrl
//        vcsUrl = gitUrl
//        licenses = ["Apache-2.0"]
//        publish = true
//    }
//}

//install {
//    repositories.mavenInstaller {
//        // This generates POM.xml with proper parameters
//        pom {
//            project {
//                packaging 'aar'
//                //Add your description here
//                name 'ScreenCapture'
//                description 'ScreenCapture library.'
//                url siteUrl
//                // Set your license
//                licenses {
//                    license {
//                        name 'The Apache Software License, Version 2.0'
//                        url 'http://www.apache.org/licenses/LICENSE-2.0.txt'
//                    }
//                }
//                developers {
//                    developer {
//                        id 'hellohudi'
//                        name 'hellohudi'
//                        email '<EMAIL>'
//                    }
//                }
//                scm {
//                    connection gitUrl
//                    developerConnection gitUrl
//                    url siteUrl
//                }
//            }
//        }
//    }
//}
//task sourcesJar(type: Jar) {
//    from android.sourceSets.main.java.srcDirs
//    classifier = 'sources'
//}
//task javadoc(type: Javadoc) {
//    failOnError false
//    source = android.sourceSets.main.java.srcDirs
//    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
//}
//task javadocJar(type: Jar, dependsOn: javadoc) {
//    classifier = 'javadoc'
//    from javadoc.destinationDir
//}
//artifacts {
//    archives javadocJar
//    archives sourcesJar
//}
//
//javadoc {
//    options{
//        encoding "UTF-8"
//        charSet 'UTF-8'
//        author true
//        version true
//        links "http://docs.oracle.com/javase/7/docs/api"
//    }
//}
