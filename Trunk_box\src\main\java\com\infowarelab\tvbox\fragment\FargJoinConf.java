package com.infowarelab.tvbox.fragment;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.ReplacementTransformationMethod;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.error.ErrorMessage;
import com.infowarelab.tvbox.utils.XMLUtils;
import com.infowarelab.tvbox.view.JoinDialog;
import com.infowarelab.tvbox.view.LoadingDialog;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.confctrl.ConferenceCommon;
import com.infowarelabsdk.conference.domain.ConferenceBean;
import com.infowarelabsdk.conference.domain.ConfigBean;
import com.infowarelabsdk.conference.domain.LoginBean;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.StringUtil;
import com.infowarelabsdk.conference.util.ToastUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

////import org.apache.log4j.Logger;

public class FargJoinConf extends BaseFragment implements View.OnClickListener, TextWatcher {

    //protected Logger log = Logger.getLogger(getClass());

    public static final int FINISH = 4;
    public static final int LOGINFAILED = 6;
    public static final int MEETINGINVALIDATE = 7;
    public static final int MEETINGNOTJOINBEFORE = 8;
    public static final int HOSTERROR = 9;
    public static final int SPELLERROR = 10;
    public static final int GET_ERROR_MESSAGE = 11;
    public static final int JOIN_CONFERENCE = 12;
    public static final int CREATECONF_ERROR = 15;
    public static final int READY_JOINCONF = 16;
    public static final int NEED_LOGIN = 1001;
    public static final int NO_CONFERENCE = 1002;
    protected static final int INIT_SDK_FAILED = 102;
    protected static final int CONF_CONFLICT = -5;
    //需要密码
    protected static final int NEED_PASSWORD = 10086;

    private View rootView;
    private Context mContext;
    private EditText numEdit;
    private Button confirm;
    private Button cancel;
    private Button delete;

    private Button mBtn0,mBtn1,mBtn2,mBtn3,mBtn4,mBtn5,mBtn6,mBtn7,mBtn8,mBtn9;

    private CommonFactory commonFactory = CommonFactory.getInstance();
    private ConferenceCommonImpl conferenceCommon;

    private SharedPreferences preferences;

    private LoginBean loginBean;
    private Config config;
    private JoinDialog joinDialog;

    private String result = "";

    private LoadingDialog loadingDialog;


    private Handler listHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case NEED_LOGIN:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_1);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case NEED_PASSWORD:
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    ConferenceBean conferenceBean = new ConferenceBean();
                    conferenceBean.setId(numEdit.getText().toString());
                    conferenceBean.setName("非公开的会议");
                    XMLUtils.CONFIGID = conferenceBean.getId();
                    XMLUtils.CONFIGNAME = conferenceBean.getName();
                    XMLUtils.CONFERENCEPATTERN = 0;
                    showJoinDialogEx(conferenceBean);
                    break;
                case JOIN_CONFERENCE:
                    conferenceCommon.setLogPath(ConferenceApplication.getConferenceApp().getFilePath("hslog"));
                    commonFactory.getConferenceCommon().initSDK();
                    joinConference();
                    break;
                case NO_CONFERENCE:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_3);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case GET_ERROR_MESSAGE:
                    hideLoading();
                    showShortToast(config.getConfigBean().getErrorMessage());
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case MEETINGNOTJOINBEFORE:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_7);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case HOSTERROR:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_13);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case SPELLERROR:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_8);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case LOGINFAILED:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_9);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case CREATECONF_ERROR:
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_14);
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                case MEETINGINVALIDATE:
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    hideLoading();
                    showShortToast(R.string.item_meetings_err_2);
                    break;
                case READY_JOINCONF:
//                    if (loadingDialog != null && loadingDialog.isShowing()){
//                        loadingDialog.dismiss();
//                    }
                    break;
                case FINISH:
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    break;
                default:
                    break;
            }
        }
    };
    private String mConfId = null;
    //上次输入框中的内容
    private String lastString;
    //光标的位置
    private int selectPosition;

    private int unitLength = 4;

    private int inputType = 0;

    //追加字符
    private String item = " ";

    private int maxLength = 8;

    public FargJoinConf(ICallParentView iCallParentView) {
        super(iCallParentView);
    }
    public FargJoinConf() {
        super();
    }
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        rootView = inflater.inflate(R.layout.frag_join_conf, container, false);
        return rootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mContext = getContext();
        initView();
    }

    private void initView(){

        loadingDialog = new LoadingDialog(getContext());
        loadingDialog.setCancelable(false);
        loadingDialog.setCanceledOnTouchOutside(false);

        loadingDialog.setTitle("正在加会");

        preferences = getActivity().getSharedPreferences(Constants.SHARED_PREFERENCES,
                Context.MODE_WORLD_READABLE);

        numEdit = (EditText) rootView.findViewById(R.id.et_number);
        confirm = rootView.findViewById(R.id.btn_join_conf);
        cancel = rootView.findViewById(R.id.btn_cancel);
        delete = rootView.findViewById(R.id.btn_delete);

        mBtn0 = rootView.findViewById(R.id.btn_0);
        mBtn1 = rootView.findViewById(R.id.btn_1);
        mBtn2 = rootView.findViewById(R.id.btn_2);
        mBtn3 = rootView.findViewById(R.id.btn_3);
        mBtn4 = rootView.findViewById(R.id.btn_4);
        mBtn5 = rootView.findViewById(R.id.btn_5);
        mBtn6 = rootView.findViewById(R.id.btn_6);
        mBtn7 = rootView.findViewById(R.id.btn_7);
        mBtn8 = rootView.findViewById(R.id.btn_8);
        mBtn9 = rootView.findViewById(R.id.btn_9);

        confirm.setOnClickListener(this);
        cancel.setOnClickListener(this);
        delete.setOnClickListener(this);

        mBtn0.setOnClickListener(this);
        mBtn1.setOnClickListener(this);
        mBtn2.setOnClickListener(this);
        mBtn3.setOnClickListener(this);
        mBtn4.setOnClickListener(this);
        mBtn5.setOnClickListener(this);
        mBtn6.setOnClickListener(this);
        mBtn7.setOnClickListener(this);
        mBtn8.setOnClickListener(this);
        mBtn9.setOnClickListener(this);

        mBtn5.requestFocus();

        conferenceCommon = (ConferenceCommonImpl) commonFactory.getConferenceCommon();
        if (confHandler != null && conferenceCommon != null) {
            if (conferenceCommon.getHandler() != confHandler) {
                conferenceCommon.setHandler(confHandler);
            }
        }

        numEdit.addTextChangedListener(this);
    }

    @Override
    public void afterTextChanged(Editable s) {

        //获取输入框中的内容,不可以去空格
        String etContent = numEdit.getText().toString();
        if (TextUtils.isEmpty(etContent)) {
//            if (listener != null) {
//                listener.textChange("");
//            }
            return;
        }

        //重新拼接字符串
        String newContent = addSpeaceByCredit(etContent);
        //保存本次字符串数据
        lastString = newContent;

        //如果有改变，则重新填充
        //防止EditText无限setText()产生死循环
        if (!newContent.equals(etContent)) {
            numEdit.setText(newContent);
            try {
                //保证光标的位置
                numEdit.setSelection(selectPosition > newContent.length() ? newContent.length() : selectPosition);
            } catch (Exception e) {
                //刚好为限制字符的整数倍时添加空格后会出现越界的情况
                //AppLogUtil.e("超过限制字符");
            }

        }
        //触发回调内容
//        if (listener != null) {
//            listener.textChange(newContent);
//        }

        if (newContent.length() >= 9){
            confirm.requestFocus();
        }
    }

    @Override
    public void beforeTextChanged(CharSequence arg0, int arg1, int arg2,
                                  int arg3) {
        // TODO Auto-generated method stub

    }

    /**
     * 当输入框内容改变时的回调
     * @param s 改变后的字符串
     * @param start 改变之后的光标下标
     * @param before 删除了多少个字符
     * @param count 添加了多少个字符
     */
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

        String editable = numEdit.getText().toString();
        String str = stringFilter(editable.toString());
        if (!editable.equals(str)) {
            numEdit.setText(str);
            //设置新的光标所在位置
            numEdit.setSelection(numEdit.getText().toString().length());
        }

        String code = numEdit.getText().toString();
        if (code.length() == 1){
            if (Character.isDigit(str.charAt(0))) {
                inputType = 0;
                unitLength = 4;
                maxLength = 8;
            }
            else if (Character.isAlphabetic(str.charAt(0))) {
                inputType = 0;
                unitLength = 3;
                maxLength = 6;
            }
        }

        if (numEdit.getText().toString().length() > 0) {
            delete.setVisibility(View.VISIBLE);
            confirm.setEnabled(true);
        } else if (numEdit.isFocused()) {
            delete.setVisibility(View.GONE);
            confirm.setEnabled(true);
//			siteButton.setEnabled(false);
        } else {
            delete.setVisibility(View.GONE);
            confirm.setEnabled(true);
        }

        //因为重新排序之后setText的存在
        //会导致输入框的内容从0开始输入，这里是为了避免这种情况产生一系列问题
        if (start == 0 && count > 1 && numEdit.getSelectionStart() == 0) {
            return;
        }

        String textTrim = numEdit.getText().toString().trim();
        if (TextUtils.isEmpty(textTrim)) {
            return;
        }

        //如果 before >0 && count == 0,代表此次操作是删除操作
        if (before > 0 && count == 0) {
            selectPosition = start;
            if (TextUtils.isEmpty(lastString)) {
                return;
            }
            //将上次的字符串去空格 和 改变之后的字符串去空格 进行比较
            //如果一致，代表本次操作删除的是空格
            if (textTrim.equals(lastString.replaceAll(item, ""))) {
                //帮助用户删除该删除的字符，而不是空格
                StringBuilder stringBuilder = new StringBuilder(lastString);
                stringBuilder.deleteCharAt(start - 1);
                selectPosition = start - 1;
                numEdit.setText(stringBuilder.toString());
            }
        } else {
            //此处代表是添加操作
            //当光标位于空格之前，添加字符时，需要让光标跳过空格，再按照之前的逻辑计算光标位置
            if ((start + count) % (unitLength + 1) == 0) {
                selectPosition = start + count + 1;
            } else {
                selectPosition = start + count;
            }
        }
    }

    public String stringFilter(String str) {
        // 只允许字母、数字、英文空白字符
        String regEx = "[^0-9\\s]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("");
    }

    public class AllCapTransformationMethod extends ReplacementTransformationMethod {
        @Override
        protected char[] getOriginal() {
            char[] aa = { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z' };
            return aa;
        }
        @Override
        protected char[] getReplacement() {
            char[] cc = { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z' };
            return cc;
        }

    }

    public String addSpeaceByCredit(String content) {
        if (TextUtils.isEmpty(content)) {
            return "";
        }

        //去空格
        content = content.replaceAll(" ", "");
        if (TextUtils.isEmpty(content)) {
            return "";
        }

        //卡号限制为16位
        if (content.length() > maxLength) {
            content = content.substring(0, maxLength);
        }

        StringBuilder newString = new StringBuilder();
        for (int i = 1; i <= content.length(); i++) {
            //当为第4位时，并且不是最后一个第4位时
            //拼接字符的同时，拼接一个空格
            //如果在最后一个第四位也拼接，会产生空格无法删除的问题
            //因为一删除，马上触发输入框改变监听，又重新生成了空格
            if (i % unitLength == 0 && i != content.length()) {
                newString.append(content.charAt(i - 1) + " ");
            } else {
                //如果不是4位的倍数，则直接拼接字符即可
                newString.append(content.charAt(i - 1));

            }
        }
        return newString.toString();
    }

    public Handler confHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case ConferenceCommon.RESULT_SUCCESS:

                    if (getActivity() == null) break;

//                    //通知是box
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    Intent intent = new Intent(getActivity(), ActConf.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                    if (mConfId != null){
                        intent.putExtra("confId", mConfId);
                    }
                    getActivity().startActivity(intent);
                    getActivity().finish();
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (loadingDialog != null && loadingDialog.isShowing()){
                                loadingDialog.dismiss();
                            }
                        }
                    }, 5 * 1000);
                    break;
                case ConferenceCommon.BEYOUNGMAXCOUNT:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    post(new Runnable() {
                        @Override
                        public void run() {
                            showShortToast(R.string.item_meetings_err_5);
                        }
                    });
                    break;
                case ConferenceCommon.BEYOUNGJIAMI:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    post(new Runnable() {
                        @Override
                        public void run() {
                            showShortToast(R.string.item_meetings_err_6);
                        }
                    });
                    break;
                case INIT_SDK_FAILED:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_15);
                    break;
                case CONF_CONFLICT:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast(R.string.item_meetings_err_9);
                    break;
                case ConferenceCommon.LEAVE:
                    break;
                case ConferenceCommon.LICENSE_ERR:
                    hideLoading();
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }
                    showShortToast("终端授权点数不足，请联系管理员处理!");
                    break;
                default:
                    ErrorMessage errorMessage = new ErrorMessage(getActivity());
                    String message = errorMessage.getErrorMessageByCode(msg.what);
//                    Toast.makeText(getActivity(), message, Toast.LENGTH_LONG).show();
                    hideLoading();
                    break;
            }
        }

    };

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btn_join_conf:
                String num = numEdit.getText().toString().trim();
                num = num.replace(" ", "");

                if (num == null || "".equals(num)){
                    ToastUtil.showMessage(mContext,"会议号不能为空",2000);
                    return;
                }
                if (!StringUtil.checkInput(num, Constants.PATTERN)){
                    ToastUtil.showMessage(mContext,"用户名含有非法字符,请输入数字、字母或者下划线",2000);
                    return;
                }

                mConfId = num;

                conferenceCommon.saveMyVideoAudioSync(false, false);

                showLoading();
                if (loadingDialog != null && !loadingDialog.isShowing()){
                    loadingDialog.show();
                }
                ConferenceBean conf = conferenceCommon.isLogin(num);
                if(conf != null){
                    joinConf(conf);
                }else {
                    conf = conferenceCommon.getConferenceByID(num);
                    if (conf != null){
                        joinConf(conf);
                    }else {
                        listHandler.sendEmptyMessage(MEETINGINVALIDATE);
                    }
                }
                break;
            case R.id.btn_cancel:
                callParentView(ACTION_SHOWHOME, null);
                break;
            case R.id.btn_delete:
                String text = numEdit.getText().toString();

                if (text.length() > 0){
                    if (text.endsWith(" "))
                        numEdit.setText(text.substring(0, text.length()-1));

                    numEdit.setText(text.substring(0, text.length()-1));
                }
                break;
            case R.id.btn_0:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"0");
                break;
            case R.id.btn_1:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"1");
                break;
            case R.id.btn_2:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"2");
                break;
            case R.id.btn_3:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"3");
                break;
            case R.id.btn_4:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"4");
                break;
            case R.id.btn_5:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"5");
                break;
            case R.id.btn_6:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"6");
                break;
            case R.id.btn_7:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"7");
                break;
            case R.id.btn_8:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"8");
                break;
            case R.id.btn_9:
                if (numEdit.length() < 9)
                    numEdit.setText(numEdit.getText()+"9");
                break;
            default:
                break;
        }
    }
    //加会操作
    private void joinConf(ConferenceBean conf){
        confHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (loadingDialog != null && loadingDialog.isShowing()){
                    loadingDialog.dismiss();
                }
            }
        }, 10 * 1000);
        //清空视频路数
        ((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).clearMap();
//                                //通知是box
        if (conf.getNeedLogin() == 1 && !isLogin()) {
            ToastUtil.showMessage(getContext(),getContext().getResources().getString(R.string.no_login_text),
                    10 * 1000);
            hideLoading();
            if (loadingDialog != null && loadingDialog.isShowing()){
                loadingDialog.dismiss();
            }
        } else {
            XMLUtils.CONFIGID = conf.getId();
            XMLUtils.CONFIGNAME = conf.getName();
            XMLUtils.CONFERENCEPATTERN = conf.getConferencePattern();
            String password = conf.getConfPassword();
            if (TextUtils.isEmpty(password) || "".equals(password)) {
                joinConf(conf, false, "");
            } else {
                if (loadingDialog != null && loadingDialog.isShowing()){
                    loadingDialog.dismiss();
                }
                showJoinDialogEx(conf);
            }
        }
    }

    @Override
    public void onResume() {

        numEdit.setText("");
        //numEdit.setInputType(InputType.TYPE_NUMBER_VARIATION_PASSWORD);
        //numEdit.setRawInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_VARIATION_PASSWORD);

        super.onResume();
    }


    private void joinConf(final ConferenceBean conferenceBean, boolean isNeedPwd, String pwd) {
        showLoading();
        final String password = StringUtil.toSemiangle(pwd);
        getLoginBean(conferenceBean, password);

        //remember the conference bean
        conferenceCommon.setConfPwd(pwd);
        conferenceCommon.setConfBean(conferenceBean);

        new Thread() {
            @Override
            public void run() {
//                if (conferenceBean.getStatus().equals("1")) {
//                    joinConf(conferenceBean, getLoginBean(conferenceBean, pwd));
//                } else {
                String nickName = "";
                if (!TextUtils.isEmpty(FileUtil.readSharedPreferences(getActivity(),
                        Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME))){
                    nickName = FileUtil.readSharedPreferences(getActivity(),
                            Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME);
                }
//                    if (conferenceBean.getHostID().equals(String.valueOf(getUid()))) {
                if (nickName.equals(conferenceBean.getHostName()) || nickName.equals(conferenceBean.getCreatorName())) {
                    startConf(conferenceBean, getLoginBean(conferenceBean, password));
                }
//                    else if (!conferenceBean.getConfType().equals("2")) {//不能加入
//                        listHandler.sendEmptyMessage(MEETINGNOTJOINBEFORE);
//                    }
                else {
                    joinConf(conferenceBean, getLoginBean(conferenceBean, password));
                }
//                }
            }

            ;
        }.start();
    }

    private LoginBean getLoginBean(ConferenceBean conferenceBean, String pwd) {
        String showName = FileUtil.readSharedPreferences(getActivity(),
                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
        int userid = preferences.getInt(Constants.USER_ID, 0);
        loginBean = new LoginBean(conferenceBean.getId(), showName, pwd);
        loginBean.setType(conferenceBean.getType());
        loginBean.setUid(userid);
        return loginBean;
    }

    private void joinConf(ConferenceBean confBean, LoginBean loginBean) {
        config = conferenceCommon.initConfig(loginBean);
        if ("0".equals(config.getConfigBean().getErrorCode())) {
            listHandler.sendEmptyMessage(JOIN_CONFERENCE);
        } else {
            ((ConferenceCommonImpl) commonFactory.getConferenceCommon()).setJoinStatus(
                    ConferenceCommon.NOTJOIN);
            if ("-1".equals(config.getConfigBean().getErrorCode())) {
                if (config.getConfigBean().getErrorMessage().startsWith("0x0604003")) {
                    if (config.getConfigBean().getErrorMessage().equals("0x0604003:you should login to meeting system! ")) {
                        loginSystem();
                    } else {
                        listHandler.sendEmptyMessage(FINISH);
                    }
                }else if (config.getConfigBean().getErrorMessage().equals("0x0604006:conference password error!")){
                    Message msg = listHandler.obtainMessage(NEED_PASSWORD);
                    msg.sendToTarget();
                }else {
                    listHandler.sendEmptyMessage(LOGINFAILED);
                }
            } else if ("-2".equals(config.getConfigBean().getErrorCode())) {
                listHandler.sendEmptyMessage(FINISH);
            } else if ("-10".equals(config.getConfigBean().getErrorCode())) {
                if (Config.HAS_LIVE_SERVER) {
                    if (confBean.getType().equals(Config.MEETING)) {
                        confBean.setType(Config.LIVE);
                        joinConf(confBean, getLoginBean(confBean, loginBean.getPassword()));
                        return;
                    }
                }
                listHandler.sendEmptyMessage(MEETINGINVALIDATE);

            } else if ("-18".equals(config.getConfigBean().getErrorCode())) {
                listHandler.sendEmptyMessage(MEETINGNOTJOINBEFORE);
            } else if (ConferenceCommon.HOSt_ERROR.equals(config.getConfigBean().getErrorCode())) {
                listHandler.sendEmptyMessage(HOSTERROR);
            } else if (ConferenceCommon.SPELL_ERROR.equals(config.getConfigBean().getErrorCode())) {
                //log.info("spell error");
                listHandler.sendEmptyMessage(SPELLERROR);
            } else {
                listHandler.sendEmptyMessage(GET_ERROR_MESSAGE);
            }
        }
    }

    private void joinConference() {
        Config config = conferenceCommon.getConfig();
        if (config != null) {
            ConfigBean configBean = config.getConfigBean();
            if (configBean != null) {
                configBean.setUserInfo_m_dwStatus(ConferenceCommon.RT_STATE_RESOURCE_AUDIO);
            } //else
                //log.info("configBean is null");
        } //else
            //log.info("config is null");
        Log.e("ldy","会议信息:"+conferenceCommon.getParam());
        commonFactory.getConferenceCommon().setMeetingBox();

        boolean existCamera = ConferenceApplication.existCamera();
        boolean existMicrophone = ConferenceApplication.existMicrophone(getActivity().getApplicationContext());

        Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
        commonFactory.getConferenceCommon().setDeviceStatus(existMicrophone, existCamera);

        commonFactory.getConferenceCommon().joinConference(conferenceCommon.getParam());
    }

    private void loginSystem() {
        Message msg = listHandler.obtainMessage(NEED_LOGIN);
        msg.sendToTarget();
    }

    private void startConf(ConferenceBean confBean, LoginBean loginBean) {
        config = conferenceCommon.initConfig();
        int uid = preferences.getInt(Constants.USER_ID, 0);
        String siteId = preferences.getString(Constants.SITE_ID, "");
        String showName = preferences.getString(Constants.LOGIN_JOINNAME, "");

        result = Config.startConf(uid, showName, siteId, confBean);
        if (result.equals("-1:error")) {
            listHandler.sendEmptyMessage(CREATECONF_ERROR);
        } else {
            conferenceCommon.setLogPath(((ConferenceApplication) getActivity().getApplication()).getFilePath("hslog"));
            conferenceCommon.initSDK();
            Config config = conferenceCommon.getConfig();
            conferenceCommon.setMeetingBox();

            boolean existCamera = ConferenceApplication.existCamera();
            boolean existMicrophone = ConferenceApplication.existMicrophone(getActivity().getApplicationContext());

            Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
            commonFactory.getConferenceCommon().setDeviceStatus(existMicrophone, existCamera);

            conferenceCommon.joinConference(conferenceCommon.getParam(loginBean, true));
            Log.d("InfowareLab.Debug","会议参数:"+conferenceCommon.getParam(loginBean, true));
            config.setMyConferenceBean(confBean);
            listHandler.sendEmptyMessage(READY_JOINCONF);
        }
    }

    public void showJoinDialogEx(final ConferenceBean confBean) {

        callParentView(ACTION_CONF_PWD, confBean);
    }

    public void showJoinDialog(final ConferenceBean conferenceBean) {
        if (joinDialog == null) {
            joinDialog = new JoinDialog(getActivity(), 0);
            joinDialog.setClickListener(new JoinDialog.OnResultListener() {

                @Override
                public void doYes(String pwd) {
                    // TODO Auto-generated method stub
                    if (loadingDialog != null && !loadingDialog.isShowing()){
                        loadingDialog.setTitle("正在加会");
                        loadingDialog.show();
                    }
                    joinConf(conferenceBean, true, pwd);
                }

                @Override
                public void doNo() {
                    // TODO Auto-generated method stub
                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.hide();
                    }
                }
            });
        }
        if (joinDialog != null && !joinDialog.isShowing()) {
            joinDialog.show(conferenceBean);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (loadingDialog != null && loadingDialog.isShowing()){
            loadingDialog.dismiss();
            loadingDialog = null;
        }
    }

    public void onKeyPressed(int keyCode) {

        if (keyCode == KeyEvent.KEYCODE_0)
            mBtn0.performClick();
        else if (keyCode == KeyEvent.KEYCODE_1)
            mBtn1.performClick();
        else if (keyCode == KeyEvent.KEYCODE_2)
            mBtn2.performClick();
        else if (keyCode == KeyEvent.KEYCODE_3)
            mBtn3.performClick();
        else if (keyCode == KeyEvent.KEYCODE_4)
            mBtn4.performClick();
        else if (keyCode == KeyEvent.KEYCODE_5)
            mBtn5.performClick();
        else if (keyCode == KeyEvent.KEYCODE_6)
            mBtn6.performClick();
        else if (keyCode == KeyEvent.KEYCODE_7)
            mBtn7.performClick();
        else if (keyCode == KeyEvent.KEYCODE_8)
            mBtn8.performClick();
        else if (keyCode == KeyEvent.KEYCODE_9)
            mBtn9.performClick();
        else if (keyCode == KeyEvent.KEYCODE_DEL)
            delete.performClick();

    }
}
