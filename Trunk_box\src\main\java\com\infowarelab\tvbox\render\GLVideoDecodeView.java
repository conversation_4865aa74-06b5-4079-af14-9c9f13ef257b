
package com.infowarelab.tvbox.render;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.SurfaceTexture;
import android.hardware.Camera.CameraInfo;
import android.opengl.GLES20;
import android.opengl.GLSurfaceView;
import android.opengl.GLSurfaceView;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.Size;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.view.VideoDecodeView;
import com.infowarelabsdk.conference.callback.CallbackManager;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.LinkedList;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;

import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;

@SuppressLint("NewApi")
public class GLVideoDecodeView extends FrameLayout {

    public GLVideoDecodeView.GLRenderer renderer = null;
    private BaseFilter mCurrentFilter;
    private int mTextureId;
    private SurfaceTexture mSurfaceTexture = null;
    private Surface mSurface = null;
    private float[] mSTMatrix = new float[16];
    private  FilterFactory.FilterType type;

    private boolean isSvc = false;
    private boolean isCreated = false;

    protected static final int VIDEO_SYNC = 8888;
    protected static final int VIDEO_MYSELF = 6666;
    public static boolean isSoftDrawing = false;


    private Semaphore mSurfaceLock = new Semaphore(1);

    /**
     * surfaceView生命周期
     **/
    private SurfaceHolder holder;


    private Context activity;
    /**
     * 当前预览的channelID
     **/
    private int channelId;
    /**
     * 当前视频的用户
     **/
    private int userID;
    /**
     * 视频SDK接口
     **/
    protected CommonFactory commonFactory = CommonFactory.getInstance();
    private VideoCommonImpl videoCommon = (VideoCommonImpl) commonFactory.getVideoCommon();
    private UserCommonImpl userCommon = (UserCommonImpl) commonFactory.getUserCommon();
    private boolean isSupport = true;
    private boolean isWaiting = false;
    private GLSurfaceView surfaceView;
    private TextView userName;
    private Button deleteView;
    private LinearLayout relative;
    private TextView tvWait;
    private LinearLayout.LayoutParams cameraParames;
    private LinearLayout.LayoutParams delParames;
    private int screenW;
    private int screenH;
    private int pHeight;

    private boolean isMobile = false;
    private String TAG = "GLVideoDecodeView";

    public void setMobile(boolean mobile) {
        isMobile = mobile;
    }

    private GLVideoDecodeView.IOnDeleteAndCameraClick onDeleteAndCameraClick;

    public GLVideoDecodeView(Context context) {
        super(context);
        initParames(context, 0, 0, 0, 0);
    }

    public GLVideoDecodeView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.activity = context;
        initParames(context, 0, 0, 0, 0);
    }

    public GLVideoDecodeView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        this.activity = context;
        initParames(context, 0, 0, 0, 0);
    }

    /**
     * 初始化布局
     * 默认字体12sp,white。
     */
    private void initParames(Context context, int textSizeSp, int textColor, int cameraSrc, int deleteSrc) {

//		int txtSize = textSizeSp>0?textSizeSp:12;
//
//		int txtColor = textSizeSp!=0?textSizeSp:Color.WHITE;//0是透明我们取白色
//
//
//		int draDelete = deleteSrc!=0?deleteSrc:android.R.drawable.ic_menu_close_clear_cancel;
//
//	    //**用户名设置**//
//        userName = new TextView(context);
//        userName.setTextSize(TypedValue.COMPLEX_UNIT_SP,txtSize);
//        userName.setTextColor(txtColor);
//        userName.setBackgroundColor(Color.TRANSPARENT);
//        userName.setGravity(Gravity.CENTER);
//        userName.setLayoutParams(new LayoutParams(LayoutParams.WRAP_CONTENT,
//                LayoutParams.WRAP_CONTENT,Gravity.BOTTOM));
//        //**用户名设置**//
//
//        //**在右上角加入相机图标和删除图标**//
//        relative = new LinearLayout(context);
//        relative.setLayoutParams(new LayoutParams(LayoutParams.WRAP_CONTENT,
//                LayoutParams.WRAP_CONTENT,Gravity.RIGHT|Gravity.TOP));
//
//
//        delParames = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
//        		LayoutParams.WRAP_CONTENT);
//        deleteView = new Button(context);
//		deleteView.setLayoutParams(delParames);
//        deleteView.setBackgroundResource(draDelete);
//        deleteView.setVisibility(View.GONE);
//        relative.addView(deleteView);
//        //**在右上角加入相机图标和删除图标**//
//
//		addView(userName);
//		addView(relative);
//
//		this.deleteView.setOnClickListener(new OnClickListener() {
//			@Override
//			public void onClick(View v) {
//				clickDelete();
//			}
//		});
    }

    private PaintFlagsDrawFilter pfdf = new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG);
    private ByteBuffer buffer;
    private Bitmap videoBit;
    private int bitWidth = 0;
    private int bitHeight = 0;

    /**
     * 重置视频分辨率
     *
     * @param width
     * @param height
     */
    public void resetSize(int width, int height) {
        if (!videoCommon.isHardDecode()) {
            dataQueue.clear();
        }
        bitWidth = width;
        bitHeight = height;
        if (bitWidth > 0 && bitHeight > 0) {
            videoBit = Bitmap.createBitmap(width, height, Config.RGB_565);
        }
        if (bitHeight * bitWidth > 1024 * 768 && width > 0 && !videoCommon.isHardDecode()) {
            setBackgroundResource(R.drawable.bg_video_nosupport);
            isSupport = false;
        } else {
            setBackgroundResource(0);
            isSupport = true;
        }
//		setParams(screenW, screenH);
        setSurface(width, height);
        invalidate();
    }

    public void setSurface(int w, int h) {
        Log.d(TAG, "GLVideoDecodeView.setSurface: " + w + "x" + h);
        if (screenH != 0 && screenW != 0 && surfaceView != null) {
            Log.d(TAG, "GLVideoDecodeView.setSurface: isMobile=" + isMobile);
            if ((w > 0 && h > 0 && isMobile) || (h > w)){
                Log.d(TAG, "GLVideoDecodeView.setSurface: 等比例缩放");
                //等比例缩放
                LayoutParams p = (LayoutParams) surfaceView.getLayoutParams();
                float ss = 1f * screenH / screenW;
                float vs = 1f * h / w;
                if (ss == vs) {
                    p.width = screenW;
                    p.height = screenH;
                } else if (ss > vs) {
                    if (Build.MODEL.indexOf("HDX2") != -1){
                        p.width = (int)(screenW * 0.8);
                        p.height = (int) (screenW * 0.8 * vs);
                    }else {
                        p.width = screenW;
                        p.height = (int) (screenW * vs);
                    }
                } else {
                    p.height = screenH;
                    p.width = (int) (1f * screenH / vs);
                }
                surfaceView.setLayoutParams(p);
            } else {
                //Log.d(TAG, "GLVideoDecodeView.setSurface: 拉满");
                //拉满
                LayoutParams p = (LayoutParams) surfaceView.getLayoutParams();
                p.width = LayoutParams.MATCH_PARENT;
                p.height = LayoutParams.MATCH_PARENT;
                surfaceView.setLayoutParams(p);
            }
        }
    }

    /**
     * 视频接收/停止接收
     *
     * @param channelID
     * @param isOpen
     */
    public void changeStatus(int channelID, boolean isOpen) {

        Log.d(TAG, "GLVideoDecodeView.changeStatus:" + channelID + ":" + isOpen);

        if (videoCommon.isHardDecode()) {
            if (isOpen) {
                this.channelId = channelID;
                if (null != surfaceView) {
                    Log.d(TAG, "GLVideoDecodeView.changeStatus: Removed old surfaceView");
                    //if (mSurface != null) mSurface.release();
                    //surfaceView.setRenderer(null);

                    if (mSurface != null){
                        mSurface.release();
                        mSurface = null;
                    }

                    try {
                        mSurfaceTexture.releaseTexImage();
                        mSurfaceTexture.release();
                        mSurfaceTexture = null;
                    }
                    catch (java.lang.IllegalStateException e)
                    {
                        mSurfaceTexture.release();
                        mSurfaceTexture = null;
                    }

                    removeView(surfaceView);
                    surfaceView = null;
                    removeNameCamera();
                }
                surfaceView = new GLSurfaceView(this.activity);
                surfaceView.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT,
                        LayoutParams.MATCH_PARENT, Gravity.CENTER));
                //holder = surfaceView.getHolder();
                //holder.addCallback(this);

                Log.d(TAG, "GLVideoDecodeView.addView: surfaceView");
                addView(surfaceView);

                surfaceView.setEGLContextClientVersion(2);

                type = FilterFactory.FilterType.Decode;

                Log.d(TAG, "VideoDecoderView.create new GLRenderer");
                renderer = new GLVideoDecodeView.GLRenderer(surfaceView,type);
                surfaceView.setRenderer(renderer);
                surfaceView.setRenderMode(surfaceView.RENDERMODE_WHEN_DIRTY);

                surfaceView.setZOrderOnTop(true);
                surfaceView.setZOrderMediaOverlay(true);
                setTag(channelID);
                showWait();
                addName();
            } else {
                if (this.channelId != 0) {
                    videoCommon.closeVideo(this.channelId);
                }

                if (null != surfaceView) {
                    Log.d(TAG, "GLVideoDecoderView.destroy the surfaceView: " + channelId);

                    //mSurfaceLock.acquire();
                    if (mSurface != null){
                        mSurface.release();
                        mSurface = null;
                    }

                    if (mSurfaceTexture != null){

                        try {
                            mSurfaceTexture.releaseTexImage();
                            mSurfaceTexture.release();
                            mSurfaceTexture = null;
                        }
                        catch (java.lang.IllegalStateException e)
                        {
                            mSurfaceTexture.release();
                            mSurfaceTexture = null;
                        }
                    }

                    //mSurfaceTexture = null;
                    removeView(surfaceView);
                    surfaceView = null;
                    removeNameCamera();
                }

                this.channelId = 0;
            }
        } else {
            if (isOpen) {
                this.channelId = channelID;
                if (null != surfaceView) {
                    removeNameCamera();
                    // 别人
                    setTag(channelID);
                    showWait();
                    addName();
                    if (videoCommon.getDeviceMap().containsKey(channelId)) {
                        this.userID = videoCommon.getDeviceMap().get(
                                channelId);
                        setUserName();
                    }
                    HandlerThread surfaceThread = new HandlerThread(
                            "SurfaceHandler");
                    surfaceThread.start();
                    videoCommon.openVideo(channelId, mSurface);
                    videoCommon.addSurfaceHanlder(surfaceThread,
                            new GLVideoDecodeView.SurfaceHandler(surfaceThread.getLooper()),
                            channelId);
                    dataQueue.clear();
                } else {
                    surfaceView = new GLSurfaceView(this.activity);

                    addView(surfaceView);

                    surfaceView.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT,
                            LayoutParams.MATCH_PARENT, Gravity.CENTER));

                    surfaceView.setEGLContextClientVersion(2);
                    //holder = surface.getHolder();
                    //holder.addCallback(this);

                    type = FilterFactory.FilterType.Original;
                    renderer = new GLVideoDecodeView.GLRenderer(surfaceView,type);
                    surfaceView.setRenderer(renderer);
                    surfaceView.setRenderMode(surfaceView.RENDERMODE_WHEN_DIRTY);

                    //surfaceView.setZOrderMediaOverlay(false);
                    surfaceView.setZOrderOnTop(true);
                    surfaceView.setZOrderMediaOverlay(true);
                    setTag(channelID);
                    showWait();
                    addName();
                }
            } else {
                videoCommon.closeVideo(channelID);
            }
        }
    }

    private void showWait() {
        this.isWaiting = true;
        if (null != surfaceView)
        {
            //surfaceView.setBackgroundColor(android.R.color.white);
            surfaceView.setBackgroundResource(R.drawable.bg_sharedt_wait_black);
        }

    }

    public void showSupport(boolean isSupport) {
        this.isSupport = isSupport;
        showSupport();
    }

    private void showSupport() {
        if (isSupport) {
            if (isWaiting) {
                showWait();
            } else {
                showSurface();
            }
        } else {
//			surfaceView.setBackgroundResource(R.drawable.bg_nosupport);
        }
    }

    public void showSupportReady() {
        this.isWaiting = false;
        if (isSupport) {
            showSurface();
        } else {
//			surfaceView.setBackgroundResource(R.drawable.bg_nosupport);
        }
    }

    private void showSurface() {
        if (surfaceView != null)
            surfaceView.setBackgroundColor(0);
    }

    private void addName() {
        int txtSize = 12;
        int txtColor = Color.WHITE;// 0是透明我们取白色
        // **用户名设置**//
        userName = new TextView(activity);
        userName.setTextSize(TypedValue.COMPLEX_UNIT_SP, txtSize);
        userName.setTextColor(txtColor);
        userName.setBackgroundColor(Color.TRANSPARENT);
        userName.setGravity(Gravity.CENTER);
        LayoutParams lp = new LayoutParams(LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT, Gravity.BOTTOM);
        lp.leftMargin = 10;
        lp.bottomMargin = 10
//				+getResources().getDimensionPixelSize(R.dimen.view_inconf_bottom_height)
        ;
        userName.setLayoutParams(lp);
        addView(userName);
        userName.setVisibility(View.GONE);
    }
    private void removeNameCamera() {
        if (userName != null) {
            removeView(userName);
            userName = null;
        }
        if (relative != null) {
            removeView(relative);
            relative = null;
        }
    }
    /*
    @Override
    public void surfaceViewCreated(SurfaceHolder holder) {
        screenW = this.getWidth();
        screenH = this.getHeight();
        if (videoCommon.isHardDecode()) {
            if (isSvc) {
                DisplayMetrics dm = getResources().getDisplayMetrics();
                videoCommon.changeSvcLevel(channelId, 1, 1.0f * screenW / dm.xdpi, 1.0f * screenH / dm.ydpi);
//                videoCommon.changeSvcLevel(channelId, 1, screenW/2, screenH/2);
            }
            if (videoCommon.getDeviceMap().containsKey(channelId)) {
                this.userID = videoCommon.getDeviceMap().get(channelId);
                setUserName();
            }
            videoCommon.openVideo(channelId, surfaceView);
        } else {
            if (videoCommon.getDeviceMap().containsKey(channelId)) {
                this.userID = videoCommon.getDeviceMap().get(channelId);
                setUserName();
            }
            HandlerThread surfaceViewThread = new HandlerThread(
                    "SurfaceHandler");
            surfaceViewThread.start();
            videoCommon.openVideo(channelId, surfaceView);
            videoCommon.addsurfaceViewHanlder(surfaceViewThread,
                    new GLVideoDecodeView.SurfaceHandler(surfaceViewThread.getLooper()),
                    channelId);
            drawImage = null;
        }
        isCreated = true;
    }

    @Override
    public void surfaceViewChanged(SurfaceViewHolder holder, int format, int width,
                               int height) {

        Log.d("InfowareLab.Debug","GLVideoDecodeView.surfaceViewChanged: " + width + "x" + height);

        screenW = this.getWidth();
        screenH = this.getHeight();
//		if (isSvc) {
//			DisplayMetrics dm = getResources().getDisplayMetrics();
//			videoCommon.changeSvcLevel(channelId, 0, 1.0f * screenW / dm.xdpi, 1.0f * screenH / dm.ydpi);
//		}
        this.holder = holder;
    }

    @Override
    public void surfaceViewDestroyed(SurfaceViewHolder holder) {
        isDrawing = false;
        videoCommon.closeVideo(channelId);
    }
    */

    private boolean isUnlock4release = false;

    public void unlock2release() {
        if (videoCommon.isHardDecode()) {
            if (drawImage != null) {
                isDrawing = false;
                isUnlock4release = true;
            } else {
                Message m = videoCommon.getHandler().obtainMessage(9999);
                videoCommon.getHandler().sendMessage(m);
            }
        } else {
            Message m = videoCommon.getHandler().obtainMessage(9999);
            videoCommon.getHandler().sendMessage(m);
        }
    }

    class SurfaceHandler extends Handler {
        public SurfaceHandler(Looper looper) {
            super(looper);
        }
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case VIDEO_SYNC:
                    if (!isSupport) return;
                    try {
                        dataQueue.put((byte[]) msg.obj);
                        if (drawImage == null) {
                            isDrawing = true;
                            isWatching = true;
                            drawImage = new GLVideoDecodeView.DrawImage();
                            drawImage.start();
                        }
                    } catch (InterruptedException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                    break;
            }
            super.handleMessage(msg);
        }
    }
    private LinkedBlockingQueue<byte[]> dataQueue = new LinkedBlockingQueue<byte[]>(8);
    private GLVideoDecodeView.DrawImage drawImage = null;
    /**
     * 是否允许绘图线程
     */
    private boolean isDrawing = false;
    /**
     * 是否处于视频界面
     */
    private boolean isWatching = false;
    private Canvas canvas1;

    class DrawImage extends Thread {
        public void run() {
            while (dataQueue != null && isDrawing) {
                if (dataQueue.size() > 5) {
                    dataQueue.poll();
                } else {
                    try {
                        if (!dataQueue.isEmpty() && isWatching) {
                            isSoftDrawing = true;
                            canvas1 = holder.lockCanvas();  // 获取画布
                            byte[] bitBuffer = dataQueue.take();
                            if (null == canvas1 || null == bitBuffer) {
                                continue;
                            }
                            startSyncDraw(canvas1, bitBuffer);
                            holder.unlockCanvasAndPost(canvas1);  // 解锁画布，提交图像
                            isSoftDrawing = false;
                        }
                        sleep(30);
                    } catch (InterruptedException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }
        }
    }
    /**
     * 填充视频画面
     *
     * @param canvas2
     * @param data
     */
    protected void startSyncDraw(Canvas canvas2, byte[] data) {
//	    canvas2.setDrawFilter(pfdf); //抗锯齿
        buffer = ByteBuffer.wrap(data);
        try {
            videoBit.copyPixelsFromBuffer(buffer);
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        Matrix m = new Matrix();
        float scaleX, scaleY;
        int xOff = 0;
        scaleX = (float) screenW / bitWidth;
        scaleY = (float) screenH / bitHeight;
        m.postScale(scaleX, scaleY);
        m.postTranslate(xOff, 0);
        drawPartBitmap(canvas2, scaleX, scaleY);
    }

    /**
     * 填充画面
     * scaleX < scaleY 显示完整优先
     * scaleX > scaleY 充满屏幕优先
     *
     * @param canvas
     * @param scaleX
     * @param scaleY
     */
    private void drawPartBitmap(Canvas canvas, float scaleX, float scaleY) {
        Rect src = new Rect();
        RectF dst = new RectF();
        int x1 = 0, y1 = 0, x2 = 0, y2 = 0;
        if (isSvc) {
            x1 = 0;
            y1 = 0;
            x2 = bitWidth;
            y2 = bitHeight;
            src.set(x1, y1, x2, y2);
            dst.set(0, 0, surfaceView.getWidth(), surfaceView.getHeight());
        } else {
            if (scaleX > scaleY) {
                x1 = 0;
                x2 = bitWidth;
                y1 = (int) ((bitHeight * scaleX - screenH) / (2 * scaleX));
                y2 = bitHeight - y1;
            } else {
                y1 = 0;
                y2 = bitHeight;
                x1 = (int) ((bitWidth * scaleY - screenW) / (2 * scaleY));
                x2 = videoBit.getWidth() - x1;
            }
            src.set(x1, y1, x2, y2);
            dst.set(0, 0, screenW, screenH);
        }
        canvas.drawBitmap(videoBit, src, dst, null);
    }


    public void stopDrawThread() {
        isDrawing = false;
    }

    public void changeWatchingState(boolean isWatching) {
        this.isWatching = isWatching;
    }


    /**
     * 设置宽高
     *
     * @param width
     * @param height
     */
    public void setParams(int width, int height) {
        screenW = width;
        screenH = height;
    }

    public void setUserName() {

        String name = userCommon.getUser(this.userID).getUsername();
        if (this.userID == userCommon.getSelf().getUid())
            name = "[我]" + name;
        if (userName != null)
            userName.setText(name);
    }

    public void setChannelId(int channelId) {
        this.channelId = channelId;
        this.deleteView.setTag(channelId);
    }

    public int getUserID() {
        return userID;
    }

    public int getChannelId() {
        return channelId;
    }

    public Surface getSurface() {
        return mSurface;
    }

    public interface IOnDeleteAndCameraClick {
        public void onClickDelete();

        public void onClickCamera();
    }

    public void setOnDeleteAndCameraClick(GLVideoDecodeView.IOnDeleteAndCameraClick onclick) {
        this.onDeleteAndCameraClick = onclick;
    }

    private void clickDelete() {
        if (this.onDeleteAndCameraClick != null) onDeleteAndCameraClick.onClickDelete();
    }

    private void clickCamera() {
        if (this.onDeleteAndCameraClick != null) onDeleteAndCameraClick.onClickCamera();
    }

    public void show() {
        setVisibility(View.VISIBLE);
    }

    public void hide() {
        setVisibility(View.GONE);
    }

//    public void changeSize(int w, int h) {
//        if (screenW != w || screenH != h) {
//            screenW = w;
//            screenH = h;
//            if (isSvc) {
//                DisplayMetrics dm = getResources().getDisplayMetrics();
////                videoCommon.changeSvcLevel(channelId, 0, 1.0f * screenW / dm.xdpi, 1.0f * screenH / dm.ydpi);
//                videoCommon.changeSvcLevel(channelId, 1, screenW/2, screenH/2);
//            }
//            setSurface(bitWidth, bitHeight);
//            invalidate();
//        }
//
//    }

    public void setCurSVCLvl(int w, int h) {
        if (screenW != w || screenH != h) {
            screenW = w;
            screenH = h;
            if (isSvc) {
                DisplayMetrics dm = getResources().getDisplayMetrics();
                videoCommon.changeSvcLevel(channelId, 1, 1.0f * screenW / dm.xdpi, 1.0f * screenH / dm.ydpi);
//                videoCommon.changeSvcLevel(channelId, 1, screenW/2, screenH/2);
            }
            if (surfaceView != null && isCreated) {
                setSurface(bitWidth, bitHeight);
            }
        }
    }

//    public void setwh11() {
//        screenW = 1;
//        screenH = 1;
//        if (surfaceView != null && isCreated) {
//            setSurface(0, 0);
//        }
//    }

    public void setSvc(boolean svc) {
        this.isSvc = svc;
    }

    public class GLRenderer implements GLSurfaceView.Renderer, SurfaceTexture.OnFrameAvailableListener {

        GLSurfaceView surfaceView;

        public GLRenderer(GLSurfaceView surfaceView, FilterFactory.FilterType type) {

            this.surfaceView = surfaceView;

            mCurrentFilter = FilterFactory.createFilter(activity,type);

        }

        @Override
        public void onSurfaceCreated(GL10 gl, EGLConfig config) {

            Log.d(TAG,"GLVideoDecodeView.onSurfaceCreated");
        }

        @Override
        public void onSurfaceChanged(GL10 gl, int width, int height) {

            Log.d(TAG,"GLVideoDecodeView.onSurfaceChanged: " + width + "x" + height);

            if (!isCreated){
                screenW=width;
                screenH=height;

                mCurrentFilter.createProgram();
                mCurrentFilter.onInputSizeChanged(width,height);

                mTextureId = BaseFilter.bindTexture();

                //Log.d(TAG,"GLVideoDecodeView.bindTexture = " + mTextureId);

                if (mSurfaceTexture != null){
                    mSurfaceTexture.releaseTexImage();
                    mSurfaceTexture.release();
                }

                if (mSurface != null){
                    mSurface.release();
                }

                mSurfaceTexture = new SurfaceTexture(mTextureId);
                mSurface = new Surface(mSurfaceTexture);
                mSurfaceTexture.setOnFrameAvailableListener(this);

                if (videoCommon.isHardDecode()) {
                    if (videoCommon.getDeviceMap().containsKey(channelId)) {
                        userID = videoCommon.getDeviceMap().get(channelId);
                        surfaceView.post(new Runnable() {
                            @Override
                            public void run() {
                                setUserName();
                            }
                        });
                    }
                    Log.d(TAG,"videoCommon.openVideo = " + channelId);
                    videoCommon.openVideo(channelId, mSurface);
                } else {
                    if (videoCommon.getDeviceMap().containsKey(channelId)) {
                        userID = videoCommon.getDeviceMap().get(channelId);
                        surfaceView.post(new Runnable() {
                            @Override
                            public void run() {
                                setUserName();
                            }
                        });
                    }
                    HandlerThread surfaceThread = new HandlerThread(
                            "SurfaceHandler");
                    surfaceThread.start();
                    videoCommon.openVideo(channelId, mSurface);
                    videoCommon.addSurfaceHanlder(surfaceThread,
                            new SurfaceHandler(surfaceThread.getLooper()),
                            channelId);
                    drawImage = null;
                }

                isCreated = true;
            }

            if (isCreated)
                GLES20.glViewport(0, 0, width, height);

            if (isSvc) {
                //DisplayMetrics dm = getResources().getDisplayMetrics();
//                double width = 1.0f * screenW / dm.xdpi;
//                double height = 1.0f * screenH / dm.ydpi;

                double width1 = 1.0f * screenW;
                double height1 = 1.0f * screenH;

                //width = 1280;
                //height = 720;

                Log.d("InfowareLab.Debug", ">>>>>>GLVideoDecodeView.setCurSVCLvl: width=" + width + "x" + height);
                videoCommon.changeSvcLevel(channelId, 1, width1, height1);

            }

 //           mCurrentFilter.onInputSizeChanged(width,height);

            //mCameraHelper.openCamera(Camera.CameraInfo.CAMERA_FACING_BACK);
//			mCurrentFilter.createProgram();
//			mCurrentFilter.onInputSizeChanged(getWidth(),getHeight());
//
//			mTextureId = BaseFilter.bindTexture();
//			mSurfaceTexture = new SurfaceTexture(mTextureId);
//			mSurface = new Surface(mSurfaceTexture);
//			mSurfaceTexture.setOnFrameAvailableListener(this);

//            RectF surfaceDimensions = new RectF(0,0,width,height);
//            RectF previewRect = new RectF(0, 0, cameraHeight, cameraWidth);
//            Matrix matrix = new Matrix();
//            matrix.setRectToRect(previewRect, surfaceDimensions, Matrix.ScaleToFit.FILL);
//            mSurfaceTexture.getTransformMatrix(mSTMatrix);

            //mCameraHelper.startPreview(mSurfaceTexture);
        }

        /**
         * 关于预览出现镜像，旋转等问题，有两种方案:
         * 1.在相机预览的地方进行调整
         * 2.通过opengl的矩阵变换在绘制的时候进行调整
         * 这里我采用了前者
         */

        @Override
        public void onDrawFrame(GL10 gl) {

            //runAll(runOnDraw);
            if (mSurfaceTexture == null || mCurrentFilter == null) return;

            try {
                if (mSurfaceTexture != null)
                    mSurfaceTexture.updateTexImage();

                if (mSurfaceTexture != null)
                    mSurfaceTexture.getTransformMatrix(mSTMatrix);

                if (mCurrentFilter != null)
                    mCurrentFilter.draw(mTextureId, mSTMatrix);
            }
            catch (java.lang.RuntimeException e){

                Log.d(TAG,"GLVideoDecodeView.onDrawFrame: java.lang.RuntimeException");
            }

        }

        @Override
        public void onFrameAvailable(SurfaceTexture surfaceTexture) {
            //Log.d(TAG,"GLVideoDecodeView.onFrameAvailable");

            if (surfaceTexture == null || surfaceView == null) return;

            if (isWaiting == true){
                showSupportReady();
                isWaiting = false;
            }

            surfaceView.requestRender();
        }

    }
}
