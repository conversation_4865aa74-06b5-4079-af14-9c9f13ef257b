package com.infowarelab.tvbox.utils;

import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioRecord;
import android.media.AudioTrack;
import android.media.MediaRecorder;
import android.os.AsyncTask;
import android.util.Log;

import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;


/**
 * Created by Lemon on 2017/2/23.
 */

public class AudioRecordUtils {
    public static final String TAG = "MRCam/AudioRecUtils";

    private int audioSource = MediaRecorder.AudioSource.MIC;
    // 设置音频采样率，44100是目前的标准，但是某些设备仍然支持22050,16000,11025
    private final int sampleRateInHz = 32000;
    // 设置音频的录制的声道CHANNEL_IN_STEREO为双声道，CHANNEL_CONFIGURATION_MONO为单声道
    private final int channelConfig = AudioFormat.CHANNEL_IN_STEREO;
    // 音频数据格式:PCM 16位每个样本。保证设备支持。PCM 8位每个样本。不一定能得到设备支持。
    private final int audioFormat = AudioFormat.ENCODING_PCM_16BIT;

    private int inBufSize = 0;

    private AudioRecord audioRecord = null;
    private AudioTrack mAudioTrack = null;


    private boolean isRecord = false;

    /**
     * 是否可以录音 true 可以录音
     */
    private boolean recordEnable = false;
    /**
     * 是否可以写音频文件 true 可以写
     */
    private boolean isWriteFile = false;
    private File _fr = null;
    private FileOutputStream _out = null;

    public AudioRecordUtils(int source){
        //initAudioRecord();
        audioSource = source;
    }
    /**
     * 初始化对象
     */
    private boolean initAudioRecord(){

        inBufSize = AudioRecord.getMinBufferSize(
                sampleRateInHz,
                channelConfig,
                audioFormat);
        if (inBufSize < 0)
            return false;

        audioRecord  = new AudioRecord(
                audioSource,
                sampleRateInHz,
                channelConfig,
                audioFormat,
                inBufSize * 10);

        // 获得音轨对象
        mAudioTrack = new AudioTrack(
                AudioManager.STREAM_MUSIC, sampleRateInHz,
                channelConfig,
                    audioFormat, inBufSize,
                AudioTrack.MODE_STREAM);
        return true;
    }

    /**
     * 开始录音
     */
    public void startRecord(){
        new AudioRecordTask().execute();
    }

    /**
     * 暂停录音
     */
    public void pauseRecord(){
        isRecord = false;
    }


    class AudioRecordTask extends AsyncTask<Void, Void, Void> {

        @Override
        protected Void doInBackground(Void... params) {
            // TODO Auto-generated method stub
            if(audioRecord == null){
                if (!initAudioRecord()) {
                    Log.e(TAG, "Init Audio Recorder failed! getMinBufferSize failed!");
                    return null;
                }
                Log.d(TAG, "Init Audio Recorder");
            }
            byte[] b = new byte[inBufSize/4];
            //开始录制音频
            try{
                // 防止某些手机崩溃，例如联想
                audioRecord.startRecording();
                // 设置喇叭音量
                mAudioTrack.setStereoVolume(1.0f, 1.0f);
                // 开始播放声音
                mAudioTrack.play();
            }catch (IllegalStateException e){
                e.printStackTrace();
            }

            //判断是否正在录制
            isRecord = true;
            long wait = 0;
            long maxWait = 10;
            while(isRecord){
                //r是实际读取的数据长度，一般而言r会小于buffersize
                int r = audioRecord.read(b, 0, b.length);
                long v = 0;
                // 将 buffer 内容取出，进行平方和运算
                for (int i = 0; i < b.length; i++) {
                    v += b[i] * b[i];
                }
                // 平方和除以数据总长度，得到音量大小。
                double mean = v / (double) r;
                double volume = 10 * Math.log10(mean);

                wait++;
                if(wait > maxWait){
                    wait = 0;
                    if(volume > 0){
                        recordEnable = true;
                    }
                }
                mAudioTrack.write(b, 0, b.length);
                if (isWriteFile) {
                    if (_fr == null || _out == null){
                        _fr = new File("/sdcard/audio.wav");
                        try {
                            _out = new FileOutputStream(_fr);
                        } catch (FileNotFoundException e) {
                            // TODO Auto-generated catch block
                            e.printStackTrace();
                        }
                    }
                    try {
                        _out.write(b);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).sendAudioData(b,b.length,0);
            }
            //停止录制
            try {
                // 防止某些手机崩溃，例如联想
                audioRecord.stop();
                // 彻底释放资源
                audioRecord.release();
                audioRecord = null;
                mAudioTrack.stop();
                mAudioTrack.release();
                mAudioTrack = null;
                Log.d(TAG, "Audio recorder release");
            }catch (IllegalStateException e){
                e.printStackTrace();
            }
            return null;
        }
    }

}
