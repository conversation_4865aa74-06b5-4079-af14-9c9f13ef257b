package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.graphics.PointF;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.action.AnnotationAction;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.view.DocView;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.confctrl.UserCommon;
import com.infowarelabsdk.conference.domain.AnnotationBean;
import com.infowarelabsdk.conference.domain.DocBean;
import com.infowarelabsdk.conference.shareDoc.DocCommon;

/**
 * Created by Always on 2018/11/6.
 */
//TODO 文档
@SuppressLint("ValidFragment")
@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
public class FragDs extends BaseFragment implements View.OnTouchListener, View.OnClickListener {

    private TextView noText;

    private View dsView;
    private DocView docView;
    private TextView tvPage;
    private DocCommonImpl docCommon;
    private DocBean curDoc;
    //上下翻页]
    /** 开始移动的点 */
    private PointF start = new PointF();
    /** 中心点 */
    private PointF mid = new PointF();
    private float oldDist = 1f;
    private boolean isDoPointUp = false;

    //注释
    private RelativeLayout rlAnnotation;
    int screenWidth, screenHeight;
    int lastX, lastY;
    int lastTop, lastLeft;
    int startX, startY;
    private AnnotationAction annotationAction;
    private ImageButton ibAnnoPull;
    private ImageView leftBtn,rightBtn;
    private Handler docHandler = null;

    public FragDs(ICallParentView iCallParentView) {
        super(iCallParentView);
    }

    public FragDs(){
        super();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        dsView = inflater.inflate(R.layout.frag_inconf_doc, container, false);
        docCommon = (DocCommonImpl) CommonFactory.getInstance().getDocCommon();
        initView();
        initData();
        initConfData();
        return dsView;
    }

    private void initData() {
        initAnnoationAction();
    }

    private void initView() {

        noText = (TextView)dsView.findViewById(R.id.frag_inconf_doc_noText);
        docView = (DocView) dsView.findViewById(R.id.frag_doc_doc);
        tvPage = (TextView) dsView.findViewById(R.id.frag_doc_tv_page);
        ibAnnoPull = (ImageButton) dsView.findViewById(R.id.ibDrag); // 收起注释工具栏
        rlAnnotation = (RelativeLayout) dsView
                .findViewById(R.id.view_inconf_share_annotation);

        leftBtn = (ImageView)dsView.findViewById(R.id.frag_inconf_doc_leftBtn);
        rightBtn = (ImageView)dsView.findViewById(R.id.frag_inconf_doc_rightBtn);

        leftBtn.setOnClickListener(this);
        rightBtn.setOnClickListener(this);

        docView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        setFilesLayout(true);
        if (docCommon.getCurrentDoc() != null){
            curDoc = docCommon.getCurrentDoc();
            showDoc(null,curDoc);
        }
    }

    public void initDocHandler() {

        if (docHandler == null)
        docHandler = new Handler() {

            @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
            @Override
            public void handleMessage(Message msg) {
                if (getActivity() == null)
                    return;
                if (ActConf.mActivity != null){
                    ActConf.mActivity.update();
                }
                switch (msg.what) {
                    case DocCommon.Doc_OPEN:
                        break;
                    case DocCommon.DOC_SHOW:
                        showDoc(msg, null);
                        if (ActConf.mActivity != null){
                            ActConf.mActivity.setViewHandler.removeCallbacksAndMessages(null);
                            ActConf.mActivity.setViewHandler.sendEmptyMessage(0);
                        }
                        break;
                    case DocCommon.DOC_CLOSE:
                        if (docCommon.getDocMap().size() < 1) {
                            closeDoc();
                        }else {
                            setFilesLayout(true);
                        }
                        break;
                    case DocCommon.NEW_PAGE:
                        break;
                    case DocCommon.DOC_DISMISS_SHAREBUTTON:
                        break;
                    case DocCommon.DOC_SHOW_SHAREBUTTON:
                        break;
                    case DocCommon.ANNOTATION_OPT_TYPE_ADD:
                        docView.refresh();
                        AnnotationBean annotation = (AnnotationBean) msg.obj;
                        docView.addAnnotation(annotation);
                        if (annotation != null
                                && annotation.isPointerAnnt()
                                && annotation.getUserId() == ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf()
                                .getUid()
                                && docCommon.getDoc(annotation.getDocID()) != null) {// 可能有在传标注消息时关闭了该标注文档的情况
                            docCommon.getDoc(annotation.getDocID()).getPage()
                                    .setPreAnnotation(annotation);
                        }
                        break;
                    case DocCommon.ANNOTATION_OPT_TYPE_DEL:
                        docView.refresh();
                        docView.removeAnnotation((Integer) msg.obj);
                        break;
                    case DocCommon.DOC_ANNOTATION_BAR_SHOW:
                        if (docCommon.getDocMap() != null && !docCommon.getDocMap().isEmpty()) {
                            enableAnnotation();
                        }
                        break;
                    case DocCommon.DOC_ANNOTATION_BAR_HIDDEN:
                        break;
                    case DocCommon.DOC_DISMISS_SEEKBAR:
                        break;
                    case DocCommon.DOC_PAGE_MOVE:
                        docView.postMove(msg.arg1, msg.arg2);
                        break;
                }
            }

        };

        if (docCommon != null) docCommon.setHandler(docHandler);
        if (docView != null) docView.setHandler(docHandler);
    }

    public void showDoc(Message msg,DocBean docbean) {
        if (msg != null) {
            curDoc = docCommon.getDoc((Integer) msg.obj);
        } else {
            curDoc = docbean;
            docCommon.setCurrentDoc(curDoc);
        }
        if (curDoc == null) return;
        if (docView.getWidth() < 10) {
            callParentView(ACTION_DSSHARED, null);
            return;
        }
        if (curDoc.getPage() != null && curDoc.getPageCount() > 1) {
            tvPage.setText(curDoc.getPage().getPageID() + "/" + curDoc.getPageCount());
            tvPage.setVisibility(View.VISIBLE);
        } else {
            tvPage.setText("1/1");
            tvPage.setVisibility(View.GONE);
        }
        docView.setDoc(curDoc, 0, 0, 0);
        docCommon.setStartToShowPage(false);
        setFilesLayout(true);
    }

    public void switchDoc(int docId) {
        if (docCommon != null){
            docCommon.switchDoc(docId);
        }
    }
    private void closeDoc() {
        curDoc = null;
        setFilesLayout(false);
        docCommon.setCurrentDoc(null);
        if (docView.getWidth() > 10) callParentView(ACTION_DSSHARED, null);
    }
    public boolean isEmpty() {
        if (docCommon != null && docCommon.getCurrentDoc() != null){
            if (rlAnnotation != null){
                rlAnnotation.setVisibility(View.VISIBLE);
            }
            return docCommon.getCurrentDoc() == null;
        }else {
            if (rlAnnotation != null){
                rlAnnotation.setVisibility(View.GONE);
            }
            return curDoc == null;
        }
    }

    public void doSetView() {
        setViewHandler.sendEmptyMessage(0);
    }
    Handler setViewHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {
            if (docView.getWidth() > 10 && curDoc != null) {
                if (curDoc.getPage() != null && curDoc.getPageCount() > 1) {
                    tvPage.setText(curDoc.getPage().getPageID() + "/" + curDoc.getPageCount());
                    tvPage.setVisibility(View.VISIBLE);
                } else {
                    tvPage.setText("1/1");
                    tvPage.setVisibility(View.GONE);
                }

                docView.setDoc(curDoc, 0, 0, 0);
                docCommon.setStartToShowPage(false);
            } else if (!docCommon.getDocMap().isEmpty() && docCommon.getCurrentDoc() != null) {
                docCommon.switchDoc(docCommon.getCurrentDoc().getDocID());
            }
        }
    };
    //往上翻页
    public void upPage(){
        if(curDoc.getPage() != null
                && docCommon.getPageCount(curDoc.getDocID()) >1){
            if (curDoc.getPage().getStepCount()>0) {
                if(curDoc.getPage().getCurrentStep()==0){
                    if (curDoc.getPage().getPageID() == 1) {
                        Toast.makeText(getContext(),
                                getContext().getString(R.string.prePage),
                                Toast.LENGTH_SHORT).show();
                    } else {
                        if(docCommon.getStepCount(curDoc.getDocID(), curDoc.getPage()
                                .getPageID() - 1)>0){
                            long count = docCommon.getStepCount(curDoc.getDocID(), curDoc.getPage()
                                    .getPageID() - 1);
                            docCommon.switchPageStep(curDoc.getDocID(), curDoc.getPage()
                                    .getPageID() - 1, count);
                        }else {
                            docCommon.switchPage(curDoc.getDocID(), curDoc.getPage()
                                    .getPageID() - 1);
                        }
                    }
                }else if (curDoc.getPage().getCurrentStep()==1) {
                    docCommon.switchPage(curDoc.getDocID(), curDoc.getPage()
                            .getPageID());
                }else{
                    docCommon.switchPageStep(curDoc.getDocID(), curDoc.getPage()
                            .getPageID(), curDoc.getPage().getCurrentStep()-1);
                }
            }else {
                if (curDoc.getPage().getPageID() == 1) {
                    Toast.makeText(getContext(),
                            getContext().getString(R.string.prePage),
                            Toast.LENGTH_SHORT).show();
                } else {
                    if(docCommon.getStepCount(curDoc.getDocID(), curDoc.getPage()
                            .getPageID() - 1)>0){
                        long count = docCommon.getStepCount(curDoc.getDocID(), curDoc.getPage()
                                .getPageID() - 1);
                        docCommon.switchPageStep(curDoc.getDocID(), curDoc.getPage()
                                .getPageID() - 1, count);
                    }else {
                        docCommon.switchPage(curDoc.getDocID(), curDoc.getPage()
                                .getPageID() - 1);
                    }
                }
            }
        }
    }
    //往下翻页
    public void downPage(){
        if(curDoc.getPage() != null
                && docCommon.getPageCount(curDoc.getDocID()) >1){
            if (curDoc.getPage().getStepCount()>0) {
                if(curDoc.getPage().getCurrentStep()==curDoc.getPage().getStepCount()){
                    if (curDoc.getPage().getPageID() == docCommon
                            .getPageCount(curDoc.getDocID())) {
                        Toast.makeText(getContext(),
                                getContext().getString(R.string.nextPage),
                                Toast.LENGTH_SHORT).show();
                    } else {
                        docCommon.switchPage(curDoc.getDocID(), curDoc.getPage()
                                .getPageID() + 1);
                    }
                }else{
                    docCommon.switchPageStep(curDoc.getDocID(), curDoc.getPage()
                            .getPageID(), curDoc.getPage().getCurrentStep()+1);
                }
            }else {
                if (curDoc.getPage().getPageID() == docCommon
                        .getPageCount(curDoc.getDocID())) {
                    Toast.makeText(getContext(),
                            getContext().getString(R.string.nextPage),
                            Toast.LENGTH_SHORT).show();
                } else {
                    docCommon.switchPage(curDoc.getDocID(), curDoc.getPage()
                            .getPageID() + 1);
                }
            }
        }
    }


    /**
     * 设置文档选择栏的显示
     *
     * @param flag
     *            怀疑是用来控制?
     */
    public void setFilesLayout(boolean flag) {
//        if ((LinkedHashMap<Integer, DocBean>) docCommon.getDocMap() != null){
//            LinkedHashMap<Integer, DocBean> docMap = (LinkedHashMap<Integer, DocBean>) docCommon.getDocMap().clone();
//            if (docMap != null && !docMap.isEmpty()) {
                if (docCommon.getPrivateAnnoPriviledge()
                        || (((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf() != null &&
                        ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf()
                                .getRole() == UserCommon.ROLE_HOST)) {
                    enableAnnotation();
                }
//            } else {
//                disableAnnotation();
//            }
//        }
    }


    private void enableAnnotation() {
        Log.d("InfowareLab.Debug", "FragDs.enableAnnotation");
        rlAnnotation.setVisibility(View.VISIBLE);
        //rlAnnotation.requestFocus();
    }

    private void disableAnnotation() {
        Log.d("InfowareLab.Debug", "FragDs.disableAnnotation");
        if(rlAnnotation.getVisibility()==View.VISIBLE&&null!=annotationAction){
            annotationAction.initBtnStatus();
        }
        rlAnnotation.setVisibility(View.INVISIBLE);
        if (((DocCommonImpl)  CommonFactory.getInstance().getDocCommon()).getAnnotation() != null)
        ((DocCommonImpl)  CommonFactory.getInstance().getDocCommon()).getAnnotation()
                .setPaint(false);
        ((ToggleButton) dsView.findViewById(R.id.annotationPen))
                .setChecked(false);
        // .setBackgroundResource(R.drawable.icon_pen);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private void initAnnoationAction() {
        annotationAction = new AnnotationAction(dsView);
        dsView.findViewById(R.id.annotationEraser).setOnClickListener(
                annotationAction);
        dsView.findViewById(R.id.annotationColor).setOnClickListener(
                annotationAction);
        dsView.findViewById(R.id.annotationPen).setOnClickListener(
                annotationAction);
        dsView.findViewById(R.id.annotationPointer).setOnClickListener(
                annotationAction);
        dsView.findViewById(R.id.annotationColorGreen).setOnClickListener(
                annotationAction);
        dsView.findViewById(R.id.annotationColorRed).setOnClickListener(
                annotationAction);
        dsView.findViewById(R.id.annotationColorYellow).setOnClickListener(
                annotationAction);
        if (docCommon.getAnnotation() != null)
            docCommon.getAnnotation().setCurrentColor(R.id.annotationColorRed);
        // ibAnnoPull.setOnClickListener(annotationAction);
        ibAnnoPull.setOnTouchListener(this);
        docCommon.isStartPointer = false;
        ((ToggleButton)dsView.findViewById(R.id.annotationPointer)).setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if(!isChecked&&null!=curDoc){
                    docCommon.removeOneAnno(curDoc.getPage().getMyPreAnnotation());
                    docView.invalidate();
                    docView.requestLayout();
                }
            }
        });
        disableAnnotation();
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                lastX = startX = (int) event.getRawX();
                lastY = startY = (int) event.getRawY();
                screenHeight = DensityUtil.getWindowHeight(getActivity());
                screenWidth = DensityUtil.getWindowWidth(getActivity());
                break;
            case MotionEvent.ACTION_MOVE:
                int dx = (int) event.getRawX() - lastX;
                int dy = (int) event.getRawY() - lastY;
                int top = rlAnnotation.getTop() + dy;
                int left = rlAnnotation.getLeft() + dx;
                if (top <= 0) {
                    top = 0;
                }
                if (top >= screenHeight - rlAnnotation.getHeight()-ibAnnoPull.getHeight()) {
                    top = screenHeight - rlAnnotation.getHeight()-ibAnnoPull.getHeight();
                }
                if (left >= screenWidth - rlAnnotation.getWidth()-DensityUtil.dip2px(getActivity(),60)) {
                    left = screenWidth - rlAnnotation.getWidth()-DensityUtil.dip2px(getActivity(),60)-10;
                }
                if (left <= 0) {
                    left = 0;
                }
                rlAnnotation.layout(left, top, left + rlAnnotation.getWidth(), top
                        + rlAnnotation.getHeight());
                lastX = (int) event.getRawX();
                lastY = (int) event.getRawY();
                lastLeft = left;
                lastTop = top;
                break;
            case MotionEvent.ACTION_UP:
                FrameLayout.LayoutParams lp = (FrameLayout.LayoutParams) rlAnnotation
                        .getLayoutParams();
                lp.setMargins(lastLeft, lastTop, 0, 0);
                rlAnnotation.setLayoutParams(lp);
                if (Math.abs(lastX - startX) < 5 && Math.abs(lastY - startY) < 5) {
                    annotationAction.onClick(v);
                }
                break;
        }

        return false;
    }

    @Override
    public void onResume() {
        super.onResume();
        setFilesLayout(true);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.frag_inconf_doc_leftBtn:
                upPage();
                break;
            case R.id.frag_inconf_doc_rightBtn:
                downPage();
                break;
            default:
                break;
        }
    }

    //初始化布局
    public void initBuju(){
        if (noText != null && docCommon != null && !isEmpty()){
             leftBtn.setVisibility(View.VISIBLE);
             rightBtn.setVisibility(View.VISIBLE);
             noText.setVisibility(View.GONE);
        }else {
            leftBtn.setVisibility(View.VISIBLE);
            rightBtn.setVisibility(View.VISIBLE);
            noText.setVisibility(View.GONE);
        }
    }

    public void initConfData() {

        docCommon = (DocCommonImpl) CommonFactory.getInstance().getDocCommon();
        initDocHandler();
    }
}
