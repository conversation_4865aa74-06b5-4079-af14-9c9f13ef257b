package com.infowarelab.tvbox.activity;

import android.app.Activity;
import android.graphics.Color;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.Nullable;

import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelab.tvbox.view.VideoEncodeView;
import com.readystatesoftware.systembartint.SystemBarTintManager;

/**
 * Created by sdvye on 2019/6/12.
 */

public class ActMain extends Activity{
    public static ActMain mActivity = null;
    private TextView titleText;
    private VideoEncodeView tvMain;
    private Spinner spinner;
    private int cameraPosition = 0; // 0代表前置摄像头，1代表后置摄像头
    private LinearLayout llCancel;

    private MediaCodecInfo getCodecInfo(final String mimeType) {
        final int numCodecs = MediaCodecList.getCodecCount();
        for (int i = 0; i < numCodecs; i++) {
            final MediaCodecInfo codecInfo = MediaCodecList.getCodecInfoAt(i);
            if (!codecInfo.isEncoder()) {
                continue;
            }
            final String[] types = codecInfo.getSupportedTypes();
            for (String type : types) {
                if (type.equalsIgnoreCase(mimeType)) {
                    return codecInfo;
                }
            }
        }
        return null;
    }

    private boolean isSupport4KH265(){

        MediaCodecInfo codecInfo = getCodecInfo("video/hevc");
        if (codecInfo == null) {
            return false;
        }
        else if (!codecInfo.isEncoder())
        {
            return false;
        }

        return true;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            //透明状态栏
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            //透明导航栏
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            SystemBarTintManager tintManager = new SystemBarTintManager(this);
            // 激活状态栏
            tintManager.setStatusBarTintEnabled(true);
            // enable navigation bar tint 激活导航栏
            tintManager.setNavigationBarTintEnabled(true);
            //设置系统栏设置颜色
            //tintManager.setTintColor(R.color.red);
            //给状态栏设置颜色
            tintManager.setStatusBarTintResource(R.color.transparent);
            //Apply the specified drawable or color resource to the system navigation bar.
            //给导航栏设置资源
            tintManager.setNavigationBarTintResource(R.color.transparent);
        }

        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

//        Window window = getWindow();
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//
//            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_FULLSCREEN;
//
//            window.getDecorView().setSystemUiVisibility(uiOptions);
//            window.setStatusBarColor(Color.TRANSPARENT);
//        }
//        else {
//            View v = this.getWindow().getDecorView();
//            v.setSystemUiVisibility(View.GONE);
//        }

        setContentView(R.layout.frag_main);

        cameraPosition = getIntent().getExtras().getInt("cameraPosition");

//        FileUtils.DeleteFolder(Environment.getExternalStorageDirectory().getPath() + "/AudioRecord/");

        tvMain = (VideoEncodeView)findViewById(R.id.tv_main);

        tvMain.setInitVideo(false);
        tvMain.setDegrees(false);
        tvMain.setSharing(false);

        titleText = (TextView)findViewById(R.id.titleText);
        if (0 == cameraPosition){
            titleText.setText(getResources().getString(R.string.set_title_video));
            tvMain.setFlow(false);
        }else {
            titleText.setText(getResources().getString(R.string.fu));
            tvMain.setFlow(true);
        }
        spinner = (Spinner) findViewById(R.id.sp);
        spinner.requestFocus();
        String[] items = {"一般", "标清", "高清", "超清", "4K"};
//        String[] items = {"一般", "标清", "高清"};
        String[] items2 = {"一般", "标清", "高清", "超清"};

        ArrayAdapter<String> stringArrayAdapter = null;

        if (isSupport4KH265())
            stringArrayAdapter = new ArrayAdapter<>(this, R.layout.item_select, items);
        else
            stringArrayAdapter = new ArrayAdapter<>(this, R.layout.item_select, items2);

        stringArrayAdapter.setDropDownViewResource(R.layout.item_drop);
        spinner.setAdapter(stringArrayAdapter);

        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                switch (position) {
                    case 0:
                        if (0 == cameraPosition){
                            SharedPreferencesUrls.getInstance().putInt("width",320);
                            SharedPreferencesUrls.getInstance().putInt("height",180);
                        }else {
                            SharedPreferencesUrls.getInstance().putInt("width1",1920);
                            SharedPreferencesUrls.getInstance().putInt("height1",1080);
                        }
                        tvMain.setStreamVideoSize(320,180);
                        break;
                    case 1:
                        if (0 == cameraPosition){
                            SharedPreferencesUrls.getInstance().putInt("width",640);
                            SharedPreferencesUrls.getInstance().putInt("height",360);
                        }else {
                            SharedPreferencesUrls.getInstance().putInt("width1",1920);
                            SharedPreferencesUrls.getInstance().putInt("height1",1080);
                        }
                        tvMain.setStreamVideoSize(640,360);
                        break;
                    case 2:
                        if (0 == cameraPosition){
                            SharedPreferencesUrls.getInstance().putInt("width",1280);
                            SharedPreferencesUrls.getInstance().putInt("height",720);
                        }else {
                            SharedPreferencesUrls.getInstance().putInt("width1",1920);
                            SharedPreferencesUrls.getInstance().putInt("height1",1080);
                        }
                        tvMain.setStreamVideoSize(1280,720);
                        break;
                    case 3:
                        if (0 == cameraPosition){
                            SharedPreferencesUrls.getInstance().putInt("width",1920);
                            SharedPreferencesUrls.getInstance().putInt("height",1080);
                        }else {
                            SharedPreferencesUrls.getInstance().putInt("width1",1920);
                            SharedPreferencesUrls.getInstance().putInt("height1",1080);
                        }
                        tvMain.setStreamVideoSize(1920,1080);
                        break;
                    case 4:
                        if (0 == cameraPosition){
                            SharedPreferencesUrls.getInstance().putInt("width",3840);
                            SharedPreferencesUrls.getInstance().putInt("height",2160);
                        }else {
                            SharedPreferencesUrls.getInstance().putInt("width1",1920);
                            SharedPreferencesUrls.getInstance().putInt("height1",1080);
                        }
                        tvMain.setStreamVideoSize(1920,1080);
                        break;
                    default:
                        break;
                }
                if (0 == cameraPosition){
                    SharedPreferencesUrls.getInstance().putInt("position",position);
                }else {
                    SharedPreferencesUrls.getInstance().putInt("position1",position);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });
        if (0 == cameraPosition){
            //主流
            spinner.setSelection(SharedPreferencesUrls.getInstance().getInt("position",2));
            tvMain.setStreamVideoSize(SharedPreferencesUrls.getInstance().getInt("width",1280),
                    SharedPreferencesUrls.getInstance().getInt("height",720));
        }else {
            //辅流
            spinner.setSelection(SharedPreferencesUrls.getInstance().getInt("position1",2));
            tvMain.setStreamVideoSize(SharedPreferencesUrls.getInstance().getInt("width1",1920),
                    SharedPreferencesUrls.getInstance().getInt("height1",1080));
        }

        llCancel = (LinearLayout) findViewById(R.id.ll_frag_set_cancel);

        llCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                tvMain.setVisibility(View.GONE);
                tvMain.destroyCamera();

                tvMain.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        finish();
                    }
                }, 200);

            }
        });
    }
    //相机的启动和Activity的生命周期进行绑定
    @Override
    protected void onResume() {
        mActivity = ActMain.this;
        super.onResume();
        tvMain.setDegrees(false);
        tvMain.setSharing(false);
        tvMain.reStartLocalView();

//        if (!tvMain.isSupport4K())
//        {
//            String[] items = {"一般", "标清", "高清", "超清"};
//
//            ArrayAdapter<String> stringArrayAdapter = new ArrayAdapter<>(this, R.layout.item_select, items);
//            stringArrayAdapter.setDropDownViewResource(R.layout.item_drop);
//            spinner.setAdapter(stringArrayAdapter);
//        }
    }
    //相机的启动和Activity的生命周期进行绑定
    @Override
    protected void onPause() {
        super.onPause();
        tvMain.destroyCamera();
    }

    @Override
    protected void onDestroy() {
        tvMain.destroyCamera();
        mActivity = null;
        super.onDestroy();
    }
}
