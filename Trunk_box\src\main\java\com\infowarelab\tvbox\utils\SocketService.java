package com.infowarelab.tvbox.utils;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.activity.ActLogo;
import com.infowarelab.tvbox.socket.impl.tcp.bio.BioClient;
import com.infowarelab.tvbox.socket.structures.BaseClient;
import com.infowarelab.tvbox.socket.structures.BaseMessageProcessor;
import com.infowarelab.tvbox.socket.structures.IConnectListener;
import com.infowarelab.tvbox.socket.structures.TcpAddress;
import com.infowarelab.tvbox.socket.structures.message.Message;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.MessageEvent;
import com.infowarelabsdk.conference.util.NetUtil;

import org.greenrobot.eventbus.EventBus;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;

import java.io.DataInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringReader;
import java.net.ConnectException;
import java.net.InetSocketAddress;
import java.net.NoRouteToHostException;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.LinkedList;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

/**
 * Created by xiaor on 2019/11/18.
 * <AUTHOR>
 * TCP Socket通信
 */

public class SocketService extends Service{


    /*socket*/
    private Socket socket;
    /*连接线程*/
    private Thread connectThread;
    private Timer timer = new Timer();
    private OutputStream outputStream;

    private SocketBinder socketBinder = new SocketBinder();
    private TimerTask task;
    /*默认重连*/
    public boolean isReConnect = true;
    //是否处于链接状态
//    public boolean isConnect = false;
//    //连接次数
//    public int count = 0;

    /**读写输入流*/
    private InputStream inputStream;
    private DataInputStream dis;
    /*线程状态,安全结束线程*/
    private boolean threadStatus = false;
    /*读取保存进数组*/
    byte buff[] = new byte[1024*1024*2];
    /*接收数据长度*/
    private int rcvLength;
    /*接收数据*/
    private String rcvMsg;

    private Handler handler = new Handler(Looper.getMainLooper());

    public boolean bReleaseSocket = false;
    private Lock socketlock = new ReentrantLock();

    private BioClient mClient =null;

    private Timer mReconnectTimer = null;
    private TimerTask mReconnectTask = null;

    private Timer mPingTimer = null;
    private TimerTask mPingTask = null;

    private IConnectListener mConnectResultListener = new IConnectListener() {
        @Override
        public void onConnectionSuccess() {
            Log.d("InfowareLab.Socket", "IConnectListener.onConnectionSuccess: Connection-Success");

            //toastMsg("连接邀请服务器成功：" + PublicWay.IP + ":" + PublicWay.PORT);

            cancelReconnectTask();
            startPingTask();
        }

        @Override
        public void onConnectionFailed() {

            Log.d("InfowareLab.Socket", "IConnectListener.onConnectionFailed: Connection-Failed");

            //toastMsg("连接邀请服务器失败：" + PublicWay.IP + ":" + PublicWay.PORT);

            cancelPingTask();
            startReconnectTask();
        }
    };

    private boolean mPingPaused = false;
    private boolean mRecvConfirmationStarted = false;
    private String mRecvMsg = "";
    private int mWaitCount = 0;

    private void cancelReconnectTask() {

        Log.d("InfowareLab.Socket", "cancelReconnectTask");

        if (mReconnectTask != null){
            mReconnectTask.cancel();
            mReconnectTask = null;
        }

        if (mReconnectTimer != null) {
            mReconnectTimer.purge();
            mReconnectTimer.cancel();
            mReconnectTimer = null;
        }
    }

    private void startReconnectTask() {

        Log.d("InfowareLab.Socket", "startReconnectTask");

        if (mReconnectTask != null){
            mReconnectTask.cancel();
            mReconnectTask = null;
        }

        if (mReconnectTimer != null) {
            mReconnectTimer.purge();
            mReconnectTimer.cancel();
            mReconnectTimer = null;
        }

        if (mReconnectTimer == null) {
            mReconnectTimer = new Timer();
        }

        if (mReconnectTask == null) {
            mReconnectTask = new TimerTask() {
                @Override
                public void run() {

                    Log.d("InfowareLab.Socket", "BioClient.reconnect...");

                    if (mClient == null)
                        mClient = new BioClient(mMessageProcessor,mConnectResultListener);

                    if (mClient != null && mClient.isConnected()) mClient.disconnect();

                    if (PublicWay.IP.length() <= 0 || PublicWay.PORT < 0){
                        try {
                            refreshInvitingServerAddress();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }

                    if (PublicWay.IP.trim().length() <= 0 || PublicWay.PORT < 0) {
                        return;
                    }

                    mClient.setConnectAddress(new TcpAddress[]{new TcpAddress(PublicWay.IP, PublicWay.PORT)});

                    Log.d("InfowareLab.Socket", "startReconnectTask: reconnect: " + PublicWay.IP + ":" + PublicWay.PORT);

                    mClient.connect();
                }
            };
        }

        mReconnectTimer.schedule(mReconnectTask, 3 * 1000, 3 * 1000);
    }

    private void cancelPingTask() {

        Log.d("InfowareLab.Socket", "cancelPingTask");

        if (mPingTask != null){
            mPingTask.cancel();
            mPingTask = null;
        }

        if (mPingTimer != null) {
            mPingTimer.purge();
            mPingTimer.cancel();
            mPingTimer = null;
        }
    }

    private void startPingTask() {

        Log.d("InfowareLab.Socket", "startPingTask");

        if (mPingTask != null){
            mPingTask.cancel();
            mPingTask = null;
        }

        if (mPingTimer != null) {
            mPingTimer.purge();
            mPingTimer.cancel();
            mPingTimer = null;
        }

        if (mPingTimer == null) {
            mPingTimer = new Timer();
        }

        if (mPingTask == null) {
            mPingTask = new TimerTask() {
                @Override
                public void run() {

                    if (mPingPaused && mWaitCount < 2) {
                        mWaitCount++;
                        return;
                    }

                    //Log.d("InfowareLab.Socket", "BioClient.Ping...");
                    mWaitCount = 0;
                    mPingPaused = false;
                    sendBeatDataEx();
                }
            };
        }

        mPingTimer.schedule(mPingTask, 0, 5 * 1000);
    }

    private BaseMessageProcessor mMessageProcessor =new BaseMessageProcessor() {

        @Override
        public void onReceiveMessages(BaseClient mClient, final LinkedList<Message> mQueen) {
            for (int i = 0 ;i< mQueen.size();i++) {
                Message msg = mQueen.get(i);
                final String recvMsg = new String(msg.data,msg.offset,msg.length);

                if (recvMsg.indexOf("request_status") != -1 && !recvMsg.trim().contains("ping_response")) return;

                //Log.d("InfowareLab.Socket", "BaseMessageProcessor.onReceiveMessages: " + recvMsg);

                if (recvMsg.indexOf("<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Pack>") != -1 && recvMsg.indexOf("</Pack>") == -1)
                {
                    Log.d("InfowareLab.Socket", "BaseMessageProcessor.onReceiveMessages(START PART): " + recvMsg);

                    mRecvConfirmationStarted = true;
                    mRecvMsg = recvMsg;
                }
                else if (mRecvMsg.length() > 0 && mRecvConfirmationStarted)
                {

                    if (recvMsg.indexOf("</Pack>") != -1)
                    {
                        mRecvMsg +=  recvMsg;
                        mRecvConfirmationStarted = false;

                        if (mRecvMsg.indexOf("request_status") != -1) return;

                        Log.d("InfowareLab.Socket", "BaseMessageProcessor.onReceiveMessages(END PART): " + recvMsg);

                        if (!mRecvMsg.trim().contains("request_status")){

                            handler.post(new Runnable() {
                                @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
                                @Override
                                public void run() {

                                    PublicWay.formatXML(0, mRecvMsg);
                                    //onReceiveDataListener.onReceiveData(mRecvMsg);
                                }
                            });
                        }

                        //mRecvMsg = "";
                        mPingPaused = false;
                    }
                    else
                    {
                        Log.d("InfowareLab.Socket", "BaseMessageProcessor.onReceiveMessages(MID PART): " + recvMsg);

                        mRecvMsg +=  recvMsg;
                        mRecvConfirmationStarted = true;
                    }
                }
                else if (recvMsg.indexOf("<?xml version=\"1.0\" encoding=\"UTF-8\" ?><Pack>") != -1 && recvMsg.indexOf("</Pack>") != -1)
                {
                    mRecvMsg =  recvMsg;
                    mRecvConfirmationStarted = false;

                    if (mRecvMsg.indexOf("request_status") != -1 && !mRecvMsg.trim().contains("ping_response")) return;

                    Log.d("InfowareLab.Socket", "BaseMessageProcessor.onReceiveMessages(FULL PART): " + recvMsg);

                    handler.post(new Runnable() {
                        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
                        @Override
                        public void run() {
                            PublicWay.formatXML(0, mRecvMsg);
                            //onReceiveDataListener.onReceiveData(mRecvMsg);
                        }
                    });

                    //mRecvMsg = "";
                    mPingPaused = false;
                }

                //if (rcvMsg.indexOf("<Request>ping</Request>") != -1) return;

                //Log.d("InfowareLab.Socket", "BaseMessageProcessor.onReceiveMessages: " + rcvMsg);

                //if (null != onReceiveDataListener && !rcvMsg.trim().contains("request_status")){
                //    onReceiveDataListener.onReceiveData(rcvMsg);
                //}

//                runOnUiThread(new Runnable() {
//                    public void run() {
//
//                        recContent.getText().append(s).append("\r\n");
//                    }
//                });
            }
        }
    };

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Log.e("Test","onBind====>>>>");
        return socketBinder;
    }

    public void reconnectSocket() {

        if (mClient != null) mClient.disconnect();
    }

    public class SocketBinder extends Binder {
        /*返回SocketService 在需要的地方可以通过ServiceConnection获取到SocketService  */
        public SocketService getService() {
            return SocketService.this;
        }
    }

    @Override
    public void onCreate() {
        //Log.e("OOOOOO","onCreate====>>>>");
        super.onCreate();
//        count = 0;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        /*初始化socket*/
//        count = 0;
        try {
            initSocketEx();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return super.onStartCommand(intent, flags, startId);
    }

    /*初始化socket*/
    private void initSocketEx() throws InterruptedException {

        if (PublicWay.IP.trim().length() <= 0 || PublicWay.PORT < 0){
            refreshInvitingServerAddress();
        }

        if (PublicWay.IP.trim().length() <= 0 || PublicWay.PORT < 0) {

            cancelPingTask();
            startReconnectTask();

            return;
        }

        if (mClient == null){
            mClient = new BioClient(mMessageProcessor,mConnectResultListener);
            mClient.setConnectAddress(new TcpAddress[]{new TcpAddress(PublicWay.IP, PublicWay.PORT)});

            Log.d("InfowareLab.Socket", "initSocketEx: connect: " + PublicWay.IP + ":" + PublicWay.PORT);
            mClient.connect();
        }
        else
        {
            if (mClient.isConnected())
                mClient.disconnect();

            mClient.setConnectAddress(new TcpAddress[]{new TcpAddress(PublicWay.IP, PublicWay.PORT)});

            Log.d("InfowareLab.Socket", "initSocketEx: connect(2): " + PublicWay.IP + ":" + PublicWay.PORT);
            mClient.connect();
        }
    }

    private void refreshInvitingServerAddress() throws InterruptedException {

        Thread refreshThread = new Thread(new Runnable() {
            @Override
            public void run() {

                String result = "-1";
                String siteId = FileUtil.readSharedPreferences(getApplicationContext(),
                        Constants.SHARED_PREFERENCES, Constants.SITE_ID);

                final StringBuffer m_url = new StringBuffer(Config.Site_URL + "/meeting/remoteServlet?funcName=getInvitingServer&siteId=" + siteId);
                try {
                    String response = NetUtil.doGet(m_url.toString());
                    if (response != null && !response.equals("")) {
                        DocumentBuilderFactory domfac = DocumentBuilderFactory
                                .newInstance();
                        DocumentBuilder dombuilder = domfac.newDocumentBuilder();
                        Document doc = dombuilder.parse(new InputSource(
                                new StringReader(response)));
                        Element root = doc.getDocumentElement();
                        if (root == null) {
                            return;
                        }
                        if (root.getElementsByTagName("return").item(0).getFirstChild().getNodeValue().equals("0")) {
                            result = root.getElementsByTagName("result").item(0).getTextContent();
                            String[] ss = result.split(":");
                            if (ss.length == 1) {
                                PublicWay.IP = ss[0];
                                PublicWay.PORT = 10002;
                                Log.d("InfowareLab.Debug", "Inviting Server:" + PublicWay.IP + "; port:" + PublicWay.PORT);
                                return;
                            } else if (ss.length > 1) {
                                PublicWay.IP = ss[0];
                                PublicWay.PORT = Integer.parseInt(ss[1]);
                                Log.d("InfowareLab.Debug", "Inviting Server::" + PublicWay.IP + "; port:" + PublicWay.PORT);
                                return;
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        refreshThread.start();
        refreshThread.join();
    }

    /*初始化socket*/
    private void initSocket() {
        if (null == socket && null == connectThread) {
            connectThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    socket = new Socket();
                    boolean bReconnect = false;
                    try {
                         /*超时时间为2秒*/
                        socket.connect(new InetSocketAddress(PublicWay.IP, PublicWay.PORT), 2 * 1000);
                        socketlock.lock();
                        bReleaseSocket = false;
                        socketlock.unlock();
                        /*连接成功的话  发送心跳包*/
                        if (socket.isConnected()) {
                            /*因为Toast是要运行在主线程的  这里是子线程  所以需要到主线程哪里去显示toast*/
                            inputStream = socket.getInputStream();
                            dis = new DataInputStream(inputStream);
//                            toastMsg("服务已连接");
                            /*开启读写线程*/
                            threadStatus = true;
                            new ReadThread().start();
//                           /*发送心跳数据*/
                            sendBeatData();
                            //发条消息
                            if (!PublicWay.isConnect){
                                PublicWay.isConnect = true;
                                MessageEvent messageEvent = new MessageEvent();
                                messageEvent.setType(6);
                                messageEvent.setSocketConnect(true);
                                EventBus.getDefault().postSticky(messageEvent);
                            }
                        } else {
                            bReconnect = true;
                        }
                    } catch (IOException e) {
                        if (PublicWay.terminalsData.size() != 0){
                            PublicWay.terminalsData.clear();
                        }
                        bReconnect = true;
                        socketlock.lock();
                        bReleaseSocket = false;
                        socketlock.unlock();
                        e.printStackTrace();
                        if (e instanceof SocketTimeoutException) {
//                            if (count == 4){
                                toastMsg("连接超时,暂停连接");
//                            }
//                            toastMsg("连接超时，正在重连");
                        } else if (e instanceof NoRouteToHostException) {
                            toastMsg("该地址不存在，请检查");
//                            stopSelf();
                        } else if (e instanceof ConnectException) {
                            if (NetUtil.isNetworkConnected(getApplicationContext())){
                                toastMsg("连接异常或被拒绝，正在重连");
                            }
                        }
                    }
                    //延迟60s重连
                    try {
                        connectThread.sleep(60 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    if (bReconnect) {
                        //发条消息
//                        if (PublicWay.isConnect && count == 4){
                        if (PublicWay.isConnect){
                            PublicWay.isConnect = false;
                            MessageEvent messageEvent = new MessageEvent();
                            messageEvent.setType(6);
                            messageEvent.setSocketConnect(false);
                            EventBus.getDefault().postSticky(messageEvent);
                        }
//                        count ++;
                        threadStatus = false;
                        isReConnect = true;
                        releaseSocket();
                    }
                }
            });
            /*启动连接线程*/
            connectThread.start();
        } else {
            socketlock.lock();
            bReleaseSocket = false;
            socketlock.unlock();
        }
    }
    /*因为Toast是要运行在主线程的   所以需要到主线程哪里去显示toast*/
    private void toastMsg(final String msg) {
        handler.post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(getApplicationContext(), msg, 2 * 1000).show();
            }
        });
    }
    /*发送数据*/
    public void sendOrder(final String order) {

        if (socket != null && socket.isConnected()) {
            //Log.e("UUUUUU","发送指令::"+order);
            /*发送指令*/
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        outputStream = socket.getOutputStream();
                        if (outputStream != null) {
                            outputStream.write((order).getBytes("UTF-8"));
                            if (outputStream == null) return;
                            outputStream.flush();
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                }
            }).start();
        } else {
//            toastMsg("socket连接错误,请重试");
        }
    }

    /*发送数据*/
    public void sendOrderEx(final String order) {

        if (mClient == null || !mClient.isConnected()) {
            Log.d("InfowareLab.Socket",">>>>>> sendOrderEx(Error): BioClient is NOT ready");
            try {
                initSocketEx();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return;
        }

        mPingPaused = true;

        Log.d("InfowareLab.Socket",">>>>>> sendOrderEx:" + order);
        mMessageProcessor.send(mClient, order.getBytes());
    }

    /*定时发送数据*/
    private void sendBeatDataEx() {

        try {

            if (mClient == null || !mClient.isConnected()) {
                Log.d("InfowareLab.Socket",">>>>>> sendBeatDataEx(Error): BioClient is NOT ready");
                try {
                    initSocketEx();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                return;
            }

            String RequestID = ""+ ConferenceApplication.currentTimeMillis();
            String name = FileUtil.readSharedPreferences(getApplicationContext(), Constants.SHARED_PREFERENCES,
                    Constants.LOGIN_JOINNAME);
            if (TextUtils.isEmpty(name)){
                name = "NO_NAME";
            }
            int uid = getApplicationContext().getSharedPreferences(Constants.SHARED_PREFERENCES,
                    getApplicationContext().MODE_WORLD_READABLE).getInt(Constants.USER_ID, 0);
            String deviceId = DeviceIdFactory.getUUID1(getApplicationContext());
            String shareCode = DeviceIdFactory.getShareCode();

            String siteId = FileUtil.readSharedPreferences(getApplicationContext(), Constants.SHARED_PREFERENCES,
                    Constants.SITE_ID);

            String ping;

            if (shareCode != null && shareCode.length() > 0) {
                ping = XMLUtils.getPingXMLWithShareCode(RequestID, uid, name, deviceId, siteId, PublicWay.longitude, PublicWay.latitude, shareCode);
                Log.d("InfowareLab.Socket",">>>>>> Ping:" + ping);
            }
            else
            {
                ping = XMLUtils.getRequestSharingCodeXML(RequestID,uid,name,deviceId,siteId, PublicWay.longitude, PublicWay.latitude, "");
                Log.d("InfowareLab.Socket",">>>>>> Ping (request sharing code):" + ping);
            }

            mMessageProcessor.send(mClient, ping.getBytes());

        } catch (Exception e) {

            Log.d("InfowareLab.Socket",">>>>>> SendBeatDataEx(Error):" + e.getMessage());

            e.printStackTrace();
        }

    }

    /*定时发送数据*/
    private void sendBeatData() {
        if (timer == null) {
            timer = new Timer();
        }
        if (task == null) {
            task = new TimerTask() {
                @Override
                public void run() {
                    try {
                        String RequestID = ""+ ConferenceApplication.currentTimeMillis();
                        String name = FileUtil.readSharedPreferences(getApplicationContext(), Constants.SHARED_PREFERENCES,
                                Constants.LOGIN_JOINNAME);
                        if (TextUtils.isEmpty(name)){
                            name = "NO_NAME";
                        }
                        int uid = getApplicationContext().getSharedPreferences(Constants.SHARED_PREFERENCES,
                                getApplicationContext().MODE_WORLD_READABLE).getInt(Constants.USER_ID, 0);
                        String deviceId = DeviceIdFactory.getUUID1(getApplicationContext());
                        String siteId = FileUtil.readSharedPreferences(getApplicationContext(), Constants.SHARED_PREFERENCES,
                                Constants.SITE_ID);
                        String ping = XMLUtils.getPingXML(RequestID,uid,name,deviceId,siteId, PublicWay.longitude, PublicWay.latitude);
                        //Log.d("InfowareLab.Debug",">>>>>> Ping:" + ping);
                        outputStream = socket.getOutputStream();
                        /*这里的编码方式根据你的需求去改*/
                        outputStream.write((ping).getBytes("UTF-8"));
                        outputStream.flush();
                    } catch (Exception e) {
                        /*发送失败说明socket断开了或者出现了其他错误*/
                        if (timer != null){
                            timer.cancel();
                        }
//                        toastMsg("连接断开，正在重连");
                        /*重连*/
//                        count ++;
                        threadStatus = false;
                        isReConnect = true;
                        releaseSocket();
                        e.printStackTrace();
                    }
                }
            };
        }
        timer.schedule(task, 0, 5 * 1000);
    }
    /*释放资源*/
    public void releaseSocket() {

        socketlock.lock();
        if (bReleaseSocket) {
            return;
        }
        bReleaseSocket = true;
        socketlock.unlock();
        if (task != null) {
            task.cancel();
            task = null;
        }
        if (timer != null) {
            timer.purge();
            timer.cancel();
            timer = null;
        }
        if (outputStream != null) {
            try {
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            outputStream = null;
        }

        if (socket != null) {
            try {
                socket.close();
            } catch (IOException e) {
            }
            socket = null;
        }
        if (connectThread != null) {
            connectThread = null;
        }
          /*重新初始化socket*/
        if (isReConnect) {
//            if (count < 5){
                Log.e("OOOOOO","重新初始化_IP:"+ PublicWay.IP);
                Log.e("OOOOOO","重新初始化_端口:"+ PublicWay.PORT);
                initSocket();
//            }else {
//                isReConnect = false;
//                stopSelf();
//            }
        }
    }
    String _rcvMsg = "";
    String packEndTag = "</Pack>";
    /*读写线程*/
    private class ReadThread extends Thread{
        @Override
        public void run() {
            super.run();
            //判断进程是否在运行，更安全的结束进程
            while (threadStatus){
                if (null != inputStream){
                    try {
                        //inputStream 数据分段传输问题
                        if (inputStream.available() > 0 == false){
                            continue;
                        }else {
                            sleep(200);
                        }
                        rcvLength = dis.read(buff);
                        if (rcvLength > 0){
                            for(int i = 0; i < rcvLength; i ++) {
                                if('\n' == buff[i] || '\r' == buff[i]) {
                                    buff[i] = 0x20;
                                }
                            }
                            rcvMsg = new String(buff, 0, rcvLength, "UTF-8");
                            //接收到数据，切换主线程，显示数据
                            rcvMsg = rcvMsg.trim();
                            _rcvMsg = _rcvMsg + rcvMsg;
                            String[] strBuffer = _rcvMsg.split(packEndTag);
                            boolean packEnd = (_rcvMsg.length() > packEndTag.length() && _rcvMsg.lastIndexOf(packEndTag) == _rcvMsg.length() - packEndTag.length());
                            _rcvMsg = "";
                            for(int i = 0; i < strBuffer.length; i ++) {
                                if(i < strBuffer.length - 1 || packEnd) {
                                    rcvMsg = strBuffer[i] + packEndTag;
                                    rcvMsg = rcvMsg.trim();
                                    if ((rcvMsg.trim()).indexOf("<Action>request_status</Action>".trim()) != -1) continue;
                                    handler.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            if (null != onReceiveDataListener && !rcvMsg.trim().contains("request_status")){
                                                onReceiveDataListener.onReceiveData(rcvMsg);
                                            }
                                        }
                                    });
                                }else {
                                    _rcvMsg = strBuffer[i].trim();
                                }
                            }
                        }
                    }catch (Exception e){
                    }
                }
            }
        }
    }
    @Override
    public void onDestroy() {

        cancelReconnectTask();

        Log.d("InfowareLab.Socket", "BioClient.disconnect");

        if (mClient != null)
            mClient.disconnect();

//        super.onDestroy();
        //Log.i("OOOOOO", "onDestroy");
//        isReConnect = false;
//        releaseSocket();
        if (timer != null) {
            timer.purge();
            timer.cancel();
            timer = null;
        }
//        Intent service = new Intent(this, SocketService.class);
//        startService(service);
        super.onDestroy();
    }

    public interface OnReceiveDataListener{
        void onReceiveData(String str);
    }
    public OnReceiveDataListener onReceiveDataListener;

    public OnReceiveDataListener getOnReceiveDataListener() {
        return onReceiveDataListener;
    }

    public void setOnReceiveDataListener(OnReceiveDataListener onReceiveDataListener) {
        this.onReceiveDataListener = onReceiveDataListener;
    }
}
