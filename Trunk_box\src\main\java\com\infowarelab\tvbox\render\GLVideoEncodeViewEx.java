package com.infowarelab.tvbox.render;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.ImageFormat;
import android.graphics.SurfaceTexture;
import android.hardware.Camera;
import android.opengl.EGL14;
import android.opengl.GLES20;
import android.opengl.GLSurfaceView;
import android.opengl.Matrix;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.annotation.RequiresApi;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.gles.FullFrameRect;
import com.infowarelab.tvbox.gles.Texture2dProgram;
import com.infowarelab.tvbox.grafika.TextureMovieEncoder;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelabsdk.conference.callback.CallbackManager;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.video.AvcHardEncoder;
import com.infowarelabsdk.conference.video.VideoCommon;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.lang.reflect.Method;
import java.util.List;

import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;


public class GLVideoEncodeViewEx extends BaseVideoEncodeView implements SurfaceTexture.OnFrameAvailableListener{

    private static final String TAG = "GLVideoEncodeViewEx";
    private static final boolean VERBOSE = false;

    private CameraSurfaceRenderer mRenderer;
    private Camera mCamera;
    private CameraHandler mCameraHandler;
    private boolean mRecordingEnabled;      // controls button state

    // this is static so it survives activity restarts
    private TextureMovieEncoder sVideoEncoder = new TextureMovieEncoder();

    public static int cameraFPS = 30;
    public static int mCameraPreviewWidth = 640;
    public static int mCameraPreviewHeight = 480;

    public static int mEncodeWidth = -1;
    public static int mEncodeHeight = -1;

    private int currentCamera = Camera.CameraInfo.CAMERA_FACING_FRONT;// 代表摄像头的方位，目前有定义值两个分别为CAMERA_FACING_FRONT前置和CAMERA_FACING_BACK后置
    private int numOfCamera = -1;

    private static boolean isSharing = false;
    private static boolean mCheckFrameRate = false;

    private VideoCommonImpl videoCommon = (VideoCommonImpl) CommonFactory
            .getInstance().getVideoCommon();

    private ConferenceCommonImpl conferenceCommon = (ConferenceCommonImpl) CommonFactory
            .getInstance().getConferenceCommon();

    private boolean isPreview = false;

    public static boolean isDestroyed = false;

    // Video Hard Encoder
    private AvcHardEncoder h264HwEncoderImpl = null;

    private boolean isEnabled = true;

    private int degrees = 90;

    private File _fr = null;
    private FileOutputStream _out = null;
    private Context activity;
    private int mFrameLength = 0;
    private static int STATE_ON = 1;
    private static int STATE_OFF = 2;
    private int mEncoderState = 2;
    private int mBitRate = 1000000;
    private boolean isFlow = false;

    private int mViewWidth = 0;
    private int mViewHeight = 0;

    private int mSurfaceWidth = 0;
    private int mSurfaceHeight = 0;

    private boolean mSurfaceCreated = false;
    private boolean mMirror = false;

    private long lastCheckTime = -1;

    private long mLastNoSharingTime = -1;

    private int realFrameRate = 0;
    private boolean needToChangeEncodeSize = false;
    private boolean needToInitMyVideoSize = false;

    private UserCommonImpl userCommon;
    private int maxWidthForJoin = 1280;//3840;//1280;
    private int maxHeightForJoin = 720;//2160;//720
    private boolean resolutionSpecified = false;

    private boolean encodeByCallback = false;
    private boolean inited = false;
    private boolean mRenderEnabled = false;

    public GLVideoEncodeViewEx(Context context) {
        super(context);
        activity = context;
        init();

    }

    public GLVideoEncodeViewEx(Context context, AttributeSet attrs) {
        super(context, attrs);
        activity = context;
        init();
    }

    public void init() {

        if (inited) return;

        // Define a handler that receives mCamera-control messages from other threads.  All calls
        // to Camera must be made on the same thread.  Note we create this before the renderer
        // thread, so we know the fully-constructed object will be visible.
        mCameraHandler = new CameraHandler(this);

        mRecordingEnabled = sVideoEncoder.isRecording();

        // Configure the GLSurfaceView.  This will start the Renderer thread, with an
        // appropriate EGL context.
        setEGLContextClientVersion(2);     // select GLES 2.0
        mRenderer = new CameraSurfaceRenderer(mCameraHandler, sVideoEncoder, null);
        setRenderer(mRenderer);
        setRenderMode(GLSurfaceView.RENDERMODE_WHEN_DIRTY);

        userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();

        Log.d(TAG, "onCreate complete: " + this);

        inited = true;
    }

    public void setRenderEnabled(boolean renderEnabled) {
        Log.d(TAG, "GLVideoEncoderViewEx.setRenderEnabled: "+ renderEnabled);
        mRenderEnabled = renderEnabled;
    }

    public void toggleEncode(boolean startEncode) {
        mRecordingEnabled = startEncode;
        queueEvent(new Runnable() {
            @Override public void run() {
                // notify the renderer that we want to change the encoder's state
                mRenderer.changeRecordingState(mRecordingEnabled);
            }
        });
        //updateControls();
    }

    /**
     * Connects the SurfaceTexture to the Camera preview output, and starts the preview.
     */
    private void handleSetSurfaceTexture(SurfaceTexture st) {

        Log.d(TAG, "handleSetSurfaceTexture");

        st.setOnFrameAvailableListener(this);

        if (mCamera == null) {
            Log.d(TAG, "handleSetSurfaceTexture error: camera is null");
            return;
        }

        if (isPreviewStarted()) return;

        try {
            Log.d(TAG,"mCamera.setPreviewTexture in handleSetSurfaceTexture()");
            mCamera.setPreviewTexture(st);
        } catch (IOException ioe) {
            throw new RuntimeException(ioe);
        }

        Log.d(TAG,"mCamera.startPreview in handleSetSurfaceTexture()");
        mCamera.startPreview();
        isPreview = true;
    }

    @Override
    public void onFrameAvailable(SurfaceTexture st) {
        // The SurfaceTexture uses this to signal the availability of a new frame.  The
        // thread that "owns" the external texture associated with the SurfaceTexture (which,
        // by virtue of the context being shared, *should* be either one) needs to call
        // updateTexImage() to latch the buffer.
        //
        // Once the buffer is latched, the GLSurfaceView thread can signal the encoder thread.
        // This feels backward -- we want recording to be prioritized over rendering -- but
        // since recording is only enabled some of the time it's easier to do it this way.
        //
        // Since GLSurfaceView doesn't establish a Looper, this will *probably* execute on
        // the main UI thread.  Fortunately, requestRender() can be called from any thread,
        // so it doesn't really matter.
        if (VERBOSE) Log.d(TAG, "ST onFrameAvailable");
        requestRender();
    }

    public void flushEncoder() {
    }


    public void AnalyzeEncodeSize_D() {

        int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
        int height = SharedPreferencesUrls.getInstance().getInt("height", 720);

        boolean autoResolution = FileUtil.readSharedPreferencesBoolean2(activity, Constants.SHARED_PREFERENCES, Constants.AUTO_RESOLUTION, true);

        int resultWidth = width;
        int resultHeight = height;

        Log.d(TAG, "AnalyzeEncodeSize: setting resolution = " + width + "x" + height);
        Log.d(TAG, "AnalyzeEncodeSize: autoResolution = " + autoResolution);

        if (!autoResolution)
        {
            Log.d(TAG, "AnalyzeEncodeSize: result size =" + resultWidth + "x" + resultHeight);

            if (mEncodeWidth != resultWidth || mEncodeHeight != resultHeight){

                mEncodeWidth = resultWidth;
                mEncodeHeight = resultHeight;

                needToChangeEncodeSize = true;
            }

            return;
        }

        float ratio = (float) mSurfaceWidth / (float) ConferenceApplication.SCREEN_WIDTH;

        Log.d(TAG, "AnalyzeEncodeSize: ratio = " + ratio);

        if (!videoCommon.isSyncSelf() /*mSurfaceWidth <= 1 || mSurfaceHeight <= 1*/) {
            if (videoCommon.isSpeakerMyself()) {

                Log.d(TAG, "AnalyzeEncodeSize: isSpeakerMyself = " + videoCommon.isSpeakerMyself());

                resultWidth = width;
                resultHeight = height;

//                if (resultWidth > 1280 || resultHeight > 720) {
//                    if (userCommon != null) {
//                        if (!userCommon.isHost()) {
//                            Log.d(TAG, "AnalyzeEncodeSize: Limited as NOT host and speaker");
//                            resultWidth = maxWidthForJoin;
//                            resultHeight = maxHeightForJoin;
//                        }
//                    }
//                }
            }
            else if (userCommon != null)
            {
                int count = userCommon.getUserNumber();

                Log.d(TAG, "AnalyzeEncodeSize: user count = " + count);

                if (count > 9){
                    resultWidth = 640;
                    resultHeight = 360;
                }
                else if (count > 25){
                    resultWidth = 320;
                    resultHeight = 180;
                }
                else
                {
                    if (resultWidth > 1280 || resultHeight > 720) {
                        if (userCommon != null) {
                            //f (!userCommon.isHost()) {
                            Log.d(TAG, "AnalyzeEncodeSize: Limited to 720P");
                            resultWidth = maxWidthForJoin;
                            resultHeight = maxHeightForJoin;
                            //}
                        }
                    }
                }
            }

        } else {
            if (ratio >= 0.66 || videoCommon.isSpeakerMyself())
            {
                resultWidth = width;
                resultHeight = height;
            }
            //大视频或者4路以下
            else if (ratio >= 0.5){
                resultWidth = width;
                resultHeight = height;

                if (resultWidth > 1280 || resultHeight > 720) {
                    if (userCommon != null) {
                        //if (!userCommon.isHost()) {
                        Log.d(TAG, "AnalyzeEncodeSize: Limited as NOT host and speaker");
                        resultWidth = maxWidthForJoin;
                        resultHeight = maxHeightForJoin;
                        //}
                    }
                }
            }
            //小视频或4路以上
            else if (ratio < 0.5 && ratio > 0.33){
                resultWidth = 640;
                resultHeight = 360;
            }
            //9路以上
            else if (ratio <= 0.33){
                resultWidth = 320;
                resultHeight = 180;
            }
        }

        if (resultWidth > width || resultHeight > height){
            resultWidth = width;
            resultHeight = height;

            Log.d(TAG, "AnalyzeEncodeSize: adjusted to: " + width + "x" + height);
        }

        Log.d(TAG, "AnalyzeEncodeSize: result size =" + resultWidth + "x" + resultHeight);

        if (mEncodeWidth != resultWidth || mEncodeHeight != resultHeight){

            mEncodeWidth = resultWidth;
            mEncodeHeight = resultHeight;

            needToChangeEncodeSize = true;
        }

        if (needToChangeEncodeSize){

            Log.d(TAG, "AnalyzeEncodeSize: need to change the encode size:" + mEncodeWidth + "x" + mEncodeHeight);

            AnalyzeBitRateByEncodeSize();

            if (sVideoEncoder.isRecording()){
                // stop recording
                Log.d(TAG, "AnalyzeEncodeSize：STOP recording");
                sVideoEncoder.stopRecording();
            }

            needToChangeEncodeSize = false;
        }
    }

    /*如果会议系统或会议设置，采用自适应，则采用如下规则； 否则还是采用原有规则；
            1.根据视频路数动态调整视频分辨率。 当本端视频被打开并且同步时，以同步视频路数为调整依据。 如本端视频被打开但没有同步时，以主持人同步的视频路数为调整依据。
            2.调整分辨率时，两个调整需求里面较大的为准。
            3.等分布局： 1路   原始分辨率    2-4路    720p或原始分辨率(<720p)     5-9路  640*360或 原始分辨率(<640*360)
                     9路以上   320*180
    主次布局：本端视频为主视频时，原始分辨率
    本端视频非主视频时，640*360

    主持人控制模式的时候，主持人端改为超过9路视频的时候降低到640， 超过25路降低到 320*/

    public void AnalyzeEncodeSizeEx() {

        if (conferenceCommon == null) {
            Log.d(TAG, "AnalyzeEncodeSize: conferenceCommon = NULL");
            return;
        }

        if (userCommon == null) {
            Log.d(TAG, "AnalyzeEncodeSize: userCommon = NULL");
            return;
        }

        int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
        int height = SharedPreferencesUrls.getInstance().getInt("height", 720);

        int resultWidth = width;
        int resultHeight = height;

        Log.d(TAG, "AnalyzeEncodeSize: setting resolution = " + width + "x" + height);
        Log.d(TAG, "AnalyzeEncodeSize: isHost = " + userCommon.isHost());

        if (!conferenceCommon.getVideoAdaptive() || userCommon.isHost())
        {
            Log.d(TAG, "AnalyzeEncodeSize: video is NOT adaptive!");

            Log.d(TAG, "AnalyzeEncodeSize: result size =" + resultWidth + "x" + resultHeight);

            needToChangeEncodeSize = false;

            if (mEncodeWidth != resultWidth || mEncodeHeight != resultHeight){

                mEncodeWidth = resultWidth;
                mEncodeHeight = resultHeight;

                needToChangeEncodeSize = true;
            }

            if (needToChangeEncodeSize){

                Log.d(TAG, "AnalyzeEncodeSize: need to change the encode size:" + mEncodeWidth + "x" + mEncodeHeight);

                AnalyzeBitRateByEncodeSize();

                if (sVideoEncoder.isRecording()){
                    // stop recording
                    Log.d(TAG, "AnalyzeEncodeSize：STOP recording");
                    sVideoEncoder.stopRecording();
                }

                needToChangeEncodeSize = false;
                needToInitMyVideoSize = true;
            }

            videoCommon.setWidth(mEncodeWidth);
            videoCommon.setHeight(mEncodeHeight);

            return;
        }

        Log.d(TAG, "AnalyzeEncodeSize: video is adaptive!");

        //本地视频没有被同步
        if (!videoCommon.isSyncSelf()) {
            //主讲人大视频
            if (videoCommon.isSpeakerMyself()) {

                Log.d(TAG, "AnalyzeEncodeSize: isSpeakerMyself = " + videoCommon.isSpeakerMyself());
                resultWidth = width;
                resultHeight = height;

            }
            else
            {
                int count = videoCommon.getOpenVideoCount();
                Log.d(TAG, "AnalyzeEncodeSize: open video count = " + count);

                if (count == 1)
                {
                    resultWidth = width;
                    resultHeight = height;
                }
                else if (count <= 9){
                    resultWidth = width;
                    resultHeight = height;
                    if (resultWidth > 1280 || resultHeight > 720) {
                        if (userCommon != null) {
                            //f (!userCommon.isHost()) {
                            Log.d(TAG, "AnalyzeEncodeSize: Limited to 720P");
                            resultWidth = maxWidthForJoin;
                            resultHeight = maxHeightForJoin;
                            //}
                        }
                    }
                }
                else if (count <= 25){
                    resultWidth = 640;
                    resultHeight = 360;
                }
                else
                {
                    resultWidth = 320;
                    resultHeight = 180;
                }
            }

        }
        //本地视频被同步
        else {
            //平铺模式
            if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_PLAIN){

                Log.d(TAG, "AnalyzeEncodeSize: sync video count = " + videoCommon.getSyncVideoCount());

                if (videoCommon.getSyncVideoCount() == 1){

                    Log.d(TAG, "AnalyzeEncodeSize: One self video synced and keep size ");
                    resultWidth = width;
                    resultHeight = height;
                }
                else if (videoCommon.getSyncVideoCount() <= 4){
                    resultWidth = width;
                    resultHeight = height;

                    if (resultWidth > 1280 || resultHeight > 720) {
                        if (userCommon != null) {
                            //if (!userCommon.isHost()) {
                            Log.d(TAG, "AnalyzeEncodeSize: Limited 720P as <= 4");
                            resultWidth = maxWidthForJoin;
                            resultHeight = maxHeightForJoin;
                            //}
                        }
                    }
                }
                else  if (videoCommon.getSyncVideoCount() <= 9){
                    resultWidth = 640;
                    resultHeight = 360;
                } else if (videoCommon.getSyncVideoCount() <= 25){
                    resultWidth = 320;
                    resultHeight = 180;
                }
                else {
                    resultWidth = 320;
                    resultHeight = 180;
                }

            }
            //主次模式
            else
            {
                //大视频
                if (videoCommon.isSpeakerMyself()) {

                    Log.d(TAG, "AnalyzeEncodeSize: isSpeakerMyself = " + videoCommon.isSpeakerMyself());
                    resultWidth = width;
                    resultHeight = height;

                }
                else if (videoCommon.getSyncVideoCount() <= 9){
                    resultWidth = 640;
                    resultHeight = 360;
                } else if (videoCommon.getSyncVideoCount() <= 25){
                    resultWidth = 320;
                    resultHeight = 180;
                }
                else {
                    resultWidth = 320;
                    resultHeight = 180;
                }
            }
        }

        if (resultWidth > width || resultHeight > height){
            resultWidth = width;
            resultHeight = height;

            Log.d(TAG, "AnalyzeEncodeSize: adjusted to: " + width + "x" + height);
        }

        Log.d(TAG, "AnalyzeEncodeSize: result size =" + resultWidth + "x" + resultHeight);

        if (mEncodeWidth != resultWidth || mEncodeHeight != resultHeight){

//            if (resolutionSpecified){
//
//                Log.d(TAG, "AnalyzeEncodeSize: Ignored as resolutionSpecified");
//
//                resolutionSpecified = false;
//                return;
//            }

            mEncodeWidth = resultWidth;
            mEncodeHeight = resultHeight;

            needToChangeEncodeSize = true;
        }

        if (needToChangeEncodeSize){

            Log.d(TAG, "AnalyzeEncodeSize: need to change the encode size:" + mEncodeWidth + "x" + mEncodeHeight);

            AnalyzeBitRateByEncodeSize();

            if (sVideoEncoder.isRecording()){
                // stop recording
                Log.d(TAG, "AnalyzeEncodeSize：STOP recording");
                sVideoEncoder.stopRecording();
            }

            needToChangeEncodeSize = false;
            needToInitMyVideoSize = true;
        }

        videoCommon.setWidth(mEncodeWidth);
        videoCommon.setHeight(mEncodeHeight);
    }


    public void AnalyzeEncodeSize(int width, int height) {

        int resultWidth = width;
        int resultHeight = height;

        Log.d(TAG, "AnalyzeEncodeSize（2）: width = " + width + "; height = " + height);

//        if (resultWidth > 1280 || resultHeight > 720) {
//            if (userCommon != null) {
//                if (!userCommon.isHost()) {
//                    Log.d(TAG, "AnalyzeEncodeSize: Limited as NOT host and speaker");
//                    resultWidth = maxWidthForJoin;
//                    resultHeight = maxHeightForJoin;
//                }
//            }
//        }

        Log.d(TAG, "AnalyzeEncodeSize(2): result size =" + resultWidth + "x" + resultHeight);

        if (mEncodeWidth != resultWidth || mEncodeHeight != resultHeight){

            mEncodeWidth = resultWidth;
            mEncodeHeight = resultHeight;

            needToChangeEncodeSize = true;

            if (needToChangeEncodeSize){

                Log.d(TAG, "onSurfaceChanged: need to change the encode size:" + mEncodeWidth + "x" + mEncodeHeight);

                AnalyzeBitRateByEncodeSize();

                if (sVideoEncoder.isRecording()){
                    // stop recording
                    Log.d(TAG, "STOP recording");
                    sVideoEncoder.stopRecording();
                }

                needToChangeEncodeSize = false;

                resolutionSpecified = true;
            }
        }

        videoCommon.setWidth(mEncodeWidth);
        videoCommon.setHeight(mEncodeHeight);
    }

    public void enableCamera(boolean enabled){isEnabled=enabled;}
    public boolean isEnabled(){return isEnabled;}

    /**
     * 设置相机显示方向的详细解读
     **/
    public void setCameraDisplayOrientation(Activity activity,
                                            int cameraId, Camera mCamera) {
        // 1.获取屏幕切换角度值。
        int rotation = activity.getWindowManager().getDefaultDisplay()
                .getRotation();

        int degree = 0;
        switch (rotation) {
            case Surface.ROTATION_0: degree = 0; break;
            case Surface.ROTATION_90: degree = 90; break;
            case Surface.ROTATION_180: degree = 180; break;
            case Surface.ROTATION_270: degree = 270; break;
        }
        // 2.获取摄像头方向。
        Camera.CameraInfo info =
                new Camera.CameraInfo();
        Camera.getCameraInfo(cameraId, info);
        // 3.设置相机显示方向。
        int result;
        if (info.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {

            Log.d(TAG, "Camera.CameraInfo.CAMERA_FACING_FRONT: info.orientation=" + info.orientation);
            result = (info.orientation + degree) % 360;
            result = (360 - result) % 360;  // compensate the mirror
        } else {  // back-facing
            Log.d(TAG, "Camera.CameraInfo.CAMERA_FACING_BACK: info.orientation=" + info.orientation);
            result = (info.orientation - degree + 360) % 360;
        }

        degrees = result;

        Log.d(TAG, "setCameraDisplayOrientation: degrees=" + result);

        mCamera.setDisplayOrientation(result);
    }

    @SuppressLint("NewApi")
    public void startCamera() {

        openCamera();

        if (mCamera == null) {
            Log.d(TAG,"openCamera failed in startCamera()");
            return;
        }

        setCameraParameters(degrees);

        queueEvent(new Runnable() {
            @Override public void run() {
                mRenderer.setCameraPreviewSize(mCameraPreviewWidth, mCameraPreviewHeight);
            }
        });

        if (mRenderer.getSurfaceTexture() != null && !isPreviewStarted()) {

            mRenderer.getSurfaceTexture().setOnFrameAvailableListener(this);

            try {
                Log.d(TAG,"mCamera.setPreviewTexture in startCamera()");
                mCamera.setPreviewTexture(mRenderer.getSurfaceTexture());

            } catch (IOException e) {
                Log.e(TAG, "mCamera.setPreviewTexture failed:" + e.getMessage());
                e.printStackTrace();
            }

            Log.d(TAG,"mCamera.startPreview in startCamera()");
            changePreview(true);
            isPreview = true;
        }

        setBackgroundColor(0);
    }

    public void openCamera(){

        if (mCamera != null) return;

        if (Integer.parseInt(Build.VERSION.SDK) > 8) {
            numOfCamera = Camera.getNumberOfCameras();

            Log.d(TAG,"GLVideoEncodeViewEx.openCamera: numOfCamera = " + numOfCamera);

            if (numOfCamera == 1) {
                currentCamera = Camera.CameraInfo.CAMERA_FACING_BACK;

                if (Build.MODEL.indexOf("HDX2") != -1){
                    //if (mCurrentFilter != null) mCurrentFilter.setMirror(true);
                    mMirror = true;
                }

                try {
                    mCamera = Camera.open(currentCamera);
                }catch (Exception e){
                    Log.d(TAG,"GLVideoEncodeViewEx.openCamera Error: " + e.getMessage());
                    numOfCamera = 0;
                    e.printStackTrace();
                }
            } else {
                if (isFlow){
                    currentCamera = Camera.CameraInfo.CAMERA_FACING_BACK;
                }else {
                    currentCamera = Camera.CameraInfo.CAMERA_FACING_FRONT;
                    //if (mCurrentFilter != null) mCurrentFilter.setMirror(true);
                    mMirror = true;
                }
                try {
                    mCamera = Camera.open(currentCamera);
                } catch (Exception e) {
                    Log.d(TAG,"GLVideoEncodeView.openCamera Error(2): " + e.getMessage());
                    numOfCamera = 0;
                    e.printStackTrace();
                }
            }
        } else {
            try {
                mCamera = Camera.open(currentCamera);
            }catch (Exception e){
                numOfCamera = 0;
                e.printStackTrace();
                Log.d(TAG,"GLVideoEncodeView.openCamera Error(3): " + e.getMessage());
            }
        }

        Log.d(TAG,"GLVideoEncodeView.openCamera Success: " + currentCamera);

        if (Build.MODEL.toUpperCase().startsWith("Lenovo".toUpperCase())){
            mCamera.setDisplayOrientation(0);
        }
    }
    /*
     * 设置相机属性
     */
    private void setCameraParameters(int degrees) {
        Camera.Parameters parameters = mCamera.getParameters();
        List<Camera.Size> previewSizes = parameters.getSupportedPreviewSizes();
        List<int[]> rates = parameters.getSupportedPreviewFpsRange();
        mCameraPreviewWidth = 0;
        mCameraPreviewHeight = 0;

        int perferWidth = SharedPreferencesUrls.getInstance().getInt("width", 1280);
        int perferHeight = SharedPreferencesUrls.getInstance().getInt("height", 720);

        Log.d(TAG,"GLVideoEncodeView: Saved Size: " + perferWidth + "x" + perferHeight);

        if (perferWidth < 1920 || perferHeight < 1080)
        {
            perferWidth = 1280;
            perferHeight = 720;
        }

        Log.d(TAG,"GLVideoEncodeView: Preferred Size: " + perferWidth + "x" + perferHeight);

        int defaultWidth = 0;
        int defaultHeight = 0;

        // 取比设定值小的像素中最大的
        for (Camera.Size size : previewSizes) {

            Log.d(TAG,"GLVideoEncodeView: Support resolution: " + size.width + "x" + size.height);

            if (size.width == perferWidth && size.height == perferHeight) {
                mCameraPreviewWidth = size.width;
                mCameraPreviewHeight = size.height;
            }

            if (size.width == perferWidth || size.height == perferHeight){
                defaultWidth = size.width;
                defaultHeight = size.height;
            }
        }
        // 如果设定值实在太小，取所支持的最小像素
        if (mCameraPreviewWidth <= 0 || mCameraPreviewHeight <= 0) {

            mCameraPreviewWidth = defaultWidth;
            mCameraPreviewHeight = defaultHeight;

            if (mCameraPreviewWidth <= 0 || mCameraPreviewHeight <= 0) {
                for (Camera.Size size : previewSizes) {
                    if (mCameraPreviewWidth == 0) {
                        mCameraPreviewWidth = size.width;
                        mCameraPreviewHeight = size.height;
                        continue;
                    }
                    if (size.width * size.height > mCameraPreviewWidth * mCameraPreviewHeight) {
                        mCameraPreviewWidth = size.width;
                        mCameraPreviewHeight = size.height;
                    }
                }
            }
        }

        Log.d(TAG,"Camera.setPreviewSize = " + mCameraPreviewWidth + "x" + mCameraPreviewHeight);

        //frameSize = mCameraPreviewWidth * mCameraPreviewHeight;
        //mEncodeWidth = mCameraPreviewWidth;
        //mEncodeHeight = mCameraPreviewHeight;

        int minimum = 0;
        int maximum = 0;
        int retMininum = 0;
        int retMaximum = 0;
        if (rates.size() > 0) {
            minimum = rates.get(0)[0];
            maximum = rates.get(0)[1];
            retMininum = rates.get(0)[0];
            retMaximum = rates.get(0)[1];
            for (int[] fps : rates){
                if (minimum < fps[0]){
                    minimum = fps[0];
                }
                if (maximum < fps[1]){
                    maximum = fps[1];
                }
            }
        }

        setCameraDisplayOrientation((ActConf) activity, currentCamera, mCamera);

        parameters.setPreviewSize(mCameraPreviewWidth, mCameraPreviewHeight);//设置预览的高度和宽度,单位为像素
        //	    parameters.setPreviewFrameRate(FRAME_RATE);//设置图片预览的帧速。
        //	    parameters.setPreviewFpsRange(minimum, maximum);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){
                if (minimum > 0 && maximum > 0){

                    Log.d(TAG,"Camera.setPreviewFpsRange(1) = " + minimum + "=>" + maximum);
                    parameters.setPreviewFpsRange(minimum,maximum);
                }
            }else {
                if (retMininum > 0 && retMaximum > 0){
                    Log.d(TAG,"Camera.setPreviewFpsRange(2) = " + retMininum + "=>" + retMaximum);
                    parameters.setPreviewFpsRange(retMininum,retMaximum);
                }
            }
        }catch (RuntimeException e){
            if (retMininum > 0 && retMaximum > 0){
                Log.d(TAG,"Camera.setPreviewFpsRange(3) = " + retMininum + "=>" + retMaximum);
                parameters.setPreviewFpsRange(retMininum,retMaximum);
            }
        }

        //让相机一直运行在视频录制的状态下，由于视频录制的时候一般需要高帧率才能保证数据的完整，所以该状态下的输出帧率会保持所能达到的最大帧率
        parameters.setRecordingHint(true);

        /*List<String> focusModes = parameters.getSupportedFocusModes();
        if (focusModes.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE)){
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);// 1连续对焦
            mCamera.setParameters(parameters);
            mCamera.cancelAutoFocus();
        }else{
            try {
                mCamera.setParameters(parameters);
            }
            catch (RuntimeException e)
            {
                Camera.Parameters appliedParam = mCamera.getParameters();
                mCameraPreviewWidth = appliedParam.getPreviewSize().width;
                mCameraPreviewHeight = appliedParam.getPreviewSize().height;

                Log.d(TAG,"Camera.CameraParameters(FAILED) = " + mCameraPreviewWidth + ", " + mCameraPreviewHeight);

            }
        }*/

        //AnalyzeBitRateByEncodeSize();

        //needToInitMyVideo = true;
    }

    private void AnalyzeBitRateByEncodeSize() {

        //2022/8/1 编码：默认帧率25帧、码流1080P -1.5M，720P-800K， 640-400K，320-200K

        int bitrate = 0;
        int picSize = mEncodeWidth * mEncodeHeight;
        if (picSize <= 320 * 240) {
            //bitrate = 256*1024;
            bitrate = 200*1024;
            //        } else if (picSize <= 352 * 288) {
            //            bitrate = 240000;
        } else if (picSize <= 640 * 480) {
            //bitrate = 512*1024;
            bitrate = 400*1024;
            //        } else if (picSize <= 960 * 720) {
            //            bitrate = 700000;
        } else if (picSize <= 1280 * 720) {
            //bitrate = 1024*1024;
            bitrate = 800*1024;
            //            bitrate = 700000;
        } else if (picSize <= 1920 * 1080) {
            //bitrate = 2048*1024;
            bitrate = 1536*1024;
        } else {
            bitrate = 3072*1024;
        }

        mBitRate = bitrate;

        Log.d(TAG,">>>>> AnalyzeBitRateByEncodeSize: mBitRate = " + mBitRate);
    }

    private void setDisplayOrientation(Camera mCamera, int angle) {
        Method downPolymorphic;
        try
        {
            downPolymorphic = mCamera.getClass().getMethod("setDisplayOrientation", new Class[] { int.class });
            if (downPolymorphic != null)
                downPolymorphic.invoke(mCamera, new Object[] { angle });
        }
        catch (Exception e1)
        {
        }
    }

    public void destroyCamera() {

        if (mCamera != null) {
            //Log.d(TAG,"destroyCamera");
            Log.d(TAG,"stopPreview and releaseCamera");
            mCamera.setPreviewCallback(null);
            changePreview(false);
            mCamera.stopPreview();
            // 停止更新预览
            mCamera.release();// 释放资源
            mCamera = null;
        }
    }

    public void preLeave() {

        if (mCamera != null) {
            Log.d(TAG,"GLVideoEncodeView.preLeave: stop and destroy mCamera");
            //Log.d(TAG,"stopPreview and releaseCamera");

            mCamera.setPreviewCallback(null);
            changePreview(false);
            mCamera.stopPreview();
            // 停止更新预览
            mCamera.release();// 释放资源

            mCamera = null;
            isSharing = false;
            enableCamera(false);

            if (sVideoEncoder.isRecording()) {
                sVideoEncoder.stopRecording();
            }
        }
    }

    public void reStartLocalView() {
        // if(mCamera != null){

        if (CallbackManager.IS_LEAVED) return;

        Log.d(TAG,"GLVideoEncoderViewEx.reStartLocalView");
        if (mCamera == null) {
            changeStatus(true);
        } else {
            if (isDestroyed) {

                toggleEncode(false);
                destroyCamera();

//                    if (!encodeByCallback) {
//                        if (hwEncoderWrapperEx != null && mEncoderState == STATE_ON) {
//                            hwEncoderWrapperEx.stopRecording();
//                            mEncoderState = STATE_OFF;
//                        }
//                    }
//                    else
//                    {
//                        if (h264HwEncoderImpl.GetMediaEncoder() != null && mEncoderState == STATE_ON) {
//                            h264HwEncoderImpl.releaseEncoder();
//                            mEncoderState = STATE_OFF;
//                        }
//                    }
                currentCamera = (currentCamera + 2) % numOfCamera;
                startCamera();
            }else {
                toggleEncode(false);
                destroyCamera();
//                    if (!encodeByCallback) {
//                        if (hwEncoderWrapperEx != null && mEncoderState == STATE_ON) {
//                            hwEncoderWrapperEx.stopRecording();
//                            mEncoderState = STATE_OFF;
//                        }
//                    }
//                    else
//                    {
//                        if (h264HwEncoderImpl.GetMediaEncoder() != null && mEncoderState == STATE_ON) {
//                            h264HwEncoderImpl.releaseEncoder();
//                            mEncoderState = STATE_OFF;
//                        }
//                    }
                startCamera();
                //				videoCommon.exChange(mCameraPreviewHeight, mCameraPreviewWidth);
            }
        }
    }

    public void setStatus(boolean isMove) {
        if (isMove) {
            //mCamera.setPreviewCallback(null);
            changePreview(false);
        } else {
            //mCamera.setPreviewCallback(this);
            changePreview(true);
        }
    }

    public void changeStatus(boolean isOpenCamera) {

        Log.d(TAG,"GLVideoEncoderViewEx.changeStatus = " + isOpenCamera);

        if (isOpenCamera) {
            if (mCamera == null) {
                //invalidate();
                //init(activity);
                startCamera();
            }
        } else {
            if (mCamera != null) {
                destroyCamera();
            }
        }
    }

    private void changePreview(boolean state) {
        try {
            if (state) {
                Log.d(TAG,"GLVideoEncoderViewEx.startPreview:" + state );

                mCamera.startPreview();
                isPreview = true;
            } else {
                Log.d(TAG,"GLVideoEncoderViewEx.stopPreview:" + state );

                mCamera.stopPreview();
                isPreview = false;
            }
        } catch (Exception e) {
            Log.e(TAG,e.getMessage());
        }
    }

    public boolean getCamera() {
        return mCamera != null;
    }

    public boolean isSharing() {
        return isSharing;
    }

    public void setSharing(boolean sharing) {

        if (isSharing == sharing || mCamera == null) return;

        isSharing = sharing;

        Log.d(TAG, "GLVideoEncoderViewEx.setSharing: "+ isSharing);

        //if (!isSharing) needToInitMyVideo = true;

        if (isSharing && !isPreviewStarted()){
            if (mRenderer.getSurfaceTexture() != null && !isPreviewStarted()) {
                try {
                    Log.d(TAG,"mCamera.setPreviewTexture in setSharing()");
                    mCamera.setPreviewTexture(mRenderer.getSurfaceTexture());

                } catch (IOException e) {
                    Log.e(TAG, e.getMessage());
                    e.printStackTrace();
                }

                Log.d(TAG,"mCamera.startPreview in setSharing()");
                changePreview(true);
                isPreview = true;
            }
        }

        if (isSharing()) {
            needToInitMyVideoSize = true;
            mLastNoSharingTime = -1;
        }
        else
        {
            mLastNoSharingTime = System.currentTimeMillis();
        }
    }

    public void setCameraLandscape() {
        degrees = 0;
        reStartLocalView();
    }

    public void setParams(int width,int height) {

        if (mViewWidth == width && mViewHeight == height)
            return;

        if (CallbackManager.IS_LEAVED) return;

        Log.d(TAG, "GLVideoEncoderViewEx.setParams: " + width + "x" + height);

        mViewWidth = width;
        mViewHeight = height;

        if(width>1&&mCamera==null&&isEnabled()){
            reStartLocalView();
        }
        if(width <= 1 || height <= 1){
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = 1;
            params.height = 1;
            setLayoutParams(params);
            setRenderEnabled(false);
        }else {

            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();

            params.setMargins(0,0,0,0);

            params.width = width;//RelativeLayout.LayoutParams.MATCH_PARENT;
            params.height = height;//RelativeLayout.LayoutParams.MATCH_PARENT;
            setLayoutParams(params);

            setRenderEnabled(true);
        }
    }

    public boolean isPreviewStarted() {
        return isPreview;
    }

    public void switchSoftEncode(boolean isSoft){
        //isHardCodec = videoCommon.isHardCodec();
        reStartLocalView();
    }
    /**
     * Handles camera operation requests from other threads.  Necessary because the Camera
     * must only be accessed from one thread.
     * <p>
     * The object is created on the UI thread, and all handlers run there.  Messages are
     * sent from other threads, using sendMessage().
     */
    static class CameraHandler extends Handler {
        public static final int MSG_SET_SURFACE_TEXTURE = 0;
        public static final int MSG_START_ENCODE = 1;
        public static final int MSG_STOP_ENCODE = 2;

        // Weak reference to the Activity; only access this from the UI thread.
        private WeakReference<GLVideoEncodeViewEx> mWeakVideoEncodeView;

        public CameraHandler(GLVideoEncodeViewEx encodeView) {
            mWeakVideoEncodeView = new WeakReference<GLVideoEncodeViewEx>(encodeView);
        }

        /**
         * Drop the reference to the activity.  Useful as a paranoid measure to ensure that
         * attempts to access a stale Activity through a handler are caught.
         */
        public void invalidateHandler() {
            mWeakVideoEncodeView.clear();
        }

        @Override  // runs on UI thread
        public void handleMessage(Message inputMessage) {
            int what = inputMessage.what;
            Log.d(TAG, "CameraHandler [" + this + "]: what=" + what);

            GLVideoEncodeViewEx encodeView = mWeakVideoEncodeView.get();
            if (encodeView == null) {
                Log.w(TAG, "CameraHandler.handleMessage: encodeView is null");
                return;
            }

            switch (what) {
                case MSG_SET_SURFACE_TEXTURE:
                    encodeView.handleSetSurfaceTexture((SurfaceTexture) inputMessage.obj);
                    break;
                case MSG_START_ENCODE:
                    encodeView.toggleEncode(true);
                    break;
                case MSG_STOP_ENCODE:
                    encodeView.toggleEncode(false);
                    break;
                default:
                    throw new RuntimeException("unknown msg " + what);
            }
        }
    }

    /**
     * Renderer object for our GLSurfaceView.
     * <p>
     * Do not call any methods here directly from another thread -- use the
     * GLSurfaceView#queueEvent() call.
     */
    class CameraSurfaceRenderer implements GLSurfaceView.Renderer {

        private static final int RECORDING_OFF = 0;
        private static final int RECORDING_ON = 1;
        private static final int RECORDING_RESUMED = 2;

        private CameraHandler mCameraHandler;
        private TextureMovieEncoder mVideoEncoder;
        private File mOutputFile;

        private FullFrameRect mFullScreen;

        private final float[] mSTMatrix = new float[16];
        private int mTextureId;

        private SurfaceTexture mSurfaceTexture;
        private boolean mRecordingEnabled;
        private int mRecordingStatus;
        private int mFrameCount;

        // width/height of the incoming camera preview frames
        private boolean mIncomingSizeUpdated;
        private int mIncomingWidth;
        private int mIncomingHeight;

        /**
         * Constructs CameraSurfaceRenderer.
         * <p>
         * @param cameraHandler Handler for communicating with UI thread
         * @param movieEncoder video encoder object
         * @param outputFile output file for encoded video; forwarded to movieEncoder
         */
        public CameraSurfaceRenderer(CameraHandler cameraHandler,
                                     TextureMovieEncoder movieEncoder, File outputFile) {
            mCameraHandler = cameraHandler;
            mVideoEncoder = movieEncoder;
            mOutputFile = outputFile;

            mTextureId = -1;

            mRecordingStatus = -1;
            mRecordingEnabled = false;
            mFrameCount = -1;

            mIncomingSizeUpdated = false;
            mIncomingWidth = mIncomingHeight = -1;

        }

        public SurfaceTexture getSurfaceTexture() {
            return mSurfaceTexture;
        }

        /**
         * Notifies the renderer thread that the activity is pausing.
         * <p>
         * For best results, call this *after* disabling Camera preview.
         */
        public void notifyPausing() {
            if (mSurfaceTexture != null) {
                Log.d(TAG, "renderer pausing -- releasing SurfaceTexture");
                mSurfaceTexture.release();
                mSurfaceTexture = null;
            }
            if (mFullScreen != null) {
                mFullScreen.release(false);     // assume the GLSurfaceView EGL context is about
                mFullScreen = null;             //  to be destroyed
            }
            mIncomingWidth = mIncomingHeight = -1;
        }

        /**
         * Notifies the renderer that we want to stop or start recording.
         */
        public void changeRecordingState(boolean isRecording) {
            Log.d(TAG, "changeRecordingState: was " + mRecordingEnabled + " now " + isRecording);
            mRecordingEnabled = isRecording;
        }

        /**
         * Records the size of the incoming camera preview frames.
         * <p>
         * It's not clear whether this is guaranteed to execute before or after onSurfaceCreated(),
         * so we assume it could go either way.  (Fortunately they both run on the same thread,
         * so we at least know that they won't execute concurrently.)
         */
        public void setCameraPreviewSize(int width, int height) {
            Log.d(TAG, "setCameraPreviewSize");
            mIncomingWidth = width;
            mIncomingHeight = height;
            mIncomingSizeUpdated = true;
        }

        @Override
        public void onSurfaceCreated(GL10 unused, EGLConfig config) {
            Log.d(TAG, "onSurfaceCreated");

        }

        @Override
        public void onSurfaceChanged(GL10 unused, int width, int height) {

            Log.d(TAG, "onSurfaceChanged " + width + "x" + height);

            if (!isEnabled) return;

            mSurfaceWidth = width;
            mSurfaceHeight = height;

            invalidateSurfaceTexture();

            if (mSurfaceCreated)
                GLES20.glViewport(0, 0, width, height);

            //本地没有被显示，但也没有被同步
            if (mSurfaceWidth <= 1 && mSurfaceHeight <= 1 && !videoCommon.isSyncSelf()){
                Log.d(TAG, "onSurfaceChanged: No need to analyze encode size:" + mSurfaceWidth + "x" + mSurfaceHeight);
                return;
            }

            AnalyzeEncodeSizeEx();
        }

        private void invalidateSurfaceTexture() {

            if (!mSurfaceCreated) {
                // We're starting up or coming back.  Either way we've got a new EGLContext that will
                // need to be shared with the video encoder, so figure out if a recording is already
                // in progress.
//                mRecordingEnabled = mVideoEncoder.isRecording();
//                if (mRecordingEnabled) {
//                    mRecordingStatus = RECORDING_RESUMED;
//                } else {
//                    mRecordingStatus = RECORDING_OFF;
//                }

                Log.d(TAG, "Create SurfaceTexture");

                // Set up the texture blitter that will be used for on-screen display.  This
                // is *not* applied to the recording, because that uses a separate shader.
                mFullScreen = new FullFrameRect(
                        new Texture2dProgram(Texture2dProgram.ProgramType.TEXTURE_EXT));

                mTextureId = mFullScreen.createTextureObject();

                // Create a SurfaceTexture, with an external texture, in this EGL context.  We don't
                // have a Looper in this thread -- GLSurfaceView doesn't create one -- so the frame
                // available messages will arrive on the main thread.
                mSurfaceTexture = new SurfaceTexture(mTextureId);

                // Tell the UI thread to enable the camera preview.
                mCameraHandler.sendMessage(mCameraHandler.obtainMessage(
                        CameraHandler.MSG_SET_SURFACE_TEXTURE, mSurfaceTexture));

                mSurfaceCreated = true;
            }
        }

        @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR1)
        @Override
        public void onDrawFrame(GL10 unused) {

            if (VERBOSE) Log.d(TAG, "onDrawFrame tex=" + mTextureId);

            invalidateSurfaceTexture();

            //boolean showBox = false;

            // Latch the latest frame.  If there isn't anything new, we'll just re-use whatever
            // was there before.
            mSurfaceTexture.updateTexImage();

            if (isSharing()) {

                if (needToInitMyVideoSize) {
                    Log.d(TAG, ">>>>> initializeMyVideo: " + mEncodeWidth + "x" + mEncodeHeight);
                    videoCommon.initializeMyVideo(mEncodeWidth, mEncodeHeight, cameraFPS);
                    needToInitMyVideoSize = false;
                }

                if (!mVideoEncoder.isRecording()) {
                    Log.d(TAG,"onDrawFrame: START Recording: " + mEncodeWidth + "x" + mEncodeHeight);

                    if (mEncodeWidth <= 0 || mEncodeHeight <= 0 || (videoCommon != null && !videoCommon.isSyncSelf() && !resolutionSpecified)){
                        AnalyzeEncodeSizeEx();
                        Log.d(TAG,">>>>> onDrawFrame: AnalyzeEncodeSizeEx: " + mEncodeWidth + "x" + mEncodeHeight);
                    }

                    resolutionSpecified = false;

                    // start recording
                    mVideoEncoder.startRecording(new TextureMovieEncoder.EncoderConfig(
                            videoCommon, null, mOutputFile, mEncodeWidth, mEncodeHeight, mBitRate, EGL14.eglGetCurrentContext()));
                }

            }
            else {

                if (mLastNoSharingTime > 0){

                    long timeElapsed = System.currentTimeMillis() - mLastNoSharingTime;

                    if (timeElapsed > 30*1000) {
                        if (mVideoEncoder.isRecording()) {
                            // stop recording
                            Log.d(TAG, "onDrawFrame: STOP recording");
                            mVideoEncoder.stopRecording();
                        }

                        mLastNoSharingTime = -1;
                    }
                }
            }

            if (mIncomingWidth <= 0 || mIncomingHeight <= 0) {
                // Texture size isn't set yet.  This is only used for the filters, but to be
                // safe we can just skip drawing while we wait for the various races to resolve.
                // (This seems to happen if you toggle the screen off/on with power button.)
                Log.d(TAG, "Drawing before incoming texture size set; skipping");
                return;
            }

            mVideoEncoder.setShare(isSharing());

            //mirror horizontally
            mVideoEncoder.setMirror(mMirror);

            // Set the video encoder's texture name.  We only need to do this once, but in the
            // current implementation it has to happen after the video encoder is started, so
            // we just do it here.
            //
            // TODO: be less lame.
            mVideoEncoder.setTextureId(mTextureId);

            // Tell the video encoder thread that a new frame is available.
            // This will be ignored if we're not actually recording.
            mVideoEncoder.frameAvailable(mSurfaceTexture);

            if (mIncomingSizeUpdated) {
                mFullScreen.getProgram().setTexSize(mIncomingWidth, mIncomingHeight);
                mIncomingSizeUpdated = false;
            }

            // Draw the video frame.
            mSurfaceTexture.getTransformMatrix(mSTMatrix);

            if (mMirror) {
                //以列为主序，左右镜像
                mSTMatrix[0] = -1 * mSTMatrix[0];
                mSTMatrix[12] = 1.0f - mSTMatrix[12];
            }

            mFullScreen.drawFrame(mTextureId, mSTMatrix);

            // Draw a flashing box if we're recording.  This only appears on screen.
//            showBox = (mRecordingStatus == RECORDING_ON);
//            if (showBox && (++mFrameCount & 0x04) == 0) {
//                drawBox();
//            }

            if (mCheckFrameRate) {
                if (lastCheckTime < 0) {
                    lastCheckTime = System.currentTimeMillis();
                    realFrameRate = 0;
                    return;
                } else {
                    if (System.currentTimeMillis() - lastCheckTime < 1000) {
                        realFrameRate++;
                    } else {
                        realFrameRate++;
                        Log.d(TAG, ">>>>> onDrawFrame: Camera Frame Rate = " + realFrameRate);
                        realFrameRate = 0;
                        lastCheckTime = System.currentTimeMillis();
                    }
                }
            }
        }

        /**
         * Draws a red box in the corner.
         */
        private void drawBox() {
            GLES20.glEnable(GLES20.GL_SCISSOR_TEST);
            GLES20.glScissor(0, 0, 100, 100);
            GLES20.glClearColor(1.0f, 0.0f, 0.0f, 1.0f);
            GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT);
            GLES20.glDisable(GLES20.GL_SCISSOR_TEST);
        }
    }

    public boolean existCamera(){
        if (numOfCamera < 0)
            numOfCamera = Camera.getNumberOfCameras();
        return (numOfCamera > 0);
    }
}
