package com.infowarelab.tvbox.utils;

import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.infowarelab.tvbox.base.PreferenceServer;
import com.infowarelabsdk.conference.util.Constants;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.UUID;

import static android.text.TextUtils.isEmpty;

/**
 * Created by Always on 2017/10/25.
 */

public class DeviceIdFactory {

    private static String deviceId = null;
    private static String shareCode = null;

    public static String getDeviceId(Context context) {
//        String id = PreferenceServer.readSharedPreferencesStirng(context, Constants.SHARED_PREFERENCES, PreferenceServer.DEVICEID);
//        if(id!=null&&!id.equals("")){
//            return id;
//        }else {
//            id = getNewId(context);
//            PreferenceServer.saveSharedPreferences(context,Constants.SHARED_PREFERENCES,PreferenceServer.DEVICEID,id);
//            return id;
//        }
         return deviceId;
    }
    public static String getShareCode() {
        return shareCode;
    }


    public static String getRandomCode() {


        if (deviceId != null && deviceId.length() > 0) return deviceId;

        String randomcode = "";
        // 用字符数组的方式随机
        String model = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        char[] m = model.toCharArray();
        for (int j = 0; j < 6; j++) {
            char c = m[(int) (Math.random() * 26)];
            // 保证六位随机数之间没有重复的
            if (randomcode.contains(String.valueOf(c))) {
                j--;
                continue;
            }
            randomcode = randomcode + c;
        }
        deviceId = randomcode;

        //Log.d("InfowareLab.Debug", ">>>>>> deviceId = " + deviceId);

        return randomcode;
    }

    public static String getNewId2(Context context){

        if (deviceId != null && deviceId.length() > 0) return deviceId;

        try {
            //如果上面都没有， 则生成一个id：随机码
            String uuid = getUUID();
            if(isEmpty(uuid)){
                String mac = getMacAddress();
                if(!isEmpty(mac)){
                    //deviceId.append("wifi");
                    //deviceId.append(lanMac);

                    mac = mac.replaceAll( "\\d+" , "");
                    mac = mac.replaceAll( "-" , "");

                    mac = mac.toUpperCase().substring(0,6);

                    deviceId = mac;
                    return mac;
                }
            }
            else
            {
                uuid = uuid.replaceAll( "\\d+" , "");
                uuid = uuid.replaceAll( "-" , "");

                uuid = uuid.toUpperCase().substring(0,6);

                deviceId = uuid;
                return uuid;
            }

        } catch (Exception e) {
            e.printStackTrace();
            //deviceId.append("id").append(getUUID());
        }
        return "ASDTRW";
    }

    public static String getNewId(Context context){
        //StringBuilder deviceId = new StringBuilder();
        // 渠道标志
        //deviceId.append("a");
        try {
            //wifi mac地址
//            WifiManager wifi = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
//            WifiInfo info = wifi.getConnectionInfo();
//            String wifiMac = info.getMacAddress();
//            if(!isEmpty(wifiMac)){
//                //deviceId.append("wifi");
//                //deviceId.append(wifiMac);
//                return wifiMac;
//            }

            String mac = getMacAddress();
            if(!isEmpty(mac)){
                //deviceId.append("wifi");
                //deviceId.append(lanMac);
                return mac;
            }

//            String lanMac = getLanMacAddress();
//            if(!isEmpty(lanMac)){
//                //deviceId.append("wifi");
//                //deviceId.append(lanMac);
//                return lanMac;
//            }

            //IMEI（imei）
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            String imei = tm.getDeviceId();
            if(!isEmpty(imei)){
                //deviceId.append("imei");
                //deviceId.append(imei);
                return imei;
            }

            //序列号（sn）
            String sn = tm.getSimSerialNumber();
            if(!isEmpty(sn)){
                //deviceId.append("sn");
                //deviceId.append(sn);
                return sn;
            }

            //如果上面都没有， 则生成一个id：随机码
            String uuid = getUUID();
            if(!isEmpty(uuid)){
                //deviceId.append("id");
                //deviceId.append(uuid);
                return uuid;
            }
        } catch (Exception e) {
            e.printStackTrace();
            //deviceId.append("id").append(getUUID());
        }
        return "unknown-device-id";
    }
    /**
     *  获取可变UUID
     */
    public static String getUUID(){
        String uuid = UUID.randomUUID().toString();
        return uuid;
    }
    /**
     * UUID+设备号序列号 唯一识别码（不可变）
     * */
    public static String getUUID1(Context context){
//        final TelephonyManager tm = (TelephonyManager) mContext.getSystemService(Context.TELEPHONY_SERVICE);
//        final String tmDevice, tmSerial, tmPhone, androidId;
//        tmDevice = "" + tm.getDeviceId();
//        tmSerial = "" + tm.getSimSerialNumber();
//        androidId = "" + android.provider.Settings.Secure.getString(mContext.getContentResolver(),android.provider.Settings.Secure.ANDROID_ID);
//        UUID deviceUuid = new UUID(androidId.hashCode(), ((long)tmDevice.hashCode() << 32) | tmSerial.hashCode());
//        String uniqueId = deviceUuid.toString().replace("-","");
//        String uuid = "";
////        Log.e("gggg","uniqueId::"+uniqueId);
//        uuid =  uniqueId.substring(8,16).replaceAll("(.{2})","$1-").toUpperCase();
//        String strDeviceId =  uuid.substring(0,uuid.length()-1);

        //String strDeviceId = getRandomCode();

        String strDeviceId = getMacAddress();

        //Log.d("InfowareLab.Debug", ">>>>>> DeviceId = " + strDeviceId);

        return strDeviceId;
    }

    private static String getMacAddress() {

        String wifiMacAddress = "";
        String lanMacAddress = "";
        int netType = -1;

        NetworkInterface intf;
        Enumeration<NetworkInterface> interfaces = null;
        try {
            interfaces = NetworkInterface.getNetworkInterfaces();
        } catch (SocketException e) {
            e.printStackTrace();
            return "";
        }

        while (interfaces.hasMoreElements()) {
            intf = interfaces.nextElement();
            String name = intf.getName().toLowerCase();
            if (null != intf && TextUtils.equals("wlan0", name)) {
                netType = 1;
            }
            else if (null != intf && TextUtils.equals("eth0", name)) {
                netType = 2;
            }
            else
                netType = -1;

            if (netType > 0) {

                byte[] mac = null;

                try {
                    mac = intf.getHardwareAddress();
                } catch (SocketException e) {
                    e.printStackTrace();
                }

                if (mac != null) {
                    StringBuilder buf = new StringBuilder();
                    for (byte aMac : mac) {
                        buf.append(String.format("%02X:", aMac));
                    }
                    if (buf.length() > 0) {
                        buf.deleteCharAt(buf.length() - 1);
                    }

                    if (netType == 1)
                        wifiMacAddress = buf.toString();
                    else if (netType == 2)
                        lanMacAddress = buf.toString();
                }
            }
        }

        if (!lanMacAddress.isEmpty())
            return lanMacAddress;
        else
            return wifiMacAddress;
    }

    private static String getLanMacAddress(){
        String strMacAddr = null;
        try {
            InetAddress ip = getLocalInetAddress();
            byte[] b = NetworkInterface.getByInetAddress(ip)
                    .getHardwareAddress();
            StringBuffer buffer = new StringBuffer();
            for (int i = 0; i < b.length; i++) {
                if (i != 0) {
                    buffer.append(':');
                }
                String str = Integer.toHexString(b[i]&0xFF);
                buffer.append(str.length() == 1 ? 0 + str : str);
            }
            strMacAddr = buffer.toString().toUpperCase();
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        //AvcLog.printd("yttest", "strMacAddr:" + strMacAddr);
        //String mac = getMac();
        //AvcLog.printd("yttest" ,"mac:"+mac);
        return strMacAddr;
    }
    /**
     * 获取移动设备本地IP
     * @return
     */
    protected static InetAddress getLocalInetAddress() {
        InetAddress ip = null;
        try {
            //列举
            Enumeration en_netInterface = NetworkInterface.getNetworkInterfaces();
            while (en_netInterface.hasMoreElements()) {//是否还有元素
                NetworkInterface ni = (NetworkInterface) en_netInterface.nextElement();//得到下一个元素
                Enumeration en_ip = ni.getInetAddresses();//得到一个ip地址的列举
                while (en_ip.hasMoreElements()) {
                    ip = (InetAddress) en_ip.nextElement();
                    if (!ip.isLoopbackAddress() && ip.getHostAddress().indexOf(":") == -1)
                        break;
                    else
                        ip = null;
                }
                if (ip != null) {
                    break;
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return ip;
    }

    public static void setShareCode(String shareCode) {
        DeviceIdFactory.shareCode = shareCode;
    }
}
