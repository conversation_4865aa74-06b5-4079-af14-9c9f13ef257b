package com.infowarelab.tvbox.view;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.adapter.TerminalAdapter;
import com.infowarelab.tvbox.modle.TerminalsListBean;
import com.infowarelab.tvbox.utils.DeviceIdFactory;
import com.infowarelab.tvbox.utils.PublicWay;
import com.infowarelabsdk.conference.util.ToastUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Handler;

/**
 * Created by xiaor on 2019/12/25.
 * <AUTHOR>
 */

public class InviteDialog extends Dialog implements View.OnClickListener,
        AdapterView.OnItemClickListener,AdapterView.OnItemSelectedListener{

    private int width;
    private LinearLayout searchLayout;
    private ActionSendEdit keywordEdit;
    private TextView searchText;
    private LinearLayout noLayout;
    private ListView listview;
    private TextView countText;
    private Button cancelBtn,confirmBtn;

    private TerminalAdapter adapter;
    //获取在线的终端
    private List<TerminalsListBean> datas;
    //选择的终端
    private List<TerminalsListBean> selDatas;

    public List<TerminalsListBean> getSelDatas() {
        return selDatas;
    }

    private OnResultListener onResultListener;
    //获取焦点的下标
    private int fousePos = 0;

    public void setOnResultListener(OnResultListener onResultListener) {
        this.onResultListener = onResultListener;
    }

    public InviteDialog(Context context) {
        super(context, R.style.style_dialog_normal);
    }
    public InviteDialog(Context context, int width) {
        super(context, R.style.style_dialog_normal);
        this.width = width;
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_invite_list);
        setCanceledOnTouchOutside(true);

        searchLayout = (LinearLayout)findViewById(R.id.dialog_inviteList_searchLayout);
        keywordEdit = (ActionSendEdit)findViewById(R.id.dialog_inviteList_keywordEdit);
        searchText = (TextView)findViewById(R.id.dialog_inviteList_searchText);
        noLayout = (LinearLayout)findViewById(R.id.dialog_inviteList_noLayout);
        listview = (ListView)findViewById(R.id.dialog_inviteList_listview);
        countText = (TextView) findViewById(R.id.dialog_inviteList_countText);
        cancelBtn = (Button)findViewById(R.id.dialog_inviteList_cancelBtn);
        confirmBtn = (Button)findViewById(R.id.dialog_inviteList_confirmBtn);

        searchLayout.setVisibility(View.VISIBLE);

        datas = new ArrayList<>();
        selDatas = new ArrayList<>();

        adapter = new TerminalAdapter(getContext());
        listview.setAdapter(adapter);
        searchText.setOnClickListener(this);
        cancelBtn.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);
        listview.setOnItemClickListener(this);
        listview.setOnItemSelectedListener(this);
        listview.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (adapter.getDatas() != null && adapter.getDatas().size() > fousePos){
                    adapter.getDatas().get(fousePos).setFouse(hasFocus);
                }
                adapter.notifyDataSetChanged();
            }
        });
        keywordEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                search(s.toString().trim());
            }
        });
        keywordEdit.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_GO
                    || actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_SEND){
                    search(keywordEdit.getText().toString().trim());
                }
                return true;
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.dialog_inviteList_cancelBtn:
                doNo();
                cancel();
                break;
            case R.id.dialog_inviteList_confirmBtn:
                doYes();
                cancel();
                break;
            case R.id.dialog_inviteList_searchText:
                search(keywordEdit.getText().toString().trim());
                break;
            default:
                break;
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        TerminalsListBean bean = adapter.getDatas().get(position);
        if (bean.isSelected()){
            if (selDatas.contains(bean)){
                selDatas.remove(bean);
            }
            bean.setSelected(false);
        }else {
            if (selDatas.size() < 6){
                if (!selDatas.contains(bean)){
                    selDatas.add(bean);
                }
                bean.setSelected(true);
            }else {
                ToastUtil.showMessage(getContext(),"最多可以邀请6人参加会议",5 * 1000);
            }
        }
        countText.setText("已选 "+selDatas.size() + "/6");
        adapter.notifyDataSetChanged();
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        TerminalsListBean bean = adapter.getDatas().get(position);
        if (fousePos != position){
            adapter.getDatas().get(fousePos).setFouse(false);
            bean.setFouse(true);
            fousePos = position;
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    public interface OnResultListener {
        public void doYes();

        public void doNo();
    }

    private void doYes() {
        if (onResultListener != null) {
            onResultListener.doYes();
        }
    }

    private void doNo() {
        if (onResultListener != null) {
            onResultListener.doNo();
        }
    }

    @Override
    public void show() {
        super.show();
        refreshData(false,"");
        setFouse();
    }
    //搜索
    private void search(String keyword){
        refreshData(true,keyword);
    }
    //刷新数据
    private void refreshData(boolean isSearch,String keyword){
        if (selDatas.size() != 0){
            selDatas.clear();
        }
        if (datas.size() != 0){
            datas.clear();
        }
        for (TerminalsListBean bean : PublicWay.terminalsData){
            bean.setSelected(false);
            if (bean.isOnline() && !bean.getID().equals(DeviceIdFactory.getUUID1(getContext()))){
                if (!isSearch || TextUtils.isEmpty(keyword)){
                    datas.add(bean);
                }else {
                    if (bean.getName().indexOf(keyword.trim()) != -1){
                        datas.add(bean);
                    }
                }
            }
        }
        if (datas.size() != 0){
            keywordEdit.setFocusable(true);
            listview.setFocusable(true);
            listview.requestFocus();
            listview.setVisibility(View.VISIBLE);
            noLayout.setVisibility(View.GONE);
//            datas.get(0).setSelected(true);
            datas.get(0).setFouse(true);
//            selDatas.add(datas.get(0));
            listview.setSelected(true);
            adapter.setDatas(datas);
            adapter.notifyDataSetChanged();
        }else {
            if (selDatas.size() != 0){
                selDatas.clear();
            }
            keywordEdit.setFocusable(true);
            listview.setVisibility(View.GONE);
            noLayout.setVisibility(View.VISIBLE);
            confirmBtn.setFocusable(true);
            confirmBtn.requestFocus();
        }
        countText.setText("已选 "+selDatas.size() + "/6");
    }

    public void setFouse(){
        if (listview != null && listview.getVisibility() == View.VISIBLE){
            listview.setFocusable(true);
            listview.requestFocus();
        }

    }
}
