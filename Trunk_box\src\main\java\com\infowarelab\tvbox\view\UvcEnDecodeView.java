package com.infowarelab.tvbox.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.hardware.Camera;
import android.util.AttributeSet;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceHolder.Callback;
import android.view.SurfaceView;
import android.widget.RelativeLayout;

import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.video.VideoCommon;
import com.mingri.uvc.MrVideoDecoder;
import com.mingri.uvc.Uvc;

////import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * PreviewCallback回到接口，用于显示预览框
 */
@SuppressLint("NewApi")
public class UvcEnDecodeView extends SurfaceView implements Uvc.PreviewCallback, Callback {
    public static final String TAG = "UvcEnDecoderView";
    public static final String H264_MIME = "video/avc";
    private Context mContext;
    private VideoCommonImpl videoCommon;
    private UserCommonImpl userCommon;
    private ShareDtCommonImpl shareDtCommon;
//    private int cameraWidth = 640;
//    private int cameraHeight = 480;
    private int cameraWidth = 1920;
    private int cameraHeight = 1080;

    public static int mCameraPreviewWidth = 640;
    public static int mCameraPreviewHeight = 480;

    public static int mEncodeWidth = -1;
    public static int mEncodeHeight = -1;

    private boolean needToChangeEncodeSize = false;
    private boolean needToInitMyVideoSize = false;

    private boolean surfaceReady = false;
    private int degrees = 0;
    private static boolean isSharing = false;
    private Uvc mCamera;
    private SurfaceHolder mHolder;
    private Uvc.Size curSize;
    private byte[] mMediaHead = null;
    private byte[] mOutput = new byte[10240 * 8];
    private File _fr = null;
    private FileOutputStream _out = null;
    private boolean isWriteFile = false;
    //private Logger logger = Logger.getLogger("UvcEncodeView");
    private MrVideoDecoder mVideoDecoder;
    private AtomicBoolean sendStarted = new AtomicBoolean(false);

    private int nCameraID = Uvc.ID_LOCAL;

    private byte g_streamType = -1;
    private Uvc.Size mVideoSize;
    private int mBitRate = 1000000;

    private ConferenceCommonImpl conferenceCommon = (ConferenceCommonImpl) CommonFactory
            .getInstance().getConferenceCommon();
    private int maxWidthForJoin = 1280;//3840;//1280;
    private int maxHeightForJoin = 720;//2160;//720
    private boolean resolutionSpecified = false;
    private boolean debugable = false;
    private boolean mStarted = false;

    public void setCameraID(int nCameraID) {
        this.nCameraID = nCameraID;
    }

    public UvcEnDecodeView(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public UvcEnDecodeView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        this.mContext = context;
        init();
    }

    public UvcEnDecodeView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    public void init() {
        ////logger.info("initLog");
        Log.d(TAG, "UvcEnDecoderView.init()");
        videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
        shareDtCommon = (ShareDtCommonImpl)CommonFactory.getInstance().getSdCommon();
        if (mHolder == null) mHolder = this.getHolder();
        mHolder.addCallback(this);
        mHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        if (isWriteFile) {
            _fr = new File("/sdcard/yv12buf.src");
            try {
                _out = new FileOutputStream(_fr);
            } catch (FileNotFoundException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        mStarted = false;
    }

    public void startCamera() {
        Log.d(TAG,"startCamera");
//        int nCameraID = Uvc.ID_HDMI;
        // Open Camera
        if (mCamera == null) {
            mCamera = Uvc.open(nCameraID);
        }
        if (mCamera == null) {
            Log.e(TAG,"startCamera Error");
            return;
        }
        mCamera.setPreset(0);
        // set Stream parames
        initStream(Uvc.MAIN_STREAM);
//        initStream(Uvc.SUB_STREAM);
//        initStream(Uvc.THIRD_STREAM);
        //create decoder
        mCamera.setPreviewCallback(this);

        if (surfaceReady) {

            if (mVideoDecoder == null) {
                mVideoDecoder = new MrVideoDecoder();
                Log.d(TAG, "UvcEnDecoderView.createDecoder() at startCamera");
                mVideoDecoder.createDecoder(mEncodeWidth, mEncodeHeight, MrVideoDecoder.H264_MIME, mHolder.getSurface());
            }
//          mVideoDecoder.createDecoder(1920, 1080, MrVideoDecoder.H264_MIME, holder.getSurface());
            Log.d(TAG, "startPreview() at startCamera");
            sendStarted.set(false);
            mCamera.startPreview();
            //setCmd(Uvc.MAIN_STREAM);
            mHolder.setFixedSize(mEncodeWidth,mEncodeHeight);
        }
        else
        {
            Log.d(TAG, "UvcEnDecodeView: Surface is NOT ready.");
        }

        mStarted = true;
        setBackgroundColor(0);
    }

    private void releaseCamera() {

        Log.d(TAG, "UvcEnDecoderView.releaseCamera");

        g_streamType = -1;
        if (mCamera != null) {
            mCamera.release();
            mCamera = null;
        }
        if (mVideoDecoder != null) {
            mVideoDecoder.release();
            mVideoDecoder = null;
        }

//        if (!mList.isEmpty()) {
//            for (int i = 0; i < mList.size(); i++) {
//                MrVideoDecoder mrVideoDecoder = mList.get(i);
//                mrVideoDecoder.release();
//            }
//            mList.clear();
//        }
    }

    /**
     * 检测是否是关键帧
     * &#64;param buffer 编码后的h264视频数据
     */
    public static boolean isKeyFrame(byte[] buffer) {

        if (buffer.length < 5) {
            return false;
        }

        //00 00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 0
                && buffer[3] == 1) {
            int nalType = buffer[4] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                return true;
            }
        }

        //00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 1) {
            int nalType = buffer[3] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                return true;
            }
        }

        return false;
    }

    @Override
    public void onPreviewFrame(byte[] data, Uvc uvc) {
        if (isWriteFile) {
            try {
                _out.write(data);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
//        //logger.info("onPreviewFrame dataLength = " + data.length);
//        if (!isSharing) return;
        //Log.e("rrrr","数据::"+data.toString());
        //Log.e("ldy","onPreviewFrame dataLength = " + data.length);
        byte nData1, nData2;
        byte nStreamInx, nFrameRefType;
        int frameSize;

        frameSize = data.length;

        nData1 = data[0];
        nData2 = data[1];
        data[0] = nData2 == 0 ? (byte) 0x00 : (byte) 0xFF;
        //Is main Stream
        nStreamInx = (byte) (nData1 & 0x03);
        //get svc information
        nFrameRefType = (byte) ((nData1 & 0x7C) >> 2);
        // Main Stream
//        //logger.info("onPreviewFrame star");
        //Log.e("ldy","onPreviewFrame star");
        //Log.e("ldy","nStreamInx::"+nStreamInx);
        if (nStreamInx == 0) {
//            //logger.info("onPreviewFrame nStreamInx = 0");
            //Log.e("Test","onPreviewFrame nStreamInx = 0");
            if (mVideoDecoder != null && mVideoDecoder.getStart()&&getWidth()>0&&getHeight()>0) {
                try {
                    mVideoDecoder.decodeFrame(data, frameSize);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (Uvc.ID_LOCAL == nCameraID){
                if (!isSharing) return;
            }
            int nalUnitType = data[4] & 0x1F;
            /**
             * 防止花屏
             * */
            if (data[4] == 0x61){
                data[4] = 0x41;
            }
            //Log.e("ttttt","onPreviewFrame length= "+data.length);
            if (isKeyFrame(data)) { //IDR frame
//               int len;
//                     mMediaHead[4] = (byte) (mMediaHead[4] | (3 << 5));
//                    videoCommon.sendMyVideoData(mMediaHead, mMediaHead.length, true,cameraWidth, cameraHeight, true);
//                      System.arraycopy(data, 0, mOutput, 0, data.length);
//                       mOutput[4] = (byte) (mOutput[4] | (3 << 5)); //nal_ref_idc = 3,IDR frame high priority
//                      len = data.length;
//                       videoCommon.sendMyVideoData(mOutput, len, true, cameraWidth, cameraHeight, true);

                if (!sendStarted.get()) {
                    sendStarted.set(true);
                    Log.d(TAG, "onPreviewFrame: IDR frame is got! size=" + data.length);
                }

                data[4] = (byte) (data[4] | (3 << 5));

                if (debugable) Log.d(TAG, "onPreviewFrame(I): size=" + mVideoSize.width + "x" + mVideoSize.height + "; " + data.length);

                if (nCameraID == Uvc.ID_LOCAL){
                    videoCommon.sendMyVideoData(data, data.length, true, mVideoSize.width, mVideoSize.height, true);
                }else {
//                    shareDtCommon.setScreenInfo(cameraWidth,cameraHeight,24);
                    shareDtCommon.sendScreenData(mVideoSize.width, mVideoSize.height,24,data,data.length,true,true);

                }
            } else { //P frame

                if (!sendStarted.get()){
                    Log.d(TAG,"onPreviewFrame: Ignored the P frame as NOT started! size=" + data.length);
                    return;
                }

                if (debugable) Log.d(TAG, "onPreviewFrame(P): size=" + mVideoSize.width + "x" + mVideoSize.height + "; " + data.length);

                if (nCameraID == Uvc.ID_LOCAL){
                    videoCommon.sendMyVideoData(data, data.length, false, mVideoSize.width, mVideoSize.height, true);
                }else {
                    shareDtCommon.sendScreenData(mVideoSize.width, mVideoSize.height,24,data,data.length,true,false);
                }
            }
//            } else {
//                //logger.info("onPreviewFrame333333");
//                // 保存pps sps 只有开始时 第一个帧里有， 保存起来后面用
//                ByteBuffer spsPpsBuffer = ByteBuffer.wrap(data);
//                if (spsPpsBuffer.getInt() == 0x00000001) {
//                    mMediaHead = new byte[data.length];
//                    System.arraycopy(data, 0, mMediaHead, 0, data.length);
//                    if (mMediaHead[4] == 0x27) //0x27的数据发送失败，故改为 0x67
//                    {
//                        mMediaHead[4] = 0x67;
//                    }
//                    if ((mMediaHead[4] & 0x1f) != 7) {
//                        mMediaHead = null;
//                        videoCommon.reStartCam();
//                    }
//                } else {
//                    Log.e("offerEncoder", "not found media head.");
//                }
//            }
            //videoCommon.sendMyVideoData(data, frameSize, false, cameraWidth, cameraHeight, true);
        }
    }

    private boolean initStream(byte streamType) {
        int ret;
        // open stream
        ret = mCamera.openStream(streamType);
        if (ret != 0) {

            Log.e(TAG, "open mCamera failed");
            return false;
        }
        setCmd(streamType);
        return true;
    }

    private boolean setCmd(byte streamType) {

//        if(g_streamType == streamType)
//        {
//            return true;
//        }
        g_streamType = streamType;
        int ret;
        //Uvc.Size size;
        // setStreamencodeType
        ret = mCamera.setStreamEncodeType(streamType, Uvc.CODEC_H264);
        if (ret != 0) {
            Log.d(TAG, "set mCamera encode type failed");
            return false;
        }

        mVideoSize = getSetVideoSize();
        Log.d(TAG, "setStreamVideoSize at setCmd: " + mVideoSize.width + "x" + mVideoSize.height);

        ret = mCamera.setStreamVideoSize(streamType, mVideoSize);
        if (ret != 0) {
            Log.d(TAG, "set mCamera video size failed");
            return false;
        }

        if (videoCommon != null && nCameraID == Uvc.ID_LOCAL) {
            videoCommon.initializeMyVideo(mVideoSize.width, mVideoSize.height, 30);
            //videoCommon.exChange(cameraHeight, cameraWidth);
            Log.d(TAG, "initializeMyVideo: " + mVideoSize.width + "x" + mVideoSize.height);
        }

        ret = mCamera.setStreamFrameRate(streamType, (byte) 30);
        if (ret != 0) {
            Log.d(TAG, "set mCamera frame rate failed");
            return false;
        }

        ret = mCamera.setStreamIDR(streamType, 30);
        if (ret != 0) {
            Log.d(TAG, "set mCamera I Frame Interval failed");
            return false;
        }

        AnalyzeBitRateByEncodeSize();

//        int picSize = mVideoSize.width * mVideoSize.height;
//        if (picSize < 320 * 240) {
//            bitrate = 200;
//        } else if (picSize <= 352 * 288) {
//            bitrate = 240;
//        } else if (picSize <= 640 * 360) {
//            bitrate = 400;
//        } else if (picSize <= 960 * 540) {
//            bitrate = 600;
//        } else if (picSize <= 1280 * 720) {
//            bitrate = 1000;
//        } else {
//            bitrate = 2000;
//        }

        Log.d(TAG, "setCmd: set mCamera bit rate: " + mBitRate);

        ret = mCamera.setStreamBitRate(streamType, Uvc.CODEC_H264, (byte) 0, (byte) 5, (byte) 51, mBitRate);
        if (ret != 0) {
            Log.d(TAG, "setCmd: set mCamera bit rate failed");

            return false;
        }

        //去掉标题
        mCamera.setStringOSD(streamType, 0, 255, 0, 0, 5, 0, "去掉标题");
        return true;
    }

    private Uvc.Size getSetVideoSize() {
        List<Uvc.Size> mSizes = mCamera.getSupportedFrameSizes();

        int perferWidth = SharedPreferencesUrls.getInstance().getInt("width", 1280);
        int perferHeight = SharedPreferencesUrls.getInstance().getInt("height", 720);

        Log.d(TAG,"Saved Size: " + perferWidth + "x" + perferHeight);

//        if (perferWidth < 1920 || perferHeight < 1080)
//        {
//            perferWidth = 1280;
//            perferHeight = 720;
//        }

        perferWidth = videoCommon.getWidth();
        perferHeight = videoCommon.getHeight();

        Log.d(TAG,"Preferred Size: " + videoCommon.getWidth() + "x" + videoCommon.getHeight());

        int defaultWidth = 0;
        int defaultHeight = 0;

        // 取比设定值小的像素中最大的
        for (Uvc.Size size : mSizes) {

            Log.d(TAG,"Support resolution: " + size.width + "x" + size.height);

            if (size.width == perferWidth && size.height == perferHeight) {
                mCameraPreviewWidth = size.width;
                mCameraPreviewHeight = size.height;
            }

            if (size.width == perferWidth || size.height == perferHeight){
                defaultWidth = size.width;
                defaultHeight = size.height;
            }
        }

        // 如果设定值实在太小，取所支持的最小像素
        if (mCameraPreviewWidth <= 0 || mCameraPreviewHeight <= 0) {

            mCameraPreviewWidth = defaultWidth;
            mCameraPreviewHeight = defaultHeight;

            if (mCameraPreviewWidth <= 0 || mCameraPreviewHeight <= 0) {
                for (Uvc.Size size : mSizes) {
                    if (mCameraPreviewWidth == 0) {
                        mCameraPreviewWidth = size.width;
                        mCameraPreviewHeight = size.height;
                        continue;
                    }
                    if (size.width * size.height > mCameraPreviewWidth * mCameraPreviewHeight) {
                        mCameraPreviewWidth = size.width;
                        mCameraPreviewHeight = size.height;
                    }
                }
            }
        }

        Log.d(TAG,"getSetVideoSize = " + mCameraPreviewWidth + "x" + mCameraPreviewHeight);

        mEncodeWidth = mCameraPreviewWidth;
        mEncodeHeight = mCameraPreviewHeight;

        /*cameraWidth = 0;
        cameraHeight = 0;

        //Log.e("ttttt","videoCommon宽度::"+videoCommon.getWidth());
        // 取比设定值小的像素中最大的
        for (Uvc.Size size : mSizes) {
            if (size.width * size.height <= videoCommon.getWidth()
                    * videoCommon.getHeight()
                    && size.height >= 0) {
                if (cameraWidth == 0) {
                    cameraWidth = size.width;
                    cameraHeight = size.height;
                }
                if (size.width * size.height >= cameraWidth * cameraHeight) {
                    cameraWidth = size.width;
                    cameraHeight = size.height;
                }
            }
        }
        // 如果设定值实在太小，取所支持的最小像素
        if (cameraWidth == 0) {
            for (Uvc.Size size : mSizes) {
                if (size.height >= 0) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width * size.height <= cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
        }
         */

        Uvc.Size currentSize = new Uvc.Size(mEncodeWidth, mEncodeHeight);

        //Log.d(TAG, "UvcEnDecoderView.getSetVideoSize=" + cameraWidth + "x" + cameraHeight);

        return currentSize;
    }

    public void flushEncoder() {
        if (mCamera != null) {
//            mCamera.setStreamIDR(Uvc.MAIN_STREAM, 0);
        }
    }
    public void reStartLocalView() {

        Log.d(TAG, "reStartLocalView");
        /*if (mCamera == null) {
            changeStatus(true);
        } else {
            if (null != videoCommon){
                if ((this.cameraWidth != videoCommon.mWidth) || (this.cameraHeight != videoCommon.mHeight)) {
                    destroyCamera();
                    startCamera();
                    videoCommon.exChange(cameraHeight, cameraWidth);
                }
                else {
                    Log.d(TAG, "No need to restart the main camera, ignored!");
                }
            }
        }*/
        if(mCamera!=null) {
            stopPreview();
            releaseCamera();
        }

        changeStatus(true);

        if (mCamera != null) {
            mCamera.setStreamIDR(Uvc.MAIN_STREAM, 0);
        }
//
//        if(mCamera!=null){
//            /*
//            Uvc.Size size = getSetVideoSize();
//            mCamera.setStreamVideoSize(Uvc.MAIN_STREAM, size);
//
//            if (videoCommon != null && nCameraID == Uvc.ID_LOCAL) {
//                videoCommon.initializeMyVideo(cameraWidth, cameraHeight, 30);
//                Log.d(TAG, "videoCommon.initializeMyVideo(restart): " + cameraWidth + "x" + cameraHeight);
//            }
//
//            //设置码流
//            setCmd(Uvc.MAIN_STREAM);
//            */
//
//        }else{
//            //changeStatus(true);
//            //releaseCamera();
//            //startCamera();
//        }
        //videoCommon.exChange(cameraHeight, cameraWidth);
    }
    public void changeStatus(boolean isOpenCamera) {
        if (isOpenCamera) {
            if (mCamera == null) {
                //invalidate();
                //init();
                startCamera();
            }
        } else {
            if (mCamera != null) {
                releaseCamera();
            }
        }
    }

    public void destroyCamera() {
        stopPreview();
        releaseCamera();
    }
    public void setCameraLandscape() {

    }

    public void setSharing(boolean isSharing) {

        if (UvcEnDecodeView.isSharing == isSharing) return;

        UvcEnDecodeView.isSharing = isSharing;

        if (isSharing && mCamera != null && mCamera.isOpen()){
            if (videoCommon != null){

                Log.d(TAG, "setSharing: initializeMyVideo size = " + mVideoSize.width + "x" + mVideoSize.height);

                videoCommon.initializeMyVideo(mVideoSize.width, mVideoSize.height, 30);
            }
        }
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {

        if (holder != null && holder.getSurface() != null) {

            Log.d(TAG, "surfaceCreated");

            mHolder = holder;
            if (mVideoDecoder == null) {
                mVideoDecoder = new MrVideoDecoder();
                mVideoDecoder.createDecoder(cameraWidth, cameraHeight, MrVideoDecoder.H264_MIME, mHolder.getSurface());
            }
//          mVideoDecoder.createDecoder(1920, 1080, MrVideoDecoder.H264_MIME, holder.getSurface());
            if (mCamera != null) {
                Log.d(TAG, "UvcEnDecoderView.startPreview() at surfaceCreated");
                sendStarted.set(false);
                mCamera.startPreview();
                //setCmd(Uvc.MAIN_STREAM);
            }
            mHolder.setFixedSize(cameraWidth,cameraHeight);
            surfaceReady = true;
        }
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width,
                               int height) {

        Log.d(TAG, "UvcEnDecoderView.surfaceChanged");
        mHolder = holder;
        if (holder != null && holder.getSurface() != null){
            surfaceReady = true;
        }
    }
    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        //Log.e("Test","释放成功");

        Log.d(TAG, "surfaceDestroyed");

        surfaceReady = false;
        stopPreview();
        releaseCamera();
    }
    public void setParams(int width, int height) {

        if (width > 1 && mCamera == null) {
            Log.d(TAG,"setParams: " + width + "x" + height);
            if (!AnalyzeEncodeSizeEx())
                reStartLocalView();
        }

        if (width <= 1) {
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = 1;
            params.height = 1;
            setLayoutParams(params);
        } else {
//            int h = height;
//            int w = width;
//            if (degrees % 180 == 0) {
//                if ((1.0f * cameraWidth / cameraHeight) > (1.0f * width / height)) {
//                    h = (int) ((1.0f * cameraHeight / cameraWidth) * width);
//                } else {
//                    w = (int) ((1.0f * cameraWidth / cameraHeight) * height);
//                }
//            } else {
//                if ((1.0f * cameraHeight / cameraWidth) > (1.0f * width / height)) {
//                    h = (int) ((1.0f * cameraWidth / cameraHeight) * width);
//                } else {
//                    w = (int) ((1.0f * cameraHeight / cameraWidth) * height);
//                }
//            }
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
//            params.width = w;
//            params.height = h;
            params.width = width;
            params.height = height;
//            params.setMargins((width - w) / 2, (height - h) / 2, 0, 0);
            params.setMargins(0, 0, DensityUtil.dip2px(getContext(),1.0f), 0);
            setLayoutParams(params);
        }
    }
    /**
     * 功能：云台控制
     *
     * @param ptz PTZ type, such as: PTZ_LEFT, PTZ_UP...
     * @return 0-success, -1-invalid argument -2-not support, others-failed
     */
    public int startPtz(byte ptz) {
        if (null != mCamera){
            return mCamera.startPtz(ptz);
        }
        return -2;
    }
    /**
     * 功能：停止云台
     *
     * @param ptz PTZ type, such as: PTZ_LEFT, PTZ_UP...
     * @return 0-success, -1-invalid argument -2-not support, others-failed
     */
    public int stopPtz(byte ptz) {
        if (null != mCamera){
            return mCamera.stopPtz(ptz);
        }
        return -2;
    }
    public void stopPreview() {

        Log.d(TAG, "UvcEnDecoderView.stopPreview");

        if (null != mCamera){
            mCamera.setPreviewCallback(null);
            mCamera.stopPreview();
        }

        mStarted = false;
    }
    public void switchSoftEncode(boolean isSoft){
        reStartLocalView();
    }
    public void setStreamVideoSize(int w, int h) {
        if(mCamera!=null){
            List<Uvc.Size> mSizes = mCamera.getSupportedFrameSizes();
            cameraWidth = 0;
            cameraHeight = 0;
            // 取比设定值小的像素中最大的
            for (Uvc.Size size : mSizes) {
                if (size.width * size.height <= w
                        * h
                        && size.height >= 0) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width * size.height >= cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
            // 如果设定值实在太小，取所支持的最小像素
            if (cameraWidth == 0) {
                for (Uvc.Size size : mSizes) {
                    if (size.height >= 0) {
                        if (cameraWidth == 0) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                        if (size.width * size.height <= cameraWidth * cameraHeight) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                    }
                }
            }
            Uvc.Size currentSize = new Uvc.Size(cameraWidth, cameraHeight);
            mCamera.setStreamVideoSize(Uvc.MAIN_STREAM, currentSize);
            setStreamBitRate(Uvc.MAIN_STREAM,currentSize);
        }
//        else{
//            releaseCamera();
//            startCamera();
//        }
    }

    private boolean setStreamBitRate(byte streamType,Uvc.Size size) {
        int bitrate = 200;
        int picSize = size.width * size.height;
        if (picSize < 320 * 240) {
            bitrate = 200;
        } else if (picSize <= 352 * 288) {
            bitrate = 240;
        } else if (picSize <= 640 * 360) {
            bitrate = 400;
        } else if (picSize <= 960 * 540) {
            bitrate = 600;
        } else if (picSize <= 1280 * 720) {
            bitrate = 800;
        } else {
            bitrate = 1600;
        }
        int ret = mCamera.setStreamBitRate(streamType, Uvc.CODEC_H264, (byte) 0, (byte) 5, (byte) 51, bitrate);
        if (ret != 0) {
            Log.e(TAG, "set mCamera bit rate failed");
            return false;
        }

        Log.d(TAG, "UvcEnDecoderView.setStreamBitRate: bitrate = " + bitrate);

        return true;
    }

    /*如果会议系统或会议设置，采用自适应，则采用如下规则； 否则还是采用原有规则；
            1.根据视频路数动态调整视频分辨率。 当本端视频被打开并且同步时，以同步视频路数为调整依据。 如本端视频被打开但没有同步时，以主持人同步的视频路数为调整依据。
            2.调整分辨率时，两个调整需求里面较大的为准。
            3.等分布局： 1路   原始分辨率    2-4路    720p或原始分辨率(<720p)     5-9路  640*360或 原始分辨率(<640*360)
                     9路以上   320*180
    主次布局：本端视频为主视频时，原始分辨率
    本端视频非主视频时，640*360

    主持人控制模式的时候，主持人端改为超过9路视频的时候降低到640， 超过25路降低到 320*/

    public boolean AnalyzeEncodeSizeEx() {

        if (conferenceCommon == null) {
            Log.d(TAG, "AnalyzeEncodeSize: conferenceCommon = NULL");
            return false;
        }

        if (userCommon == null) {
            Log.d(TAG, "AnalyzeEncodeSize: userCommon = NULL");
            return false;
        }

        int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
        int height = SharedPreferencesUrls.getInstance().getInt("height", 720);

        int resultWidth = width;
        int resultHeight = height;

        Log.d(TAG, "AnalyzeEncodeSize: setting resolution = " + width + "x" + height);
        Log.d(TAG, "AnalyzeEncodeSize: isHost = " + userCommon.isHost());

        if (!conferenceCommon.getVideoAdaptive() || userCommon.isHost())
        {
            Log.d(TAG, "AnalyzeEncodeSize: video is NOT adaptive!");

            Log.d(TAG, "AnalyzeEncodeSize: result size =" + resultWidth + "x" + resultHeight);

            needToChangeEncodeSize = false;

            if (mEncodeWidth != resultWidth || mEncodeHeight != resultHeight){

                mEncodeWidth = resultWidth;
                mEncodeHeight = resultHeight;

                needToChangeEncodeSize = true;
            }

            if (needToChangeEncodeSize){

                Log.d(TAG, "AnalyzeEncodeSize: need to change the encode size:" + mEncodeWidth + "x" + mEncodeHeight);

                videoCommon.setWidth(mEncodeWidth);
                videoCommon.setHeight(mEncodeHeight);

                AnalyzeBitRateByEncodeSize();

                if (mCamera != null && mCamera.isOpen()){
                    needToChangeEncodeSize = false;
                    needToInitMyVideoSize = true;
                    reStartLocalView();
                    return true;
                }

                needToChangeEncodeSize = false;
                needToInitMyVideoSize = true;
            }

            return false;
        }

        Log.d(TAG, "AnalyzeEncodeSize: video is adaptive!");

        //本地视频没有被同步
        if (!videoCommon.isSyncSelf()) {
            //主讲人大视频
            if (videoCommon.isSpeakerMyself()) {

                Log.d(TAG, "AnalyzeEncodeSize: isSpeakerMyself = " + videoCommon.isSpeakerMyself());
                resultWidth = width;
                resultHeight = height;

            }
            else
            {
                int count = videoCommon.getOpenVideoCount();
                Log.d(TAG, "AnalyzeEncodeSize: open video count = " + count);

                if (count == 1)
                {
                    resultWidth = width;
                    resultHeight = height;
                }
                else if (count <= 9){
                    resultWidth = width;
                    resultHeight = height;
                    if (resultWidth > 1280 || resultHeight > 720) {
                        if (userCommon != null) {
                            //f (!userCommon.isHost()) {
                            Log.d(TAG, "AnalyzeEncodeSize: Limited to 720P");
                            resultWidth = maxWidthForJoin;
                            resultHeight = maxHeightForJoin;
                            //}
                        }
                    }
                }
                else if (count <= 25){
                    resultWidth = 640;
                    resultHeight = 360;
                }
                else
                {
                    resultWidth = 320;
                    resultHeight = 180;
                }
            }

        }
        //本地视频被同步
        else {
            //平铺模式
            if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_PLAIN){

                Log.d(TAG, "AnalyzeEncodeSize: sync video count = " + videoCommon.getSyncVideoCount());

                if (videoCommon.getSyncVideoCount() == 1){

                    Log.d(TAG, "AnalyzeEncodeSize: One self video synced and keep size ");
                    resultWidth = width;
                    resultHeight = height;
                }
                else if (videoCommon.getSyncVideoCount() <= 4){
                    resultWidth = width;
                    resultHeight = height;

                    if (resultWidth > 1280 || resultHeight > 720) {
                        if (userCommon != null) {
                            //if (!userCommon.isHost()) {
                            Log.d(TAG, "AnalyzeEncodeSize: Limited 720P as <= 4");
                            resultWidth = maxWidthForJoin;
                            resultHeight = maxHeightForJoin;
                            //}
                        }
                    }
                }
                else  if (videoCommon.getSyncVideoCount() <= 9){
                    resultWidth = 640;
                    resultHeight = 360;
                } else if (videoCommon.getSyncVideoCount() <= 25){
                    resultWidth = 320;
                    resultHeight = 180;
                }
                else {
                    resultWidth = 320;
                    resultHeight = 180;
                }

            }
            //主次模式
            else
            {
                //大视频
                if (videoCommon.isSpeakerMyself()) {

                    Log.d(TAG, "AnalyzeEncodeSize: isSpeakerMyself = " + videoCommon.isSpeakerMyself());
                    resultWidth = width;
                    resultHeight = height;

                }
                else if (videoCommon.getSyncVideoCount() <= 9){
                    resultWidth = 640;
                    resultHeight = 360;
                } else if (videoCommon.getSyncVideoCount() <= 25){
                    resultWidth = 320;
                    resultHeight = 180;
                }
                else {
                    resultWidth = 320;
                    resultHeight = 180;
                }
            }
        }

        if (resultWidth > width || resultHeight > height){
            resultWidth = width;
            resultHeight = height;

            Log.d(TAG, "AnalyzeEncodeSize: adjusted to: " + width + "x" + height);
        }

        Log.d(TAG, "AnalyzeEncodeSize: result size =" + resultWidth + "x" + resultHeight);

        if (mEncodeWidth != resultWidth || mEncodeHeight != resultHeight){

//            if (resolutionSpecified){
//
//                Log.d(TAG, "AnalyzeEncodeSize: Ignored as resolutionSpecified");
//
//                resolutionSpecified = false;
//                return;
//            }

            mEncodeWidth = resultWidth;
            mEncodeHeight = resultHeight;

            needToChangeEncodeSize = true;
        }

        if (needToChangeEncodeSize){

            Log.d(TAG, "AnalyzeEncodeSize: need to change the encode size:" + mEncodeWidth + "x" + mEncodeHeight);

            videoCommon.setWidth(mEncodeWidth);
            videoCommon.setHeight(mEncodeHeight);

            AnalyzeBitRateByEncodeSize();

            if (mCamera != null && mCamera.isOpen()){
                needToChangeEncodeSize = false;
                needToInitMyVideoSize = true;
                reStartLocalView();
                return true;
            }

            needToChangeEncodeSize = false;
            needToInitMyVideoSize = true;
        }
        else
            Log.d(TAG, "AnalyzeEncodeSize: NO need to change the encode size:" + mEncodeWidth + "x" + mEncodeHeight);


        return  false;
    }


    public void AnalyzeEncodeSize(int width, int height) {

        int resultWidth = width;
        int resultHeight = height;

        Log.d(TAG, "AnalyzeEncodeSize（2）: width = " + width + "; height = " + height);

//        if (resultWidth > 1280 || resultHeight > 720) {
//            if (userCommon != null) {
//                if (!userCommon.isHost()) {
//                    Log.d(TAG, "AnalyzeEncodeSize: Limited as NOT host and speaker");
//                    resultWidth = maxWidthForJoin;
//                    resultHeight = maxHeightForJoin;
//                }
//            }
//        }

        Log.d(TAG, "AnalyzeEncodeSize(2): result size =" + resultWidth + "x" + resultHeight);

        if (mEncodeWidth != resultWidth || mEncodeHeight != resultHeight){

            mEncodeWidth = resultWidth;
            mEncodeHeight = resultHeight;

            needToChangeEncodeSize = true;

            if (needToChangeEncodeSize){

                Log.d(TAG, "AnalyzeEncodeSize: need to change the encode size:" + mEncodeWidth + "x" + mEncodeHeight);

                videoCommon.setWidth(mEncodeWidth);
                videoCommon.setHeight(mEncodeHeight);

                AnalyzeBitRateByEncodeSize();

                if (mCamera != null && mCamera.isOpen()){
                    reStartLocalView();
                }

                needToChangeEncodeSize = false;

                resolutionSpecified = true;
            }
            else
                Log.d(TAG, "AnalyzeEncodeSize: NO need to change the encode size:" + mEncodeWidth + "x" + mEncodeHeight);


        }

    }

    private void AnalyzeBitRateByEncodeSize() {

        //2022/8/1 编码：默认帧率25帧、码流1080P -1.5M，720P-800K， 640-400K，320-200K

        int bitrate = 200;
        int picSize = mEncodeWidth * mEncodeHeight;
        if (picSize <= 320 * 240) {
            //bitrate = 256*1024;
            bitrate = 200;
            //        } else if (picSize <= 352 * 288) {
            //            bitrate = 240000;
        } else if (picSize <= 640 * 480) {
            //bitrate = 512*1024;
            bitrate = 400;
            //        } else if (picSize <= 960 * 720) {
            //            bitrate = 700000;
        } else if (picSize <= 1280 * 720) {
            //bitrate = 1024*1024;
            bitrate = 800;
            //            bitrate = 700000;
        } else if (picSize <= 1920 * 1080) {
            //bitrate = 2048*1024;
            bitrate = 1536;
        } else {
            bitrate = 3072;
        }

        mBitRate = bitrate;

        Log.d(TAG,">>>>> AnalyzeBitRateByEncodeSize: mBitRate = " + mBitRate);
    }


    public boolean isStarted() {
        return mStarted;
    }
}