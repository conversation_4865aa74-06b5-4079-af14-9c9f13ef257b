package com.infowarelab.tvbox.view;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;

import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.adapter.UserListAdapter;
import com.infowarelab.tvbox.modle.FacilityListBean;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.confctrl.UserCommon;
import com.infowarelabsdk.conference.domain.UserBean;
import com.infowarelabsdk.conference.util.ToastUtil;
import com.mingri.uvc.Uvc;

import java.util.LinkedList;
import java.util.List;

/**
 * Created by xiaor on 2019/12/25.
 * <AUTHOR>
 */

public class VideoListDialog extends AlertDialog implements View.OnClickListener,
        AdapterView.OnItemClickListener,AdapterView.OnItemSelectedListener{

    private LinearLayout searchLayout;
    private VideoCommonImpl videoCommon;
    private UserCommonImpl userCommon;
    private ShareDtCommonImpl shareDtCommon;
    private TextView titleText;
    private int width;
    private LinearLayout noLayout;
    private ListView listview;
    private TextView countText;
    private Button cancelBtn,confirmBtn;

    private UserListAdapter adapter;
    private OnResultListener onResultListener;
    //获取焦点的下标
    private int fousePos = 0;
    private LinkedList<FacilityListBean> datas;

    private int cameraId = Uvc.ID_LOCAL;

    //保存打开得视屏路数
    public LinkedList<FacilityListBean> selData;

    private int limit = 9;

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public void setCameraId(int cameraId) {
        this.cameraId = cameraId;
    }

    public List<FacilityListBean> getDatas() {
        return datas;
    }

    public void setOnResultListener(OnResultListener onResultListener) {
        this.onResultListener = onResultListener;
    }

    public VideoListDialog(Context context) {
        super(context, R.style.style_dialog_normal);
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_invite_list);
        setCanceledOnTouchOutside(true);

        datas = new LinkedList<>();
        selData = new LinkedList<>();

        videoCommon =(VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
        shareDtCommon = (ShareDtCommonImpl)CommonFactory.getInstance().getSdCommon();

        searchLayout = (LinearLayout)findViewById(R.id.dialog_inviteList_searchLayout);

        titleText = (TextView)findViewById(R.id.dialog_inviteList_titleText);
        noLayout = (LinearLayout)findViewById(R.id.dialog_inviteList_noLayout);
        listview = (ListView)findViewById(R.id.dialog_inviteList_listview);
        countText = (TextView) findViewById(R.id.dialog_inviteList_countText);
        cancelBtn = (Button)findViewById(R.id.dialog_inviteList_cancelBtn);
        confirmBtn = (Button)findViewById(R.id.dialog_inviteList_confirmBtn);

        countText.setVisibility(View.VISIBLE);

        searchLayout.setVisibility(View.GONE);
        titleText.setText(getContext().getResources().getString(R.string.video_title));

        adapter = new UserListAdapter(getContext(), new UserListAdapter.OnClickListener() {
            @Override
            public void onClickListener(int position,boolean on) {
                FacilityListBean bean = adapter.getDatas().get(position);
                if (on){
                    if (selData.size() >= limit){
                        ToastUtil.showMessage(getContext(),"最多只能打开"+limit+"路视频",5 * 1000);
                        return;
                    }
                    if (!selData.contains(bean)){
                        selData.add(bean);
                    }
                }else {
                    if (selData.contains(bean)){
                        selData.remove(bean);
                    }
                }
                bean.setSelected(on);
                onResultListener.openVideo(on,bean);
                if (countText != null){
                    countText.setText(""+selData.size()+"/"+limit);
                }
                adapter.notifyDataSetChanged();
            }
        });
        adapter.setDatas(datas);
        listview.setAdapter(adapter);
        if (videoCommon.getDeviceMap().size() != 0){
            listview.setVisibility(View.VISIBLE);
            noLayout.setVisibility(View.GONE);
        }else {
            listview.setVisibility(View.GONE);
            noLayout.setVisibility(View.VISIBLE);
        }
        cancelBtn.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);
        listview.setOnItemClickListener(this);
        listview.setOnItemSelectedListener(this);
        listview.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (adapter.getDatas() != null && adapter.getDatas().size() != 0
                        && adapter.getDatas().size() > fousePos){
                    adapter.getDatas().get(fousePos).setFouse(hasFocus);
                    adapter.notifyDataSetChanged();
                }
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.dialog_inviteList_cancelBtn:
                doNo();
                cancel();
                break;
            case R.id.dialog_inviteList_confirmBtn:
                doYes();
                cancel();
                break;
            default:
                break;
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        FacilityListBean bean = adapter.getDatas().get(position);
        if (bean.isSelected()){
            bean.setSelected(false);
            onResultListener.openVideo(false, bean);
            if (selData.contains(bean)){
                selData.remove(bean);
            }
        }else {
            if (selData.size() >= limit){
                ToastUtil.showMessage(getContext(),"最多只能打开"+limit+"路视频",5 * 1000);
                return;
            }
            bean.setSelected(true);
            onResultListener.openVideo(true,bean);
            if (!selData.contains(bean)){
                selData.add(bean);
            }
        }
        if (countText != null){
            countText.setText(""+selData.size()+"/"+limit);
        }
        adapter.notifyDataSetChanged();
    }
    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        if (adapter.getDatas() != null && adapter.getDatas().size() != 0
                && adapter.getDatas().size() > fousePos){
            FacilityListBean bean = adapter.getDatas().get(position);
            if (fousePos != position){
                adapter.getDatas().get(fousePos).setFouse(false);
                bean.setFouse(true);
                fousePos = position;
                adapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    public interface OnResultListener {
        public void doYes();

        public void doNo();
        void openVideo(boolean isOpen, FacilityListBean channelId);
    }

    private void doYes() {
        if (onResultListener != null) {
            onResultListener.doYes();
        }
    }
    private void doNo() {
        if (onResultListener != null) {
            onResultListener.doNo();
        }
    }

    @Override
    public void setOnShowListener(@Nullable OnShowListener listener) {
        super.setOnShowListener(listener);
        for (FacilityListBean bean : datas){
            if (cameraId == Uvc.ID_HDMI && bean.getUid() == userCommon.getSelf().getUid()){
                datas.remove(bean);
            }
        }
        adapter.notifyDataSetChanged();
    }

    @Override
    protected void onStart() {
        super.onStart();
        listview.setFocusable(true);
        listview.requestFocus();
        refreshData();
    }

    //刷新数据
    public void refreshData(){
        if (datas.size() != 0){
            datas.clear();
        }
        if (selData.size() != 0){
            selData.clear();
        }
        fousePos = 0;
        for (int key : videoCommon.getDeviceMap().keySet()){
            FacilityListBean bean = new FacilityListBean();
            UserBean userBean = userCommon.getUser(videoCommon.getDeviceMap().get(key));

            //ignored the DVR device
            if (userBean.getDevice() == UserCommon.DEVICE_DVR){
                Log.d("InfowareLab.Debug", "VideoListDialog.refreshData: ignored the DVR " + userBean.getUsername());
                continue;
            }

            if (!userBean.isHaveVideo()){
                Log.d("InfowareLab.Debug", "VideoListDialog.refreshData: no video " + userBean.getUsername());
                continue;
            }

            bean.setUid(videoCommon.getDeviceMap().get(key));
            bean.setUsername(userBean.getUsername());
            bean.setChannelId(key);
            if (videoCommon.getSyncMap().containsKey(key)){
                if (selData.size() < 9){
                    bean.setSelected(true);
                    if (!selData.contains(bean)){
                        selData.add(bean);
                    }
                }
            }else {
                bean.setSelected(false);
                if (selData.contains(bean)){
                    selData.remove(bean);
                }
            }
            datas.add(bean);
        }
        adapter.setDatas(datas);
        adapter.notifyDataSetChanged();
        if (countText != null){
            countText.setText(""+selData.size()+"/9");
        }
    }

    public void refreshStatus(int channelId, boolean status){

        if (adapter == null) return;

        List<FacilityListBean> videoList = adapter.getDatas();

        if (videoList == null) return;

        boolean changed = false;

        for (int i = 0; i < videoList.size(); i++){
            FacilityListBean bean = videoList.get(i);

            if (bean == null) continue;

            if (channelId == bean.getChannelId() && status != bean.isSelected()){
                bean.setSelected(status);
                changed = true;

                if (status){
                    if (!selData.contains(bean)){
                        selData.add(bean);
                    }
                }else {
                    if (selData.contains(bean)){
                        selData.remove(bean);
                    }
                }
            }
        }

        if (changed)
            adapter.notifyDataSetChanged();

    }
}
