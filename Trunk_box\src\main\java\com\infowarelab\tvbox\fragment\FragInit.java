package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActHome;

/**
 * Created by sdvye on 2019/6/11.
 */

@SuppressLint("ValidFragment")
public class FragInit extends BaseFragment implements View.OnClickListener {
    private View initView;
    private ActHome actHome;
    private FragmentManager fragmentManager;
    private FragmentTransaction fragmentTransaction;

    public FragInit(ICallParentView iCallParentView) {
        super(iCallParentView);
    }
    public FragInit(){
        super();
    }
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        fragmentManager = getFragmentManager();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        initView = inflater.inflate(R.layout.frag_init, container, false);
        return initView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        Button btnNet = (Button) getActivity().findViewById(R.id.btn_net);
        btnNet.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                actHome = (ActHome) getActivity();
                actHome.startActivity(new Intent(Settings.ACTION_SETTINGS));
//                startActivity(new Intent(Settings.ACTION_SETTINGS));
            }
        });
        Button btnInfo = (Button) getActivity().findViewById(R.id.btn_info);
        btnInfo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                fragmentTransaction = fragmentManager.beginTransaction();
                actHome = (ActHome) getActivity();
                actHome.switchFrag(3);
                actHome.setLastPage(0);
                fragmentTransaction.commit();
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_net:
                break;
            case R.id.btn_info:
                break;
        }
    }
}
