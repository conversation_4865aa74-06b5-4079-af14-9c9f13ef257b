package com.infowarelab.tvbox.activity;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.Spinner;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;

/**
 * Created by sdvye on 2019/6/12.
 */

public class ActSound extends BaseFragmentActivity {

    public static ActSound mActivity = null;

    private Spinner spinner;
    private SeekBar sbSound;
    private Button button;
    private AudioManager audioManager;
    private MediaPlayer mediaPlayer;

    //调节音量的大小
    private int maxVolume = 50; // 最大音量值
    private int curVolume = 20; // 当前音量值
    private int stepVolume = 0; // 每次调整的音量幅度
    private AudioManager audioMgr = null; // Audio管理器，用了控制音量
    private LinearLayout llCancel;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        setContentView(R.layout.frag_sound);

        //hideBottomUIMenu();

        audioMgr = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        // 获取最大音乐音量
        maxVolume = audioMgr.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        // 初始化音量大概为最大音量的1/2
        curVolume = SharedPreferencesUrls.getInstance().getInt("progress",maxVolume *4/5);
        // 每次调整的音量大概为最大音量的1/6
        stepVolume = maxVolume / 6;

//        //播放器
//        mediaPlayer = MediaPlayer.create(ActSound.this, R.raw.wave_cn);

        SharedPreferences sharedPreferences = getSharedPreferences("sound", Context.MODE_PRIVATE);
        final SharedPreferences.Editor editor = sharedPreferences.edit();
        audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        audioManager.setMode(AudioManager.STREAM_SYSTEM);
        audioManager.setStreamVolume(AudioManager.STREAM_SYSTEM, 5, AudioManager.FLAG_SHOW_UI);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB){
            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
        } else {
            audioManager.setMode(AudioManager.MODE_IN_CALL);
        }
        audioManager.setSpeakerphoneOn(SharedPreferencesUrls.getInstance().getBoolean("speakerphoneon",false));//false走usb麦，true走hdmi
        sbSound = (SeekBar) findViewById(R.id.sb_sound);
        sbSound.setMax(maxVolume);
        sbSound.setProgress(curVolume);
        adjustVolume();
        spinner = (Spinner) findViewById(R.id.sp_sound);
        spinner.requestFocus();
        String[] items = {"内置扬声器","外接音频设备"};
        ArrayAdapter<String> stringArrayAdapter = new ArrayAdapter<>(this, R.layout.item_select, items);
        stringArrayAdapter.setDropDownViewResource(R.layout.item_drop);
        spinner.setAdapter(stringArrayAdapter);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                editor.putInt("position", position);
                editor.commit();
                switch (position) {
                    case 0:
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB){
                            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
                        } else {
                            audioManager.setMode(AudioManager.MODE_IN_CALL);
                        }
                        audioManager.setSpeakerphoneOn(true);//false走usb麦，true走hdmi
                        SharedPreferencesUrls.getInstance().putBoolean("speakerphoneon",true);
                        break;
                    case 1:
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB){
                            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
                        } else {
                            audioManager.setMode(AudioManager.MODE_IN_CALL);
                        }
                        audioManager.setSpeakerphoneOn(false);//false走usb麦，true走hdmi
                        SharedPreferencesUrls.getInstance().putBoolean("speakerphoneon",false);
                        break;
                }
            }

            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                audioManager.setSpeakerphoneOn(true);
            }
        });
        spinner.setSelection(sharedPreferences.getInt("position", 0));
//        spinner.setNextFocusUpId(R.id.btn_sounds);
//        spinner.setNextFocusDownId(R.id.sb_sound);

        button = (Button) findViewById(R.id.btn_sounds);
//        button.setNextFocusUpId(R.id.sb_sound);
//        button.setNextFocusDownId(R.id.sp_sound);

        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Uri uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
//                Ringtone ringtone = RingtoneManager.getRingtone(ActSound.this, uri);
//                ringtone.play();
                startMediaPlay();
                button.setEnabled(false);
            }
        });

//        sbSound.setNextFocusUpId(R.id.sp_sound);
//        sbSound.setNextFocusDownId(R.id.btn_sounds);
        sbSound.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                curVolume = progress;
                adjustVolume();
            }
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });

        llCancel = (LinearLayout) findViewById(R.id.ll_frag_set_cancel);

        llCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    protected void hideBottomUIMenu() {
        //隐藏虚拟按键，并且全屏
        if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) {
            View v = this.getWindow().getDecorView();
            v.setSystemUiVisibility(View.GONE);
        } else if (Build.VERSION.SDK_INT >= 19) {
            View decorView = getWindow().getDecorView();
            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_IMMERSIVE;
            decorView.setSystemUiVisibility(uiOptions);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_VOLUME_UP:
            case KeyEvent.KEYCODE_PLUS:
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                addVolume();
                break;
            case KeyEvent.KEYCODE_VOLUME_DOWN:
            case KeyEvent.KEYCODE_MINUS:
            case KeyEvent.KEYCODE_DPAD_LEFT:
                reduceVolume();
                break;
            case KeyEvent.KEYCODE_BACK:
                SharedPreferencesUrls.getInstance().putInt("progress",curVolume);
                break;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onBackPressed() {
        SharedPreferencesUrls.getInstance().putInt("progress",curVolume);
        super.onBackPressed();
    }
    @Override
    protected void onDestroy() {
        resetMediaPlay();
        mActivity = null;
        super.onDestroy();
    }
    /**
     * 重置播放器
     * */
    private void resetMediaPlay(){
        if (mediaPlayer != null && mediaPlayer.isPlaying()){
            mediaPlayer.stop();
            mediaPlayer.release();
            mediaPlayer = null;
        }
    }
    /**
     * 播放音频
     * */
    private void startMediaPlay(){
        //播放器
        mediaPlayer = MediaPlayer.create(ActSound.this, R.raw.wave_cn);
        mediaPlayer.start();
        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                resetMediaPlay();
                button.setEnabled(true);
            }
        });
    }
    /**
     * 调整音量
     */
    private void adjustVolume() {
        audioMgr.setStreamVolume(AudioManager.STREAM_MUSIC, curVolume,
                AudioManager.FLAG_PLAY_SOUND);
    }
    //增加音量
    private void addVolume(){
        curVolume += stepVolume;
        if (curVolume >= maxVolume) {
            curVolume = maxVolume;
        }
        sbSound.setProgress(curVolume);
        adjustVolume();
    }
    //降低音量
    private void reduceVolume(){
        curVolume -= stepVolume;
        if (curVolume <= 0) {
            curVolume = 0;
        }
        sbSound.setProgress(curVolume);
        adjustVolume();
    }

    @Override
    protected void onResume() {
        mActivity = ActSound.this;
        super.onResume();
    }
}
