package com.infowarelab.tvbox.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioRecord;
import android.media.MediaPlayer;
import android.media.MediaRecorder;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.Toast;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by sdvye on 2019/6/12.
 */

public class ActMic extends Activity {

    public static ActMic mActivity = null;

    private static final int FILE_SELECT = 1;
    private Spinner spinner;
    private SeekBar sbMic;
    private Button btnVoice;
    private Button btnStop;
    private Button btnPlay;
    private Button btnStart;
//    private AudioManager audioManager;
    private MediaPlayer mediaPlayer;
    private String path;
    private AudioRecord mAudioRecord;
    private int mRecorderBufferSize;
    private byte[] mAudioData;
    private int mSampleRateInHZ = 44100;//采样率
    private int mAudioFormat = AudioFormat.ENCODING_PCM_16BIT;//位数
    private int mChannelConfig = AudioFormat.CHANNEL_IN_MONO;//声道
    private boolean isRecording = false;
    private String mTmpFileAbs = "";
    private ConcurrentLinkedQueue<byte[]> audioQueue = new ConcurrentLinkedQueue<>();
    private ThreadPoolExecutor mExecutor = new ThreadPoolExecutor(2, 2, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());
    //调节音量的大小
    private int maxVolume = 50; // 最大音量值
    private int curVolume = 20; // 当前音量值
    private int stepVolume = 0; // 每次调整的音量幅度
    private AudioManager audioMgr = null; // Audio管理器，用了控制音量


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.frag_mic);

        Window window = getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {

            int uiOptions = View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN|View.SYSTEM_UI_FLAG_IMMERSIVE;

            window.getDecorView().setSystemUiVisibility(uiOptions);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
        else {
            View v = this.getWindow().getDecorView();
            v.setSystemUiVisibility(View.GONE);
        }

        SharedPreferences sharedPreferences = getSharedPreferences("mic", Context.MODE_PRIVATE);

        audioMgr = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        // 获取最大音乐音量
        maxVolume = audioMgr.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        // 初始化音量大概为最大音量的1/2
        curVolume = SharedPreferencesUrls.getInstance().getInt("video_progress",maxVolume * 4/5);
        // 每次调整的音量大概为最大音量的1/6
        stepVolume = maxVolume / 6;
        audioMgr.setStreamVolume(AudioManager.STREAM_MUSIC, 5, AudioManager.FLAG_SHOW_UI);

        final SharedPreferences.Editor editor = sharedPreferences.edit();
        initData();
        sbMic = (SeekBar) findViewById(R.id.sb_mic);
        sbMic.setMax(maxVolume);
        sbMic.setProgress(curVolume);
        adjustVolume();
        spinner = (Spinner) findViewById(R.id.sp_mic);
        String[] items = {"耳机", "外放"};
        ArrayAdapter<String> stringArrayAdapter = new ArrayAdapter<>(this, R.layout.item_select, items);
        stringArrayAdapter.setDropDownViewResource(R.layout.item_drop);
        spinner.setAdapter(stringArrayAdapter);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                editor.putInt("position", position);
                editor.commit();
                switch (position) {
                    case 0:
                        audioMgr.setSpeakerphoneOn(false);
                        break;
                    case 1:
                        audioMgr.setSpeakerphoneOn(true);
                        break;
                }
            }

            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                audioMgr.setSpeakerphoneOn(true);
            }
        });
        spinner.setSelection(sharedPreferences.getInt("position", 0));
        btnVoice = (Button) findViewById(R.id.btn_mics);
        btnVoice.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Toast.makeText(ActMic.this, "开始录音", Toast.LENGTH_SHORT).show();
                String tmpName = ConferenceApplication.currentTimeMillis() + "_" + mSampleRateInHZ + "";
                final File tmpFile = createFile("audio.pcm");
                final File tmpOutFile = createFile("audio.wav");
                assert tmpFile != null;
                mTmpFileAbs = tmpFile.getAbsolutePath();
                isRecording = true;
                mAudioRecord.startRecording();
                mExecutor.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            FileOutputStream outputStream = new FileOutputStream(tmpFile.getAbsoluteFile());
                            while (isRecording) {
                                int readSize = mAudioRecord.read(mAudioData, 0, mAudioData.length);
                                outputStream.write(mAudioData);
                            }
                            outputStream.close();
                            assert tmpOutFile != null;
                            pcmToWave(tmpFile.getAbsolutePath(), tmpOutFile.getAbsolutePath());
                        } catch (FileNotFoundException e) {
                            e.printStackTrace();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });
        btnStop = (Button) findViewById(R.id.btn_stop);
        btnStop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Toast.makeText(ActMic.this, "停止录音", Toast.LENGTH_SHORT).show();
                isRecording = false;
                mAudioRecord.stop();
            }
        });
        btnPlay = (Button) findViewById(R.id.btn_play_mic);
        btnPlay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
                intent.setType("*/*");
                intent.addCategory(Intent.CATEGORY_OPENABLE);
                startActivityForResult(intent, 1);
            }
        });
        btnStart = (Button) findViewById(R.id.btn_start);
        btnStart.setOnClickListener(new View.OnClickListener() {
            int count = 0;

            @Override
            public void onClick(View v) {
                count++;
                if (count % 2 == 1) {
                    btnStart.setText("暂停播放");
                    Toast.makeText(ActMic.this, "开始播放", Toast.LENGTH_SHORT).show();
                    try {
                        mediaPlayer = new MediaPlayer();
                        mediaPlayer.reset();
                        mediaPlayer.setDataSource("/sdcard/AudioRecord/audio.wav");
//                        mediaPlayer.setDataSource("/sdcard/AudioRecord/1563159422109_8000.wav");
                        mediaPlayer.prepare();
                        mediaPlayer.start();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                } else {
                    btnStart.setText("开始播放");
                    Toast.makeText(ActMic.this, "暂停播放", Toast.LENGTH_SHORT).show();
                    mediaPlayer.pause();
                }
            }
        });

        sbMic.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                curVolume = progress;
                adjustVolume();
            }
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
    }
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == FILE_SELECT) {
            if (resultCode == Activity.RESULT_OK) {
                Uri uri = data.getData();
                path = uri.getPath();
            }
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    private void initData() {
        mRecorderBufferSize = AudioRecord.getMinBufferSize(mSampleRateInHZ, mChannelConfig, mAudioFormat);
        mAudioData = new byte[320];
        //mAudioRecord = new AudioRecord(MediaRecorder.AudioSource.VOICE_NORMAL, mSampleRateInHZ, mChannelConfig, mAudioFormat, mRecorderBufferSize);

        mAudioRecord = new AudioRecord(MediaRecorder.AudioSource.VOICE_COMMUNICATION, mSampleRateInHZ, mChannelConfig, mAudioFormat, mRecorderBufferSize);
    }

    private File createFile(String name) {
        String dirPath = Environment.getExternalStorageDirectory().getPath() + "/AudioRecord/";
        File file = new File(dirPath);
        if (!file.exists()) {
            file.mkdirs();
        }
        String filePath = dirPath + name;
        File totalFile = new File(filePath);
        if (!totalFile.exists()) {
            try {
                totalFile.createNewFile();
                return totalFile;
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            try {
                FileWriter fileWriter = new FileWriter(totalFile);
                fileWriter.write("");
                fileWriter.flush();
                fileWriter.close();
                return totalFile;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private void pcmToWave(String inFileName, String outFileName) {

        FileInputStream in = null;

        FileOutputStream out = null;

        long totalAudioLen = 0;

        long longSampleRate = mSampleRateInHZ;

        long totalDataLen = totalAudioLen + 36;

        int channels = 1;//单声道1 双声道2

        long byteRate = 16 * longSampleRate * channels / 8;


        byte[] data = new byte[mRecorderBufferSize];

        try {

            in = new FileInputStream(inFileName);

            out = new FileOutputStream(outFileName);


            totalAudioLen = in.getChannel().size();

            totalDataLen = totalAudioLen + 36;

            writeWaveFileHeader(out, totalAudioLen, totalDataLen, longSampleRate, channels, byteRate);

            while (in.read(data) != -1) {

                out.write(data);

            }
            in.close();

            out.close();

        } catch (FileNotFoundException e) {

            // TODO Auto-generated catch block

            e.printStackTrace();

        } catch (IOException e) {

            // TODO Auto-generated catch block

            e.printStackTrace();

        }


    }

    private void writeWaveFileHeader(FileOutputStream out, long totalAudioLen, long totalDataLen, long longSampleRate, int channels, long byteRate) throws IOException {

        byte[] header = new byte[44];

        header[0] = 'R'; // RIFF

        header[1] = 'I';

        header[2] = 'F';

        header[3] = 'F';

        header[4] = (byte) (totalDataLen & 0xff);//数据大小

        header[5] = (byte) ((totalDataLen >> 8) & 0xff);

        header[6] = (byte) ((totalDataLen >> 16) & 0xff);

        header[7] = (byte) ((totalDataLen >> 24) & 0xff);

        header[8] = 'W';//WAVE

        header[9] = 'A';

        header[10] = 'V';

        header[11] = 'E';

        //FMT Chunk

        header[12] = 'f'; // 'fmt '

        header[13] = 'm';

        header[14] = 't';

        header[15] = ' ';//过渡字节

        //数据大小

        header[16] = 16; // 4 bytes: size of 'fmt ' chunk

        header[17] = 0;

        header[18] = 0;

        header[19] = 0;

        //编码方式 10H为PCM编码格式

        header[20] = 1; // format = 1

        header[21] = 0;

        //通道数

        header[22] = (byte) channels;

        header[23] = 0;

        //采样率，每个通道的播放速度

        header[24] = (byte) (longSampleRate & 0xff);

        header[25] = (byte) ((longSampleRate >> 8) & 0xff);

        header[26] = (byte) ((longSampleRate >> 16) & 0xff);

        header[27] = (byte) ((longSampleRate >> 24) & 0xff);

        //音频数据传送速率,采样率*通道数*采样深度/8

        header[28] = (byte) (byteRate & 0xff);

        header[29] = (byte) ((byteRate >> 8) & 0xff);

        header[30] = (byte) ((byteRate >> 16) & 0xff);

        header[31] = (byte) ((byteRate >> 24) & 0xff);

        // 确定系统一次要处理多少个这样字节的数据，确定缓冲区，通道数*采样位数

        header[32] = (byte) (1 * 16 / 8);

        header[33] = 0;

        //每个样本的数据位数

        header[34] = 16;

        header[35] = 0;

        //Data chunk

        header[36] = 'd';//data

        header[37] = 'a';

        header[38] = 't';

        header[39] = 'a';

        header[40] = (byte) (totalAudioLen & 0xff);

        header[41] = (byte) ((totalAudioLen >> 8) & 0xff);

        header[42] = (byte) ((totalAudioLen >> 16) & 0xff);

        header[43] = (byte) ((totalAudioLen >> 24) & 0xff);

        out.write(header, 0, 44);

    }
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_VOLUME_UP:
            case KeyEvent.KEYCODE_PLUS:
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                addVolume();
                break;
            case KeyEvent.KEYCODE_VOLUME_DOWN:
            case KeyEvent.KEYCODE_MINUS:
            case KeyEvent.KEYCODE_DPAD_LEFT:
                reduceVolume();
                break;
            case KeyEvent.KEYCODE_BACK:
                SharedPreferencesUrls.getInstance().putInt("video_progress",curVolume);
                break;
        }
        return super.onKeyDown(keyCode, event);
    }
    @Override
    protected void onDestroy() {
        if (mediaPlayer != null && mediaPlayer.isPlaying()){
            mediaPlayer.stop();
            mediaPlayer.release();
            mediaPlayer = null;
        }
        mActivity = null;
        super.onDestroy();
    }

    /**
     * 调整音量
     */
    private void adjustVolume() {
        audioMgr.setStreamVolume(AudioManager.STREAM_MUSIC, curVolume,
                AudioManager.FLAG_PLAY_SOUND);
    }
    //增加音量
    private void addVolume(){
        curVolume += stepVolume;
        if (curVolume >= maxVolume) {
            curVolume = maxVolume;
        }
        sbMic.setProgress(curVolume);
        adjustVolume();
    }
    //降低音量
    private void reduceVolume(){
        curVolume -= stepVolume;
        if (curVolume <= 0) {
            curVolume = 0;
        }
        sbMic.setProgress(curVolume);
        adjustVolume();
    }

    @Override
    protected void onResume() {
        mActivity = ActMic.this;
        super.onResume();
    }
}
