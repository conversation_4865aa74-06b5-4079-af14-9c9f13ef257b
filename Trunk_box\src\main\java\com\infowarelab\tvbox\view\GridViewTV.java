package com.infowarelab.tvbox.view;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.widget.GridView;

/**
 * GridView TV版本.
 *
 * @<NAME_EMAIL>
 */
public class GridViewTV extends GridView {

    private int lastPosition = -1;

    public GridViewTV(Context context) {
        this(context, null);
    }

    public GridViewTV(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
        this.setFocusable(true);//允许获取上层焦点
    }

    @Override
    public boolean isInTouchMode() {
        return !(hasFocus() && !super.isInTouchMode());
    }

    private void init(Context context, AttributeSet attrs) {
        this.setChildrenDrawingOrderEnabled(true);
    }

    @Override
    protected int getChildDrawingOrder(int childCount, int i) {
        if (this.getSelectedItemPosition() != -1) {
            if (i + this.getFirstVisiblePosition() == this.getSelectedItemPosition()) {// 这是原本要在最后一个刷新的item
                return childCount - 1;
            }
            if (i == childCount - 1) {// 这是最后一个需要刷新的item
                return this.getSelectedItemPosition() - this.getFirstVisiblePosition();
            }
        }
        return i;
    }

    public void setDefaultSelect(int pos) {
        requestFocusFromTouch();
        setSelection(pos);
    }

    /**
     * 强行执行一次onItemSelected
     */
    @Override
    public void setSelection(int position) {
        super.setSelection(position);
        if (position == lastPosition) {
            getOnItemSelectedListener().onItemSelected(this, getSelectedView(), position, 0);
        }
        lastPosition = position;
    }

}