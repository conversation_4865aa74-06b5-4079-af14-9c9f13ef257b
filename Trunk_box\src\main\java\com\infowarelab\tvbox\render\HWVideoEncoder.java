package com.infowarelab.tvbox.render;


import android.annotation.SuppressLint;
import android.content.Context;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaFormat;
import android.os.Build;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;

import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;

import java.nio.ByteBuffer;
import java.util.Arrays;

public class HWVideoEncoder{

    private static final String TAG = "InfowareLab.Encoder";

    private static final long DEFAULT_TIMEOUT = 10 * 1000;

    private static final int DEFAULT_FRAME_RATE = 30;
    private static final int DEFAULT_IFRAME_INTERVAL = 1;

    private static String rtmlUrl = "rtmp://192.168.2.123:8022/meeting/123456";

    private MediaCodec mVideoEncoder;

    private MediaCodec.BufferInfo mVBufferInfo;

    private boolean mIsInitialized = false;

    private Surface mInputSurface;
    private byte[] mMediaHead = null;
    private VideoCommonImpl mVideoCommon = null;
    private ShareDtCommonImpl mShareDtCommon = null;
    private int mWidth = 0;
    private int mHeight = 0;
    private boolean isShareDt = false;
    private boolean mAsyncCodec = false;
    private boolean mDebugable = false;

    private int mBitRate = 800 * 1024;
    private int mFrameRate = 25;
    private int mColorFormat = 0;
    private boolean mUseH265 = false;


    //private SrsPublisher mPublisher = null;
    //private SrsFlvMuxer  mFlvMuxer = null;
    private int mVideoFlvTrack = -1;

    @SuppressLint("NewApi")
    public void init(VideoCommonImpl videoCommon, int width, int height, int colorFormat, int bitRate, int frameRate, boolean asyncCodec) throws Exception {

        String codecType = HWCodec.MIME_TYPE_AVC;

        if (mUseH265) codecType = HWCodec.MIME_TYPE_HEVC;

        if (getCodecInfo(codecType) == null) {
            throw new Exception("cannot find suitable codec");
        }

        mVideoCommon = videoCommon;
        isShareDt = false;
        mAsyncCodec = asyncCodec;

        mWidth = width;
        mHeight = height;


        MediaFormat videoFormat = MediaFormat.createVideoFormat(codecType, width, height);
        //videoFormat.setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileMain);
        videoFormat.setInteger("vendor.rtc-ext-enc-low-latency.enable",1);

        videoFormat.setInteger(MediaFormat.KEY_BIT_RATE, bitRate);
        videoFormat.setInteger(MediaFormat.KEY_FRAME_RATE, frameRate);
        videoFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, DEFAULT_IFRAME_INTERVAL);
        videoFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, colorFormat);

        mBitRate = bitRate;
        mFrameRate = frameRate;
        mColorFormat = colorFormat;

        Log.d(TAG, ">>>>>> vendor.rtc-ext-enc-low-latency.enable => 1");
        //Log.d(TAG, ">>>>>> bitRate => " + bitRate);
        //Log.d(TAG, ">>>>>> MediaFormat.KEY_PROFILE => AVCProfileMain");

        mVideoEncoder = MediaCodec.createEncoderByType(codecType);

        Log.d(TAG, ">>>>>> HWCodec.MIME_TYPE_HEVC");

        if (mAsyncCodec)
            mVideoEncoder.setCallback(mCodecCallback);

        mVideoEncoder.configure(videoFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);

        mInputSurface = mVideoEncoder.createInputSurface();
        mVideoEncoder.start();

        mVBufferInfo = new MediaCodec.BufferInfo();

        mIsInitialized = true;

        Log.d(TAG, "HWVideoEncoder initialized(Video): resolution=" + width + "x" + height);
        Log.d(TAG, "HWVideoEncoder initialized(Video): bitRate=" + bitRate/1024 + "kbps; frameRate=" + frameRate);
    }

    @SuppressLint("NewApi")
    public void init(ShareDtCommonImpl shareDtCommon, int width, int height, int colorFormat, int bitRate, int frameRate, boolean asyncCodec) throws Exception {

        String codecType = HWCodec.MIME_TYPE_AVC;

        if (mUseH265) codecType = HWCodec.MIME_TYPE_HEVC;

        if (getCodecInfo(codecType) == null) {
            throw new Exception("cannot find suitable codec");
        }

        mShareDtCommon = shareDtCommon;
        isShareDt = true;
        mAsyncCodec = asyncCodec;

        mWidth = width;
        mHeight = height;

        MediaFormat videoFormat = MediaFormat.createVideoFormat(codecType, width, height);
        //videoFormat.setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileMain);
        videoFormat.setInteger("vendor.rtc-ext-enc-low-latency.enable",1);

        videoFormat.setInteger(MediaFormat.KEY_BIT_RATE, bitRate);
        videoFormat.setInteger(MediaFormat.KEY_FRAME_RATE, frameRate);
        videoFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, DEFAULT_IFRAME_INTERVAL);
        videoFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, colorFormat);

        Log.d(TAG, ">>>>>> (ShareDt)vendor.rtc-ext-enc-low-latency.enable => 1");
        //Log.d(TAG, ">>>>>> bitRate => " + bitRate);
        //Log.d(TAG, ">>>>>> MediaFormat.KEY_PROFILE => AVCProfileMain");

        mBitRate = bitRate;
        mFrameRate = frameRate;
        mColorFormat = colorFormat;

        mVideoEncoder = MediaCodec.createEncoderByType(codecType);

        Log.d(TAG, ">>>>>> HWCodec.MIME_TYPE_HEVC");

        if (mAsyncCodec)
            mVideoEncoder.setCallback(mCodecCallback);

        mVideoEncoder.configure(videoFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);

        mInputSurface = mVideoEncoder.createInputSurface();

        mVideoEncoder.start();

        mVBufferInfo = new MediaCodec.BufferInfo();

        mIsInitialized = true;

        Log.d(TAG, "HWVideoEncoder initialized(ShareDt): resolution=" + width + "x" + height);
        Log.d(TAG, "HWVideoEncoder initialized(ShareDt): bitRate=" + bitRate/1024 + "kbps; frameRate=" + frameRate);
    }

    @SuppressLint("NewApi")
    private MediaCodec.Callback mCodecCallback = new MediaCodec.Callback() {
        @Override
        public void onInputBufferAvailable(@NonNull MediaCodec codec, int index) {

            Log.d(TAG, ">>>>>>onInputBufferAvailable");
        }

        @Override
        public void onOutputBufferAvailable(@NonNull MediaCodec codec, int index, @NonNull MediaCodec.BufferInfo info) {

            Log.d(TAG, "onOutputBufferAvailable: output buffer Index：" + index);

            ByteBuffer encodedData = codec.getOutputBuffer(index);

            sendEncodedFrame(encodedData, info);

            codec.releaseOutputBuffer(index, false);
        }

        @Override
        public void onError(@NonNull MediaCodec codec, @NonNull MediaCodec.CodecException e) {

            Log.d(TAG, "onError: " + e.getMessage());
        }

        @Override
        public void onOutputFormatChanged(@NonNull MediaCodec codec, @NonNull MediaFormat format) {

            Log.d(TAG, ">>>>>>onOutputFormatChanged");

            ByteBuffer spsb = format.getByteBuffer("csd-0");
            ByteBuffer ppsb = format.getByteBuffer("csd-1");

            byte[] sps = new byte[spsb.remaining()];
            spsb.get(sps, 0, sps.length);

            byte[] pps = new byte[ppsb.remaining()];
            ppsb.get(pps, 0, pps.length);

            mMediaHead = splicingArrays(sps, pps);

        }
    };

    private static MediaCodecInfo getCodecInfo(final String mimeType) {
        final int numCodecs = MediaCodecList.getCodecCount();
        for (int i = 0; i < numCodecs; i++) {
            final MediaCodecInfo codecInfo = MediaCodecList.getCodecInfoAt(i);
            if (!codecInfo.isEncoder()) {
                continue;
            }
            final String[] types = codecInfo.getSupportedTypes();
            for (String type : types) {
                if (type.equalsIgnoreCase(mimeType)) {
                    return codecInfo;
                }
            }
        }
        return null;
    }

    /**
     * Returns the encoder's input surface.
     */
    public Surface getInputSurface() {
        return mInputSurface;
    }

    public void sendFrame()throws Exception{
        if (mVideoEncoder != null)
            drainEncoder(mVideoEncoder, mVBufferInfo);
    }

    public void sendFrameEx(boolean endofStream){
        if (mVideoEncoder != null)
            drainEncoderEx(mVideoEncoder, mVBufferInfo, endofStream);
    }

    @SuppressWarnings("WeakerAccess")
    public void recordSample(byte[] sample) throws Exception {

        Log.v("abcde:time","pts"+getPTSUs());

        //doRecord(mAudioEncoder, mABufferInfo, sample, getPTSUs());
    }

    /**
     * previous presentationTimeUs for writing
     */
    private long prevOutputPTSUs = 0;
    /**
     * get next encoding presentationTimeUs
     * @return
     */
    protected long getPTSUs() {
        long result = System.nanoTime() / 1000L;
        // presentationTimeUs should be monotonic
        // otherwise muxer fail to write
        if (result < prevOutputPTSUs)
            result = (prevOutputPTSUs - result) + result;


        prevOutputPTSUs = result;

        return result;
    }


    private void doRecord(MediaCodec encoder, MediaCodec.BufferInfo bufferInfo, byte[] data,
                          long pts) throws Exception {
        if (!mIsInitialized) {
            Log.e(TAG, "Recorder must be initialized!");
            return;
        }
        int index = encoder.dequeueInputBuffer(DEFAULT_TIMEOUT);
        ByteBuffer[] inputBuffers = encoder.getInputBuffers();
        ByteBuffer buffer = inputBuffers[index];
        buffer.clear();
        buffer.put(data);

        encoder.queueInputBuffer(index, 0, data.length, pts, 0);

        drainEncoder(encoder, bufferInfo);
    }

    public static byte[] splicingArrays(byte[]... bytes) {
        int length = 0;
        for (byte[] b : bytes) {
            length += b.length;
        }
        int interimLength = 0;
        byte[] result = new byte[length];
        for (byte[] b : bytes) {
            System.arraycopy(b, 0, result, interimLength, b.length);
            interimLength += b.length;
        }
        return result;
    }

    @SuppressLint("NewApi")
    private void drainEncoder(MediaCodec encoder, MediaCodec.BufferInfo bufferInfo) throws Exception {
        //int trackIndex = mVTrackIndex;
        ByteBuffer[] outputBuffers = encoder.getOutputBuffers();
        while (true) {
            int index = encoder.dequeueOutputBuffer(bufferInfo, 0);
            if (index == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {
                outputBuffers = encoder.getOutputBuffers();
            } else if (index == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                Log.d(TAG, "drainEncoder MediaCodec.INFO_OUTPUT_FORMAT_CHANGED");
                ByteBuffer spsb = encoder.getOutputFormat().getByteBuffer("csd-0");
                ByteBuffer ppsb = encoder.getOutputFormat().getByteBuffer("csd-1");

                byte[] sps = new byte[spsb.remaining()];
                spsb.get(sps, 0, sps.length);

                byte[] pps = new byte[ppsb.remaining()];
                ppsb.get(pps, 0, pps.length);

                mMediaHead = splicingArrays(sps, pps);

            } else if (index == MediaCodec.INFO_TRY_AGAIN_LATER) {
                //Log.d(TAG, "drainEncoder MediaCodec.INFO_TRY_AGAIN_LATER");
                break;
            } else if (index < 0) {
                Log.d(TAG, "drainEncoder unexpected result: " + index);
            } else {
                if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) {
                    continue;
                }

                if (bufferInfo.size != 0) {
                    ByteBuffer outputBuffer = outputBuffers[index];

                    if (outputBuffer == null) {
                        throw new RuntimeException("drainEncoder get outputBuffer " + index + " was null");
                    }

                    outputBuffer.position(bufferInfo.offset);
                    outputBuffer.limit(bufferInfo.offset + bufferInfo.size);

                    if (mDebugable)
                        Log.d(TAG,"drainEncoder size = "+bufferInfo.size);

//                    if (mFlvMuxer != null && mVideoFlvTrack > 0)
//                    {
//                        Log.d(TAG,"mFlvMuxer.writeSampleData = "+bufferInfo.size);
//                        mFlvMuxer.writeSampleData(mVideoFlvTrack, outputBuffer, bufferInfo);
//                    }

                    sendEncodedFrame(outputBuffer, bufferInfo);

                    //mMuxer.writeSampleData(trackIndex, outputBuffer, bufferInfo);
                }

                encoder.releaseOutputBuffer(index, false);
            }
        }
    }

    /**
     * Extracts all pending data from the encoder and forwards it to the muxer.
     * <p>
     * If endOfStream is not set, this returns when there is no more data to drain.  If it
     * is set, we send EOS to the encoder, and then iterate until we see EOS on the output.
     * Calling this with endOfStream set should be done once, right before stopping the muxer.
     * <p>
     * We're just using the muxer to get a .mp4 file (instead of a raw H.264 stream).  We're
     * not recording audio.
     */
    public void drainEncoderEx(MediaCodec encoder, MediaCodec.BufferInfo bufferInfo, boolean endOfStream) {
        final int TIMEOUT_USEC = 0;
        if (mDebugable) Log.d(TAG, "drainEncoder(" + endOfStream + ")");

        if (endOfStream) {
            if (mDebugable) Log.d(TAG, "sending EOS to encoder");
            encoder.signalEndOfInputStream();
        }

        ByteBuffer[] encoderOutputBuffers = encoder.getOutputBuffers();
        while (true) {
            int encoderStatus = encoder.dequeueOutputBuffer(bufferInfo, TIMEOUT_USEC);
            if (encoderStatus == MediaCodec.INFO_TRY_AGAIN_LATER) {
                // no output available yet
                if (!endOfStream) {
                    break;      // out of while
                } else {
                    if (mDebugable) Log.d(TAG, "no output available, spinning to await EOS");
                }
            } else if (encoderStatus == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {
                // not expected for an encoder
                encoderOutputBuffers = encoder.getOutputBuffers();
            } else if (encoderStatus == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                // should happen before receiving buffers, and should only happen once

                MediaFormat newFormat = encoder.getOutputFormat();
                Log.d(TAG, "encoder output format changed: " + newFormat);

                ByteBuffer spsb = encoder.getOutputFormat().getByteBuffer("csd-0");
                ByteBuffer ppsb = encoder.getOutputFormat().getByteBuffer("csd-1");

                byte[] sps = new byte[spsb.remaining()];
                spsb.get(sps, 0, sps.length);

                byte[] pps = new byte[ppsb.remaining()];
                ppsb.get(pps, 0, pps.length);

                mMediaHead = splicingArrays(sps, pps);

            } else if (encoderStatus < 0) {
                Log.w(TAG, "unexpected result from encoder.dequeueOutputBuffer: " +
                        encoderStatus);
                // let's ignore it
            } else {
                ByteBuffer encodedData = encoderOutputBuffers[encoderStatus];
                if (encodedData == null) {
                    throw new RuntimeException("encoderOutputBuffer " + encoderStatus +
                            " was null");
                }

                if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) {
                    // The codec config data was pulled out and fed to the muxer when we got
                    // the INFO_OUTPUT_FORMAT_CHANGED status.  Ignore it.
                    if (mDebugable) Log.d(TAG, "ignoring BUFFER_FLAG_CODEC_CONFIG");
                    bufferInfo.size = 0;
                }

                if (bufferInfo.size != 0) {

                    // adjust the ByteBuffer values to match BufferInfo (not needed?)
                    encodedData.position(bufferInfo.offset);
                    encodedData.limit(bufferInfo.offset + bufferInfo.size);

                    if (mDebugable)
                        Log.d(TAG,"drainEncoder size = "+bufferInfo.size);

//                    if (mFlvMuxer != null && mVideoFlvTrack > 0)
//                    {
//                        Log.d(TAG,"mFlvMuxer.writeSampleData = "+bufferInfo.size);
//                        mFlvMuxer.writeSampleData(mVideoFlvTrack, outputBuffer, bufferInfo);
//                    }

                    sendEncodedFrame(encodedData, bufferInfo);
                }

                encoder.releaseOutputBuffer(encoderStatus, false);

                if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                    if (!endOfStream) {
                        Log.w(TAG, "reached end of stream unexpectedly");
                    } else {
                        if (mDebugable) Log.d(TAG, "end of stream reached");
                    }
                    break;      // out of while
                }
            }
        }
    }

    private boolean isKeyFrame(byte[] buffer) {

        if (buffer.length < 5) {
            return false;
        }

        //00 00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 0
                && buffer[3] == 1) {
            int nalType = buffer[4] & 0x1f;

            if (Build.VERSION.SDK_INT == 31) {
                if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08 || nalType == 0x06) {
                    //Log.d(TAG, ">>>>>>isKeyFrame: nalType = " + nalType);
                    return true;
                }
            }
            else
            {
                if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                    //Log.d(TAG, ">>>>>>isKeyFrame: nalType = " + nalType);
                    return true;
                }
            }
        }

        //00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 1) {
            int nalType = buffer[3] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                //Log.d(TAG, ">>>>>>isKeyFrame: nalType = " + nalType);
                return true;
            }
        }

        return false;
    }

    public String byteTo16(byte bt){
        String[] strHex={"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"};
        String resStr="";
        int low =(bt & 15);
        int high = bt>>4 & 15;
        resStr = strHex[high]+strHex[low];
        return resStr;
    }


    private void sendEncodedFrame(ByteBuffer outputBuffer, MediaCodec.BufferInfo bufferInfo) {

        byte[] data = new byte[bufferInfo.size];  //delete []data;

        outputBuffer.get(data);

        if (mMediaHead != null) {

            int length = bufferInfo.size;

            boolean keyFrame = isKeyFrame(data);

            if (mDebugable &&length > 21) {

                String showInfo = "";

                if (keyFrame) {
                    showInfo = "encodeFrame =>!Key frame length = " + length + "; ";

                } else {
                    showInfo = "encodeFrame =>P frame length = " + length + "; ";
                }

                for (int count = 0; count <= 21; count++) {

                    if (keyFrame) {
                        //showInfo = "encodeFrame =>!Key frame length = " + length + "; ";
                        showInfo += byteTo16(data[count]);
                        if (count < 21) showInfo += ",";

                    } else {
                        //showInfo = "encodeFrame =>P frame length = " + length + "; ";
                        showInfo += byteTo16(data[count]);
                        if (count < 21) showInfo += ",";
                    }
                }

                Log.d(TAG, showInfo);
            }

            if (keyFrame) { //IDR frame

                //int len;
                mMediaHead[4] = (byte) (mMediaHead[4] | (3 << 5));

                if (!isShareDt)
                {
                    if (mDebugable) {
                        Log.d("InfowareLab.Encoder", "(Video)I Frame PPS.SPS length: " + mMediaHead.length);
                        Log.d("InfowareLab.Encoder", "(Video)I Frame PPS.SPS: " + Arrays.toString(mMediaHead));
                    }
                    if (mVideoCommon != null) mVideoCommon.sendMyVideoData(mMediaHead, mMediaHead.length, true, this.mWidth, this.mHeight, true);
                }
                else
                {
                    if (mDebugable) {
                        Log.d("InfowareLab.Encoder", "(ShareDt)I Frame PPS.SPS length: " + mMediaHead.length);
                        Log.d("InfowareLab.Encoder", "(ShareDt)I Frame PPS.SPS: " + Arrays.toString(mMediaHead));
                    }
                    if (mShareDtCommon != null) mShareDtCommon.sendScreenData(this.mWidth, this.mHeight, 24, mMediaHead, mMediaHead.length, true, true);
                }

                data[4] = (byte) (data[4] | (3 << 5)); //nal_ref_idc = 3,IDR frame high priority
                //len = data.length;

                if (!isShareDt) {
                    if (mDebugable) {
                        Log.d("InfowareLab.Encoder", "(Video)I Frame length: " + data.length);
                        Log.d("InfowareLab.Encoder", "(Video)I Frame resolution: " + mWidth + "x" + mHeight);
                    }
                    if (mVideoCommon != null) mVideoCommon.sendMyVideoData(data, data.length, true, this.mWidth, this.mHeight, true);
                }
                else
                {
                    if (mDebugable) {
                        Log.d("InfowareLab.Encoder", "(ShareDt)I Frame length: " + data.length);
                        Log.d("InfowareLab.Encoder", "(ShareDt)I Frame resolution: " + mWidth + "x" + mHeight);
                    }
                    if (mShareDtCommon != null) mShareDtCommon.sendScreenData(this.mWidth, this.mHeight, 24, data, data.length, true, true);
                }

            } else { //P frame

                if (!isShareDt) {

                    if (mDebugable) {
                        Log.d("InfowareLab.Encoder", "(Video)P Frame length: " + data.length);
                        Log.d("InfowareLab.Encoder", "(Video)P Frame resolution: " + mWidth + "x" + mHeight);
                    }

                    if (mVideoCommon != null) mVideoCommon.sendMyVideoData(data, data.length, false, this.mWidth, this.mHeight, true);
                }
                else
                {
                    if (mDebugable) {
                        Log.d("InfowareLab.Encoder", "(ShareDt)P Frame length: " + data.length);
                        Log.d("InfowareLab.Encoder", "(ShareDt)P Frame resolution: " + mWidth + "x" + mHeight);
                    }

                    if (mShareDtCommon != null) mShareDtCommon.sendScreenData(this.mWidth, this.mHeight, 24, data, data.length, true, false);
                }
            }
        }
    }

    public void stop() {
        try {
            release();
            mIsInitialized = false;

        } catch (Exception e) {
            Log.d(TAG, "stop exception occur: " + e.getLocalizedMessage());
        }
    }

    @SuppressLint("NewApi")
    public void release() throws Exception {
        if (mVideoEncoder != null) {
            mVideoEncoder.stop();
            mVideoEncoder.release();
            mVideoEncoder = null;
        }
    }

    public void changeResolution(int width, int height){

        if (!mIsInitialized) return;

        if (mVideoEncoder != null) {

            mWidth = width;
            mHeight = height;

            MediaFormat videoFormat = MediaFormat.createVideoFormat(HWCodec.MIME_TYPE_AVC, width, height);
            videoFormat.setInteger("vendor.rtc-ext-enc-low-latency.enable",1);

            videoFormat.setInteger(MediaFormat.KEY_BIT_RATE, mBitRate);
            videoFormat.setInteger(MediaFormat.KEY_FRAME_RATE, mFrameRate);
            videoFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, DEFAULT_IFRAME_INTERVAL);
            videoFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, mColorFormat);

            mVideoEncoder.stop();

            mVideoEncoder.configure(videoFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);

            mVideoEncoder.start();

            Log.d(TAG, "changeResolution: " + width + "x" + height);
            Log.d(TAG, "changeResolution: bitRate=" + mBitRate);
            Log.d(TAG, "changeResolution: mFrameRate=" + mFrameRate);
        }
    }

}
