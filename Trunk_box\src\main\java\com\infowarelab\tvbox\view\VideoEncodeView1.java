package com.infowarelab.tvbox.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.hardware.Camera;
import android.hardware.Camera.CameraInfo;
import android.hardware.Camera.Parameters;
import android.hardware.Camera.PreviewCallback;
import android.os.Build;
import android.util.AttributeSet;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceHolder.Callback;
import android.view.SurfaceView;
import android.widget.RelativeLayout;

import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.video.AvcHardEncoder1;

////import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.List;

/**
 * PreviewCallback回到接口，用于显示预览框
 */
@SuppressLint("NewApi")
public class VideoEncodeView1 extends SurfaceView implements PreviewCallback,
        Callback {
    private Context activity;

    private boolean isWriteFile = false;
    private File _fr = null;
    private FileOutputStream _out = null;

    public static int cameraWidth = 320;
    public static int cameraHeight = 240;
    private SurfaceHolder holder;
    private SurfaceHolder mPreviewHolder = null;
    private static boolean initVideo = true;
    private static boolean isSharing = false;
    private boolean isPortrait = true;
    private VideoCommonImpl videoCommon = (VideoCommonImpl) CommonFactory
            .getInstance().getVideoCommon();
    private ShareDtCommonImpl shareDtCommon = (ShareDtCommonImpl)CommonFactory.getInstance().getSdCommon();
    private int degrees = 90;
    private byte[] yv12buf;
    private Camera camera = null;
    static int numOfCamera = Camera.getNumberOfCameras();
    static int currentCamera = CameraInfo.CAMERA_FACING_FRONT;
    //    static int currentCamera = CameraInfo.CAMERA_FACING_BACK;
    private AvcHardEncoder1 h264HwEncoderImpl;
    private boolean isHardCodec = true;
    //private Logger logger = Logger.getLogger("VideoEncodeView");
    //是否是辅流
    private boolean isFlow = false;
    private boolean mPreviewing = false;

    public void setFlow(boolean flow) {
        isFlow = flow;
    }

    public VideoEncodeView1(Context context) {
        super(context);
        activity = context;
        init();
    }

    public VideoEncodeView1(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        activity = context;
        init();
    }

    public VideoEncodeView1(Context context, AttributeSet attrs) {
        super(context, attrs);
        activity = context;
        init();
    }

    public void init() {
        if (holder == null) {
            holder = this.getHolder();
        }
        holder.addCallback(this);
        holder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        if (null == h264HwEncoderImpl)
            h264HwEncoderImpl = new AvcHardEncoder1();
        isHardCodec = AvcHardEncoder1.IsSupportHardEncode();
        //Log.e("YYYYYY","是否是硬编:"+isHardCodec);

//        mPreviewHolder = null;

//        if (isWriteFile) {
//            _fr = new File("/sdcard/yv12buf.src");
//            try {
//                _out = new FileOutputStream(_fr);
//            } catch (FileNotFoundException e) {
//                // TODO Auto-generated catch block
//                e.printStackTrace();
//            }
//        }
    }

    @Override
    public void onPreviewFrame(byte[] data, Camera camera) {
        //Log.d("InfowareLab.Debug", "辅流onPreviewFrame datalength = "+ data.length);

        if (data == null) {
            return;
        }
        /**
         * 防止花屏
         * */
        if (data[4] == 0x61){
            data[4] = 0x41;
        }
        if (isSharing) {

            if (isPortrait) {//竖屏

                if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16) {
                    if (h264HwEncoderImpl.GetMediaEncoder() == null)
                        h264HwEncoderImpl.initEncoder(cameraHeight, cameraWidth);
//                    if (!isFlow){
//                        yv12buf = h264HwEncoderImpl.getAdapterYv12bufPortrait(data, cameraWidth, cameraHeight, currentCamera);
//                        h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
//                    }else {
                    if (currentCamera == CameraInfo.CAMERA_FACING_BACK) {
                        yv12buf = rotateYUV420SPBackfacing(data, cameraWidth, cameraHeight);
                    } else {
                        yv12buf = rotateYUV420SPFrontfacing(data, cameraWidth, cameraHeight);
                    }
                    h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon,shareDtCommon,isFlow);
//                    }
//                    h264HwEncoderImpl.offerEncoderAndSend(data, videoCommon);
                } else //soft ware encoding
                {
                    if (currentCamera == CameraInfo.CAMERA_FACING_BACK) {
                        yv12buf = rotateYUV420SPBackfacing(data, cameraWidth, cameraHeight);
                    } else {
                        yv12buf = rotateYUV420SPFrontfacing(data, cameraWidth, cameraHeight);
                    }
                    if (!isFlow){
                        videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraHeight, cameraWidth, false);
                    }else {
                        shareDtCommon.sendScreenData(cameraWidth,cameraHeight,24,data,data.length,true,false);
                    }
                }
                //end
            } else {//横屏
                if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16)//hardware encode.....
                {
                    if (h264HwEncoderImpl.GetMediaEncoder() == null)
                        h264HwEncoderImpl.initEncoder(cameraWidth, cameraHeight);
//                    if (!isFlow){
//                        yv12buf = h264HwEncoderImpl.getAdapterYv12bufLandscape(data, cameraWidth, cameraHeight, degrees);
//                        h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
//                    }else {
                    if (degrees == 0) {
                        yv12buf = changeYUV420SP2P(data, cameraWidth, cameraHeight);
                    } else {
                        yv12buf = Rotate180YUV420SP2P(data, cameraWidth, cameraHeight);
                    }
                    if (isWriteFile) {
                        try {
                            _out.write(data);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon,shareDtCommon,isFlow);
//                    }
//                    h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
//                    h264HwEncoderImpl.offerEncoderAndSend(data, videoCommon);
                } else  //software encode
                {
                    if (degrees == 0) {
                        yv12buf = changeYUV420SP2P(data, cameraWidth, cameraHeight);
                    } else {
                        yv12buf = Rotate180YUV420SP2P(data, cameraWidth, cameraHeight);
                    }
                    if (!isFlow){
                        videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraWidth, cameraHeight, false);
                    }else {
                        shareDtCommon.sendScreenData(cameraWidth,cameraHeight,24,data,data.length,true,false);
                    }
                }
            }
        }
        camera.addCallbackBuffer(data);
    }

    public void startCamera() {

        Log.d("InfowareLab.Debug", "VideoEncoderView1.startCamera");
        openCamera();
        if (camera == null) {
            return;
        }
        setCameraParameters(degrees);
        if (Integer.parseInt(Build.VERSION.SDK) >= 16 && isHardCodec) {
            int ret;
            if (isPortrait)
                ret = h264HwEncoderImpl.initEncoder(cameraHeight, cameraWidth);
            else
                ret = h264HwEncoderImpl.initEncoder(cameraWidth, cameraHeight);
            if (ret == 1)
                isHardCodec = false;
        }
        camera.setPreviewCallback(this);
        if (mPreviewHolder != null) {
            try {
                camera.setPreviewDisplay(mPreviewHolder);
            } catch (IOException e) {
                e.printStackTrace();
            }

            changePreview(true);
        }
        else
        {
            Log.d("InfowareLab.Debug", "VideoEncoderView1: Surface is NOT ready.");
        }
        setBackgroundColor(0);
    }

    public void openCamera() {
        //Log.e("YYYYYY","相机个数::"+numOfCamera);
        Log.d("InfowareLab.Debug", "VideoEncoderView1.openCamera");

        if (Integer.parseInt(Build.VERSION.SDK) > 8) {
            if (numOfCamera == 1) {
//                currentCamera = CameraInfo.CAMERA_FACING_FRONT;
                currentCamera = CameraInfo.CAMERA_FACING_BACK;
                camera = Camera.open(currentCamera);
            } else {
                if (isFlow){
                    currentCamera = CameraInfo.CAMERA_FACING_BACK;
                }else {
                    currentCamera = CameraInfo.CAMERA_FACING_FRONT;
                }
                try {
                    camera = Camera.open(currentCamera);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            camera = Camera.open(currentCamera);
        }
        if (Build.MODEL.toUpperCase().startsWith("Lenovo".toUpperCase())){
            camera.setDisplayOrientation(0);
        }
    }

    private void setCameraOrientation(int degrees, Parameters p) {
        if (Integer.parseInt(Build.VERSION.SDK) >= 8)
            setDisplayOrientation(camera, degrees);
        else {
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                p.set("orientation", "portrait");
                p.set("rotation", degrees);
            }
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
                p.set("orientation", "landscape");
                p.set("rotation", degrees);
            }
        }
    }

    private void setDisplayOrientation(Camera mCamera, int angle) {
        Method downPolymorphic;
        try {
            downPolymorphic = mCamera.getClass().getMethod(
                    "setDisplayOrientation", new Class[]{int.class});
            if (downPolymorphic != null)
                downPolymorphic.invoke(mCamera, new Object[]{angle});
        } catch (Exception e1) {
        }
    }

    /*
     * 设置相机属性
     */
    private void setCameraParameters(int degrees) {
        Parameters parameters = camera.getParameters();
        List<Camera.Size> previewSizes = parameters.getSupportedPreviewSizes();
        List<int[]> rates = parameters.getSupportedPreviewFpsRange();
        cameraWidth = 0;
        cameraHeight = 0;

        // 取比设定值小的像素中最大的
        if (!isFlow){
            for (Camera.Size size : previewSizes) {
                if (size.width * size.height <= videoCommon.getWidth()
                        * videoCommon.getHeight()
                        && size.height >= 0) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width * size.height >= cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
            // 如果设定值实在太小，取所支持的最小像素
            if (cameraWidth == 0) {
                for (Camera.Size size : previewSizes) {
                    if (size.height >= 0) {
                        if (cameraWidth == 0) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                        if (size.width * size.height <= cameraWidth * cameraHeight) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                    }
                }
            }
        }else {
            //保存的值
            int width = SharedPreferencesUrls.getInstance().getInt("width1",1920);
            int height = SharedPreferencesUrls.getInstance().getInt("height1",1080);
            for (Camera.Size size : previewSizes) {
                Log.e("DDDDDD","支持的分辨率宽::"+size.width);
                Log.e("DDDDDD","支持的分辨率高::"+size.height);
                if (size.width * size.height <= width *height
                        && size.height >= 0) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width * size.height >= cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
            // 如果设定值实在太小，取所支持的最小像素
            if (cameraWidth == 0) {
                for (Camera.Size size : previewSizes) {
                    if (size.height >= 0) {
                        if (cameraWidth == 0) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                        if (size.width * size.height <= cameraWidth * cameraHeight) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                    }
                }
            }
        }

        int minimum = 0;
        int maximum = 0;
        if (rates.size() > 0) {
            minimum = rates.get(0)[0];
            maximum = rates.get(0)[1];
        }

        if (initVideo) {
            videoCommon
                    .initializeMyVideo(cameraWidth, cameraHeight, 15);
            initVideo = false;
        }

        //Log.e("DDDDDD","相机宽度:"+cameraWidth);
        //Log.e("DDDDDD","相机高度:"+cameraHeight);
        setCameraOrientation(degrees, parameters);
        holder.setFixedSize(cameraWidth,cameraHeight);
        parameters.setPreviewSize(cameraWidth, cameraHeight);// 设置预览的高度和宽度,单位为像素
        parameters.setPreviewFpsRange(minimum, maximum);// 设置图片预览的帧速。
        camera.setParameters(parameters);
    }

    public void changeCameraFacing() {
        destroyCamera();
        currentCamera = CameraInfo.CAMERA_FACING_BACK;
//        currentCamera = (currentCamera + 1) % numOfCamera;
        startCamera();
    }

    public void flushEncoder() {
        //if (isHardCodec && h264HwEncoderImpl.GetMediaEncoder() != null) {
        //    h264HwEncoderImpl.flushEncoder();
       // }
    }

    public void reStartLocalView() {
        Log.d("InfowareLab.Debug", "VideoEncoderView1.reStartLocalView");
        if (camera == null) {
            changeStatus(true);
        } else {
            if (null != videoCommon){
                if ((this.cameraWidth != 1920) || (this.cameraHeight != 1080)) {
                    destroyCamera();
                    startCamera();
                    videoCommon.exChange(cameraHeight, cameraWidth);
                }
                else {
                    Log.d("InfowareLab.Debug", "No need to restart the second camera, ignored!");
                }
            }
        }
    }

    public void changeStatus(boolean isOpenCamera) {
        Log.d("InfowareLab.Debug", "VideoEncoderView1.changeStatus: Open=" + isOpenCamera);
        if (isOpenCamera) {
            if (camera == null) {
                invalidate();
                init();
                startCamera();
            }
        } else {
            if (camera != null) {
                destroyCamera();
            }
        }
    }

    public void destroyCamera() {
        if (camera != null) {
            camera.setPreviewCallback(null);
//            changePreview(false);
            camera.stopPreview();
            mPreviewing = false;
            camera.release();
            h264HwEncoderImpl.releaseEncoder();
            camera = null;
            //mPreviewHolder = null;
        }
    }

    private void changePreview(boolean state) {

        Log.d("InfowareLab.Debug", "VideoEncodeView1.changePreview=" + state);

        if (null == camera)
            return;

        try {
            if (state) {
                camera.startPreview();
                mPreviewing = true;
            } else {
                camera.stopPreview();
                mPreviewing = false;
            }
        } catch (Exception e) {
            Log.d("InfowareLab.Debug", "VideoEncodeView1: Start preview error:" + e.getMessage());
        }
    }

    public void changeOrietation(Configuration newConfig) {
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // 横向
            setPortrait(false);
            setCameraLandscape();
        } else {
            // 竖向
            setPortrait(true);
            setCameraPortrait();
        }
    }

    public void setPortrait(boolean isPortrait) {
        if (isPortrait) {
            videoCommon.exChange(cameraHeight, cameraWidth);
        } else {
            videoCommon.exChange(cameraWidth, cameraHeight);
        }
    }

    public void setCameraLandscape() {
        degrees = 0;
        isPortrait = false;
        if (camera != null) {
            destroyCamera();
            openCamera();
            if (camera == null) {
                return;
            }

            setCameraParameters(0);
            camera.setPreviewCallback(this);

            if (mPreviewHolder != null) {
                try {
                    camera.setPreviewDisplay(mPreviewHolder);
                } catch (IOException e) {
                    e.printStackTrace();
                }

                changePreview(true);
            }
            else
            {
                Log.d("InfowareLab.Debug", "VideoEncoderView1: Surface is NOT ready.");
            }

        }
    }

    public void setCameraPortrait() {
        degrees = 90;
        isPortrait = true;
        if (camera != null) {
            destroyCamera();
            openCamera();
            if (camera == null) {
                return;
            }

            setCameraParameters(90);
            camera.setPreviewCallback(this);
            if (null !=  mPreviewHolder) {
                try {
                    camera.setPreviewDisplay(mPreviewHolder);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                changePreview(true);
            }
        }
    }

    public void setInitVideo(boolean initVideo) {
        VideoEncodeView1.initVideo = initVideo;
    }

    public boolean isSharing() {
        return isSharing;
    }

    public void setSharing(boolean isSharing) {
        VideoEncodeView1.isSharing = isSharing;
    }

    public int getDegrees() {
        return degrees;
    }

    public void setDegrees(int degrees) {
        this.degrees = degrees;
    }

    public boolean isCameraNUll() {
        return camera != null;
    }

    private byte[] rotateYUV420SPFrontfacing(byte[] src, int width, int height) {
        byte[] des = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;
        // 旋转Y
        int k = 0;
        for (int i = width - 1; i >= 0; i--) {
            for (int j = 0; j < height; j++) {
                des[k++] = src[width * j + i];
            }
        }
        for (int i = width - 2; i >= 0; i -= 2) {
            for (int j = 0; j < height / 2; j++) {
                des[k] = src[wh + width * j + i + 1];
                des[k + uv] = src[wh + width * j + i];
                k++;
            }
        }
        return des;
    }

    public static byte[] rotateYUV420SPBackfacing(byte[] src, int width,
                                                  int height) {
        byte[] des = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;
        // 旋转Y
        int k = 0;
        for (int i = 0; i < width; i++) {
            for (int j = height - 1; j >= 0; j--) {
                des[k] = src[width * j + i];
                k++;
            }
        }

        for (int i = 0; i < width; i += 2) {
            for (int j = height / 2 - 1; j >= 0; j--) {
                des[k] = src[wh + width * j + i + 1];
                des[k + uv] = src[wh + width * j + i];
                k++;
            }
        }

        return des;

    }

    private byte[] changeYUV420SP2P(byte[] src, int width, int height) {
        System.gc();
        byte[] yv12buf = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;

        System.arraycopy(src, 0, yv12buf, 0, wh);

        int k = 0;
        for (int i = 0; i < wh / 2; i += 2) {
            yv12buf[wh + k] = src[wh + i + 1];
            yv12buf[wh + uv + k] = src[wh + i];
            k++;
        }
        return yv12buf;
    }

    private byte[] Rotate180YUV420SP2P(byte[] src, int width, int height) {
        byte[] yv12buf = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;
        int k = 0;

        for (int i = 0; i < wh; i++) {
            yv12buf[k++] = src[wh - i];
        }

        for (int i = wh * 3 / 2 - 1; i >= wh; i -= 2) {
            yv12buf[k] = src[i];
            yv12buf[uv + k] = src[i - 1];
            k++;
        }
        return yv12buf;
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {

        Log.d("InfowareLab.Debug", "VideoEncoderView1.surfaceCreated");

        mPreviewHolder = holder;

        if (!mPreviewing && camera != null){
            try {
                camera.setPreviewDisplay(holder);
            } catch (IOException e) {
                e.printStackTrace();
            }
            changePreview(true);
        }
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width,
                               int height) {
        Log.d("InfowareLab.Debug", "VideoEncoderView1.surfaceChanged");
        mPreviewHolder = holder;
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        Log.d("InfowareLab.Debug", "VideoEncoderView1.surfaceDestroyed");
        mPreviewHolder = null;
        destroyCamera();
    }


    public void setParams(int width, int height) {
        if (width > 1 && camera == null) {
            reStartLocalView();
        }

        if (width <= 1) {
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = 1;
            params.height = 1;
            setLayoutParams(params);
        } else {
            int h = height;
            int w = width;
            if (degrees % 180 == 0) {
                if ((1.0f * cameraWidth / cameraHeight) > (1.0f * width / height)) {
                    h = (int) ((1.0f * cameraHeight / cameraWidth) * width);
                } else {
                    w = (int) ((1.0f * cameraWidth / cameraHeight) * height);
                }
            } else {
                if ((1.0f * cameraHeight / cameraWidth) > (1.0f * width / height)) {
                    h = (int) ((1.0f * cameraWidth / cameraHeight) * width);
                } else {
                    w = (int) ((1.0f * cameraHeight / cameraWidth) * height);
                }
            }
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = width;
            params.height = height;
//            params.setMargins((width - w) / 2, (height - h) / 2, 0, 0);
            params.setMargins(0, 0, DensityUtil.dip2px(getContext(),1.0f), 0);
            setLayoutParams(params);
        }
    }

    //修改分辨率
    public void setStreamVideoSize(int width, int height ){
        if (camera == null) {
            invalidate();
            init();
            startCamera();
        }
        this.getHolder().setFixedSize(width,height);
    }
}