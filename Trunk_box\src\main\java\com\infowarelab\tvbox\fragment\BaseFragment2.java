package com.infowarelab.tvbox.fragment;


import android.annotation.SuppressLint;
import android.app.Presentation;
import android.content.Context;
import android.content.SharedPreferences;
import android.view.Display;
import android.view.KeyboardShortcutGroup;
import android.view.Menu;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;

import java.util.List;

public class BaseFragment2 extends Presentation {
	public static final String ACTION_SHOWLOADING = "action_showloading";
	public static final String ACTION_HIDELOADING = "action_hideloading";
	public static final String ACTION_LOGOUT = "action_logout";
	public static final String ACTION_UPDATELIST = "action_updatelist";
	public static final String ACTION_SETFINISH = "action_setfinish";
	public static final String ACTION_DSSHARED = "action_dsshared";
	public static final String ACTION_ASSHARED = "action_asshared";
	public static final String ACTION_VSSHARED = "action_vsshared";
	private final Context context;

	public BaseFragment2(Context outerContext, Display display) {
		super(outerContext, display);
		context = outerContext;
	}

	public BaseFragment2(BaseFragment.ICallParentView iCallParentView, Context outerContext, Display display) {
		super(outerContext, display);
		context = outerContext;
		this.iCallParentView = iCallParentView;
	}
	
	protected void showLoading(){
		callParentView(ACTION_SHOWLOADING,"");
	}

	protected void callParentView(String msg, String obj) {
		if(iCallParentView!=null){
			iCallParentView.onCallParentView(msg,obj);
		}
	}

	protected void hideLoading(){
		callParentView(ACTION_HIDELOADING,"");
	}
	protected void logout(){
		callParentView(ACTION_LOGOUT,"");
	}

	private BaseFragment.ICallParentView iCallParentView = null;

	public void setiCallParentView(BaseFragment.ICallParentView iCallParentView){
		this.iCallParentView = iCallParentView;
	}

}
