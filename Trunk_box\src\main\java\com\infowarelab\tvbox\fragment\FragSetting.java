package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActHome;
import com.infowarelab.tvbox.activity.ActMain;
import com.infowarelab.tvbox.activity.ActMain1;
import com.infowarelab.tvbox.activity.ActSound;
import com.infowarelab.tvbox.activity.MicSetActivity;
import com.infowarelab.tvbox.okhttp.abc.bean.VersionInfo;
import com.infowarelab.tvbox.okhttp.abc.constant.HttpConstant;
import com.infowarelab.tvbox.okhttp.abc.download.DownloadListener;
import com.infowarelab.tvbox.okhttp.abc.request.RequestCenter;
import com.infowarelab.tvbox.okhttp.listener.DisposeDataListener;
import com.infowarelab.tvbox.utils.FileUtils;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.ToastUtil;
import com.infowarelabsdk.conference.util.Utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;

/**
 * Created by sdvye on 2019/6/10.
 */

@SuppressLint("ValidFragment")
public class FragSetting extends BaseFragment {
    private static final String TAG = "InfowareLab.update";
    private View setView;
    private Button btnUpgrade;
    private ActHome home;
    private  LinearLayout btnMain;
    private Button btnFu;
    private LinearLayout btnMic;
    private  LinearLayout btnSound;
    private  Button btnReturn;

    //是否是最新版本
    private boolean isNew = true;

    public FragSetting(ICallParentView iCallParentView) {
        super(iCallParentView);
    }
    //无参构造器
    public FragSetting(){
        super();
    }
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        setView = inflater.inflate(R.layout.frag_setting, container, false);

        initView();

        getVersionInfo(true);
        return setView;
    }

    private void initView() {

        btnMain = setView.findViewById(R.id.btn_main);

        setFouse();

        //站点名称
        Config.SiteName = FileUtil.readSharedPreferences(getActivity(),
                Constants.SHARED_PREFERENCES, Constants.SITE_NAME);
        btnMain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;
                //btnMain.setEnabled(false);
                home = (ActHome) getActivity();
                Intent intentMain = null;
                if (Build.MODEL.indexOf("MV200") == -1
                        && Build.MODEL.indexOf("MRUT33") == -1){
                    intentMain = new Intent(home, ActMain.class);
                }else {
                    intentMain = new Intent(home, ActMain1.class);
                }
                intentMain.putExtra("cameraPosition",0);
                home.startActivity(intentMain);
            }
        });
        btnFu = (Button) setView.findViewById(R.id.btn_fu);
        btnFu.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;

                if (Build.MODEL.contains("HDX2")){
                    ToastUtil.showMessage(getContext(), "对不起，你的设备暂不支持该功能", 5 * 1000);
                    return;
                }
                else
                {
                    //btnFu.setEnabled(false);
                    home = (ActHome) getActivity();
                    Intent intentFu = null;
                    if (Build.MODEL.indexOf("MV200") == -1
                            && Build.MODEL.indexOf("MRUT33") == -1) {
                        intentFu = new Intent(home, ActMain.class);
                    } else {
                        intentFu = new Intent(home, ActMain1.class);
                    }
                    intentFu.putExtra("cameraPosition", 1);
                    home.startActivity(intentFu);
                }
            }
        });
        btnMic = setView.findViewById(R.id.btn_mic);
        btnMic.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;
                //btnMic.setEnabled(false);
//                home = (ActHome) getActivity();
                Intent intentMic = new Intent(getActivity(), MicSetActivity.class);
                getActivity().startActivity(intentMic);
            }
        });
        btnSound = (LinearLayout) setView.findViewById(R.id.btn_sound);
        btnSound.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;
                //btnSound.setEnabled(false);
                home = (ActHome) getActivity();
                Intent intentSound = new Intent(home, ActSound.class);
                home.startActivity(intentSound);
            }
        });
        LinearLayout btnUser = (LinearLayout) setView.findViewById(R.id.btn_user);
        btnUser.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;
                home = (ActHome) getActivity();
                home.setLastPage(1);
                home.switchFrag(3);
            }
        });
        LinearLayout btnSet = (LinearLayout) setView.findViewById(R.id.btn_set);
        btnSet.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;
                startActivity(new Intent(Settings.ACTION_SETTINGS));
            }
        });
        LinearLayout btnVersion = (LinearLayout) setView.findViewById(R.id.btn_version);
        btnVersion.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;
                home = (ActHome) getActivity();
                home.switchFrag(4);
            }
        });
        btnUpgrade = (Button) setView.findViewById(R.id.btn_upgrade);
        btnUpgrade.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //if (!isNew){
                home = (ActHome) getActivity();
                Toast.makeText(home, "升级检测中，请稍候...", Toast.LENGTH_LONG).show();

                getVersionInfo(false);
            }
        });

        btnReturn = (Button) setView.findViewById(R.id.btn_return);
        btnReturn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;
                callParentView(ACTION_SHOWHOME, null);
            }
        });
    }


    @Override
    public void onResume() {
        super.onResume();
        btnMain.setEnabled(true);
        btnFu.setEnabled(true);
        btnMic.setEnabled(true);
        btnSound.setEnabled(true);
        setFouse();
    }

    //默认选中第一个
    public void  setFouse(){
        if (null != btnMain){
            btnMain.setFocusable(true);
            btnMain.requestFocus();
        }else if (ActHome.mActivity != null){
            ActHome.mActivity.applyFouse(1);
        }
    }
//    @Override
//    public void onHiddenChanged(boolean hidden) {
//        super.onHiddenChanged(hidden);
//        if (!hidden) {
//            getVersionInfo();
//        }
//    }

    private void getVersionInfo(boolean onStart) {
        String downUrl =  Config.Site_URL = FileUtil.readSharedPreferences(getContext(),
                Constants.SHARED_PREFERENCES, Constants.SITE);
        if (TextUtils.isEmpty(downUrl)){
            downUrl = HttpConstant.BASEURL;
        }else {
            if (!downUrl.startsWith("http://")){
                downUrl = "http://"+ downUrl;
            }
        }
        String medurl = "";
        if (!"box".equals(Config.SiteName)){
            medurl = HttpConstant.MEDURL+Config.SiteName+"/";
        }else {
            medurl = HttpConstant.MEDURL;
        }
        Log.d("InfowareLab.update","upgrade version file：" + downUrl + medurl + HttpConstant.VERSION);

        RequestCenter.getVersionInfo(downUrl + medurl + HttpConstant.VERSION, new DisposeDataListener() {
            @Override
            public void onSuccess(Object responseObj) {
                if (responseObj instanceof VersionInfo) {
                    VersionInfo versionInfo = (VersionInfo) responseObj;

                    Log.d("InfowareLab.update",">>>>>>Server Version Code：" + versionInfo.getVersionCode());
                    Log.d("InfowareLab.update",">>>>>>Server version Name：" + versionInfo.getVersionName());

                    try {
                        PackageInfo packageInfo = getActivity()
                                .getApplicationContext()
                                .getPackageManager()
                                .getPackageInfo(getActivity().getPackageName(), 0);
                        //String v = packageInfo.versionName;

                        Log.d("InfowareLab.update",">>>>>>Local Version Code：" + packageInfo.versionCode);
                        Log.d("InfowareLab.update",">>>>>>Local version Name：" + packageInfo.versionName);

                        isNew = versionInfo.getVersionCode() > packageInfo.versionCode;

                        if (!isNew && !onStart) {
                            Toast.makeText(getActivity(), "目前已是最新版本", Toast.LENGTH_LONG).show();
                            return;
                        }

                        if (!onStart && isNew)
                        {
                            Toast.makeText(home, "正在下载更新，请稍候...", Toast.LENGTH_LONG).show();

                            FileUtils.deleteFile((String) versionInfo.getFileName());
                            downApk((String) versionInfo.getFileName());
                        }

                    } catch (PackageManager.NameNotFoundException e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onFailure(Object responseObj) {

                Log.d("InfowareLab.Debug","Failed to download upgrading version information.");
            }
        });
    }
    private void downApk(String fileName) {
        String downUrl =  Config.Site_URL = FileUtil.readSharedPreferences(getContext(),
                Constants.SHARED_PREFERENCES, Constants.SITE);
        if (TextUtils.isEmpty(downUrl)){
            downUrl = HttpConstant.BASEURL;
        }else {
            if (!downUrl.startsWith("http://")){
                downUrl = "http://"+ downUrl;
            }
        }
        String medurl = "";
        if (!"box".equals(Config.SiteName)){
            medurl = HttpConstant.MEDURL+Config.SiteName+"/";
        }else {
            medurl = HttpConstant.MEDURL;
        }
        RequestCenter.downloadFile(downUrl + medurl, fileName, new DownloadListener() {
            @Override
            public void start(long max) {
                Message m = new Message();
                m.what = 0;
                m.arg1 = 0;
                m.arg2 = (int) max;
                setParaHandler.sendMessage(m);
            }

            @Override
            public void loading(int cur, int total) {
                Message m = new Message();
                m.what = 0;
                m.arg1 = cur;
                m.arg2 = total;
                setParaHandler.sendMessage(m);
            }
            @Override
            public void complete(String path) {
                Log.d(TAG, ">>>>>>downloadFile path = " + path);

                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        installApk(getActivity(), path);
                    }
                });

            }

            @Override
            public void fail(int code, String message) {
            }
            @Override
            public void loadfail(String message) {
            }
        });
    }

    public static void ExecuteSUCmd(Context context, String currenttempfilepath) {

        Process process = null;
        OutputStream out = null;
        InputStream in = null;
        try {
            // 请求root
            process = Runtime.getRuntime().exec("su");
            out = process.getOutputStream();

            // 调用安装
            out.write(("pm install -r " + currenttempfilepath + "\n").getBytes());
            in = process.getInputStream();
            int len = 0;
            byte[] bs = new byte[256];
            while (-1 != (len = in.read(bs))) {
                String state = new String(bs, 0, len);
                if (state.equals("success\n")) {

                    //安装成功后的操作

                    //静态注册自启动广播
                    Intent intent=new Intent();
                    //与清单文件的receiver的anction对应
                    intent.setAction("android.intent.action.PACKAGE_REPLACED");

                    //发送广播
                    context.sendBroadcast(intent);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.flush();
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void installApkEx(Context context, String downloadApk) {
        String cmd = "";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {
            cmd = "pm install -r -d " + downloadApk;
        } else {
            cmd = "pm install -r -d -i packageName --user 0 " + downloadApk;
        }
        Runtime runtime = Runtime.getRuntime();
        try {
            Process process = runtime.exec(cmd);
            InputStream errorInput = process.getErrorStream();
            InputStream inputStream = process.getInputStream();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String error = "";
            String result = "";
            String line = "";
            while ((line = bufferedReader.readLine()) != null) {
                result += line;
            }
            bufferedReader = new BufferedReader(new InputStreamReader(errorInput));
            while ((line = bufferedReader.readLine()) != null) {
                error += line;
            }
            if (result.equals("Success")) {
                Log.d("installs", "install: Success");
            } else {
                Log.d("installs", "install: error" + error);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void installApk(Context context, String downloadApk) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        File file = new File(downloadApk);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Uri apkUri = FileProvider.getUriForFile(context, context.getPackageName() + ".fileProvider", file);
//            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        } else {
//            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            Uri uri = Uri.fromFile(file);
            intent.setDataAndType(uri, "application/vnd.android.package-archive");
        }
        context.startActivity(intent);

//        AutoInstaller installer = AutoInstaller.getDefault(context);
//
//        installer.setOnStateChangedListener(new AutoInstaller.OnStateChangedListener() {
//            @Override
//            public void onStart() {
//                // 当后台安装线程开始时回调
//                //mProgressDialog.show();
//                Toast.makeText(context, "后台安装线程开始!", Toast.LENGTH_SHORT).show();
//            }
//            @Override
//            public void onComplete() {
//                // 当请求安装完成时回调
//                Toast.makeText(context, "安装完成!", Toast.LENGTH_SHORT).show();
//                //mProgressDialog.dismiss();
//            }
//            @Override
//            public void onNeed2OpenService() {
//                // 当需要用户手动打开 `辅助功能服务` 时回调
//                // 可以在这里提示用户打开辅助功能
//                Toast.makeText(context, "请打开辅助功能服务", Toast.LENGTH_SHORT).show();
//            }
//        });
//
//        Log.d(TAG, "installApk = " + downloadApk);
//        installer.install(downloadApk);

//        String packageName = context.getApplicationContext().getPackageName();
//        try {
//            new ProcessBuilder()
//                    .command("pm", "install", "-i", packageName, downloadApk)
//                    .start();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

//        ExecuteSUCmd(context, downloadApk);
    }

    Handler setParaHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == 0) {

                Log.d(TAG, "downloading....(" + msg.arg1 + "/" + msg.arg2 + ")");
                //home = (ActHome) getActivity();
                //Toast.makeText(home, "正在下载...(" + msg.arg1 + "/" + msg.arg2 + ")", Toast.LENGTH_LONG).show();
            }
        }
    };
}
