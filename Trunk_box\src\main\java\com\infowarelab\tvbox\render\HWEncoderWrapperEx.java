package com.infowarelab.tvbox.render;


import android.content.Context;
import android.graphics.SurfaceTexture;
import android.media.MediaCodecInfo;
import android.opengl.EGLContext;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.infowarelab.tvbox.gles.EglCore;
import com.infowarelab.tvbox.gles.WindowSurface;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;

import java.lang.ref.WeakReference;

public class HWEncoderWrapperEx implements Runnable {

    private static final String TAG = "HWEncoderWrapperEx";

    private Thread mVExecutor;
    private HWVideoEncoder mEncoder = new HWVideoEncoder();
    private WindowSurface mInputWindowSurface;
    private EglCore mEglCore;
    private BaseFilter mFullScreen;
    private Context c;
    private FilterFactory.FilterType type = FilterFactory.FilterType.Original;
    private int mImageFormat = MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface;

    private Handler mVideoHandler;
    private boolean mAsyncCodec = false;

    //private boolean isReady = false;
    //private boolean isStarted = false;

    private static final boolean VERBOSE = false;

    private static final int MSG_START_RECORDING = 0;
    private static final int MSG_STOP_RECORDING = 1;
    private static final int MSG_FRAME_AVAILABLE = 2;
    private static final int MSG_SET_TEXTURE_ID = 3;

    private static final int MSG_UPDATE_SHARED_CONTEXT = 4;
    private static final int MSG_QUIT = 5;

    private static final int MSG_SET_TEXTURE_ID_2 = 6;
    private static final int MSG_SET_TEXTURE_ID_3 = 7;

    // ----- accessed exclusively by encoder thread -----

    private int mTextureId1 = -1;
    private int mTextureId2 = -1;
    private int mTextureId3 = -1;

    private float[] mMatrix1 = null;
    private float[] mMatrix2 = null;
    private float[] mMatrix3 = null;

    // ----- accessed by multiple threads -----
    private volatile EncoderHandler mHandler;

    private Object mReadyFence = new Object();      // guards ready/running
    private boolean mReady;
    private boolean mRunning;
    private boolean mRotated = false;
    private boolean mMirror = false;

    public HWEncoderWrapperEx(Context c) {
        this.c = c;
    }

    public void setRotated(boolean rotated) {
        if (mRotated == rotated) return;

        mRotated = rotated;
    }

    public void setMirror(boolean mirror) {

        mMirror = mirror;
    }

    /**
     * Encoder configuration.
     * <p>
     * Object is immutable, which means we can safely pass it between threads without
     * explicit synchronization (and don't need to worry about it getting tweaked out from
     * under us).
     * <p>
     * TODO: make frame rate and iframe interval configurable?  Maybe use builder pattern
     *       with reasonable defaults for those and bit rate.
     */
    public static class EncoderConfig {
        final int mWidth;
        final int mHeight;
        final int mBitRate;
        final int mFrameRate;
        VideoCommonImpl mVideoCommon;
        ShareDtCommonImpl mShareDtCommon;

        final EGLContext mEglContext;

        public EncoderConfig(VideoCommonImpl videoCommon, ShareDtCommonImpl shareDtCommon, int width, int height, int bitRate, int frameRate,
                             EGLContext sharedEglContext) {

            mVideoCommon = videoCommon;
            mShareDtCommon = shareDtCommon;
            mWidth = width;
            mHeight = height;
            mBitRate = bitRate;
            mEglContext = sharedEglContext;
            mFrameRate = frameRate;
        }

        @Override
        public String toString() {
            return "EncoderConfig: " + mWidth + "x" + mHeight + " @" + mBitRate +
                    " to '" + "' ctxt=" + mEglContext;
        }
    }

    /**
     * Tells the video recorder to start recording.  (Call from non-encoder thread.)
     * <p>
     * Creates a new thread, which will create an encoder using the provided configuration.
     * <p>
     * Returns after the recorder thread has started and is ready to accept Messages.  The
     * encoder may not yet be fully configured.
     */
    public void startRecording(EncoderConfig config) {
        Log.d(TAG, "Encoder: startRecording()");
        synchronized (mReadyFence) {
            if (mRunning) {
                Log.w(TAG, "Encoder thread already running");
                return;
            }
            mRunning = true;
            new Thread(this, "HWEncoderWrapperEx").start();
            while (!mReady) {
                try {
                    mReadyFence.wait();
                } catch (InterruptedException ie) {
                    // ignore
                }
            }
        }

        mHandler.sendMessage(mHandler.obtainMessage(MSG_START_RECORDING, config));
    }

    /**
     * Tells the video recorder to stop recording.  (Call from non-encoder thread.)
     * <p>
     * Returns immediately; the encoder/muxer may not yet be finished creating the movie.
     * <p>
     * TODO: have the encoder thread invoke a callback on the UI thread just before it shuts down
     * so we can provide reasonable status UI (and let the caller know that movie encoding
     * has completed).
     */
    public void stopRecording() {

        if (mHandler != null) {
            mHandler.sendMessage(mHandler.obtainMessage(MSG_STOP_RECORDING));
            mHandler.sendMessage(mHandler.obtainMessage(MSG_QUIT));
        }

        // We don't know when these will actually finish (or even start).  We don't want to
        // delay the UI thread though, so we return immediately.
    }

    /**
     * Returns true if recording has been started.
     */
    public boolean isRecording() {
        synchronized (mReadyFence) {
            return mRunning;
        }
    }

    /**
     * Tells the video recorder to refresh its EGL surface.  (Call from non-encoder thread.)
     */
    public void updateSharedContext(EGLContext sharedContext) {
        mHandler.sendMessage(mHandler.obtainMessage(MSG_UPDATE_SHARED_CONTEXT, sharedContext));
    }

    /**
     * Tells the video recorder that a new frame is available.  (Call from non-encoder thread.)
     * <p>
     * This function sends a message and returns immediately.  This isn't sufficient -- we
     * don't want the caller to latch a new frame until we're done with this one -- but we
     * can get away with it so long as the input frame rate is reasonable and the encoder
     * thread doesn't stall.
     * <p>
     * TODO: either block here until the texture has been rendered onto the encoder surface,
     * or have a separate "block if still busy" method that the caller can execute immediately
     * before it calls updateTexImage().  The latter is preferred because we don't want to
     * stall the caller while this thread does work.
     */
    public void frameAvailable(SurfaceTexture st) {
        synchronized (mReadyFence) {
            if (!mReady) {
                return;
            }
        }

        float[] transform = new float[16];      // TODO - avoid alloc every frame
        st.getTransformMatrix(transform);
        long timestamp = st.getTimestamp();
        if (timestamp == 0) {
            // Seeing this after device is toggled off/on with power button.  The
            // first frame back has a zero timestamp.
            //
            // MPEG4Writer thinks this is cause to abort() in native code, so it's very
            // important that we just ignore the frame.
            Log.w(TAG, "HEY: got SurfaceTexture with timestamp of zero");
            return;
        }

        mHandler.sendMessage(mHandler.obtainMessage(MSG_FRAME_AVAILABLE,
                (int) (timestamp >> 32), (int) timestamp, transform));
    }

    /**
     * Tells the video recorder what texture name to use.  This is the external texture that
     * we're receiving camera previews in.  (Call from non-encoder thread.)
     * <p>
     * TODO: do something less clumsy
     */
    public void setTextureId(int id) {
        synchronized (mReadyFence) {
            if (!mReady) {
                return;
            }
        }
        if (mTextureId1 == id) return;

        //Log.d("HWEncoderWrapperEx", ">>>>>> setTextureId : mTextureId1=" + mTextureId1);

        mTextureId1 = id;
        //mHandler.sendMessage(mHandler.obtainMessage(MSG_SET_TEXTURE_ID, id, 0, null));
    }

    public void setTextureId2(int id, float[] matrix) {
        synchronized (mReadyFence) {
            if (!mReady) {
                return;
            }
        }

        if (mTextureId2 == id) return;

        mTextureId2 = id;
        mMatrix2 = matrix;

        //mHandler.sendMessage(mHandler.obtainMessage(MSG_SET_TEXTURE_ID_2, id, 0, matrix));
    }

    public void setTextureId3(int id, float[] matrix) {
        synchronized (mReadyFence) {
            if (!mReady) {
                return;
            }
        }

        if (mTextureId3 == id) return;

        mTextureId3 = id;
        mMatrix3 = matrix;

        //mHandler.sendMessage(mHandler.obtainMessage(MSG_SET_TEXTURE_ID_3, id, 0, matrix));
    }

    /**
     * Encoder thread entry point.  Establishes Looper/Handler and waits for messages.
     * <p>
     * @see Thread#run()
     */
    @Override
    public void run() {
        // Establish a Looper for this thread, and define a Handler for it.
        Looper.prepare();
        synchronized (mReadyFence) {
            mHandler = new EncoderHandler(this);
            mReady = true;
            mReadyFence.notify();
        }
        Looper.loop();

        Log.d(TAG, "Encoder thread exiting");
        synchronized (mReadyFence) {
            mReady = mRunning = false;
            mHandler = null;
        }
    }

    /**
     * Handles encoder state change requests.  The handler is created on the encoder thread.
     */
    private static class EncoderHandler extends Handler {
        private WeakReference<HWEncoderWrapperEx> mWeakEncoder;

        public EncoderHandler(HWEncoderWrapperEx encoder) {
            mWeakEncoder = new WeakReference<HWEncoderWrapperEx>(encoder);
        }

        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        @Override  // runs on encoder thread
        public void handleMessage(Message inputMessage) {
            int what = inputMessage.what;
            Object obj = inputMessage.obj;

            HWEncoderWrapperEx encoder = mWeakEncoder.get();
            if (encoder == null) {
                Log.w(TAG, "EncoderHandler.handleMessage: encoder is null");
                return;
            }

            switch (what) {
                case MSG_START_RECORDING:
                    encoder.handleStartRecording((EncoderConfig) obj);
                    break;
                case MSG_STOP_RECORDING:
                    encoder.handleStopRecording();
                    break;
                case MSG_FRAME_AVAILABLE:
                    long timestamp = (((long) inputMessage.arg1) << 32) |
                            (((long) inputMessage.arg2) & 0xffffffffL);
                    encoder.handleFrameAvailable((float[]) obj, timestamp);
                    break;
                case MSG_SET_TEXTURE_ID:
                    encoder.handleSetTexture(inputMessage.arg1);
                    break;
                case MSG_SET_TEXTURE_ID_2:
                    encoder.handleSetTexture2(inputMessage.arg1, (float[])obj);
                    break;
                case MSG_SET_TEXTURE_ID_3:
                    encoder.handleSetTexture3(inputMessage.arg1, (float[])obj);
                    break;
                case MSG_UPDATE_SHARED_CONTEXT:
                    encoder.handleUpdateSharedContext((EGLContext) inputMessage.obj);
                    break;
                case MSG_QUIT:
                    Looper.myLooper().quit();
                    break;
                default:
                    throw new RuntimeException("Unhandled msg what=" + what);
            }
        }
    }

    /**
     * Starts recording.
     */
    private void handleStartRecording(EncoderConfig config) {
        Log.d(TAG, "handleStartRecording " + config);
        prepareEncoder(config.mVideoCommon, config.mShareDtCommon, config.mEglContext, config.mWidth, config.mHeight, config.mBitRate, config.mFrameRate);
    }

    /**
     * Handles notification of an available frame.
     * <p>
     * The texture is rendered onto the encoder's input surface, along with a moving
     * box (just because we can).
     * <p>
     * @param transform The texture transform, from SurfaceTexture.
     * @param timestampNanos The frame's timestamp, from SurfaceTexture.
     */
    private void handleFrameAvailable(float[] transform, long timestampNanos) {

        if (mEncoder == null) return;

        mEncoder.sendFrameEx(false);
        if (mTextureId1 > 0 && mTextureId2 < 0 && mTextureId3 < 0)
        {
            //if (VERBOSE) Log.d(TAG, "handleFrameAvailable timestampNanos=" + timestampNanos);
            //Log.d(TAG, "handleFrameAvailable: mTextureId1=" + mTextureId1);

            mFullScreen.setMirror(mMirror);

            mFullScreen.draw(mTextureId1, transform);
        }
        else if (mTextureId1 > 0 && mTextureId2 > 0 && mTextureId3 < 0)
        {
            //if (VERBOSE) Log.d(TAG, "handleFrameAvailable timestampNanos=" + timestampNanos);
            //Log.d(TAG, "handleFrameAvailable（2）: mTextureId1/mTextureId2=" + mTextureId1 + "/" + mTextureId2);
            mFullScreen.setMirror(true);

            if (!mRotated)
                mFullScreen.drawEx_record(mTextureId1, mTextureId2, transform, mMatrix2);
            else
                mFullScreen.drawEx_rotated_record(mTextureId1, mTextureId2, transform, mMatrix2);
        }
        else if (mTextureId1 > 0 && mTextureId2 > 0 && mTextureId3 > 0)
        {
            //Log.d(TAG, "handleFrameAvailable（3）: mTextureId1/mTextureId2/mTextureId3=" + mTextureId1 + "/" + mTextureId2+ "/" + mTextureId3);
            mFullScreen.setMirror(true);

            if (!mRotated)
                mFullScreen.drawEx3_record(mTextureId1, mTextureId2, mTextureId3, transform, mMatrix2, mMatrix3);
            else
                mFullScreen.drawEx3_rotated_record(mTextureId1, mTextureId2, mTextureId3, transform, mMatrix2, mMatrix3);
        }

        mInputWindowSurface.setPresentationTime(timestampNanos);
        mInputWindowSurface.swapBuffers();
    }

    /**
     * Handles a request to stop encoding.
     */
    private void handleStopRecording() {
        Log.d(TAG, "handleStopRecording");
        //mEncoder.sendFrameEx(true);
        releaseEncoder();

        mTextureId1 = -1;
        mTextureId2 = -1;
        mTextureId3 = -1;

        mMatrix1 = null;
        mMatrix2 = null;
        mMatrix3 = null;
    }

    /**
     * Sets the texture name that SurfaceTexture will use when frames are received.
     */
    private void handleSetTexture(int id) {
        //Log.d(TAG, "handleSetTexture " + id);
        mTextureId1 = id;
    }

    /**
     * Sets the texture name that SurfaceTexture will use when frames are received.
     */
    private void handleSetTexture2(int id, float[] matrix) {
        //Log.d(TAG, "handleSetTexture " + id);
        mTextureId2 = id;
        mMatrix2 = matrix;
    }


    /**
     * Sets the texture name that SurfaceTexture will use when frames are received.
     */
    private void handleSetTexture3(int id, float[] matrix) {
        //Log.d(TAG, "handleSetTexture " + id);
        mTextureId3 = id;
        mMatrix3 = matrix;
    }


    /**
     * Tears down the EGL surface and context we've been using to feed the MediaCodec input
     * surface, and replaces it with a new one that shares with the new context.
     * <p>
     * This is useful if the old context we were sharing with went away (maybe a GLSurfaceView
     * that got torn down) and we need to hook up with the new one.
     */
    private void handleUpdateSharedContext(EGLContext newSharedContext) {
        Log.d(TAG, "handleUpdatedSharedContext " + newSharedContext);

        // Release the EGLSurface and EGLContext.
        mInputWindowSurface.releaseEglSurface();
        mFullScreen.releaseProgram();
        mEglCore.release();

        // Create a new EGLContext and recreate the window surface.
        mEglCore = new EglCore(newSharedContext, EglCore.FLAG_RECORDABLE);
        mInputWindowSurface.recreate(mEglCore);
        mInputWindowSurface.makeCurrent();

        // Create new programs and such for the new context.
        mFullScreen = FilterFactory.createFilter(c, type);
        mFullScreen.createProgram();
        //mFullScreen.onInputSizeChanged(width, height);
    }

    private void prepareEncoder(VideoCommonImpl videoCommon, ShareDtCommonImpl shareDtCommon, EGLContext sharedContext, int width, int height, int bitRate, int frameRate) {

        if (videoCommon != null){
            try {
                if (mEncoder == null) mEncoder = new HWVideoEncoder();
                mEncoder.init(videoCommon, width, height, mImageFormat, bitRate, frameRate, mAsyncCodec);
            } catch (Exception e) {
                Log.d(TAG, "HWEncoderWrapper.start: " + e.getMessage() + "and" + e.getLocalizedMessage());
                return ;
            }
        }
        else if (shareDtCommon != null) {
            try {
                if (mEncoder == null) mEncoder = new HWVideoEncoder();
                mEncoder.init(shareDtCommon, width, height, mImageFormat, bitRate, frameRate, mAsyncCodec);
            } catch (Exception e) {
                Log.d(TAG, "HWEncoderWrapper.start: " + e.getMessage() + "and" + e.getLocalizedMessage());
                return;
            }
        }
        else
            return;

        mVideoWidth = width;
        mVideoHeight = height;
        mEglCore = new EglCore(sharedContext, EglCore.FLAG_RECORDABLE);
        mInputWindowSurface = new WindowSurface(mEglCore, mEncoder.getInputSurface(), true);
        mInputWindowSurface.makeCurrent();

        mFullScreen = FilterFactory.createFilter(c, type);
        mFullScreen.createProgram();
        //mFullScreen.onInputSizeChanged(width, height);

    }

    private void releaseEncoder() {

        if (mEncoder != null) {
            try {
                mEncoder.stop();
                mEncoder.release();
            } catch (Exception e) {
                e.printStackTrace();
            }
            mEncoder = null;
        }
        if (mInputWindowSurface != null) {
            mInputWindowSurface.release();
            mInputWindowSurface = null;
        }
        if (mFullScreen != null) {
            mFullScreen.releaseProgram();
            mFullScreen = null;
        }
        if (mEglCore != null) {
            mEglCore.release();
            mEglCore = null;
        }
    }

    private int mPreviewWidth = -1;
    private int mPreviewHeight = -1;
    private int mVideoWidth = -1;
    private int mVideoHeight = -1;

    public void setPreviewSize(int width, int height){
        mPreviewWidth = width;
        mPreviewHeight = height;
    }

}
