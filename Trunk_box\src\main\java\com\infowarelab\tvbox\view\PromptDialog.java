package com.infowarelab.tvbox.view;

import static android.content.Context.INPUT_METHOD_SERVICE;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.infowarelab.tvbox.R;

/**
 * Created by xiaor on 2019/12/24.
 */

public class PromptDialog extends AlertDialog implements View.OnClickListener, View.OnFocusChangeListener {

    private final Context mContext;
    private OnResultListener onResultListener;
    private int width;
    private TextView titleText;
    private TextView contentText;
    private Button cancelBtn,confirmBtn;

    private String title = null;
    private String content = null;
    private String password;

    public void setOnResultListener(OnResultListener onResultListener) {
        this.onResultListener = onResultListener;
    }

    public void setTitle(String title) {
        this.title = title;
        if (titleText != null){
            titleText.setText(title);
        }
    }

    public void setPassword(String pwd) {
        this.password = pwd;
    }

    public void setContent(String content) {
        this.content = content;
        if (contentText != null){
            contentText.setText(content);
        }
    }

    public PromptDialog(Context context) {
        super(context, R.style.style_dialog_normal);
        mContext = context;
    }
    public PromptDialog(Context context, int width) {
        super(context, R.style.style_dialog_normal);
        this.width = width;
        mContext = context;
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.dialog_prompt);

//        getWindow().clearFlags(//不去掉焦点不能正常弹出软件键盘
//                WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
//
//        getWindow().setSoftInputMode(
//                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);

        setCanceledOnTouchOutside(false);

        //setCanceledOnTouchOutside(true);

        //requestWindowFeature(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

        titleText = (TextView)findViewById(R.id.dialog_custom_titleText);
        contentText = findViewById(R.id.dialog_custom_contentText);
        cancelBtn = (Button)findViewById(R.id.dialog_custom_cancelBtn);
        confirmBtn = (Button)findViewById(R.id.dialog_custom_confirmBtn);

        if (title != null)
            titleText.setText(title);

        if (content != null)
            contentText.setText(content);

        cancelBtn.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);

//        contentText.setOnFocusChangeListener(this);
//
//        contentText.setFocusable(true);
//        contentText.setFocusableInTouchMode(true);
//        contentText.requestFocus();

    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.dialog_custom_cancelBtn) {
            doNo();
            cancel();
        } else if (id == R.id.dialog_custom_confirmBtn) {

//            if (!contentText.getText().toString().equals(password)){
//                //Toast.makeText(getContext(), getContext().getResources().getString(R.string.conf_pwd_error), Toast.LENGTH_LONG).show();
//                return;
//            }

            doYes();
            cancel();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
//        confirmBtn.setPressed(true);
//        confirmBtn.setFocusable(true);
//        confirmBtn.requestFocus();

    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if(hasFocus){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                ((InputMethodManager) mContext.getSystemService(INPUT_METHOD_SERVICE)).showSoftInput(v, 0);
            }

            ((EditText) v).setCursorVisible(true);
        }
    }

    public interface OnResultListener {
        public void doYes();

        public void doNo();
    }

    private void doYes() {
        if (onResultListener != null) {
            onResultListener.doYes();
        }
    }

    private void doNo() {
        if (onResultListener != null) {
            onResultListener.doNo();
        }
    }
}
