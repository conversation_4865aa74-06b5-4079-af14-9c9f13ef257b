package com.infowarelab.tvbox.fragment;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.adapter.DocListAdapter;
import com.infowarelab.tvbox.view.GridViewTV;
import com.infowarelab.tvbox.view.MyEffectNoDrawBridge;
import com.infowarelab.tvbox.view.MyMainUpView;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.domain.DocBean;

/**
 * Created by xia<PERSON> on 2019/12/30.
 * <AUTHOR>
 */

public class DocListFragment extends BaseFragment implements View.OnClickListener,AdapterView.OnItemClickListener,
        AdapterView.OnItemSelectedListener{

    private View rootView;
    private LinearLayout backLayout;
    private RelativeLayout mainLayout;
    private LinearLayout llGv;
    private GridViewTV gridView;
    private MyMainUpView mainUpView;
    private LinearLayout llNone;

    private DocListAdapter myAdapter;
    private DocCommonImpl docCommon;

    private View mOldView;
    private MyEffectNoDrawBridge bridget;
    private int mCurrentRow = 0;
    private int offsetheight = 0;
    //与Activity交互
    private FragmentInteraction listterner;
//    private TextView noDataText;

    public DocListFragment(ICallParentView iCallParentView) {
        super(iCallParentView);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.frag_home_meetings, container, false);
        return rootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initView();
    }
    private void initView(){
        backLayout = (LinearLayout)rootView.findViewById(R.id.frag_meeting_backLayout);
        backLayout.setVisibility(View.VISIBLE);
        backLayout.setOnClickListener(this);
        mainLayout = (RelativeLayout)rootView.findViewById(R.id.frag_meeting_mainLayout);
        mainLayout.setBackgroundResource(R.drawable.bg_home);
        llGv = (LinearLayout) rootView.findViewById(R.id.llgv);
        gridView = (GridViewTV)rootView.findViewById(R.id.gv_frag_meetings);
        mainUpView = (MyMainUpView)rootView.findViewById(R.id.fl_frag_meetings_mainUpView);
        llNone = (LinearLayout)rootView.findViewById(R.id.llNone);
//        noDataText = (TextView)rootView.findViewById(R.id.fl_frag_meetings_noDataText);
//        noDataText.setText("暂无任何文档共享");

        docCommon = (DocCommonImpl) CommonFactory.getInstance().getDocCommon();
        myAdapter = new DocListAdapter(getContext());
        if (docCommon.getDocMapList() != null && docCommon.getDocMapList().size() != 0){
            myAdapter.setDatas(docCommon.getDocMapList());
        }
        gridView.setAdapter(myAdapter);
        // 建议使用 NoDraw.
        mainUpView.setEffectBridge(new MyEffectNoDrawBridge());
        bridget = (MyEffectNoDrawBridge) mainUpView.getEffectBridge();
        bridget.setTranDurAnimTime(200);
        // 设置移动边框的图片.
        mainUpView.setUpRectResource(R.drawable.white_light_10);
        // 移动方框缩小的距离.
        mainUpView.setDrawUpRectPadding(new Rect(-65, 10, -65, -65));
        gridView.setSelector(new ColorDrawable(Color.TRANSPARENT));
        gridView.post(new Runnable() {
            @Override
            public void run() {
                if (myAdapter.getDatas() != null){
                    if (myAdapter.getDatas().size() >1) {
                        for (int i = 0; i < myAdapter.getDatas().size(); i++){
                            DocBean bean = myAdapter.getDatas().get(i);
                            if (bean.getDocID() == docCommon.getCurrentDoc().getDocID()){
                                gridView.setDefaultSelect(i);
                            }
                        }
                    }else if (myAdapter.getDatas().size() ==1){
                        gridView.setDefaultSelect(0);
                    }
                }
            }
        });
        gridView.setOnItemClickListener(this);
        gridView.setOnItemSelectedListener(this);
        myAdapter.setOnSelectListener(new DocListAdapter.OnSelectListener() {
            @Override
            public void doSelect(final int position) {
                DocBean docBean = myAdapter.getDatas().get(position);
                if (myAdapter.getDatas().size() == 1){
                    listterner.onDocItemListener(docBean.getDocID());
                    docCommon.setCurrentDoc(docBean);
                }else {
                    if (docBean.getDocID() != docCommon.getCurrentDoc().getDocID()){
                        gridView.post(new Runnable() {
                            @Override
                            public void run() {
                                gridView.setDefaultSelect(position);
                            }
                        });
                        listterner.onDocItemListener(docBean.getDocID());
                        docCommon.setCurrentDoc(docBean);
                    }else {
                        listterner.onCloseListener();
                    }
                }
            }
        });
    }
    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.frag_meeting_backLayout:
                if (ActConf.mActivity != null){
                    ActConf.mActivity.onBackPressed();
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, final int position, long id) {
        myAdapter.doSelect(position);
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        int offset = 0;
        if (mCurrentRow == 0) {
            offsetheight = (gridView.getHeight() - view.getHeight()) / 2;
            mCurrentRow = getItemIndex(position);
        } else if (getItemIndex(position) != mCurrentRow) {
            offsetheight = (gridView.getHeight() - view.getHeight()) / 2;
            gridView.smoothScrollToPositionFromTop(position, offsetheight, 200);
            mCurrentRow = getItemIndex(position);
            if (getItemIndex(position) != 1 && getItemIndex(position) != myAdapter.getNumRows()) {
                int h = view.getHeight();
                offset = offsetheight - (view.getTop() - gridView.getTop()) - (llGv.getHeight() - gridView.getHeight()) / 2;
            }
        } else if (getItemIndex(position) < mCurrentRow) {
            offsetheight = (gridView.getHeight() - view.getHeight()) / 2;
            gridView.smoothScrollToPositionFromTop(position, offsetheight, 200);
            mCurrentRow = getItemIndex(position);
            if (getItemIndex(position) != 1 && getItemIndex(position) != myAdapter.getNumRows()) {
                int h = view.getHeight();
                offset = offsetheight + (gridView.getTop() - llGv.getTop());
            }
        }
        if (mOldView == null) {
            bridget.setVisibleWidget(false);
        }
        /**
         * 这里注意要加判断是否为NULL.keneng
         * 因为在重新加载数据以后会出问题.
         */
        if (view != null) {
            mainUpView.setFocusView(view, mOldView, 1.1f, offset);
        }
        mOldView = view;
    }
    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    private int getItemIndex(int position) {
        int r = position % myAdapter.getNumColumns();
        if (r == 0) {
            if (position > 0) {
                return (position / myAdapter.getNumColumns()) + 1;
            } else {
                return 1;
            }
        } else {
            return (position / myAdapter.getNumColumns()) + 1;
        }
    }

    public void onLosefocus() {
        if (bridget != null)
            bridget.setVisibleWidget(true);
        if (mOldView != null) {
            mainUpView.setUnFocusView(mOldView);
            mOldView = null;
        }
    }
    //刷新数据
    public void update(){
        myAdapter.update(docCommon.getDocMapList());
        if (myAdapter.getDatas().size() > 1) {
            for (int i = 0; i < myAdapter.getDatas().size(); i++){
                DocBean bean = myAdapter.getDatas().get(i);
                if (bean.getDocID() == docCommon.getCurrentDoc().getDocID()){
                    gridView.setDefaultSelect(i);
                }
            }
        }else {
            if (0 == myAdapter.getDatas().size()){
                gridView.setVisibility(View.GONE);
                mainUpView.setVisibility(View.GONE);
                llNone.setVisibility(View.VISIBLE);
            }else {
                gridView.setVisibility(View.VISIBLE);
                mainUpView.setVisibility(View.VISIBLE);
                llNone.setVisibility(View.GONE);
            }
        }
    }
    /**
     * 定义了所有activity必须实现的接口
     */
    public interface FragmentInteraction {
        /**
         * Fragment 向Activity传递指令，这个方法可以根据需求来定义
         *
         * @param str
         */
        void onDocItemListener(int docId);
        void onCloseListener();
    }
    /**
     * 当FRagmen被加载到activity的时候会被回调
     *
     * @param activity
     */
    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity instanceof FragMenu.FragmentInteraction) {
            listterner = (FragmentInteraction)activity;
        } else {
            throw new IllegalArgumentException("activity must implements FragmentInteraction");
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        listterner = null;
    }
}
