package com.infowarelab.tvbox.render;

import android.content.Context;
import android.util.Log;

import com.infowarelab.tvbox.R;

public class OriginalFilter extends BaseFilter {

    public OriginalFilter(Context c) {
        super(c);

        Log.d("InfowareLab.Debug", "OriginalFilter.textureUnit = " + mTextureUnit);
    }

    @Override
    public void setPath() {

        path1 = R.raw.base_vertex_shader;
        path2 = R.raw.base_fragment_shader;

    }

    @Override
    public void onDrawArraysPre() {

    }

    @Override
    public void onDrawArraysAfter() {

    }


}
