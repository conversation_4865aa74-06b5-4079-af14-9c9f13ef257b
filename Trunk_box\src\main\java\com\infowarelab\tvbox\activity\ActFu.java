package com.infowarelab.tvbox.activity;

import android.app.Activity;
import android.graphics.SurfaceTexture;
import android.hardware.Camera;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.TextureView;

import com.infowarelab.tvbox.R;

import java.io.IOException;

/**
 * Created by sdvye on 2019/6/12.
 */

public class ActFu extends Activity {
    public static ActFu mActivity = null;
    private TextureView tvFu;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.frag_fu);
        final Camera camera = Camera.open(Camera.CameraInfo.CAMERA_FACING_BACK);
        tvFu = (TextureView) findViewById(R.id.tv_fu);
        tvFu.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
            @Override
            public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
                try {
                    camera.setPreviewTexture(surface);
                    camera.startPreview();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {

            }

            @Override
            public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
                camera.stopPreview();
                camera.release();
                return true;
            }

            @Override
            public void onSurfaceTextureUpdated(SurfaceTexture surface) {

            }
        });
    }
    @Override
    protected void onResume() {
        mActivity = ActFu.this;
        super.onResume();
    }
    @Override
    protected void onDestroy() {
        mActivity = null;
        super.onDestroy();
    }
}
