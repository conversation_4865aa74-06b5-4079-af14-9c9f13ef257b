package com.infowarelab.tvbox.base;

import java.io.Serializable;

/**
 * Created by Always on 2017/10/24.
 */

public class Terminal implements Serializable {
    private String name;
    private String deviceId;
    private String isOnline;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(String isOnline) {
        this.isOnline = isOnline;
    }

    @Override
    public String toString() {
        return "Terminal{" +
                "name='" + name + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", isOnline='" + isOnline + '\'' +
                '}';
    }
}
