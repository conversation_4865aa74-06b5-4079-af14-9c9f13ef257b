package com.infowarelab.tvbox.utils;

import android.util.Log;

/**
 * Created by Always on 2017/10/23.
 */

public interface IUDP {
    public void onReceive(int code, Object object);

    public void onSend(int code);
}
//
//interface IUdp {
//    public void onReceive(int code, Object object);
//
//    public void onSend(int code);
//}
//
//class a implements IUdp {
//
//    @Override
//    public void onReceive(int code, Object object) {
//        Log.i("receive", "a");
//
//    }
//
//    @Override
//    public void onSend(int code) {
//        Log.d("send", "b");
//
//    }
//}

