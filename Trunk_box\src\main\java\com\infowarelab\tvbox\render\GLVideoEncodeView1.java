package com.infowarelab.tvbox.render;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.SurfaceTexture;
import android.hardware.Camera;
import android.opengl.EGL14;
import android.opengl.GLES20;
import android.opengl.GLSurfaceView;
import android.os.Build;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.widget.RelativeLayout;

import androidx.annotation.RequiresApi;

import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.grafika.TextureMovieEncoder;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelabsdk.conference.callback.CallbackManager;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.video.AvcHardEncoder;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.List;

import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;


public class GLVideoEncodeView1 extends GLSurfaceView implements Camera.PreviewCallback {

    private final String TAG = "InfowareLab.GLVideoEncodeView1";
    private boolean inited = false;
    public GLVideoEncodeView1.GLRenderer renderer = null;
    public BaseFilter mCurrentFilter = null;

    //private HWEncoderWrapper hwEncoderWrapper = null;

    // this is static so it survives activity restarts
    private TextureMovieEncoder sVideoEncoder = new TextureMovieEncoder();

    private int mTextureId;
    private SurfaceTexture mSurfaceTexture = null;
    private float[] mSTMatrix = new float[16];
    private boolean mRenderEnabled = true;
    private  FilterFactory.FilterType type;

    public static int cameraFPS = 25;
    public static int cameraWidth = 640;
    public static int cameraHeight = 480;

    private Camera camera;
    private int currentCamera = Camera.CameraInfo.CAMERA_FACING_FRONT;// 代表摄像头的方位，目前有定义值两个分别为CAMERA_FACING_FRONT前置和CAMERA_FACING_BACK后置
    private int numOfCamera = 1;
    File file = new File("/sdcard/testView.yuv");
    private FileOutputStream fos;
    // private Handler handler;
    private static boolean initVideo = true;
    private static boolean isSharing = false;
    private boolean isPortrait = false;
    private int frameSize;
    private int[] rgba;

    private VideoCommonImpl videoCommon = (VideoCommonImpl) CommonFactory
            .getInstance().getVideoCommon();

    private ShareDtCommonImpl shareDtCommon = (ShareDtCommonImpl)CommonFactory.getInstance().getSdCommon();

    private boolean isPreview = false;
    private boolean isOpen = false;

    public static boolean isDestroyed = false;
    public static boolean isInited = false;

    // Video Hard Encoder
    private AvcHardEncoder h264HwEncoderImpl = null;
    private static final int FRAME_RATE = 25;
    private boolean isHardCodec = true; // true
    private boolean isWriteFile = false;
    private boolean isEnabled = true;

    byte[] yv12buf;

    private int degrees = 90;
    public boolean isSmall = false;

    private File _fr = null;
    private FileOutputStream _out = null;
    private Context activity;
    private int mFrameLength = 0;
    private static int STATE_ON = 1;
    private static int STATE_OFF = 2;
    private int mEncoderState = 2;
    private int mBitRate = 2000000;
    private boolean isFlow = false;
    private int mViewWidth = 0;
    private int mViewHeight = 0;
    private int mSurfaceWidth = 0;
    private int mSurfaceHeight = 0;
    private boolean isCreated = false;
    private boolean mMirror = false;
    private File mOutputFile = null;

    public GLVideoEncodeView1(Context context) {
        super(context);
        activity = context;
        init();

    }

    public GLVideoEncodeView1(Context context, AttributeSet attrs) {
        super(context, attrs);
        activity = context;
        init();
    }

    public void init() {

        if (inited) return;

        setEGLContextClientVersion(2);

        type = FilterFactory.FilterType.Original;
        renderer = new GLVideoEncodeView1.GLRenderer(this, type);

        setRenderer(renderer);
        setRenderMode(RENDERMODE_WHEN_DIRTY);

        inited = true;
    }

    public boolean rendererCreated(){return renderer != null;}
    public boolean rendererEnabled(){return mRenderEnabled;}
    public void setRenderEnabled(boolean renderEnabled) {
        Log.d(TAG, "GLVideoEncodeView1.setRenderEnabled: "+ renderEnabled);
        mRenderEnabled = renderEnabled;
    }

    public void setFlow(boolean flow) {
        isFlow = flow;
    }

    public class GLRenderer implements Renderer, SurfaceTexture.OnFrameAvailableListener {

        GLSurfaceView surfaceView;

        public GLRenderer(GLSurfaceView surfaceView, FilterFactory.FilterType type) {

            this.surfaceView = surfaceView;

//            if (null == hwEncoderWrapper) {
//                hwEncoderWrapper = new HWEncoderWrapper(surfaceView.getContext());
//            }

            mCurrentFilter = FilterFactory.createFilter(activity,type);
        }

        @Override
        public void onSurfaceCreated(GL10 gl, EGLConfig config) {

            Log.d(TAG,"GLVideoEncodeView1.onSurfaceCreated");

        }

        @Override
        public void onSurfaceChanged(GL10 gl, int width, int height) {

            Log.d(TAG,"GLVideoEncodeView1.onSurfaceChanged: " + width + "x" + height);

            if (width <= 1 || height <= 1) {
                return;
            }

            mSurfaceWidth = width;
            mSurfaceHeight = height;

            if (!isCreated){

                if (mCurrentFilter == null) mCurrentFilter = FilterFactory.createFilter(activity,type);

                mCurrentFilter.createProgram();
                mTextureId = BaseFilter.bindTexture();
                mSurfaceTexture = new SurfaceTexture(mTextureId);
                mSurfaceTexture.setOnFrameAvailableListener(this);

                if (camera != null && mSurfaceTexture != null && isEnabled) {
                    try {
                        Log.d(TAG,"GLVideoEncodeView1.camera.setPreviewTexture");
                        camera.setPreviewTexture(mSurfaceTexture);

                    } catch (IOException e) {
                        Log.e(TAG, e.getMessage());
                        e.printStackTrace();
                    }

                    if (!isPreviewStarted() && isEnabled()){
                        Log.d(TAG, "GLVideoEncodeView1.startPreview in onSurfaceChanged");
                        changePreview(true);
                    }
                }

                if (mEncoderState == STATE_OFF) {

                    Log.d(TAG,">>>>> Start HW encoder wrapper...");

                    mEncoderState = STATE_ON;

//                    hwEncoderWrapper.start(
//                            shareDtCommon,
//                            cameraWidth,
//                            cameraHeight,
//                            mBitRate,
//                            cameraFPS,
//                            EGL14.eglGetCurrentContext());

                    if (!sVideoEncoder.isRecording()) {
                        Log.d(TAG,"onSurfaceChanged: START Recording: " + cameraWidth + "x" + cameraHeight);

                        // start recording
                        sVideoEncoder.startRecording(new TextureMovieEncoder.EncoderConfig(
                                null, shareDtCommon, mOutputFile, cameraWidth, cameraHeight, mBitRate, EGL14.eglGetCurrentContext()));
                    }
                }

                isCreated = true;
            }

            if (isCreated)
                GLES20.glViewport(0, 0, width, height);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                mCurrentFilter.onInputSizeChanged(width,height);
            }

        }

        /**
         * 关于预览出现镜像，旋转等问题，有两种方案:
         * 1.在相机预览的地方进行调整
         * 2.通过opengl的矩阵变换在绘制的时候进行调整
         * 这里我采用了前者
         */

        @Override
        public void onDrawFrame(GL10 gl) {

            //if (mSurfaceWidth <= 1 || mSurfaceHeight <= 1) return;

            if (mSurfaceTexture == null) return;

            if (!isPreviewStarted()) return;

            mSurfaceTexture.updateTexImage();

            if (isSharing && mEncoderState == STATE_ON){

                sVideoEncoder.setShare(isSharing());

                //mirror horizontally
                sVideoEncoder.setMirror(mMirror);

                // Set the video encoder's texture name.  We only need to do this once, but in the
                // current implementation it has to happen after the video encoder is started, so
                // we just do it here.
                //
                // TODO: be less lame.
                sVideoEncoder.setTextureId(mTextureId);

                // Tell the video encoder thread that a new frame is available.
                // This will be ignored if we're not actually recording.
                sVideoEncoder.frameAvailable(mSurfaceTexture);

            }

            mSurfaceTexture.getTransformMatrix(mSTMatrix);

            mCurrentFilter.draw(mTextureId,mSTMatrix);

        }

        @Override
        public void onFrameAvailable(SurfaceTexture surfaceTexture) {
            //Log.d(TAG,"GLVideoEncodeView1.onFrameAvailable");
            surfaceView.requestRender();

        }

    }


    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void updateFilter(final FilterFactory.FilterType type){

        this.type = type;

        //renderer.runOnDraw(() -> {

            mCurrentFilter.releaseProgram();
            mCurrentFilter = FilterFactory.createFilter(activity,type);

            //调整预览画面
            mCurrentFilter.createProgram();
            mCurrentFilter.onInputSizeChanged(getWidth(),getHeight());
            //调整录像画面
            //hwRecorderWrapper.updateFilter(type);

            //Log.v("aaaaa","updateFilter:"+ Thread.currentThread());

        //});

    }

    public void enableBeauty(boolean enableBeauty){

        if (enableBeauty){

            type = FilterFactory.FilterType.Beauty;

        }else{

            type = FilterFactory.FilterType.Original;
        }

        updateFilter(type);
    }

    public void enableCamera(boolean enabled){isEnabled=enabled;}
    public boolean isEnabled(){return isEnabled;}

    /**
     * 设置相机显示方向的详细解读
     **/
    public void setCameraDisplayOrientation(Activity activity,
                                            int cameraId, Camera camera) {
        // 1.获取屏幕切换角度值。
        int rotation = activity.getWindowManager().getDefaultDisplay()
                .getRotation();

        int degree = 0;
        switch (rotation) {
            case Surface.ROTATION_0: degree = 0; break;
            case Surface.ROTATION_90: degree = 90; break;
            case Surface.ROTATION_180: degree = 180; break;
            case Surface.ROTATION_270: degree = 270; break;
        }
        // 2.获取摄像头方向。
        Camera.CameraInfo info =
                new Camera.CameraInfo();
        Camera.getCameraInfo(cameraId, info);
        // 3.设置相机显示方向。
        int result;
        if (info.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {

            Log.d(TAG, "Camera.CameraInfo.CAMERA_FACING_FRONT: info.orientation=" + info.orientation);
            result = (info.orientation + degree) % 360;
            result = (360 - result) % 360;  // compensate the mirror
        } else {  // back-facing
            Log.d(TAG, "Camera.CameraInfo.CAMERA_FACING_BACK: info.orientation=" + info.orientation);
            result = (info.orientation - degree + 360) % 360;
        }

        degrees = result;

        Log.d(TAG, "setCameraDisplayOrientation: degrees=" + result);

        camera.setDisplayOrientation(result);
    }

    @SuppressLint("NewApi")
    public void startCamera() {

        openCamera();

        if (camera == null) {
            return;
        }

        setCameraParameters(degrees);

        if (mCurrentFilter != null) {
            if (isPortrait)
                mCurrentFilter.setTextureSize(new Size(cameraHeight, cameraWidth));
            else
                mCurrentFilter.setTextureSize(new Size(cameraWidth, cameraHeight));
        }

		if (mSurfaceTexture != null && !isPreviewStarted()) {
            try {
                Log.d(TAG,"camera.setPreviewTexture in startCamera()");
                camera.setPreviewTexture(mSurfaceTexture);

            } catch (IOException e) {
                Log.e(TAG, e.getMessage());
                e.printStackTrace();
            }

            Log.d(TAG,"camera.startPreview in startCamera()");
            changePreview(true);
        }

        setBackgroundColor(0);
    }

    public void openCamera(){

        if (camera != null) return;

        Log.d(TAG,"GLVideoEncodeView1.openCamera");

        if (Integer.parseInt(Build.VERSION.SDK) > 8) {
            numOfCamera = Camera.getNumberOfCameras();
            if (numOfCamera == 1) {
                currentCamera = Camera.CameraInfo.CAMERA_FACING_BACK;

                if (Build.MODEL.indexOf("HDX2") != -1){
                    if (mCurrentFilter != null) mCurrentFilter.setMirror(true);
                    mMirror = true;
                }

                try {
                    camera = Camera.open(currentCamera);
                }catch (Exception e){
                    Log.d(TAG,"GLVideoEncodeView1.openCamera Error: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                if (isFlow){
                    currentCamera = Camera.CameraInfo.CAMERA_FACING_BACK;
                }else {
                    currentCamera = Camera.CameraInfo.CAMERA_FACING_FRONT;
                    if (mCurrentFilter != null) mCurrentFilter.setMirror(true);
                    mMirror = true;
                }
                try {
                    camera = Camera.open(currentCamera);
                } catch (Exception e) {
                    Log.d(TAG,"GLVideoEncodeView1.openCamera Error(2): " + e.getMessage());
                    e.printStackTrace();
                }
            }
        } else {
            try {
                camera = Camera.open(currentCamera);
            }catch (Exception e){
                e.printStackTrace();
                Log.d(TAG,"GLVideoEncodeView1.openCamera Error(3): " + e.getMessage());
            }
        }
        if (Build.MODEL.toUpperCase().startsWith("Lenovo".toUpperCase())){
            camera.setDisplayOrientation(0);
        }
    }
    /*
     * 设置相机属性
     */
    private void setCameraParameters(int degrees) {
        Camera.Parameters parameters = camera.getParameters();
        List<Camera.Size> previewSizes = parameters.getSupportedPreviewSizes();
        List<int[]> rates = parameters.getSupportedPreviewFpsRange();
        cameraWidth = 0;
        cameraHeight = 0;

        if (!isFlow) {
            if (videoCommon.getWidth() <= 0 || videoCommon.getHeight() <= 0) {

                int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
                int height = SharedPreferencesUrls.getInstance().getInt("height", 720);

                if (videoCommon != null) {
                    videoCommon.setWidth(width);
                    videoCommon.setHeight(height);
                    //Log.d("InfowareLab.Debug", ">>>>>>FragVs.local resolution: " + width + "x" + height);
                }
            }

            Log.d(TAG, "GLVideoEncodeView: Preferred Size: " + videoCommon.getWidth() + "x" + videoCommon.getHeight());

            // 取比设定值小的像素中最大的
            for (Camera.Size size : previewSizes) {
                if (size.width * size.height <= videoCommon.getWidth()
                        * videoCommon.getHeight()
                        && size.height >= 0) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width * size.height >= cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
            // 如果设定值实在太小，取所支持的最小像素
            if (cameraWidth == 0) {
                for (Camera.Size size : previewSizes) {
                    if (size.height >= 0) {
                        if (cameraWidth == 0) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                        if (size.width * size.height <= cameraWidth * cameraHeight) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                    }
                }
            }
        }
        else {
            //保存的值
            int width = SharedPreferencesUrls.getInstance().getInt("width1",1920);
            int height = SharedPreferencesUrls.getInstance().getInt("height1",1080);
            for (Camera.Size size : previewSizes) {
                Log.e("DDDDDD","支持的分辨率宽::"+size.width);
                Log.e("DDDDDD","支持的分辨率高::"+size.height);
                if (size.width * size.height <= width *height
                        && size.height >= 0) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width * size.height >= cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
            // 如果设定值实在太小，取所支持的最小像素
            if (cameraWidth == 0) {
                for (Camera.Size size : previewSizes) {
                    if (size.height >= 0) {
                        if (cameraWidth == 0) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                        if (size.width * size.height <= cameraWidth * cameraHeight) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                    }
                }
            }
        }

        Log.d(TAG,"Camera.setPreviewSize = " + cameraWidth + "x" + cameraHeight);

        frameSize = cameraWidth * cameraHeight;
        int minimum = 0;
        int maximum = 0;
        int retMininum = 0;
        int retMaximum = 0;
        if (rates.size() > 0) {
            minimum = rates.get(0)[0];
            maximum = rates.get(0)[1];
            retMininum = rates.get(0)[0];
            retMaximum = rates.get(0)[1];
            for (int[] fps : rates){
                if (minimum < fps[0]){
                    minimum = fps[0];
                }
                if (maximum < fps[1]){
                    maximum = fps[1];
                }
            }
        }

        //setCameraOrientation(degrees,parameters);

        setCameraDisplayOrientation((ActConf) activity, currentCamera, camera);

        parameters.setPreviewSize(cameraWidth, cameraHeight);//设置预览的高度和宽度,单位为像素
//	    parameters.setPreviewFrameRate(FRAME_RATE);//设置图片预览的帧速。
//	    parameters.setPreviewFpsRange(minimum, maximum);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){
                if (minimum > 0 && maximum > 0){
                    parameters.setPreviewFpsRange(minimum,maximum);
                }
            }else {
                if (retMininum > 0 && retMaximum > 0){
                    parameters.setPreviewFpsRange(retMininum,retMaximum);
                }
            }
        }catch (RuntimeException e){
            if (retMininum > 0 && retMaximum > 0){
                parameters.setPreviewFpsRange(retMininum,retMaximum);
            }
        }
        List<String> focusModes = parameters.getSupportedFocusModes();
        if (focusModes.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE)){
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);// 1连续对焦
            camera.setParameters(parameters);
            camera.cancelAutoFocus();
        }else{
            try {
                camera.setParameters(parameters);
            }
            catch (RuntimeException e)
            {
                Camera.Parameters appliedParam = camera.getParameters();
                cameraWidth = appliedParam.getPreviewSize().width;
                cameraHeight = appliedParam.getPreviewSize().height;

                Log.d(TAG,"Camera.CameraParameters(FAILED) = " + cameraWidth + ", " + cameraHeight);

            }
        }

//        if(initVideo){
//            Log.d(TAG,"videoCommon.initializeMyVideo: " + cameraWidth + "x" + cameraHeight);
//
//            videoCommon.initializeMyVideo(cameraWidth, cameraHeight, FRAME_RATE);
//            //initVideo = false;
//        }

        int bitrate = 0;
        int picSize = cameraWidth * cameraHeight;
        if (picSize <= 320 * 240) {
            bitrate = 256*1024;
//        } else if (picSize <= 352 * 288) {
//            bitrate = 240000;
        } else if (picSize <= 640 * 480) {
            bitrate = 512*1024;
//        } else if (picSize <= 960 * 720) {
//            bitrate = 700000;
        } else if (picSize <= 1280 * 720) {
            bitrate = 1024*1024;
//            bitrate = 700000;
        } else if (picSize <= 1920 * 1080) {
            bitrate = 2048*1024;
        } else {
            bitrate = 512*1024;
        }

        mBitRate = bitrate;
    }

    private void setCameraOrientation(int degrees, Camera.Parameters p){
        if (Integer.parseInt(Build.VERSION.SDK) >= 8)
            setDisplayOrientation(camera, degrees);
        else
        {
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT)
            {
                p.set("orientation", "portrait");
                p.set("rotation", degrees);
            }
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE)
            {
                p.set("orientation", "landscape");
                p.set("rotation", degrees);
            }
        }
    }
    private void setDisplayOrientation(Camera mCamera, int angle) {
        Method downPolymorphic;
        try
        {
            downPolymorphic = mCamera.getClass().getMethod("setDisplayOrientation", new Class[] { int.class });
            if (downPolymorphic != null)
                downPolymorphic.invoke(mCamera, new Object[] { angle });
        }
        catch (Exception e1)
        {
        }
    }
    /**
     * 相应地，在surfaceDestroyed中也需要释放该Camera对象。 我们将首先调用stopPreview，以确保应该释放的资源都被清理。
     */

    @Override
    public void onPreviewFrame(byte[] data, Camera camera) {

       /* if (data == null) {
            return;
        }

//        if (isWriteFile) {
//            try {
//                _out.write(data);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }

        //防止花屏

        if (data[4] == 0x61){
            data[4] = 0x41;
        }

        if (data.length != mFrameLength)
        {
            Log.d(TAG, "VideoEncodeView.onPreviewFrame: wrong data length: " + data.length + "<" + mFrameLength);
            camera.addCallbackBuffer(data);
            return;
        }

        if (false == isSharing)
        {
            camera.addCallbackBuffer(data);
            return;
        }

        if (isSharing) {

                if (isPortrait) {//竖屏

                    if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16) {
                        if (h264HwEncoderImpl.GetMediaEncoder() == null)
                            h264HwEncoderImpl.initEncoder(cameraHeight, cameraWidth);

                        yv12buf = h264HwEncoderImpl.getAdapterYv12bufPortrait(data, cameraWidth, cameraHeight, currentCamera);
                        h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
                    } else //soft ware encoding
                    {
                        if (currentCamera == Camera.CameraInfo.CAMERA_FACING_BACK) {
                            yv12buf = rotateYUV420SPBackfacing(data, cameraWidth, cameraHeight);
                        } else {
                            yv12buf = rotateYUV420SPFrontfacing(data, cameraWidth, cameraHeight);
                        }

                        videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraHeight, cameraWidth, false);
                    }
                    //end
                } else {//横屏
                    if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16)//hardware encode.....
                    {
                        if (h264HwEncoderImpl.GetMediaEncoder() == null)
                            h264HwEncoderImpl.initEncoder(cameraWidth, cameraHeight);

                        yv12buf = h264HwEncoderImpl.getAdapterYv12bufLandscape(data, cameraWidth, cameraHeight, degrees);
                        h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
                    } else  //software encode
                    {
                        if (degrees == 0) {
                            yv12buf = changeYUV420SP2P(data, cameraWidth, cameraHeight);
                        } else {
                            yv12buf = Rotate180YUV420SP2P(data, cameraWidth, cameraHeight);
                        }
                        videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraWidth, cameraHeight, false);
                    }
                }
        }

        camera.addCallbackBuffer(data);
        */
    }

    public void flushEncoder(){
        //if(isHardCodec&&h264HwEncoderImpl.GetMediaEncoder()!= null){
            //h264HwEncoderImpl.flushEncoder();
        //}
    }


    public void destroyCamera() {

        if (camera != null) {
            //Log.d(TAG,"destroyCamera");
            Log.d(TAG,"stopPreview and releaseCamera");

            camera.setPreviewCallback(null);
            changePreview(false);
            camera.stopPreview();
            // 停止更新预览
            camera.release();// 释放资源

            camera = null;
            isOpen = false;

            if (mEncoderState == STATE_ON) {
                Log.d(TAG,">>>>> GLVideoEncodeView1.Stop HW encoder wrapper...");
                mEncoderState = STATE_OFF;
                if (sVideoEncoder.isRecording())
                    sVideoEncoder.stopRecording();
            }

            Log.d(TAG,">>>>> GLVideoEncodeView1.releaseProgram");

            if (mCurrentFilter != null) {
                mCurrentFilter.releaseProgram();
                mCurrentFilter = null;
            }

            if (mSurfaceTexture != null) {
                mSurfaceTexture.setOnFrameAvailableListener(null);
                mSurfaceTexture = null;
            }

            isCreated = false;
        }
    }

    public void preLeave() {

        if (camera != null) {

            if (sVideoEncoder!=null&&mEncoderState==STATE_ON){
                Log.d(TAG,">>>>> Stop HW encoder wrapper...");
                if (sVideoEncoder.isRecording())
                    sVideoEncoder.stopRecording();
                mEncoderState = STATE_OFF;
            }

            Log.d(TAG,"GLVideoEncodeView1.preLeave: stop and destroy camera");
            //Log.d(TAG,"stopPreview and releaseCamera");

            camera.setPreviewCallback(null);
            changePreview(false);
            camera.stopPreview();
            // 停止更新预览
            camera.release();// 释放资源

            camera = null;
            isOpen = false;


        }
    }

    public void reStartLocalView() {
        // if(camera != null){

        if (CallbackManager.IS_LEAVED) return;

        Log.d(TAG,"GLVideoEncodeView1.reStartLocalView");
        if (camera == null) {
            changeStatus(true);
        } else {
//            if (isDestroyed) {
//                destroyCamera();
//                if (hwEncoderWrapper!=null&&mEncoderState==STATE_ON){
//                    Log.d(TAG,">>>>> Stop HW encoder wrapper...");
//                    hwEncoderWrapper.stop();
//                    mEncoderState = STATE_OFF;
//                }
//                currentCamera = (currentCamera + 2) % numOfCamera;
//                startCamera();
//            }else {
//                destroyCamera();
//                if (hwEncoderWrapper!=null&&mEncoderState==STATE_ON){
//                    Log.d(TAG,">>>>> Stop HW encoder wrapper...");
//                    hwEncoderWrapper.stop();
//                    mEncoderState = STATE_OFF;
//                }
//                startCamera();
////				videoCommon.exChange(cameraHeight, cameraWidth);
//            }
        }
    }

    public void setStatus(boolean isMove) {
        if (isMove) {
            camera.setPreviewCallback(null);
            changePreview(false);
        } else {
            camera.setPreviewCallback(this);
            changePreview(true);
        }
    }

    public void changeStatus(boolean isOpenCamera) {

        Log.d(TAG,"GLVideoEncodeView1.changeStatus = " + isOpenCamera);

        if (isOpenCamera) {
            if (camera == null) {
                //invalidate();
                //init(activity);
                startCamera();
            }
        } else {
            if (camera != null) {
                destroyCamera();
            }
        }
    }

    private void changePreview(boolean state) {
        try {
            if (state) {
                Log.d(TAG,"GLVideoEncodeView1.startPreview:" + state );

                camera.startPreview();
                isPreview = true;
            } else {
                Log.d(TAG,"GLVideoEncodeView1.stopPreview:" + state );

                camera.stopPreview();
                isPreview = false;
            }
        } catch (Exception e) {
            Log.e(TAG,e.getMessage());
        }
    }

    public boolean getCamera() {
        return camera != null;
    }

    public boolean isSharing() {
        return isSharing;
    }

    public void setSharing(boolean sharing) {
        
        if (isSharing == sharing) return;
        
        isSharing = sharing;

        Log.d(TAG, "GLVideoEncodeView1.setSharing: "+ isSharing);

    }

    public void setCameraLandscape() {
        degrees = 0;
        //reStartLocalView();
    }

    public void setParams(int width,int height) {

        if (mViewWidth == width && mViewHeight == height)
            return;

        if (CallbackManager.IS_LEAVED) return;

        Log.d(TAG, "GLVideoEncodeView1.setParams: " + width + "x" + height);

        mViewWidth = width;
        mViewHeight = height;

        if(width>1&&camera==null&&isEnabled()){
            reStartLocalView();
        }
        if(width <= 1 || height <= 1){
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = 1;
            params.height = 1;
            setLayoutParams(params);
            setRenderEnabled(false);
        }else {
            /*if(degrees%180==0){
                if((1.0f*cameraWidth/cameraHeight)>(1.0f*width/height)){
                    height = (int) ((1.0f*cameraHeight/cameraWidth)*width);
                }else{
                    width = (int) ((1.0f*cameraWidth/cameraHeight)*height);
                }
            }else {
                if((1.0f*cameraHeight/cameraWidth)>(1.0f*width/height)){
                    height = (int) ((1.0f*cameraWidth/cameraHeight)*width);
                }else{
                    width = (int) ((1.0f*cameraHeight/cameraWidth)*height);
                }
            }*/
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();

            params.setMargins(0,0,0,0);

            params.width = width;//RelativeLayout.LayoutParams.MATCH_PARENT;
            params.height = height;//RelativeLayout.LayoutParams.MATCH_PARENT;
            setLayoutParams(params);

            setRenderEnabled(true);
        }
    }

    public boolean isPreviewStarted() {
        return isPreview;
    }

    public void switchSoftEncode(boolean isSoft){
        isHardCodec = videoCommon.isHardCodec();
        reStartLocalView();
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        super.surfaceDestroyed(holder);

        Log.d(TAG,"GLVideoEncodeView1.surfaceDestroyed");

        if (sVideoEncoder!=null&&mEncoderState==STATE_ON){
            if (sVideoEncoder.isRecording())
                sVideoEncoder.stopRecording();
            mEncoderState = STATE_OFF;
        }

        if (mCurrentFilter != null) mCurrentFilter.releaseProgram();
    }
}
