package com.infowarelab.tvbox.render;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.hardware.Camera;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CaptureRequest;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.opengl.GLES20;
import android.opengl.GLSurfaceView;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Size;
import android.util.SparseIntArray;
import android.view.Surface;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;

import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;


@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
public class GLVideoEncodeView2 extends GLSurfaceView{

    private static final int MAX_PREVIEW_WIDTH = 1920;
    private static final int MAX_PREVIEW_HEIGHT = 1080;
    public boolean mPreviewStarted = false;
    private boolean inited = false;
    public GLVideoEncodeView2.GLRenderer renderer = null;
    public BaseFilter mCurrentFilter = null;
    private static final String TAG = "InfowareLab.Debug";
    private Context mContext;
    private int mTextureId;

    private SurfaceTexture mPreviewSurfaceTexture = null;
    private Surface mPreviewSurface = null;

    private float[] mSTMatrix = new float[16];

    private  FilterFactory.FilterType type;

    public static int cameraFPS = 30;
    public static int cameraWidth = 640;
    public static int cameraHeight = 480;

    //private Camera camera;
    //private int currentCamera = Camera.CameraInfo.CAMERA_FACING_FRONT;// 代表摄像头的方位，目前有定义值两个分别为CAMERA_FACING_FRONT前置和CAMERA_FACING_BACK后置
    //private int numOfCamera = 1;

    private static boolean initVideo = true;
    private static boolean isSharing = false;
    private boolean isPortrait = false;
    private int frameSize;

    private VideoCommonImpl videoCommon = (VideoCommonImpl) CommonFactory
            .getInstance().getVideoCommon();

    private boolean isOpen = false;

    public static boolean isDestroyed = false;
    public static boolean isInited = false;

    // Video Hard Encoder
    private MediaCodec mVideoEncoder = null;
    private MediaFormat mVideoFormat = null;     //上传编码格

    private SurfaceTexture mEncoderSurfaceTexture = null;
    private Surface mEncoderSurface = null;

    private String mCodecType = MediaFormat.MIMETYPE_VIDEO_AVC; //压缩格式

    //private boolean recordFlag = false; //录制标志
    private MediaCodec.BufferInfo mBufferInfo = null;

    private static final SparseIntArray ORIENTATIONS = new SparseIntArray();

    static {
        ORIENTATIONS.append(Surface.ROTATION_0, 90);
        ORIENTATIONS.append(Surface.ROTATION_90, 0);
        ORIENTATIONS.append(Surface.ROTATION_180, 270);
        ORIENTATIONS.append(Surface.ROTATION_270, 180);
    }

    /**
     * A {@link Semaphore} to prevent the app from exiting before closing the camera.
     */
    private Semaphore mCameraOpenCloseLock = new Semaphore(1);

    //Orientation of the camera sensor
    private int mSensorOrientation;

    //ID of the current {@link CameraDevice
    private String mCameraId = null;

    //CameraCaptureSession for camera preview.
    private CameraCaptureSession mCaptureSession = null;

    //A reference to the opened {@link CameraDevice}.
    private CameraDevice mCameraDevice = null;

    private Size mPreviewSize;

    //An additional thread for running tasks that shouldn't block the UI.
    private HandlerThread mBackgroundThread;
    private Handler mBackgroundHandler;

    //CaptureRequest.Builder for the camera preview
    private CaptureRequest.Builder mPreviewRequestBuilder;

    //CaptureRequest generated by mPreviewRequestBuilder}
    private CaptureRequest mPreviewRequest;

    //private AvcHardEncoder h264HwEncoderImpl = null;
    private static final int FRAME_RATE = 25;
    private boolean isHardCodec = true; // true

    private boolean isEnabled = true;

    byte[] yv12buf;

    private int degrees = 90;
    private byte[] mVideoHead = null;
    private boolean mEncoderEnabled = false;
    private HandlerThread mCameraThread = null;
    private Handler mCameraHandler = null;

    public GLVideoEncodeView2(Context context) {
        super(context);
        init(context);
    }

    public GLVideoEncodeView2(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {

        if (inited) return;
        this.mContext = context;

        setEGLContextClientVersion(2);
        type = FilterFactory.FilterType.Original;
        renderer = new GLVideoEncodeView2.GLRenderer(this, type);

        setRenderer(renderer);
        setRenderMode(RENDERMODE_WHEN_DIRTY);

//		if (null == h264HwEncoderImpl)
//			h264HwEncoderImpl = new AvcHardEncoder();  //release ???? sunny add 2016-5-23
//
//		isHardCodec = h264HwEncoderImpl.IsSupportHardEncode();

        inited = true;
    }

    public class GLRenderer implements Renderer, SurfaceTexture.OnFrameAvailableListener {

        GLSurfaceView surfaceView;
        public GLRenderer(GLSurfaceView surfaceView, FilterFactory.FilterType type) {
            this.surfaceView = surfaceView;
            mCurrentFilter = FilterFactory.createFilter(mContext,type);
        }

        @Override
        public void onSurfaceCreated(GL10 gl, EGLConfig config) {
            Log.d("InfowareLab.Debug","GLVideoEncodeView2.onSurfaceCreated: " + config.toString());
            mCurrentFilter.createProgram();
            mTextureId = BaseFilter.bindTexture();
            mPreviewSurfaceTexture = new SurfaceTexture(mTextureId);
            mPreviewSurfaceTexture.setOnFrameAvailableListener(this);
            mPreviewSurface = new Surface(mPreviewSurfaceTexture);

            if (mCameraDevice == null) {
                Log.d("InfowareLab.Debug","GLVideoEncodeView2.onSurfaceCreated: Camera NOT ready.");
                openCamera();
                return;
            }

            if (mCameraDevice != null && mPreviewSurfaceTexture != null && isEnabled && !isPreview()) {
                createCameraPreviewSession();
            }
        }

        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        @Override
        public void onSurfaceChanged(GL10 gl, int width, int height) {
            if (width <= 1 || height <= 1) return;
            Log.d("InfowareLab.Debug","GLVideoEncodeView2.onSurfaceChanged: " + width + "x" + height);
            GLES20.glViewport(0, 0, width, height);
            mCurrentFilter.onInputSizeChanged(width,height);
        }

        /**
         * 关于预览出现镜像，旋转等问题，有两种方案:
         * 1.在相机预览的地方进行调整
         * 2.通过opengl的矩阵变换在绘制的时候进行调整
         * 这里我采用了前者
         */
        @Override
        public void onDrawFrame(GL10 gl) {

            if (mPreviewSurfaceTexture == null || mPreviewSurface == null) {
                Log.d("InfowareLab.Debug","GLVideoEncodeView2.onDrawFrame: mPreviewSurfaceTexture NOT ready.");
                return;
            }

            Log.d("InfowareLab.Debug","GLVideoEncodeView2.onDrawFrame");

            mPreviewSurfaceTexture.updateTexImage();
            mPreviewSurfaceTexture.getTransformMatrix(mSTMatrix);

            //Matrix.rotateM(mSTMatrix, 0, 180, 0, 0, 1);
            //Matrix.translateM(mSTMatrix, 0, -1, -1, 0);

            mCurrentFilter.draw(mTextureId,mSTMatrix);
        }

        @Override
        public void onFrameAvailable(SurfaceTexture surfaceTexture) {
            Log.d("InfowareLab.Debug","GLVideoEncodeView2.onFrameAvailable");
            surfaceView.requestRender();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void updateFilter(final FilterFactory.FilterType type){

        this.type = type;

        mCurrentFilter.releaseProgram();
        mCurrentFilter = FilterFactory.createFilter(mContext,type);

        //调整预览画面
        mCurrentFilter.createProgram();
        mCurrentFilter.onInputSizeChanged(getWidth(),getHeight());
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void enableBeauty(boolean enableBeauty){

        if (enableBeauty){
            type = FilterFactory.FilterType.Beauty;
        }else{
            type = FilterFactory.FilterType.Original;
        }

        updateFilter(type);
    }

    public void enableCamera(boolean enabled){isEnabled=enabled;}
    public boolean isEnabled(){return isEnabled;}

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void startCamera() {

        openCamera();
        //setCameraParameters(degrees);

        //if (isPortrait)
        //    mCurrentFilter.setTextureSize(new Size(cameraHeight, cameraWidth));
        //else

        mCurrentFilter.setTextureSize(new Size(cameraWidth, cameraHeight));

        // start happy add for hard encode 2016-5-3
		/*if (Integer.parseInt(Build.VERSION.SDK) >= 16 && isHardCodec) {

            if (null == h264HwEncoderImpl)
                h264HwEncoderImpl = new AvcHardEncoder();  //release ???? sunny add 2016-5-23
            isHardCodec = h264HwEncoderImpl.IsSupportHardEncode();

			int ret;
			if (isPortrait)
				ret = h264HwEncoderImpl.initEncoder(cameraHeight, cameraWidth);
			else
				ret = h264HwEncoderImpl.initEncoder(cameraWidth, cameraHeight);
			if (ret == 1)
				isHardCodec = false; // sunny add 2016-5-19
		}
        // end

        camera.setPreviewCallback(this);

		if (mPreviewSurfaceTexture != null && !isPreview()) {
            try {
                //camera.setPreviewDisplay(holder);
                Log.d("InfowareLab.Debug","camera.setPreviewTexture at startCamera()");
                camera.setPreviewTexture(mSurfaceTexture);

            } catch (IOException e) {
                Log.e("InfowareLab.Debug", e.getMessage());
                e.printStackTrace();
            }
            changePreview(true);
        }
        */
        setBackgroundColor(0);
        Log.d("InfowareLab.Debug","GLVideoEncodeView2.startCamera");
    }

    /**
     * {@link CameraDevice.StateCallback} is called when {@link CameraDevice} changes its state.
     */
    private final CameraDevice.StateCallback mStateCallback = new CameraDevice.StateCallback() {

        @Override
        public void onOpened(@NonNull CameraDevice cameraDevice) {

            Log.d("InfowareLab.Debug","CameraDevice.StateCallback.onOpened");
            // This method is called when the camera is opened.  We start camera preview here.
            mCameraOpenCloseLock.release();
            mCameraDevice = cameraDevice;
            videoCommon.onErrMsg(2);
            createCameraPreviewSession();
        }

        @Override
        public void onDisconnected(@NonNull CameraDevice cameraDevice) {
            Log.d("InfowareLab.Debug","CameraDevice.StateCallback.onDisconnected");
            mCameraOpenCloseLock.release();
            cameraDevice.close();
            mCameraDevice = null;
            destroyEncoder();
        }

        @Override
        public void onError(@NonNull CameraDevice cameraDevice, int error) {
            Log.d("InfowareLab.Debug","CameraDevice.StateCallback.onError: " + error);
            mCameraOpenCloseLock.release();
            cameraDevice.close();
            mCameraDevice = null;
            destroyEncoder();
            videoCommon.onErrMsg(1);
        }
    };

    /**
     * Creates a new {@link CameraCaptureSession} for camera preview.
     */
    private void createCameraPreviewSession() {

        if (mPreviewSurfaceTexture == null || mPreviewSurface == null) {
            Log.d("InfowareLab.Debug","GLVideoEncodeView2.createCameraPreviewSession Error: mSurfaceTexture NOT ready!");
            return;
        }

//        if (mEncoderSurface == null) {
//            Log.d("InfowareLab.Debug","GLVideoEncodeView2.createCameraPreviewSession Error: mEncoderSurface NOT ready!");
//            return;
//        }

        try {
            // We configure the size of default buffer to be the size of camera preview we want.
            mPreviewSurfaceTexture.setDefaultBufferSize(mPreviewSize.getWidth(), mPreviewSize.getHeight());

            // We set up a CaptureRequest.Builder with the output Surface.
            mPreviewRequestBuilder
                    = mCameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);

            mPreviewRequestBuilder.addTarget(mPreviewSurface);
            //mPreviewRequestBuilder.addTarget(mEncoderSurface);

            // Here, we create a CameraCaptureSession for camera preview.
            mCameraDevice.createCaptureSession(Arrays.asList(mPreviewSurface),
                    new CameraCaptureSession.StateCallback() {
                        @Override
                        public void onConfigured(@NonNull CameraCaptureSession cameraCaptureSession) {

                            Log.d("InfowareLab.Debug","GLVideoEncodeView2.CameraCaptureSession.onConfigured");

                            // The camera is already closed
                            if (null == mCameraDevice) {
                                Log.d("InfowareLab.Debug","GLVideoEncodeView2.CameraCaptureSession.onConfigured: camera is closed!");
                                return;
                            }

                            // When the session is ready, we start displaying the preview.
                            mCaptureSession = cameraCaptureSession;

                            try {
                                // Auto focus should be continuous for camera preview.
                                //mPreviewRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE,
                                 //       CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_VIDEO);

                                // Flash is automatically enabled when necessary.
                                //setAutoFlash(mPreviewRequestBuilder);

                                // Finally, we start displaying the camera preview.
                                mPreviewRequest = mPreviewRequestBuilder.build();
                                mCaptureSession.setRepeatingRequest(mPreviewRequest,
                                        null, mCameraHandler);

                            } catch (CameraAccessException e) {

                                Log.d("InfowareLab.Debug","GLVideoEncodeView2.CameraCaptureSession.onConfigured Error:" + e.getMessage());
                                e.printStackTrace();
                            }
                        }

                        @Override
                        public void onConfigureFailed(
                                @NonNull CameraCaptureSession cameraCaptureSession) {

                            Log.d("InfowareLab.Debug","GLVideoEncodeView2.CameraCaptureSession.onConfigureFailed");
                            //showToast("Failed");
                        }
                    }, mCameraHandler
            );
        } catch (CameraAccessException e) {
            e.printStackTrace();
        }
    }

    /**
     * Compares two {@code Size}s based on their areas.
     */
    static class CompareSizesByArea implements Comparator<Size> {

        @Override
        public int compare(Size lhs, Size rhs) {
            // We cast here to ensure the multiplications won't overflow
            return Long.signum((long) lhs.getWidth() * lhs.getHeight() -
                    (long) rhs.getWidth() * rhs.getHeight());
        }

    }

    /**
     * Given {@code choices} of {@code Size}s supported by a camera, choose the smallest one that
     * is at least as large as the respective texture view size, and that is at most as large as the
     * respective max size, and whose aspect ratio matches with the specified value. If such size
     * doesn't exist, choose the largest one that is at most as large as the respective max size,
     * and whose aspect ratio matches with the specified value.
     *
     * @param choices           The list of sizes that the camera supports for the intended output
     *                          class
     * @param width  The width of the texture view relative to sensor coordinate
     * @param height The height of the texture view relative to sensor coordinate
     * @param maxWidth          The maximum width that can be chosen
     * @param maxHeight         The maximum height that can be chosen
     * @return The optimal {@code Size}, or an arbitrary one if none were big enough
     */
    private static Size chooseOptimalSize(Size[] choices, int width,
                                          int height, int maxWidth, int maxHeight) {

        // Collect the supported resolutions that are at least as big as the preview Surface
        List<Size> bigEnough = new ArrayList<>();
        // Collect the supported resolutions that are smaller than the preview Surface
        List<Size> notBigEnough = new ArrayList<>();
        int w = width;//aspectRatio.getWidth();
        int h = height;//aspectRatio.getHeight();
        for (Size option : choices) {

            Log.d("InfowareLab.Debug","chooseOptimalSize.resolution: " + option.getWidth() + "x" + option.getHeight());

            if (option.getWidth() <= maxWidth && option.getHeight() <= maxHeight &&
                    option.getHeight() == option.getWidth() * h / w) {
                if (option.getWidth() >= width &&
                        option.getHeight() >= height) {
                    bigEnough.add(option);
                } else {
                    notBigEnough.add(option);
                }
            }
        }

        // Pick the smallest of those big enough. If there is no one big enough, pick the
        // largest of those not big enough.
        if (bigEnough.size() > 0) {
            return Collections.min(bigEnough, new CompareSizesByArea());
        } else if (notBigEnough.size() > 0) {
            return Collections.max(notBigEnough, new CompareSizesByArea());
        } else {
            Log.e(TAG, "Couldn't find any suitable preview size");
            return choices[0];
        }
    }

    /**
     * Sets up member variables related to camera.
     *
     * @param width  The width of available size for camera preview
     * @param height The height of available size for camera preview
     */
    @SuppressWarnings("SuspiciousNameCombination")
    private void setUpCameraOutputs(int width, int height) {

        Log.d("InfowareLab.Debug","GLVideoEncodeView2.setUpCameraOutputs: " + width + "x" + height);
        CameraManager manager = (CameraManager) mContext.getSystemService(Context.CAMERA_SERVICE);
        try {

            String[] cameraIdList = manager.getCameraIdList();

            for (String cameraId : cameraIdList) {
                CameraCharacteristics characteristics
                        = manager.getCameraCharacteristics(cameraId);

                // We don't use a back facing camera in this sample.
                Integer facing = characteristics.get(CameraCharacteristics.LENS_FACING);
                if (facing != null && cameraIdList.length > 1 && facing == CameraCharacteristics.LENS_FACING_BACK) {
                    continue;
                }

                StreamConfigurationMap map = characteristics.get(
                        CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
                if (map == null) {
                    continue;
                }

                // For still image captures, we use the largest available size.
//                Size largest = Collections.max(
//                        Arrays.asList(map.getOutputSizes(ImageFormat.JPEG)),
//                        new CompareSizesByArea());
//                mImageReader = ImageReader.newInstance(largest.getWidth(), largest.getHeight(),
//                        ImageFormat.JPEG, /*maxImages*/2);
//                mImageReader.setOnImageAvailableListener(
//                        mOnImageAvailableListener, mBackgroundHandler);

                // Find out if we need to swap dimension to get the preview size relative to sensor
                // coordinate.
                /*
                int displayRotation = activity.getWindowManager().getDefaultDisplay().getRotation();
                //noinspection ConstantConditions
                mSensorOrientation = characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION);
                boolean swappedDimensions = false;
                switch (displayRotation) {
                    case Surface.ROTATION_0:
                    case Surface.ROTATION_180:
                        if (mSensorOrientation == 90 || mSensorOrientation == 270) {
                            swappedDimensions = true;
                        }
                        break;
                    case Surface.ROTATION_90:
                    case Surface.ROTATION_270:
                        if (mSensorOrientation == 0 || mSensorOrientation == 180) {
                            swappedDimensions = true;
                        }
                        break;
                    default:
                        Log.e(TAG, "Display rotation is invalid: " + displayRotation);
                }

                Point displaySize = new Point();
                activity.getWindowManager().getDefaultDisplay().getSize(displaySize);
                int rotatedPreviewWidth = width;
                int rotatedPreviewHeight = height;
                int maxPreviewWidth = displaySize.x;
                int maxPreviewHeight = displaySize.y;

                if (swappedDimensions) {
                    rotatedPreviewWidth = height;
                    rotatedPreviewHeight = width;
                    maxPreviewWidth = displaySize.y;
                    maxPreviewHeight = displaySize.x;
                }

                if (maxPreviewWidth > MAX_PREVIEW_WIDTH) {
                    maxPreviewWidth = MAX_PREVIEW_WIDTH;
                }

                if (maxPreviewHeight > MAX_PREVIEW_HEIGHT) {
                    maxPreviewHeight = MAX_PREVIEW_HEIGHT;
                }
                */

                // Danger, W.R.! Attempting to use too large a preview size could  exceed the camera
                // bus' bandwidth limitation, resulting in gorgeous previews but the storage of
                // garbage capture data.
                mPreviewSize = chooseOptimalSize(map.getOutputSizes(SurfaceTexture.class),
                        width, height, MAX_PREVIEW_WIDTH,
                        MAX_PREVIEW_HEIGHT);

                Log.d("InfowareLab.Debug","GLVideoEncodeView2.chooseOptimalSize: " + mPreviewSize.getWidth() + "x" + mPreviewSize.getHeight());

                cameraWidth = mPreviewSize.getWidth();
                cameraHeight = mPreviewSize.getHeight();

                videoCommon.initializeMyVideo(cameraWidth, cameraHeight, cameraFPS);

                // We fit the aspect ratio of TextureView to the size of preview we picked.
//                int orientation = getResources().getConfiguration().orientation;
//                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//                    mTextureView.setAspectRatio(
//                            mPreviewSize.getWidth(), mPreviewSize.getHeight());
//                } else {
//                    mTextureView.setAspectRatio(
//                            mPreviewSize.getHeight(), mPreviewSize.getWidth());
//                }

                // Check if the flash is supported.
                //Boolean available = characteristics.get(CameraCharacteristics.FLASH_INFO_AVAILABLE);
                //mFlashSupported = available == null ? false : available;

                mCameraId = cameraId;
                return;
            }
        } catch (CameraAccessException e) {
            Log.d("InfowareLab.Debug","GLVideoEncodeView2.chooseOptimalSize CameraAccessException:" + e.getMessage());
            e.printStackTrace();
        } catch (NullPointerException e) {
            // Currently an NPE is thrown when the Camera2API is used but not supported on the
            // device this code runs.
            Log.d("InfowareLab.Debug","GLVideoEncodeView2.chooseOptimalSize NullPointerException:" + e.getMessage());
            //ErrorDialog.newInstance(getString(R.string.camera_error))
             //       .show(getChildFragmentManager(), FRAGMENT_DIALOG);
        }
    }

    public void startCameraThread() {
        mCameraThread = new HandlerThread("CameraThread");
        mCameraThread.start();
        mCameraHandler = new Handler(mCameraThread.getLooper());
    }

    public void openCamera(){

        if (videoCommon != null){
            cameraWidth = videoCommon.getWidth();
            cameraHeight = videoCommon.getHeight();
        }

        setUpCameraOutputs(cameraWidth, cameraHeight);
        //initEncoder(cameraWidth, cameraHeight);
        startCameraThread();

        CameraManager manager = (CameraManager) mContext.getSystemService(Context.CAMERA_SERVICE);
        try {
            if (!mCameraOpenCloseLock.tryAcquire(2500, TimeUnit.MILLISECONDS)) {
                if (videoCommon != null) videoCommon.onErrMsg(1);
                throw new RuntimeException("Time out waiting to lock camera opening.");
            }
            manager.openCamera(mCameraId, mStateCallback, mCameraHandler);
        } catch (CameraAccessException e) {
            if (videoCommon != null) videoCommon.onErrMsg(1);
            e.printStackTrace();
        } catch (InterruptedException e) {
            if (videoCommon != null) videoCommon.onErrMsg(1);
            throw new RuntimeException("Interrupted while trying to lock camera opening.", e);
        }
    }

    private MediaCodec.Callback mCodecCallback = new MediaCodec.Callback() {
        @Override
        public void onInputBufferAvailable(@NonNull MediaCodec codec, int index) {
        }

        @Override
        public void onOutputBufferAvailable(@NonNull MediaCodec codec, int index, @NonNull MediaCodec.BufferInfo info) {
            Log.d("InfowareLab.Debug","GLVideoEncodeView2.mCodecCallback.onOutputBufferAvailable: " + index);
            sendEncodedVideoFrame(index, info);
        }

        @Override
        public void onError(@NonNull MediaCodec codec, @NonNull MediaCodec.CodecException e) {
            Log.e("InfowareLab.Debug","GLVideoEncodeView2.mCodecCallback.onError: " + e.getMessage());
        }

        @Override
        public void onOutputFormatChanged(@NonNull MediaCodec codec, @NonNull MediaFormat format) {

        }
    };

    private boolean isKeyFrame(byte[] buffer) {

        if (buffer.length < 5) {
            return false;
        }

        //00 00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 0
                && buffer[3] == 1) {
            int nalType = buffer[4] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                return true;
            }
        }

        //00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 1) {
            int nalType = buffer[3] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                return true;
            }
        }

        return false;
    }

    private void sendEncodedVideoFrame(int index, MediaCodec.BufferInfo info) {

            if (!isSharing() || mCameraDevice == null || mVideoEncoder == null) {
                Log.d("InfowareLab.Debug","GLVideoEncodeView2.sendEncodedVideoFrame Warning: video NOT shared.");
                return;
            }

            ByteBuffer outputBuffer = mVideoEncoder.getOutputBuffer(index);

            byte[] data = new byte[info.size];
            outputBuffer.get(data);

            if (mVideoHead != null) {
                //int nalUnitType = data[4] & 0x1F;
                if (isKeyFrame(data)/*nalUnitType == 5 || nalUnitType == 6*/) { //IDR frame
                    int len;
                    mVideoHead[4] = (byte) (mVideoHead[4] | (3 << 5));

                    //Log.d("InfowareLab.Debug","I Frame PPS.SPS length: " + mVideoHead.length);
                    //Log.d("InfowareLab.Debug","I Frame PPS.SPS: " + Arrays.toString(mVideoHead));

                    videoCommon.sendMyVideoData(mVideoHead, mVideoHead.length, true, this.getCameraWidth(), this.getCameraHeight(), true);
    //                        System.arraycopy(data, 0, mOutput, 0, data.length);
    //                        mOutput[4] = (byte) (mOutput[4] | (3 << 5)); //nal_ref_idc = 3,IDR frame high priority
                    data[4] = (byte) (data[4] | (3 << 5)); //nal_ref_idc = 3,IDR frame high priority
                    len = data.length;

                    //Log.d("InfowareLab.Debug","I Frame length: " + len);
                    //Log.d("InfowareLab.Debug","I Frame resolution: " + cameraWidth + "x" + cameraHeight);
                    videoCommon.sendMyVideoData(data, len, true, this.cameraWidth, this.cameraHeight, true);
                } else { //P frame

                    //Log.d("InfowareLab.Debug","P Frame length: " + data.length);
                    //Log.d("InfowareLab.Debug","P Frame resolution: " + cameraWidth + "x" + cameraHeight);
                    videoCommon.sendMyVideoData(data, data.length, false, this.cameraWidth, this.cameraHeight, true);
                }
            }
            else // 保存pps sps 只有开始时 第一个帧里有， 保存起来后面用
            {
                ByteBuffer spsPpsBuffer = ByteBuffer.wrap(data);
                if (spsPpsBuffer.getInt() == 0x00000001) {

                    Log.d("InfowareLab.Debug", "Encoder PPS_SPS data length: " + data.length);
                    Log.d("InfowareLab.Debug", "Encoder PPS_SPS data: " + Arrays.toString(data));

                    //Log.d("InfowareLab.Debug","PPS.SPS: data.length"+data.length);
                    mVideoHead = new byte[data.length];

                    System.arraycopy(data, 0, mVideoHead, 0, data.length);

                    Log.d("InfowareLab.Debug", "PPS.SPS: mVideoHead[4]:" + mVideoHead[4]);
                    Log.d("InfowareLab.Debug", "PPS.SPS: mVideoHead[5]:" + mVideoHead[5]);
                    Log.d("InfowareLab.Debug", "PPS.SPS: mVideoHead[6]:" + mVideoHead[6]);

                    if (mVideoHead[4] == 0x27) //0x27的数据发送失败，故改为 0x67
                    {
                        mVideoHead[4] = 0x67;
                    }
                    if ((mVideoHead[4] & 0x1f) != 7) {
                        mVideoHead = null;
                        Log.d("InfowareLab.Debug", "Encoder Error: videoCommon.reStartCam()");
                        videoCommon.reStartCam();
                    }

                } else {
                    Log.d("InfowareLab.Debug", "Error: NOT found PPS_SPS media head.");
                }
            }

            mVideoEncoder.releaseOutputBuffer(index, false);
    }

    private void initEncoder(int width, int height) {

        Log.d("InfowareLab.Debug","GLVideoEncodeView2.initEncoder: " + width + "x" + height);

        int bitrate = 0;
        int picSize = width * height;
        if (picSize < 320 * 240) {
            bitrate = 200000;
//        } else if (picSize <= 352 * 288) {
//            bitrate = 240000;
        } else if (picSize <= 640 * 480) {
            bitrate = 500000;
//        } else if (picSize <= 960 * 720) {
//            bitrate = 700000;
        } else if (picSize <= 1280 * 720) {
            bitrate = 1000000;
//            bitrate = 700000;
        } else if (picSize <= 1600 * 1200) {
            bitrate = 1* 1200000;
//            bitrate = 2* 1000000;
        } else {
//            bitrate = 1 * 1200000;
            bitrate = 2* 1000000;
        }

        try {
            mVideoEncoder = MediaCodec.createEncoderByType(mCodecType);
        } catch (Exception e) {
            Log.e("InfowareLab.Debug","GLVideoEncodeView2.createEncoderByType: " + e.getMessage());

            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        mVideoFormat = MediaFormat.createVideoFormat(mCodecType, width, height); //width, height
        //mVideoFormat.setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileMain);
        mVideoFormat.setInteger("vendor.rtc-ext-enc-low-latency.enable",1);

        mVideoFormat.setInteger(MediaFormat.KEY_BIT_RATE, bitrate); //125000 * 10
        mVideoFormat.setInteger(MediaFormat.KEY_FRAME_RATE, cameraFPS);

        //设置码流模式
        mVideoFormat.setInteger(MediaFormat.KEY_BITRATE_MODE,
                MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CBR);

        mVideoFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
        mVideoFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);

        mVideoEncoder.setCallback(mCodecCallback);

        try {
            mVideoEncoder.configure(mVideoFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        } catch (Exception e) {
            Log.e("InfowareLab.Debug","GLVideoEncodeView2.mVideoEncoder.configure: " + e.getMessage());

            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        mEncoderSurface = mVideoEncoder.createInputSurface();

//        try {
//            mVideoEncoder.start();
//        } catch (Exception e) {
//            Log.e("InfowareLab.Debug","GLVideoEncodeView2.mVideoEncoder.start: " + e.getMessage());
//
//            // TODO Auto-generated catch block
//            e.printStackTrace();
//        }
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    private void enableEncoder(boolean enable){

        if (mEncoderEnabled == enable) return;

        Log.e("InfowareLab.Debug","GLVideoEncodeView2.enableEncoder: " + enable);

        if (enable){
            try {
                if (mVideoEncoder == null) initEncoder(cameraWidth, cameraHeight);
                //mVideoEncoder.setInputSurface(mPreviewSurface);
                mVideoEncoder.start();
            } catch (Exception e) {
                Log.e("InfowareLab.Debug","GLVideoEncodeView2.mVideoEncoder.start: " + e.getMessage());
                // TODO Auto-generated catch block
                e.printStackTrace();
                return;
            }
        }
        else
        {
            try {
                if (mVideoEncoder == null) return;

                mVideoEncoder.stop();
            } catch (Exception e) {
                Log.e("InfowareLab.Debug","GLVideoEncodeView2.mVideoEncoder.stop: " + e.getMessage());
                // TODO Auto-generated catch block
                e.printStackTrace();
                return;
            }
        }

        mEncoderEnabled = enable;
    }
    /*
     * 设置相机属性
     */
    private void setCameraParameters(int degrees) {
        /*
        Camera.Parameters parameters = camera.getParameters();
        List<Camera.Size> previewSizes = parameters.getSupportedPreviewSizes();
        List<int[]> rates = parameters.getSupportedPreviewFpsRange();
        cameraWidth = 0;
        cameraHeight = 0;
        // 取比设定值小的像素中最大的
        for (Camera.Size size : previewSizes) {
            Log.d("InfowareLab.Debug","Camera.Size = " + size.width + ", " + size.height + ", "
                    + cameraFPS);
            if (size.width * size.height <= videoCommon.getWidth()
                    * videoCommon.getHeight()
                    && size.height >= 0&&size.width%16==0&&size.height%16==0) {
                if (cameraWidth == 0) {
                    cameraWidth = size.width;
                    cameraHeight = size.height;
                }
                if (size.width*size.height >= cameraWidth*cameraHeight) {
                    cameraWidth = size.width;
                    cameraHeight = size.height;
                }
            }
        }
        // 如果设定值实在太小，取所支持的最小像素
        if (cameraWidth == 0) {
            for (Camera.Size size : previewSizes) {
                if (size.height >= 0&&size.width%16==0&&size.height%16==0) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width*size.height <= cameraWidth*cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
        }
        Log.d("InfowareLab.Debug","Camera.CameraParameters = " + cameraWidth + ", " + cameraHeight);
        frameSize = cameraWidth * cameraHeight;
        int minimum = 0;
        int maximum = 0;
        int retMininum = 0;
        int retMaximum = 0;
        if (rates.size() > 0) {
            minimum = rates.get(0)[0];
            maximum = rates.get(0)[1];
            retMininum = rates.get(0)[0];
            retMaximum = rates.get(0)[1];
            for (int[] fps : rates){
                if (minimum < fps[0]){
                    minimum = fps[0];
                }
                if (maximum < fps[1]){
                    maximum = fps[1];
                }
            }
        }

        setCameraOrientation(degrees,parameters);
        parameters.setPreviewSize(cameraWidth, cameraHeight);//设置预览的高度和宽度,单位为像素
//	    parameters.setPreviewFrameRate(FRAME_RATE);//设置图片预览的帧速。
//	    parameters.setPreviewFpsRange(minimum, maximum);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){
                if (minimum > 0 && maximum > 0){
                    parameters.setPreviewFpsRange(minimum,maximum);
                }
            }else {
                if (retMininum > 0 && retMaximum > 0){
                    parameters.setPreviewFpsRange(retMininum,retMaximum);
                }
            }
        }catch (RuntimeException e){
            if (retMininum > 0 && retMaximum > 0){
                parameters.setPreviewFpsRange(retMininum,retMaximum);
            }
        }
        List<String> focusModes = parameters.getSupportedFocusModes();
        if (focusModes.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE)){
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);// 1连续对焦
            camera.setParameters(parameters);
            camera.cancelAutoFocus();
        }else{
            try {
                camera.setParameters(parameters);
            }
            catch (RuntimeException e)
            {
                Camera.Parameters appliedParam = camera.getParameters();
                cameraWidth = appliedParam.getPreviewSize().width;
                cameraHeight = appliedParam.getPreviewSize().height;

                Log.d("InfowareLab.Debug","Camera.CameraParameters(FAILED) = " + cameraWidth + ", " + cameraHeight);

            }
        }

        if(initVideo){
            videoCommon.initializeMyVideo(cameraWidth, cameraHeight, FRAME_RATE);
            initVideo = false;
        }
        */
    }

    private void setCameraOrientation(int degrees, Camera.Parameters p){
        /*
        if (Integer.parseInt(Build.VERSION.SDK) >= 8)
            setDisplayOrientation(camera, degrees);
        else
        {
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT)
            {
                p.set("orientation", "portrait");
                p.set("rotation", degrees);
            }
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE)
            {
                p.set("orientation", "landscape");
                p.set("rotation", degrees);
            }
        }*/
    }
    private void setDisplayOrientation(Camera mCamera, int angle) {
        Method downPolymorphic;
        try
        {
            downPolymorphic = mCamera.getClass().getMethod("setDisplayOrientation", new Class[] { int.class });
            if (downPolymorphic != null)
                downPolymorphic.invoke(mCamera, new Object[] { angle });
        }
        catch (Exception e1)
        {
        }
    }

    public void flushEncoder(){
        //if(isHardCodec&&mVideoEncoder!=null){
        //    mVideoEncoder.flush();
        //}
    }


    public void destroyCamera() {
        Log.d("InfowareLab.Debug","GLVideoEncodeView2.destroyCamera");

        if (mCameraDevice != null) {
            try {
                mCameraOpenCloseLock.acquire();
                if (null != mCaptureSession) {
                    mCaptureSession.stopRepeating();
                    mPreviewStarted = false;
                    mCaptureSession.close();
                    mCaptureSession = null;
                }

                if (mVideoEncoder != null){
                    destroyEncoder();
                }

                if (null != mCameraDevice) {
                    mCameraDevice.close();
                    mCameraDevice = null;
                    isOpen = false;
                }

                if (null != mCameraThread){
                    //mCameraThread.interrupt();
                    mCameraThread.quitSafely();
                    mCameraHandler = null;
                    mCameraThread = null;
                }

            } catch (InterruptedException | CameraAccessException e) {
                throw new RuntimeException("Interrupted while trying to lock camera closing.", e);
            } finally {
                mCameraOpenCloseLock.release();
            }
        }
    }

    private void destroyEncoder() {

        Log.d("InfowareLab.Debug","GLVideoEncodeView2.destroyEncoder");

        if (mVideoEncoder != null){
            mVideoEncoder.stop();
            mVideoEncoder.release();
            mVideoEncoder = null;
            mEncoderSurface.release();
            mEncoderSurface = null;
            mVideoFormat = null;
        }
    }

    public void reStartLocalView() {
        // if(camera != null){
        Log.d("InfowareLab.Debug","GLVideoEncoderView2.reStartLocalView111");
        if (mCameraDevice == null) {
            changeStatus(true);
        }
        else {
            destroyCamera();
            startCamera();
        }
    }

    public void setStatus(boolean isMove) {
        /*if (isMove) {
            camera.setPreviewCallback(null);
            changePreview(false);
        } else {
            camera.setPreviewCallback(this);
            changePreview(true);
        }*/
    }

    public void changeStatus(boolean isOpenCamera) {
        if (isOpenCamera) {
            if (mCameraDevice == null) {
                startCamera();
            }
        } else {
            if (mCameraDevice != null) {
                destroyCamera();
            }
        }
    }

    private void changePreview(boolean state) {

        /*
        try {
            if (state) {
                Log.d("InfowareLab.Debug","startPreview:" + state );

                camera.startPreview();
                mPreviewStarted = true;
            } else {
                Log.d("InfowareLab.Debug","stopPreview:" + state );

                camera.stopPreview();
                mPreviewStarted = false;
            }
        } catch (Exception e) {
            Log.e("InfowareLab.Debug",e.getMessage());
        }*/
    }

    public boolean getCamera() {
        return mCameraDevice != null;
    }

    public int getCameraWidth() {
        return cameraWidth;
    }

    public int getCameraHeight() {
        return cameraHeight;
    }

    public void setInitVideo(boolean initVideo) {
        GLVideoEncodeView2.initVideo = initVideo;
    }

    public boolean isSharing() {
        return isSharing;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    public void setSharing(boolean isSharing) {
        GLVideoEncodeView2.isSharing = isSharing;
        enableEncoder(isSharing);
    }

    public void setPortrait(boolean isPortrait) {
        this.isPortrait = isPortrait;
        if (isPortrait) {
            videoCommon.exChange(cameraHeight, cameraWidth);
        } else {
            videoCommon.exChange(cameraWidth, cameraHeight);
        }
    }

    public void setCameraLandscape() {
        degrees = 0;
        reStartLocalView();
    }

    //	public void setParams(int width, int height) {
//		if (width == 1) {
//			isSmall = true;
//		} else {
//			isSmall = false;
//		}
//		LayoutParams params = (LayoutParams) getLayoutParams();
//		params.width = width;
//		params.height = height;
//		setLayoutParams(params);
//	}
    public void setParams(int width,int height) {
        if(width>1&&mCameraDevice==null&&isEnabled()){
            reStartLocalView();
        }
        if(width<=1){
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = 1;
            params.height = 1;
            setLayoutParams(params);
        }else {
            /*if(degrees%180==0){
                if((1.0f*cameraWidth/cameraHeight)>(1.0f*width/height)){
                    height = (int) ((1.0f*cameraHeight/cameraWidth)*width);
                }else{
                    width = (int) ((1.0f*cameraWidth/cameraHeight)*height);
                }
            }else {
                if((1.0f*cameraHeight/cameraWidth)>(1.0f*width/height)){
                    height = (int) ((1.0f*cameraWidth/cameraHeight)*width);
                }else{
                    width = (int) ((1.0f*cameraHeight/cameraWidth)*height);
                }
            }*/
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();

            params.setMargins(0,0,0,0);

            params.width = width;//RelativeLayout.LayoutParams.MATCH_PARENT;
            params.height = height;//RelativeLayout.LayoutParams.MATCH_PARENT;
            setLayoutParams(params);

            Log.d("InfowareLab.Debug","GLVideoEncodeView2.setParams: " + width + "x" + height);

        }
    }

    public boolean isPreview() {
        return mPreviewStarted;
    }

    public void switchSoftEncode(boolean isSoft){
        isHardCodec = videoCommon.isHardCodec();
        reStartLocalView();
    }
}
