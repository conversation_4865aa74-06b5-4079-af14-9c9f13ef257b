package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActHome;
import com.infowarelab.tvbox.activity.ActMain;
import com.infowarelab.tvbox.activity.ActMain1;
import com.infowarelab.tvbox.activity.ActSound;
import com.infowarelab.tvbox.activity.MicSetActivity;
import com.infowarelab.tvbox.okhttp.abc.bean.VersionInfo;
import com.infowarelab.tvbox.okhttp.abc.constant.HttpConstant;
import com.infowarelab.tvbox.okhttp.abc.download.DownloadListener;
import com.infowarelab.tvbox.okhttp.abc.request.RequestCenter;
import com.infowarelab.tvbox.okhttp.listener.DisposeDataListener;
import com.infowarelab.tvbox.utils.DeviceIdFactory;
import com.infowarelab.tvbox.utils.FileUtils;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.ToastUtil;
import com.infowarelabsdk.conference.util.Utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;

/**
 * Created by Fred on 2023/4/20
 */

@SuppressLint("ValidFragment")
public class FragDesktop extends BaseFragment {
    private static final String TAG = "InfowareLab.desktop";
    private View rootView;
    private TextView tvShareCode;
    private  Button btnReturn;

    public FragDesktop(ICallParentView iCallParentView) {
        super(iCallParentView);
    }
    //无参构造器
    public FragDesktop(){
        super();
    }
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.frag_desktop, container, false);
        initView();
        return rootView;
    }


    public void initView() {

        tvShareCode = (TextView) rootView.findViewById(R.id.tv_share_code);

        String deviceId = DeviceIdFactory.getShareCode();

        Log.d("InfowareLab.Debug","FragDesktop.initView: >>>>>> deviceId = " + deviceId);

        if (deviceId != null && deviceId.length() == 6){
            String regex = "(.{3})";
            deviceId = deviceId.replaceAll(regex,"$1 ");
            tvShareCode.setText(deviceId);
        }

        btnReturn = (Button) rootView.findViewById(R.id.btn_return);
        btnReturn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;
                callParentView(ACTION_SHOWHOME, null);
            }
        });
        btnReturn.requestFocus();

    }

    @Override
    public void onResume() {

        String deviceId = DeviceIdFactory.getShareCode();

        Log.d("InfowareLab.Debug","FragDesktop.onResume: >>>>>> deviceId = " + deviceId);

        if (deviceId != null && deviceId.length() == 6){
            String regex = "(.{3})";
            deviceId = deviceId.replaceAll(regex,"$1 ");
            tvShareCode.setText(deviceId);
        }

        super.onResume();
    }
}
