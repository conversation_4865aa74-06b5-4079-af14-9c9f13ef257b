package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.media.AudioManager;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.AudioCommonImpl;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.util.ToastUtil;
import com.infowarelabsdk.conference.util.Utils;

/**
 * Created by sdvye on 2019/7/2.
 */
@SuppressLint("ValidFragment")
public class FragMenu extends BaseFragment {
    private View viewMenu;
    private LinearLayout micLayout,docLayout,flowLayout,videoLayout;
    private RadioButton rbMic;
    private RadioButton rbSound;
    private AudioManager audioManager;
    private RadioButton docBtn,flowBtn,videoBtn;
    private TextView flowText;
    //与Activity交互
    private FragmentInteraction listterner;
    //会议id
    private String meeting_id  = "";
    //是否有桌面共享权限
    private boolean bAS = false;
    private int currVolume = 0;

    public void setbAS(boolean bAS) {
        this.bAS = bAS;
    }

    private boolean isShare = false;

    public void setShare(boolean share) {
        isShare = share;
    }

    private CommonFactory commonFactory = CommonFactory.getInstance();

    public void setMeeting_id(String meeting_id) {
        this.meeting_id = meeting_id;
    }

    public FragMenu(ICallParentView iCallParentView) {
        super(iCallParentView);
    }

    private boolean isHost = false;

    public void setHost(boolean host) {
        isHost = host;
    }
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        viewMenu = inflater.inflate(R.layout.frag_menu, container, false);
        return viewMenu;
    }
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        audioManager = (AudioManager) getActivity().getSystemService(Context.AUDIO_SERVICE);
        micLayout = (LinearLayout)getActivity().findViewById(R.id.frag_menu_micLayout);
        docLayout = (LinearLayout)getActivity().findViewById(R.id.frag_menu_docLayout);
        flowLayout = (LinearLayout)getActivity().findViewById(R.id.frag_menu_flowLayout);
        videoLayout = (LinearLayout)getActivity().findViewById(R.id.frag_menu_videoLayout);
        rbMic = (RadioButton) getActivity().findViewById(R.id.rb_mic);
        docBtn = (RadioButton)getActivity().findViewById(R.id.frag_menu_docBtn);
        flowBtn = (RadioButton)getActivity().findViewById(R.id.frag_menu_flowBtn);
        flowText = (TextView)viewMenu.findViewById(R.id.frag_menu_flowText);
        videoBtn = (RadioButton)getActivity().findViewById(R.id.frag_menu_videoBtn);
        initFlow();
        rbMic.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (((AudioCommonImpl)commonFactory.getAudioCommon()).isMICWork()){
                    if (!SharedPreferencesUrls.getInstance().getBoolean("mic",false)) {
                        rbMic.setChecked(true);
                        listterner.micOn();
                    } else {
                        rbMic.setChecked(false);
                        listterner.micOff();
                    }
                }else {
                    ToastUtil.showMessage(getContext(),"对不起，您暂未操作麦克风权限", 5 * 1000);
                }
            }
        });
        setMicStatus(SharedPreferencesUrls.getInstance().getBoolean("mic",false));
        rbSound = (RadioButton) getActivity().findViewById(R.id.rb_sound);
        rbSound.requestFocus();
        rbSound.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick())return;
                if (audioManager.isSpeakerphoneOn()){
                    rbSound.setChecked(false);
                    closeSpeaker();
                    showShortToast(R.string.speaker_off_title);
                    SharedPreferencesUrls.getInstance().putBoolean("sound",false);
                }else {
                    rbSound.setChecked(true);
                    openSpeaker();
                    showShortToast(R.string.speaker_on_title);
                    SharedPreferencesUrls.getInstance().putBoolean("sound",true);
                }
            }
        });
        rbSound.setChecked(SharedPreferencesUrls.getInstance().getBoolean("sound",true));
        RadioButton rbMicSet = (RadioButton) getActivity().findViewById(R.id.rb_mic_set);
        rbMicSet.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listterner.setMicListener();
            }
        });
        RadioButton rbSoundSet = (RadioButton) getActivity().findViewById(R.id.rb_sound_set);
        rbSoundSet.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listterner.setSoundListener();
            }
        });
        btnState();
        docBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listterner.selectDoc();
            }
        });
        flowBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Build.MODEL.equals("MRUT33")){
                    ToastUtil.showMessage(getContext(),"对不起，你的设备暂不支持该功能",5 * 1000);
                    flowLayout.setVisibility(View.GONE);
                    return;
                }
                if (getContext().getResources().getString(R.string.flow_title).equals(flowText.getText().toString().trim())){
                    flowText.setText(getContext().getResources().getString(R.string.stop_share));
                    listterner.shareDS(true);
                }else {
                    flowText.setText(getContext().getResources().getString(R.string.flow_title));
                    listterner.shareDS(false);
                }
            }
        });
        videoBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listterner.videoPre();
            }
        });
    }
    /**
     * 定义了所有activity必须实现的接口
     */
    public interface FragmentInteraction {
        /**
         * Fragment 向Activity传递指令，这个方法可以根据需求来定义
         *
         */
        void setMicListener();
        void setSoundListener();
        //关闭麦克风
        void micOff();
        //打开麦克风
        void micOn();
        //选择文档
        void selectDoc();
        //共享辅流
        void shareDS(boolean isShare);
        //视频操作
        void videoPre();
    }
    /**
     * 当FRagmen被加载到activity的时候会被回调
     *
     * @param activity
     */
    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity instanceof FragmentInteraction) {
            listterner = (FragmentInteraction) activity;
        } else {
            throw new IllegalArgumentException("activity must implements FragmentInteraction");
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        listterner = null;
    }

    //设置焦点
    public void setFouse(){
        if (rbMic != null){
            rbMic.setFocusable(true);
            rbMic.requestFocus();
        }
    }
    //设置焦点
    public void setSound(){
        if (rbSound != null){
            rbSound.setFocusable(true);
            rbSound.requestFocus();
        }
    }
    //设置麦克风的开关
    public void setMicStatus(boolean isOn){
        if (rbMic != null){
            rbMic.setChecked(isOn);
        }
    }

    //显示或隐藏桌面共享按钮
    public void videoState(boolean isbAS){
        if (isbAS){
            flowLayout.setVisibility(View.VISIBLE);
        }else {
            flowLayout.setVisibility(View.GONE);
        }
    }
    //刷新按钮
    public void btnState(){
        Log.e("tttttt","isHost::"+isHost);
        if (micLayout != null){
            if (((AudioCommonImpl)commonFactory.getAudioCommon()).isMICWork()
                    ||isHost || ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).isHost1()){
                micLayout.setVisibility(View.VISIBLE);
                setFouse();
            }else {
                micLayout.setVisibility(View.GONE);
                setSound();
            }
        }
        if (docLayout != null){
            if (((DocCommonImpl)commonFactory.getDocCommon()).getDocMapList().size() > 1){
                if (((DocCommonImpl)commonFactory.getDocCommon()).getPrivateShareDocPriviledge()
                        || isHost || ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).isHost1()){
                    docLayout.setVisibility(View.VISIBLE);
                }else {
                    docLayout.setVisibility(View.GONE);
                }
            }else {
                docLayout.setVisibility(View.GONE);
            }
        }
        if (flowLayout != null){
            if (!Build.MODEL.equals("MRUT33")){
                if (bAS){
                    flowLayout.setVisibility(View.VISIBLE);
                }else {
                    flowLayout.setVisibility(View.GONE);
                }
            }else {
                flowLayout.setVisibility(View.GONE);
            }
        }
        if (videoLayout != null){
            if (((UserCommonImpl)commonFactory.getUserCommon()).isHost()){
                videoLayout.setVisibility(View.VISIBLE);
            }else {
                videoLayout.setVisibility(View.GONE);
            }
        }
    }

    //初始化按钮标题
    public void initFlow(){
        if (null != flowText){
            if (isShare){
                flowText.setText(getContext().getResources().getString(R.string.stop_share));
            }else {
                flowText.setText(getContext().getResources().getString(R.string.flow_title));
            }
        }
    }

    /**
     * 打开扬声器
     * */
    private void openSpeaker(){
        try {
            audioManager.setMode(AudioManager.STREAM_MUSIC);
            currVolume = audioManager.getStreamVolume(AudioManager.STREAM_VOICE_CALL);
            Log.e("tttttt","扬声器是否打开::"+audioManager.isSpeakerphoneOn());
            if(!audioManager.isSpeakerphoneOn()) {
                audioManager.setMode(AudioManager.MODE_IN_CALL);
                audioManager.setSpeakerphoneOn(true);
                audioManager.setStreamVolume(AudioManager.STREAM_VOICE_CALL,audioManager.getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL),
                        AudioManager.STREAM_VOICE_CALL);
                ToastUtil.showMessage(getContext(),"扬声器打开",5000);
            }
        }catch (Exception e){
        }
    }
    /**
     * 关闭扬声器
     */
    public void closeSpeaker() {
        try {
            if (audioManager != null && audioManager.isSpeakerphoneOn()){
                audioManager.setSpeakerphoneOn(false);
                audioManager.setStreamVolume(AudioManager.STREAM_VOICE_CALL,0,AudioManager.STREAM_VOICE_CALL);
            }
        }catch (Exception e){

        }
    }
}
