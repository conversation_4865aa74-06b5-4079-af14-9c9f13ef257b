package com.infowarelab.tvbox.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceHolder.Callback;
import android.view.SurfaceView;
import android.widget.RelativeLayout;

import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.mingri.uvc.Uvc;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.List;

////import org.apache.log4j.Logger;

/**
 * PreviewCallback回到接口，用于显示预览框
 */
@SuppressLint("NewApi")
public class UvcEncodeView extends SurfaceView implements Uvc.PreviewCallback, Callback {
    public static final String TAG = "UvcEncodeView";
    public static final String H264_MIME = "video/avc";
    private Context mContext;
    private VideoCommonImpl videoCommon;
    private int cameraWidth = 640;
    private int cameraHeight = 480;
    private int degrees = 0;
    private static boolean isSharing = false;
    private Uvc mCamera;
    private SurfaceHolder holder;
    private Uvc.Size curSize;
    private byte[] mMediaHead = null;
    private byte[] mOutput = new byte[10240 * 8];
    private File _fr = null;
    private FileOutputStream _out = null;
    private boolean isWriteFile = false;
    //private Logger logger = Logger.getLogger("UvcEncodeView");

    public UvcEncodeView(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public UvcEncodeView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        this.mContext = context;
        init();
    }

    public UvcEncodeView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    public void init() {
        //logger.info("initLog");
        videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        if (holder == null) holder = this.getHolder();
        holder.addCallback(this);
        holder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        if (isWriteFile) {
            _fr = new File("/sdcard/yv12buf.src");
            try {
                _out = new FileOutputStream(_fr);
            } catch (FileNotFoundException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }

    public void startCamera() {
        //logger.info("startCamera");
        int nCameraID = Uvc.ID_LOCAL;
        // Open Camera
        if (mCamera == null) {
            mCamera = Uvc.open(nCameraID);
        }
        if (mCamera == null) {
            return;
        }
        mCamera.setPreset(0);
        // set Stream parames
        initStream(Uvc.MAIN_STREAM);
//        initStream(Uvc.SUB_STREAM);
//        initStream(Uvc.THIRD_STREAM);

        //create decoder
        mCamera.setPreviewCallback(this);
        try {
            mCamera.setPreviewDisplay(holder);
        } catch (IOException e) {
            e.printStackTrace();
        }
        mCamera.startPreview();

        setBackgroundColor(0);
    }

    private void releaseCamera() {
        if (mCamera != null) {
            mCamera.release();
            mCamera = null;
        }
//        if (mVideoDecoder != null) {
//            mVideoDecoder.release();
//            mVideoDecoder = null;
//        }
//        if (!mList.isEmpty()) {
//            for (int i = 0; i < mList.size(); i++) {
//                MrVideoDecoder mrVideoDecoder = mList.get(i);
//                mrVideoDecoder.release();
//            }
//            mList.clear();
//        }
    }


    @Override
    public void onPreviewFrame(byte[] data, Uvc uvc) {
        if (isWriteFile) {
            try {
                _out.write(data);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        //logger.info("onPreviewFrame dataLength = " + data.length);
        if (!isSharing) return;
        byte nData1, nData2;
        byte nStreamInx, nFrameRefType;
        int frameSize;

        frameSize = data.length;

        nData1 = data[0];
        nData2 = data[1];
        data[0] = nData2 == 0 ? (byte) 0x00 : (byte) 0xFF;
        //Is main Stream
        nStreamInx = (byte) (nData1 & 0x03);
        //get svc information
        nFrameRefType = (byte) ((nData1 & 0x7C) >> 2);
        // Main Stream
        //logger.info("onPreviewFrame star");
        if (nStreamInx == 0) {
            //logger.info("onPreviewFrame nStreamInx = 0");
            //if (mMediaHead != null) {
//            if (true) {
            int nalUnitType = data[4] & 0x1F;
            /**
             * 防止花屏
             * */
            if (data[4] == 0x61){
                data[4] = 0x41;
            }
            if (nalUnitType == 7) { //IDR frame
                int len;
//                     mMediaHead[4] = (byte) (mMediaHead[4] | (3 << 5));
//                    videoCommon.sendMyVideoData(mMediaHead, mMediaHead.length, true,cameraWidth, cameraHeight, true);
//                      System.arraycopy(data, 0, mOutput, 0, data.length);
//                       mOutput[4] = (byte) (mOutput[4] | (3 << 5)); //nal_ref_idc = 3,IDR frame high priority
//                      len = data.length;
//                       videoCommon.sendMyVideoData(mOutput, len, true, cameraWidth, cameraHeight, true);
                data[4] = (byte) (data[4] | (3 << 5));
                videoCommon.sendMyVideoData(data, data.length, true, cameraWidth, cameraHeight, true);
                //logger.info("onPreviewFrame h = " + data[0] + " " + data[1] + " " + data[2] + " " + data[3]);
                //logger.info("onPreviewFrame nalUnitType == 7, len = %d" + data.length);
            } else { //P frame
                videoCommon.sendMyVideoData(data, data.length, false, cameraWidth, cameraHeight, true);
                //logger.info("onPreviewFrame p");
                //logger.info("onPreviewFrame nalUnitType != 7, len = %d" + data.length);
            }
//            } else {
//                //logger.info("onPreviewFrame333333");
//                // 保存pps sps 只有开始时 第一个帧里有， 保存起来后面用
//                ByteBuffer spsPpsBuffer = ByteBuffer.wrap(data);
//                if (spsPpsBuffer.getInt() == 0x00000001) {
//                    mMediaHead = new byte[data.length];
//                    System.arraycopy(data, 0, mMediaHead, 0, data.length);
//                    if (mMediaHead[4] == 0x27) //0x27的数据发送失败，故改为 0x67
//                    {
//                        mMediaHead[4] = 0x67;
//                    }
//                    if ((mMediaHead[4] & 0x1f) != 7) {
//                        mMediaHead = null;
//                        videoCommon.reStartCam();
//                    }
//                } else {
//                    Log.e("offerEncoder", "not found media head.");
//                }
//            }
            //videoCommon.sendMyVideoData(data, frameSize, false, cameraWidth, cameraHeight, true);
        }
    }

    private boolean initStream(byte streamType) {
        int ret;
        // open stream
        ret = mCamera.openStream(streamType);
        if (ret != 0) {
            Log.e(TAG, "open mCamera failed");
            return false;
        }
        setCmd(streamType);
        return true;
    }

    private boolean setCmd(byte streamType) {
        int ret;
        Uvc.Size size;
        // setStreamencodeType
        ret = mCamera.setStreamEncodeType(streamType, Uvc.CODEC_H264);
        if (ret != 0) {
            Log.e(TAG, "set mCamera encode type failed");
            return false;
        }

        size = getSetVideoSize();
        ret = mCamera.setStreamVideoSize(streamType, size);
        if (ret != 0) {
            Log.e(TAG, "set mCamera video size failed");
            return false;
        }

        ret = mCamera.setStreamFrameRate(streamType, (byte) 30);
        if (ret != 0) {
            Log.e(TAG, "set mCamera frame rate failed");
            return false;
        }

        //ret = mCamera.setStreamIDR(streamType, 200);
        ret = mCamera.setStreamIDR(streamType, 30);
        if (ret != 0) {
            Log.e(TAG, "set mCamera I Frame Interval failed");
            return false;
        }
        int bitrate = 0;
        int picSize = size.width * size.height;
        if (picSize < 320 * 240) {
            bitrate = 200;
        } else if (picSize <= 352 * 288) {
            bitrate = 240;
        } else if (picSize <= 720 * 576) {
            bitrate = 700;
        } else if (picSize <= 960 * 720) {
            bitrate = 900;
        } else if (picSize <= 1280 * 960) {
            bitrate = 1000;
        } else if (picSize <= 1600 * 1200) {
            bitrate = 2000;
        } else {
            bitrate = 2500;
        }

//        ret = mCamera.setStreamBitRate(streamType, Uvc.CODEC_H264, (byte) 0, (byte) 5, (byte) 51, 768);
        ret = mCamera.setStreamBitRate(streamType, Uvc.CODEC_H264, (byte) 0, (byte) 5, (byte) 51, bitrate);
        if (ret != 0) {
            Log.e(TAG, "set mCamera bit rate failed");
            return false;
        }
        //去掉标题
        mCamera.setStringOSD(streamType, 0, 255, 0, 0, 5, 0, "去掉标题");
        return true;
    }

    private Uvc.Size getSetVideoSize() {
        List<Uvc.Size> mSizes = mCamera.getSupportedFrameSizes();
        cameraWidth = 0;
        cameraHeight = 0;
        // 取比设定值小的像素中最大的
        for (Uvc.Size size : mSizes) {
            if (size.width * size.height <= videoCommon.getWidth()
                    * videoCommon.getHeight()
                    && size.height >= 0
                    && size.width % 16 == 0
                    && size.height % 16 == 0) {
                if (cameraWidth == 0) {
                    cameraWidth = size.width;
                    cameraHeight = size.height;
                }
                if (size.width * size.height >= cameraWidth * cameraHeight) {
                    cameraWidth = size.width;
                    cameraHeight = size.height;
                }
            }
        }
        // 如果设定值实在太小，取所支持的最小像素
        if (cameraWidth == 0) {
            for (Uvc.Size size : mSizes) {
                if (size.height >= 0 && size.width % 16 == 0
                        && size.height % 16 == 0) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width * size.height <= cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
        }
        Uvc.Size currentSize = new Uvc.Size(cameraWidth, cameraHeight);
        return currentSize;
    }

    public void flushEncoder() {
        if (mCamera != null) {
//            mCamera.setStreamIDR(Uvc.MAIN_STREAM, 0);
        }
    }

    public void reStartLocalView() {
        releaseCamera();
        startCamera();
        videoCommon.exChange(cameraHeight, cameraWidth);
    }

    public void changeStatus(boolean isOpenCamera) {
        if (isOpenCamera) {
            if (mCamera == null) {
                invalidate();
                init();
                startCamera();
            }
        } else {
            if (mCamera != null) {
                releaseCamera();
            }
        }
    }

    public void destroyCamera() {
        releaseCamera();
    }

    public void setCameraLandscape() {

    }

    public void setSharing(boolean isSharing) {
        UvcEncodeView.isSharing = isSharing;
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width,
                               int height) {
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        releaseCamera();
    }


    public void setParams(int width, int height) {
        if (width <= 1) {
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = 1;
            params.height = 1;
            setLayoutParams(params);
        } else {
//            int h = height;
//            int w = width;
//            if (degrees % 180 == 0) {
//                if ((1.0f * cameraWidth / cameraHeight) > (1.0f * width / height)) {
//                    h = (int) ((1.0f * cameraHeight / cameraWidth) * width);
//                } else {
//                    w = (int) ((1.0f * cameraWidth / cameraHeight) * height);
//                }
//            } else {
//                if ((1.0f * cameraHeight / cameraWidth) > (1.0f * width / height)) {
//                    h = (int) ((1.0f * cameraWidth / cameraHeight) * width);
//                } else {
//                    w = (int) ((1.0f * cameraHeight / cameraWidth) * height);
//                }
//            }
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
//            params.width = w;
//            params.height = h;
            params.width = width;
            params.height = height;
//            params.setMargins((width - w) / 2, (height - h) / 2, 0, 0);
            params.setMargins(0, 0, DensityUtil.dip2px(getContext(),1.0f), 0);
            setLayoutParams(params);
        }
    }
    /**
     * 功能：云台控制
     *
     * @param ptz PTZ type, such as: PTZ_LEFT, PTZ_UP...
     * @return 0-success, -1-invalid argument -2-not support, others-failed
     */
    public int startPtz(byte ptz) {
        if (null != mCamera){
            return mCamera.startPtz(ptz);
        }
        return -2;
    }
    /**
     * 功能：停止云台
     *
     * @param ptz PTZ type, such as: PTZ_LEFT, PTZ_UP...
     * @return 0-success, -1-invalid argument -2-not support, others-failed
     */
    public int stopPtz(byte ptz) {
        if (null != mCamera){
            return mCamera.stopPtz(ptz);
        }
        return -2;
    }

    public void switchSoftEncode(boolean isSoft){
        reStartLocalView();
    }
}