package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.hardware.Camera;
import android.media.MediaCodec;
import android.opengl.GLSurfaceView;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.modle.VideoBean;
import com.infowarelab.tvbox.render.BaseVideoEncodeView;
import com.infowarelab.tvbox.render.GLVideoEncodeView;
import com.infowarelab.tvbox.render.GLVideoEncodeView1;
import com.infowarelab.tvbox.render.GLVideoEncodeView4K;
import com.infowarelab.tvbox.render.GLVideoEncodeViewEx;
import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelab.tvbox.utils.Utils;
import com.infowarelab.tvbox.view.VideoDecodeViewTV;
import com.infowarelabsdk.conference.callback.CallbackManager;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.AudioCommonImpl;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.confctrl.UserCommon;
import com.infowarelabsdk.conference.domain.UserBean;
import com.infowarelabsdk.conference.video.VideoCommon;
import com.mingri.uvc.Uvc;

import java.io.IOException;
import java.net.SocketException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

//import com.infowarelab.tvbox.render.GLVideoEncodeView2;

/**
 * Created by Always on 2018/11/6.
 */
@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
@SuppressLint("ValidFragment")
public class FragVs extends BaseFragment{

    private View vsView;
    private RelativeLayout rlRoot, rlEncoder;
    //private GLVideoEncodeView localCamera;
    //private GLVideoEncodeView localCamera;
    //private GLVideoEncodeViewEx localCamera;
    private BaseVideoEncodeView localCamera;
    private TextView tvMyName;
    private TextView tvNoCamera;
    //private SrsCameraView localCamera1;
    private GLVideoEncodeView1 localCamera1;

    private AudioCommonImpl audioCommon;
    private VideoCommonImpl videoCommon;
    private UserCommonImpl userCommon;
    private ConferenceCommonImpl conferenceCommon;
    private ShareDtCommonImpl shareDtCommon = null;

    private Set<Integer> existingVideos;
    private Set<ImageView> existingDefault;

    private int curMaxVoiceUser = 0;
    private int curSpeakerId = -1;

    private boolean isShare =false;
    private SharedPreferences sharedPreferences;
    //保存异步视频数据
    private List<VideoBean> syncList = new ArrayList<>();
    private List<VideoBean> totalSyncList = new ArrayList<>();
    private List<Integer> uids = new ArrayList<>();
    private int totalNum = 4;
    //轮循路数
    private int loopNum = 0;

    private boolean isSupportSvc = true;
    private int videoEncoderViewType = 0; //0: Camera1 + SurfaceView; 1: Camera1 + GLSurfaceView
    //保存非主讲模式的路数
    private List<VideoBean> noSpeakerList = new ArrayList<>();
//    private int mSampleRateInHZ = 32000;//采样率
//    //音频采集工具类
//    private AudioRecordUtils audioRecordUtils;

    private String rtmpUrl = "rtmp://192.168.2.123:8022/meeting/123456";

    //与Activity交互
    private FragmentInteraction listterner;
    private boolean publishByRTMP = true;
    //private SrsPublisher mPublisher = null;
    private String TAG = "InfowareLab.FragVs";
    private boolean mIsPublished = false;
    private byte[] mMediaHead = null;
    boolean isSpeakerMySelf = false;
    private boolean isSpeakerSet = false;
    private boolean m4KMode = false;
    private boolean mLocalChannelCreated = false;
    private int mainChannelId = -1;
    private int mLocalChannelId = -1;

    public void setShare(boolean share) {
        isShare = share;
    }

    public FragVs(ICallParentView iCallParentView) {
        super(iCallParentView);
        Log.d(TAG,"FragVs construction with parameter");
    }
    //无参构造器
    public FragVs(){
        super();
        Log.d(TAG,"FragVs null construction");
    }
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        Log.d(TAG,"FragVs.onCreateView");
        vsView = inflater.inflate(R.layout.frag_inconf_video, container, false);
        initView();
        initData();
        initVideoHandler();
        return vsView;
    }

    private int lastDownTag = -1;
    private long lastDoubleDownTime = 0;
    private int timeout = 400;
    private int downCount = 0;
    private float lastDownX = 0;
    private boolean isClick = false;

    private void setVideoViewTouch(View v) {
        v.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                int action = event.getAction();
                switch (action) {
                    case MotionEvent.ACTION_DOWN:
                        //pvVideos.requestDisallowInterceptTouchEvent(false);
                        downCount++;
                        lastDownX = event.getX();
                        long currentTime = Calendar.getInstance().getTimeInMillis();

//                        if (v == llNopermission)
//                        {
//                            lastDoubleDownTime = currentTime;
//                            break;
//                        }

                        if ((int) v.getTag() == lastDownTag && (currentTime - lastDoubleDownTime < timeout)) {
                            //oneClickHandler.removeCallbacksAndMessages(null);
                            doDoubleClick(lastDownTag);
                            lastDoubleDownTime = 0;
                        } else {
                            lastDownTag = (int) v.getTag();
                            lastDoubleDownTime = currentTime;
//                            if(!isTile)
//                                oneClickHandler.sendEmptyMessageDelayed(0,timeout);
                        }
                        break;
                    case MotionEvent.ACTION_UP:
                        //if (lastDoubleDownTime != 0)
                        //    oneClickHandler.sendEmptyMessageDelayed(0, timeout);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        float x = Math.abs(event.getX() - lastDownX);
                        if (x > 5) {
                            //pvVideos.requestDisallowInterceptTouchEvent(false);
                            return false;
                        }
                        break;
                }
                return false;
            }
        });
    }

    private void doDoubleClick(int lastDownTag) {
        //if (localCamera != null) localCamera.
    }

    private void initView() {

        Log.d(TAG,"FragVs.initView");
//        audioRecordUtils = new AudioRecordUtils(MediaRecorder.AudioSource.UNPROCESSED);
        if (audioCommon == null){
            audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
        }
        existingVideos = new HashSet<Integer>();
        existingDefault = new HashSet<ImageView>();
        rlRoot = (RelativeLayout) vsView.findViewById(R.id.frag_video_rl_root);
        rlEncoder = (RelativeLayout) vsView.findViewById(R.id.frag_video_rl_camera);
        tvMyName = (TextView) vsView.findViewById(R.id.frag_video_tv_camera);
        tvNoCamera = (TextView) vsView.findViewById(R.id.frag_video_tv_no_camera);

        int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
        int height = SharedPreferencesUrls.getInstance().getInt("height", 720);

        Log.d(TAG,"FragVs.initView: video width = " + width);
        Log.d(TAG,"FragVs.initView: video height = " + height);

        if (width >= 3840 || height >= 2160){
            //4K
            m4KMode = true;
            rlEncoder.removeView(tvMyName);
            rlEncoder.removeView(tvNoCamera);
            localCamera = new GLVideoEncodeView4K(rlEncoder.getContext());
            localCamera.setId(R.id.frag_video_localCamera);
            rlEncoder.addView(localCamera);
            rlEncoder.addView(tvMyName);
            rlEncoder.addView(tvNoCamera);
        }
        else
        {
            m4KMode = false;
            rlEncoder.removeView(tvMyName);
            rlEncoder.removeView(tvNoCamera);
            localCamera = new GLVideoEncodeViewEx(rlEncoder.getContext());
            localCamera.setId(R.id.frag_video_localCamera);
            rlEncoder.addView(localCamera);
            rlEncoder.addView(tvMyName);
            rlEncoder.addView(tvNoCamera);
        }

        //localCamera = (GLVideoEncodeView2) vsView.findViewById(R.id.frag_video_localCamera);
        //localCamera.setCameraLandscape();
        //localCamera.setFlow(false);

        //测试RTMP推送临时代码
        //localCamera1 = (SrsCameraView)vsView.findViewById(R.id.frag_video_localCamera1);
        //localCamera1.setCameraLandscape();
        //localCamera1.setFlow(true);

        localCamera1 = vsView.findViewById(R.id.frag_video_localCamera1);
        localCamera1.setCameraLandscape();
        localCamera1.setFlow(true);
    }
    private void initData() {

        Log.d(TAG,"FragVs.initData");
        videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
        conferenceCommon = (ConferenceCommonImpl) CommonFactory.getInstance().getConferenceCommon();
        shareDtCommon = (ShareDtCommonImpl)CommonFactory.getInstance().getSdCommon();

        sharedPreferences = getActivity().getSharedPreferences("main", Context.MODE_PRIVATE);
        if (conferenceCommon != null){
            isSupportSvc = conferenceCommon.isSupportSvc();
        }else {
            isSupportSvc = true;
        }

        Log.d(TAG,"FragVs.isSupportSvc=" + isSupportSvc);

        if (videoCommon == null){
            videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        }

        //Log.d(TAG,"FragVs.isRemoteEmpty=" + remoteEmpty);

        if (!conferenceCommon.isAcceptSharingDesktop()) {
            boolean syncMyVideo = conferenceCommon.isMyVideoSync();

            //Log.d("InfowareLab.Net", "initData: syncMyVideo = " + syncMyVideo);

            if (syncMyVideo || userCommon.getSelf().getRole() == UserCommon.ROLE_HOST) {

                if (videoCommon.LOCAL_VIDEO_CHANNEL_ID > 0) {
                    if (videoCommon.LOCAL_VIDEO_CHANNEL_ID != localChannelID) {
                        localChannelID = videoCommon.LOCAL_VIDEO_CHANNEL_ID;
                    }
                }

                if (localChannelID > 0) {
                    if (syncMyVideo || userCommon.getSelf().getRole() == UserCommon.ROLE_HOST) {
                        videoCommon.openVideo(localChannelID, (SurfaceView) null);
                        videoCommon.setSyncVedio(localChannelID, true);
                        Log.d(TAG, "FragVs.videoCommon.openVideo(local HOST): " + localChannelID);
                    }
                }
            }
        }

        if (tvMyName != null)
            tvMyName.setText(userCommon.getSelf().getUsername());
    }
    byte action = -1;
    //本地视频通道
    int localChannelID = -1;
    boolean isSigle = false;
    public void initVideoHandler() {
        Handler videoHandler = new Handler() {
            @Override
            public void handleMessage(final Message msg) {
                if (getActivity() == null) return;
                super.handleMessage(msg);
                switch (msg.what) {
                    case VideoCommon.VIDEO_ADD_CHANNEL:
                        Log.d(TAG,"FragVs.VideoCommon.VIDEO_ADD_CHANNEL: " + videoCommon.getSyncMap().size());
                        /*if ( userCommon.getSelf().getRole() == UserCommon.ROLE_HOST || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                            if (videoCommon.getSyncMap().size() == 1)
                            {
                                if (ActConf.mActivity != null){
                                    if (!ActConf.mActivity.isAcLeave){
                                        isSigle = true;
                                        openLocalVideo();
                                        if (ActConf.mActivity.rbMicSet != null){
                                            ActConf.mActivity.rbMicSet.setChecked(true);
                                        }
                                        changeRatio(videoCommon.getSyncMap().size());
                                    }
                                }
                            }
                        }*/

                        callParentView(ACTION_VSSHARED, null);
                        updateVideoShowEnter();
                        if (!isSigle){
//                            changeRatio(videoCommon.getSyncMap().size());
                        }
                        localCamera.AnalyzeEncodeSizeEx();
                        break;
                    case VideoCommon.VIDEO_REMOVE_CHANNEL:
                        Log.d(TAG,"FragVs.VideoCommon.VIDEO_REMOVE_CHANNEL: " + videoCommon.getSyncMap().size());
                        if (!CallbackManager.IS_LEAVED) {
                            callParentView(ACTION_VSSHARED, null);
                            updateVideoShowEnter();
                            localCamera.AnalyzeEncodeSizeEx();
                        }
                        break;
                    case VideoCommon.VIDEO_LOCAL_CHANNELID:
                        //localChannelID = msg.arg1;
//                      //videoCommon.removeLocalChannel(msg.arg1);
                        Log.d(TAG, "FragVs.VideoCommon.VIDEO_LOCAL_CHANNELID");
                        //Log.d(TAG, "FragVs.VideoCommon.VIDEO_LOCAL_CHANNELID");
                        /*Log.d(TAG, "FragVs.isRemoteEmpty() = "+isRemoteEmpty());*/

                        if (!conferenceCommon.isAcceptSharingDesktop()) {
                            boolean syncMyVideo = conferenceCommon.isMyVideoSync();

                            Log.d("InfowareLab.Net", "VIDEO_LOCAL_CHANNELID: syncMyVideo = " + syncMyVideo);

                            if (userCommon != null && (syncMyVideo || userCommon.getSelf().getRole() == UserCommon.ROLE_HOST)) {
                                if (localChannelID != msg.arg1) {
                                    localChannelID = msg.arg1;
                                    if (localChannelID > 0) {
                                        videoCommon.openVideo(localChannelID, (SurfaceView) null);
                                        videoCommon.setSyncVedio(localChannelID, true);
                                        Log.d(TAG, "FragVs.videoCommon.openVideo(local HOST): " + localChannelID);
                                    }
                                }
                            }
                        }

                        break;
                    case VideoCommon.VIDEO_REMOVE_DEVICE:
                        break;
                    case VideoCommon.VIDEO_RESET_SIZE:

                        break;
                    case VideoCommon.VIDEO_DECODER_SIZE:
                        int[] param1 = (int[]) msg.obj;
                        int channelid = msg.arg1;
                        VideoDecodeViewTV videoDecoderView = findViewByChannelid(channelid);
                        if (null == videoDecoderView) {
                        } else if (!videoCommon.isHardDecode() && param1[0] * param1[1] > 1024 * 768) {
                            videoDecoderView.showSupport(false);
                            TextView tvWaiting = findWaitingViewByChannelid(msg.arg1);
                            if (tvWaiting != null) tvWaiting.setVisibility(View.GONE);

                        } else {
                            videoDecoderView.resetSize(param1[0], param1[1]);
                            videoDecoderView.showSupport(true);
                        }
                        break;
                    case VideoCommon.VIDEO_LOCAL_CHANNEL:
                        Log.d(TAG,"FragVs.VideoCommon.VIDEO_LOCAL_CHANNEL: "+((Boolean) msg.obj));
                        if ((Boolean) msg.obj) {
                            if (ActConf.mActivity != null){
                                if (!ActConf.mActivity.isAcLeave){
                                    isSigle = true;
                                    openLocalVideo();
                                    if (ActConf.mActivity.rbMicSet != null){
                                        ActConf.mActivity.rbMicSet.setChecked(true);
                                    }
                                    changeRatio(videoCommon.getSyncMap().size());
                                }
                            }
                        } else{
                            closeLocalVideo();
                            if (ActConf.mActivity != null && ActConf.mActivity.rbMicSet != null){
                                ActConf.mActivity.rbMicSet.setChecked(false);
                            }
                            if (ActConf.isShare){
                                localCamera1.reStartLocalView();
                            }
                        }
                        break;
                    case VideoCommon.VIDEO_KEYFRAME:
                        if (localCamera != null) localCamera.flushEncoder();
                        break;
                    case VideoCommon.VIDEO_LOCAL_RESTART:
                        Log.d(TAG, "FragVs.getMessage: VIDEO_LOCAL_RESTART");
                        localCamera.reStartLocalView();
                        if (msg.arg2 > 0){
                            if (240 >= msg.arg2 && 480 > msg.arg2){
                                SharedPreferencesUrls.getInstance().putInt("position",0);
                                SharedPreferencesUrls.getInstance().putInt("width",320);
                                SharedPreferencesUrls.getInstance().putInt("height",240);
                            }else if (480 >= msg.arg2 && 720 > msg.arg2){
                                SharedPreferencesUrls.getInstance().putInt("position",1);
                                SharedPreferencesUrls.getInstance().putInt("width",640);
                                SharedPreferencesUrls.getInstance().putInt("height",480);
                            }else if (720 >= msg.arg2 && 1080 > msg.arg2){
                                SharedPreferencesUrls.getInstance().putInt("position",2);
                                SharedPreferencesUrls.getInstance().putInt("width",1280);
                                SharedPreferencesUrls.getInstance().putInt("height",720);
                            }else if (1080 >= msg.arg2){
                                SharedPreferencesUrls.getInstance().putInt("position",3);
                                SharedPreferencesUrls.getInstance().putInt("width",1920);
                                SharedPreferencesUrls.getInstance().putInt("height",1080);
                            }
                        }
                        break;
                    case VideoCommon.LOCAL_RESOLUTION_CHANGE:
                        Log.d(TAG, "FragVs.getMessage: LOCAL_RESOLUTION_CHANGE");

                        localCamera.AnalyzeEncodeSize(msg.arg1, msg.arg2);

//                        localCamera.reStartLocalView();
//                        if (msg.arg2 > 0){
//                            if (240 >= msg.arg2 && 480 > msg.arg2){
//                                SharedPreferencesUrls.getInstance().putInt("position",0);
//                                SharedPreferencesUrls.getInstance().putInt("width",320);
//                                SharedPreferencesUrls.getInstance().putInt("height",240);
//                            }else if (480 >= msg.arg2 && 720 > msg.arg2){
//                                SharedPreferencesUrls.getInstance().putInt("position",1);
//                                SharedPreferencesUrls.getInstance().putInt("width",640);
//                                SharedPreferencesUrls.getInstance().putInt("height",480);
//                            }else if (720 >= msg.arg2 && 1080 > msg.arg2){
//                                SharedPreferencesUrls.getInstance().putInt("position",2);
//                                SharedPreferencesUrls.getInstance().putInt("width",1280);
//                                SharedPreferencesUrls.getInstance().putInt("height",720);
//                            }else if (1080 >= msg.arg2){
//                                SharedPreferencesUrls.getInstance().putInt("position",3);
//                                SharedPreferencesUrls.getInstance().putInt("width",1920);
//                                SharedPreferencesUrls.getInstance().putInt("height",1080);
//                            }
//                        }
                        break;
                    case VideoCommon.VIDEO_READY:
                        VideoDecodeViewTV videoMainView = findViewByChannelid(msg.arg1);
                        if (null != videoMainView) {
                            videoMainView.showSupportReady();
                        }

                        TextView tvWaiting = findWaitingViewByChannelid(msg.arg1);
                        if (tvWaiting != null) tvWaiting.setVisibility(View.GONE);

                        break;
                    case VideoCommon.VIDEO_NOPERMISSION:
                        break;
                    case VideoCommon.VIDEO_PERMISSION:
                        break;
                    case VideoCommon.VIDEO_CHANGE_SVC:
                        break;
                    case VideoCommon.VIDEO_PREVIEW_PRIVILEDGE:
                        break;
                    case VideoCommon.VIDEO_MODE_LAYOUT:
                        Log.d(TAG,"VIDEO_MODE_LAYOUT: n = " + msg.arg1);
//                        Log.e("UUUUUU","88888888888::::"+msg.arg1);
//                        curSpeakerId = msg.arg1;
//                        updateVideoShowEnter();
                        localCamera.AnalyzeEncodeSizeEx();
                        break;
                    case VideoCommon.VIDEO_COUNT_CHANGE:
                        Log.d(TAG,"VIDEO_COUNT_CHANGE: count = " + msg.arg1);
//                        curSpeakerId = msg.arg1;
//                        updateVideoShowEnter();
                        localCamera.AnalyzeEncodeSizeEx();
                        break;
                    case VideoCommon.VIDEO_VIDEO_SINGLE:
                        callParentView(ACTION_VSSHARED, null);
                        updateVideoShowEnter();
                        if ((Boolean)msg.obj){
                            for (Integer key : videoCommon.getSyncMap().keySet()){
                                if (userCommon.getSelf().getUid() == videoCommon.getSyncMap().get(key)){
                                    changeRatio(1);
                                }
                            }
                        }else {
                            changeRatio(videoCommon.getSyncMap().size());
                        }
                        break;
                    //云台控制
                    case VideoCommon.VIDEO_VIDEO_PTZ:
                        for (Integer key : videoCommon.getDeviceMap().keySet()){
                            if (key == msg.arg1){
                                control(msg);
                            }
                        }
                        break;
                    case VideoCommon.VIDEO_LOCAL_RESTART_SWITCHENCODE:
//                        localCamera.switchSoftEncode(msg.arg1 == 1);
//                        localCamera1.switchSoftEncode(msg.arg1 == 1);
                        break;
                    default:
                        break;
                }
            }
        };
        if (videoCommon == null) {
            videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        }
        videoCommon.setHandler(videoHandler);
    }
    //云台控制
    private void control(Message msg){
        if (null != localCamera){
            switch (msg.arg2){
                //向左
                case 1:
                    action = Uvc.PTZ_LEFT;
                    break;
                //向右
                case 2:
                    action = Uvc.PTZ_RIGHT;
                    break;
                //向上
                case 3:
                    action = Uvc.PTZ_UP;
                    break;
                //向下
                case 4:
                    action = Uvc.PTZ_DOWN;
                    break;
                //缩小
                case 5:
                    action = Uvc.ZOOM_DEC;
                    break;
                //放大
                case 6:
                    action = Uvc.ZOOM_ADD;
                    break;
                default:
                    break;
            }
//            //开始云台控制
//            if (-1 != action){
//                localCamera.startPtz(action);
//            }
//            if (msg.arg2 == 7 || msg.arg2 == 8){
//                //停止云台控制
//                if (-1 != action){
//                     localCamera.stopPtz(action);
//                }
//            }
        }
    }
    //主讲模式
    public void setSpeak(int curSpeakerId){

        Log.d(TAG,"Event(4): FragVs.setSpeak: " + curSpeakerId);

        this.curSpeakerId = curSpeakerId;
        listterner.setBuJuType(1);
        updateVideoShowEnter();

        boolean speaker = videoCommon.isSpeakerMyself();

        Log.d(TAG,"Event(4): FragVs.setSpeak:isSpeakerMyself= " + speaker);

        if (isSpeakerSet) {
            if (speaker != isSpeakerMySelf) {

                isSpeakerMySelf = speaker;
                if (localCamera != null) localCamera.AnalyzeEncodeSizeEx();

                isSpeakerSet = true;
            }
        }
        else
        {
            isSpeakerMySelf = speaker;
            if (localCamera != null) localCamera.AnalyzeEncodeSizeEx();

            isSpeakerSet = true;
        }

    }
    public boolean getMax() {
        if (videoCommon != null && videoCommon.getSingleChannel() > 0) {
            return true;
        } else {
            return false;
        }
    }
    public void retCamera(){
        if (localCamera != null){
            updateVideoShowEnter();
        }
    }
    private void updateVideoShowEnter() {

        Log.d(TAG,"FragVs.updateVideoShowEnter: isShareFlow=" + isShare);

        if (!isShare){
//            audioRecordUtils.pauseRecord();
            if (audioCommon == null){
                audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
            }
            if (!SharedPreferencesUrls.getInstance().getBoolean("isOpenMic",false)){
                if (audioCommon != null)
                    audioCommon.stopSend();
            }else{
                if (audioCommon != null)
                    audioCommon.startReceive();
                    audioCommon.startSend();
            }
            if (localCamera1 != null){
                localCamera1.setParams(1,1);
                localCamera1.setSharing(false);
                localCamera1.changeStatus(false);
                //localCamera1.stopCamera();
                localCamera1.setVisibility(View.GONE);

                //测试RTMP推送临时代码
//                if (publishByRTMP) {
//                    if (mIsPublished) {
//                        mPublisher.stopPublish();
//                        mPublisher.stopCamera();
//                        mIsPublished = false;
//                    }
//                }
            }
            if (videoCommon.getSingleChannel() > 0) {
                if (rlRoot.getWidth() < ConferenceApplication.Screen_W / 2) {
                    callParentView(ACTION_VSSHARED, null);
                    return;
                }
            } else if (isEmpty()) {
                if (rlRoot.getWidth() > 10) {
                    callParentView(ACTION_VSSHARED, null);
                    updateVideoShow(false);
                    return;
                }
            } else {
                if (rlRoot.getWidth() < 10) {
                    callParentView(ACTION_VSSHARED, null);
                    return;
                }
            }
            if (isHidden()) return;
            updateVideoShow(true);
        }else {
            //设置线程优先级，android.os.Process.THREAD_PRIORITY_AUDIO-标准音乐播放使用的线程优先级
            android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_AUDIO);
            if (localCamera1 != null){
                localCamera1.setVisibility(View.VISIBLE);
                localCamera1.reStartLocalView();

                //测试RTMP推送的临时代码
//                if (publishByRTMP) {
//                    if (mPublisher == null) {
//                        mPublisher = new SrsPublisher(localCamera1);
//
//                        mPublisher.setEncodeHandler(new SrsEncodeHandler(this));
//                        mPublisher.setEncoderCallback(this);
//                        mPublisher.setRtmpHandler(new RtmpHandler(this));
//                        mPublisher.setRecordHandler(new SrsRecordHandler(this));
//                        mPublisher.setPreviewResolution(1920, 1080);
//                        mPublisher.setOutputResolution(1920, 1080); // 这里要和preview反过来
//                        mPublisher.setVideoHDMode();
//                    }
//
//                    if (!mIsPublished) {
//                        mPublisher.startPublish(rtmpUrl);
//                        mPublisher.startCamera();
//                        mIsPublished = true;
//                    }
//                }

            }
            if (audioCommon == null){
                audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
            }

           //修改分辨率
//            if (localCamera != null){
//                if (Camera.getNumberOfCameras() > 1 && Build.MODEL.indexOf("YT-500") != -1){
//                    localCamera.setStreamVideoSize(640,480);
//                }
//            }
            //开始录制
//            audioRecordUtils.startRecord();
            removeUselessView(videoCommon.getSyncMap());
            removeDefault();
            syncList.clear();
            uids.clear();
            totalSyncList.clear();
            for (Integer key : videoCommon.getSyncMap().keySet()){
                uids.add(videoCommon.getSyncMap().get(key));
                VideoBean bean = new VideoBean();
                bean.setChannelid(key);
                bean.setUid(videoCommon.getSyncMap().get(key));
                totalSyncList.add(bean);
            }
            if (uids.contains(userCommon.getSelf().getUid())){
                totalNum = 3;
            }else {
                if (totalSyncList.size() != 0){
                    totalNum = 4;
                }else {
                    totalNum = 0;
                }
            }
            for (VideoBean bean : totalSyncList){
                if (userCommon.getSelf().getUid() == bean.getUid()){
                    continue;
                }
//                if (syncList.size() > totalNum){
//                    return;
//                }
                syncList.add(bean);
            }

            addVideo();

            if (!SharedPreferencesUrls.getInstance().getBoolean("isOpenMic",false)){
                if (audioCommon != null)
                    audioCommon.stopSend();
            }else{
                if (audioCommon != null) {
                    audioCommon.startReceive();
                    audioCommon.startSend();
                }
            }
        }
    }

    private void updateVideoShow(boolean isShow){
        if (!isAdded()) return;
        if (isShow) {
            boolean isLocalSync = false;
            mainChannelId = -1;
            int num = 0;

            boolean existSpeakerId = true;

            boolean isFull = !(rlRoot.getWidth() < ConferenceApplication.Root_W / 2);
            HashMap<Integer, Integer> mm = new HashMap<Integer, Integer>();
            mm.putAll(videoCommon.getSyncMap());

            Log.d(TAG, ">>>>>>updateVideoShow: syncMap size = " + mm.size());

            int mainUid = 0;
            if (videoCommon.getSingleChannel() > 0) {
                int c = videoCommon.getSingleChannel();
                if (!mm.containsKey(c)){
                    SharedPreferencesUrls.getInstance().putBoolean("flag",true);
                    mainChannelId = 0;
                    updateSyncVideos1(mm, false, mainChannelId, true);
                }else if (mm.get(c) == userCommon.getSelf().getUid()){
                    SharedPreferencesUrls.getInstance().putBoolean("flag",false);
                    mainChannelId = 0;
                    mm.clear();
                    updateSyncVideos(mm, false, mainChannelId, true);
                }else {
                    SharedPreferencesUrls.getInstance().putBoolean("flag",false);
                    mainChannelId = c;
                    int uid = mm.get(c);
                    mm.clear();
                    mm.put(c, uid);
                    updateSyncVideos(mm, false, mainChannelId, false);
                }
                return;
            } else if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_PLAIN) {
                mainUid = 0;
                curMaxVoiceUser = 0;
                curSpeakerId = -1;
                listterner.setBuJuType(0);
                SharedPreferencesUrls.getInstance().putInt("curSpeakerId",-1);
            } else if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_VOICE) {
                validateCurMaxVoiceUser();
                mainUid = curMaxVoiceUser;
                curSpeakerId = -1;
                listterner.setBuJuType(3);
                SharedPreferencesUrls.getInstance().putInt("curSpeakerId",-1);
            } else if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_SPEAKER) {
                curMaxVoiceUser = 0;
                listterner.setBuJuType(1);
                if (curSpeakerId < 0) {
                    if (-1 != SharedPreferencesUrls.getInstance().getInt("curSpeakerId", -1)) {
                        curSpeakerId = SharedPreferencesUrls.getInstance().getInt("curSpeakerId", -1);
                    }
                }

                if (mm.containsKey(curSpeakerId)) {
                    mainUid = mm.get(curSpeakerId);
                    existSpeakerId = true;
                } else {

                    existSpeakerId = false;
                    //
                    Iterator<Map.Entry<Integer, Integer>> it = mm.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry<Integer, Integer> item = it.next();
                        int cid = item.getKey();
                        int uid = item.getValue();

                        mainUid = uid;
                    }
                    //mainUid = 0;
                }
            }
            if (mm.containsValue(userCommon.getSelf().getUid())) {
                isLocalSync = true;
                num++;
            }
            int limit = 0;
            boolean isSpeakerMode = mainUid != 0 ? true : false;
            if (videoCommon.getSyncMap().size() > 6){
                if (isSpeakerMode){
                    //主讲模式最大8路
                    limit = isFull ? 8 : 4;
                }else {
                    limit = isFull ? 9 : 4;
                }
            }else {
                limit = isFull ? 6 : 4;
            }
            listterner.getLimit(limit);
            if (mm.containsValue(mainUid)) {
                if (mainUid == userCommon.getOwnID()) {
                    Iterator<Map.Entry<Integer, Integer>> it = mm.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry<Integer, Integer> item = it.next();
                        int cid = item.getKey();
                        int uid = item.getValue();
                        if (uid == userCommon.getOwnID()) {
                            mainChannelId = cid;
                            if (limit < 7){
                                mainChannelId = 0;
                                it.remove();
                            }
                        } else if (num >= limit) {
                            it.remove();
                        } else {
                            num++;
                        }
                    }
                } else {
                    int backChannelId = -1;
                    mainChannelId = -1;

                    Iterator<Map.Entry<Integer, Integer>> it = mm.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry<Integer, Integer> item = it.next();
                        int cid = item.getKey();
                        int uid = item.getValue();
                        if (uid == mainUid) {

                            if (curSpeakerId == cid)
                                mainChannelId = cid;
                            else if (mainChannelId < 0)
                                mainChannelId = cid;
                            else
                                backChannelId = cid;

                            num++;
                        } else if (uid == userCommon.getOwnID()) {
                            if (limit < 7){
                                it.remove();
                            }
                        } else if (num >= (limit - 1) && mainChannelId == -1) {
                            it.remove();
                        } else if (num >= limit && mainChannelId != -1) {
                            it.remove();
                        } else {
                            num++;
                        }
                    }

                    if (mainChannelId < 0) {
                        mainChannelId = backChannelId;
                        if (!existSpeakerId) curSpeakerId = backChannelId;
                    }
                }
            } else {

                mainChannelId = -1;

                Iterator<Map.Entry<Integer, Integer>> it = mm.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<Integer, Integer> item = it.next();
                    int cid = item.getKey();
                    int uid = item.getValue();

                    if (isSpeakerMode) {
                        if (mainChannelId < 0) {
                            curMaxVoiceUser = uid;
                            mainChannelId = cid;
                        }
                    }

                    if (uid == userCommon.getOwnID()) {
                        if (limit < 7){
                            it.remove();
                        }

                    } else if (num >= limit) {
                        it.remove();
                    } else {
                        num++;
                    }
                }
            }
            updateSyncVideos(mm, false, mainChannelId, isLocalSync);
        } else {
            updateSyncVideos(new HashMap<Integer, Integer>(), true, 0, false);
        }
    }

    private void validateCurMaxVoiceUser() {
        if (curMaxVoiceUser > 0 || videoCommon == null) return;

        for (Integer key : videoCommon.getSyncMap().keySet()) {
            curMaxVoiceUser = videoCommon.getSyncMap().get(key);
            return;
        }

        Log.d(TAG, ">>>>>> fragVs.validateCurMaxVoiceUser: curMaxVoiceUser = " + curMaxVoiceUser);
    }

    private void updateSyncVideos(Map<Integer, Integer> syncMap, boolean isHidden, int hostChannelId, boolean isLocalSync) {
        int rootW = rlRoot.getWidth();
        removeUselessView(syncMap);
        removeDefault();
        int count = 1;
        int num = syncMap.size() + (isLocalSync ? 1 : 0);

        Log.d(TAG, ">>>>>> updateSyncVideos: rootW = " + rootW);
        Log.d(TAG, ">>>>>> updateSyncVideos: ConferenceApplication.Root_W / 2 = " + ConferenceApplication.Root_W / 2);

        boolean isFull = !(rootW < ConferenceApplication.Root_W / 2);
        boolean isSpeakerMode = hostChannelId != -1 ? true : false;

        Log.d(TAG, ">>>>>>updateSyncVideos: syncMap num = " + num);

        if (num > 6){
            //6路视频以上
            updateVideos(syncMap);
        }else {
            //6路视频以下
            if (num == 0 && isFull) {
                callLocalCamera(1, 1, isFull, isSpeakerMode, true);
                changeRatio(1);
            } else if (hostChannelId > 0) {
                callLocalCamera(num, 2, isFull, isSpeakerMode, isLocalSync);
            } else {
                callLocalCamera(num, 1, isFull, isSpeakerMode, isLocalSync);
            }
            for (Integer key : syncMap.keySet()) {
                if (userCommon.getSelf().getUid() == syncMap.get(key)) {
                    continue;
                }
                int uid = syncMap.get(key);
                if (isLocalSync) {
                    if (isSpeakerMode) {
                        if (key == hostChannelId) {
                            addVideo(num, 1, key, uid, isFull, isSpeakerMode);
                        } else if (hostChannelId == 0) {
                            addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                            count++;
                        } else {
                            addVideo(num, count + 2, key, uid, isFull, isSpeakerMode);
                            count++;
                        }
                    } else {
                        addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                        count++;
                    }
                } else {
                    if (isSpeakerMode) {
                        if (key == hostChannelId) {
                            addVideo(num, 1, key, uid, isFull, isSpeakerMode);
                        } else {
                            addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                            count++;
                        }
                    } else {
                        addVideo(num, count, key, uid, isFull, isSpeakerMode);
                        count++;
                    }
                }
            }
            if(num>0){
                addDefault(num,num+1,isFull,isSpeakerMode);
            }
        }
    }
    private void updateSyncVideos1(Map<Integer, Integer> syncMap, boolean isHidden, int hostChannelId, boolean isLocalSync) {
        int rootW = rlRoot.getWidth();

        removeUselessView(syncMap);
        removeDefault();

        int count = 1;
        int num = syncMap.size();
        boolean isFull = !(rootW < ConferenceApplication.Root_W / 2);
        boolean isSpeakerMode = hostChannelId != -1 ? true : false;
        if (num == 0 && isFull) {
            callLocalCamera(1, 1, isFull, isSpeakerMode, true);
        } else if (hostChannelId > 0) {
            callLocalCamera(num, 2, isFull, isSpeakerMode, isLocalSync);
        } else {
            callLocalCamera(num, 1, isFull, isSpeakerMode, isLocalSync);
        }

        for (Integer key : syncMap.keySet()) {
            if (userCommon.getSelf().getUid() == syncMap.get(key)) {
                continue;
            }
            int uid = syncMap.get(key);
            if (isLocalSync) {
                if (isSpeakerMode) {
                    if (key == hostChannelId) {
                        addVideo(num, 1, key, uid, isFull, isSpeakerMode);
                    } else if (hostChannelId == 0) {
                        addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                        count++;
                    } else {
                        addVideo(num, count + 2, key, uid, isFull, isSpeakerMode);
                        count++;
                    }
                } else {
                    addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                    count++;
                }
            } else {
                if (isSpeakerMode) {
                    if (key == hostChannelId) {
                        addVideo(num, 1, key, uid, isFull, isSpeakerMode);
                    } else {
                        addVideo(num, count + 1, key, uid, isFull, isSpeakerMode);
                        count++;
                    }
                } else {
                    addVideo(num, count, key, uid, isFull, isSpeakerMode);
                    count++;
                }
            }
        }
        if(num>0){
            addDefault(num,num+1,isFull,isSpeakerMode);
        }
    }
    private void callLocalCamera(int total, int count, boolean isFull, boolean isSpeakerMode, boolean isShow) {
        int left = 0, right = 0, top = 0, bottom = 0, w = 0, h = 0;
        //int p = getResources().getDimensionPixelOffset(R.dimen.dp_2);
//        int rootW = rlRoot.getWidth();
//        int rootH = rlRoot.getHeight() +Utils.getNavigationBarHeight(getActivity()) ;
        int rootW = DensityUtil.getWindowWidth(getActivity());
        int rootH = DensityUtil.getWindowHeight(getActivity())+Utils.getNavigationBarHeight(getActivity());
        if (isShow) {
            if (isFull) {
                if (isSpeakerMode) {
                    if (total == 1) {
                        left = 0;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW;
                        h = rootH;
                    } else if (total > 4) {
                        if (count == 1) {
                            left = 0;
                            top = 0;
                            right = 0;
                            bottom = 0;
                            w = rootW * 2 / 3;
                            h = rootH * 2 / 3;
                        } else {
                            left = rootW * 2 / 3 - (count / 5 + count / 6) * rootW / 3;
                            top = ((count - 1) / 3 + (count + 1) / 4) * rootH / 3;
                            right = 0;
                            bottom = 0;
                            w = rootW / 3;
                            h = rootH / 3;
                        }
                    } else {
                        if (count == 1) {
                            left = rootW / 6;
                            top = 0;
                            right = 0;
                            bottom = 0;
                            w = rootW * 2 / 3;
                            h = rootH * 2 / 3;
                        } else {
                            left = (count - 2) * rootW / 3;
                            top = rootH * 2 / 3;
                            right = 0;
                            bottom = 0;
                            w = rootW / 3;
                            h = rootH / 3;
                        }
                    }
                } else {
                    if (total == 1) {
                        left = 0;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW;
                        h = rootH;
                        changeRatio(1);
                    } else if (total == 2) {
                        left = (count - 1) * rootW / 2;
//                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW / 2;
//                        h = rootH;
                        //h = w * 9/15+DensityUtil.dip2px(getActivity(),8);
                        h = w * 9/16;
                        top = (rootH - h)/2;
                    } else if (total == 3) {
                        if (count == 1) {
                            left = rootW / 4;
                            top = 0;
                            right = 0;
                            bottom = 0;
                            w = rootW / 2;
                            h = rootH / 2;
                        } else {
                            left = (count - 2) * rootW / 2;
                            top = rootH / 2;
                            right = 0;
                            bottom = 0;
                            w = rootW / 2;
                            h = rootH / 2;
                        }
                    } else if (total == 4) {
                        left = (count - 1) % 2 * rootW / 2 + ((count - 1) / 4) * rootW;
                        top = ((count - 1) % 4 / 2) * (rootH / 2);
                        right = 0;
                        bottom = 0;
                        w = rootW / 2;
                        h = rootH / 2;
                    } else {
                        left = (count - 1) % 3 * rootW / 3 + ((count - 1) / 6) * rootW;
                        top = rootH / 6 + (count / 4) * rootH / 3;
                        right = 0;
                        bottom = 0;
                        w = rootW / 3;
                        h = rootH / 3;
                    }
                }
            } else {
                left = 0;
                top = (count - 1) * rootH / 4;
                right = 0;
                bottom = 0;
                w = rootW;
                h = rootH / 4;
            }
        } else {
            left = 0;
            top = 0;
            right = 0;
            bottom = 0;
            w = 1;
            h = 1;
        }
        Log.d(TAG,"FragVs: local camera width = "+w);
        Log.d(TAG,"FragVs: local camera height = "+h);
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rlEncoder.getLayoutParams();
        params.setMargins(left, top, right, bottom);
        params.width = w;
        params.height = h;
        rlEncoder.setLayoutParams(params);
        localCamera.setParams(w,h);

        if (!localCamera.existCamera()) {
            if (tvNoCamera != null) tvNoCamera.setVisibility(View.VISIBLE);
            callParentView(ACTION_HIDE_VIDEO, null);
//            if (mLocalChannelCreated){
//                if (videoCommon != null)
//                    videoCommon.removeLocalChannel();
//            }
        }
        else
        {
            if (tvNoCamera != null) tvNoCamera.setVisibility(View.GONE);
//            if (!mLocalChannelCreated){
//                if (videoCommon != null)
//                   videoCommon.createLocalChannel();
//
//                mLocalChannelCreated = true;
//            }
        }
    }
    //修改分辨率
    public void changeRatio(int total){
//        if (userCommon.getSelf() != null){
//            for (Integer key : videoCommon.getSyncMap().keySet()){
//                if (userCommon.getSelf().getUid() == videoCommon.getSyncMap().get(key)){
//                    int width = SharedPreferencesUrls.getInstance().getInt("width", 0);
//                    int height = SharedPreferencesUrls.getInstance().getInt("height", 0);
//                    switch (total){
//                        case 0:
//                        case 1:
//                            localCamera.setStreamVideoSize(width,height);
//                            break;
//                        case 2:
//                            if (width * height >= 1280 * 720){
//                                localCamera.setStreamVideoSize(1280,720);
//                            }
//                            break;
//                        case 3:
//                        case 4:
//                            if (width * height >= 640 * 480){
//                                localCamera.setStreamVideoSize(640,480);
//                            }
//                            break;
//                        case 5:
//                        case 6:
//                        case 7:
//                        case 8:
//                        case 9:
//                            if (key == curSpeakerId){
//                                if (width * height >= 1280 * 720){
//                                    localCamera.setStreamVideoSize(1280,720);
//                                }
//                            }else {
//                                if (width * height >= 640 * 360){
//                                    localCamera.setStreamVideoSize(640,360);
//                                }
//                            }
//                            if (userCommon.getSelf().getUid() == curMaxVoiceUser){
//                                if (width * height >= 1280 * 720){
//                                    localCamera.setStreamVideoSize(1280,720);
//                                }
//                            }else {
//                                if (width * height >= 640 * 360){
//                                    localCamera.setStreamVideoSize(640,360);
//                                }
//                            }
//                            break;
//                        default:
//                            break;
//                    }
//                }
//            }
//        }
    }
    public void changeRatio(int total,int model){
//        for (Integer key : videoCommon.getSyncMap().keySet()){
//            if (userCommon.getSelf().getUid() == videoCommon.getSyncMap().get(key)){
//                int width = SharedPreferencesUrls.getInstance().getInt("width", 0);
//                int height = SharedPreferencesUrls.getInstance().getInt("height", 0);
//                if (model != ActConf.MODEL_AS_VS && model != ActConf.MODEL_DS_VS){
//                    switch (total){
//                        case 0:
//                        case 1:
//                            localCamera.setStreamVideoSize(width,height);
//                            break;
//                        case 2:
//                            if (width * height >= 1280 * 720){
//                                localCamera.setStreamVideoSize(1280,720);
//                            }
//                            break;
//                        case 3:
//                        case 4:
//                            if (width * height >= 960 * 540){
//                                localCamera.setStreamVideoSize(960,540);
//                            }
//                            break;
//                        case 5:
//                        case 6:
//                        case 7:
//                        case 8:
//                        case 9:
//                            if (key == curSpeakerId){
//                                if (width * height >= 1280 * 720){
//                                    localCamera.setStreamVideoSize(1280,720);
//                                }
//                            }else {
//                                if (width * height >= 640 * 360){
//                                    localCamera.setStreamVideoSize(640,360);
//                                }
//                            }
//                            if (userCommon.getSelf().getUid() == curMaxVoiceUser){
//                                if (width * height >= 1280 * 720){
//                                    localCamera.setStreamVideoSize(1280,720);
//                                }
//                            }else {
//                                if (width * height >= 640 * 360){
//                                    localCamera.setStreamVideoSize(640,360);
//                                }
//                            }
//                            break;
//                        default:
//                            break;
//                    }
//                }else {
//                    Log.e("ttttt","**********");
//                    localCamera.setStreamVideoSize(640,360);
//                }
//            }
//        }
    }
    private void addVideo(int total, int count, int channelid, int uid, boolean isFull, boolean isSpeakerMode) {
        if (count > total) return;
        int p = getResources().getDimensionPixelOffset(R.dimen.dp_2);
//        int rootW = rlRoot.getWidth();
//        int rootH = rlRoot.getHeight() +Utils.getNavigationBarHeight(getActivity()) ;
        int rootW = DensityUtil.getWindowWidth(getActivity());
        int rootH = DensityUtil.getWindowHeight(getActivity())+Utils.getNavigationBarHeight(getActivity());
        int left = 0, right = 0, top = 0, bottom = 0, w = 0, h = 0;
        if (isFull) {
            if (isSpeakerMode) {
                if (total == 1) {
                    left = 0;
                    top = 0;
                    right = 0;
                    bottom = 0;
                    w = rootW;
                    h = rootH;
                } else if (total > 4) {
                    if (count == 1) {
                        left = 0;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW * 2 / 3;
                        h = rootH * 2 / 3;
                    } else {
                        left = rootW * 2 / 3 - (count / 5 + count / 6) * rootW / 3;
                        top = ((count - 1) / 3 + (count + 1) / 4) * rootH / 3;
                        right = 0;
                        bottom = 0;
                        w = rootW / 3;
                        h = rootH / 3;
                    }
                } else {
                    if (count == 1) {
                        left = rootW / 6;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW * 2 / 3;
                        h = rootH * 2 / 3;
                    } else {
                        left = (count - 2) * rootW / 3;
                        top = rootH * 2 / 3;
                        right = 0;
                        bottom = 0;
                        w = rootW / 3;
                        h = rootH / 3;
                    }
                }
            } else {
                if (total == 1) {
                    left = 0;
                    top = 0;
                    right = 0;
                    bottom = 0;
                    w = rootW;
                    h = rootH;
                } else
                    if (total == 2) {
                    left = (count - 1) * rootW / 2;
//                    top = 0;
                    right = 0;
                    bottom = 0;
                    w = rootW / 2;
//                    h = rootH;
                    h = w*9/16;
                    top = (rootH - h)/2 ;//+DensityUtil.dip2px(getActivity(),20);
                } else if (total == 3) {
                    if (count == 1) {
                        left = rootW / 4;
                        top = 0;
                        right = 0;
                        bottom = 0;
                        w = rootW / 2;
                        h = rootH / 2;
                    } else {
                        left = (count - 2) * rootW / 2;
                        top = rootH / 2;
                        right = 0;
                        bottom = 0;
                        w = rootW / 2;
                        h = rootH / 2;
                    }

                } else if (total == 4) {
                    left = (count - 1) % 2 * rootW / 2 + ((count - 1) / 4) * rootW;
                    top = ((count - 1) % 4 / 2) * (rootH / 2);
                    right = 0;
                    bottom = 0;
                    w = rootW / 2;
                    h = rootH / 2;
                } else {
                    left = (count - 1) % 3 * rootW / 3 + ((count - 1) / 6) * rootW;
                    top = rootH / 6 + (count / 4) * rootH / 3;
                    right = 0;
                    bottom = 0;
                    w = rootW / 3;
                    h = rootH / 3;
                }
            }

        } else {
            left = 0;
            top = (count - 1) * rootH / 4;
            right = 0;
            bottom = 0;
            w = rootW;
            h = rootH / 4;
        }
        UserBean user = userCommon.getUser(uid);
        String name = user.getUsername();
        boolean isHost = false;
        if (user.getRole() == UserCommon.ROLE_SPEAKER || user.getRole() == UserCommon.ROLE_HOST) {
            isHost = true;
        } else {
            isHost = false;
        }
        View v = rlRoot.findViewWithTag(channelid);
        if (v != null) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) v.getLayoutParams();
            if (isFull && !isSpeakerMode && total == 2){
                //lp.setMargins(left, top -h/10, right, h/10);
                lp.setMargins(left, top, right, bottom);
                lp.width = w;
                lp.height = h/*+ h/11*/;
            }else {
                lp.setMargins(left, top, right, bottom);
                lp.width = w;
                lp.height = h;
            }
            v.setLayoutParams(lp);
//            if (isHost) {
//                v.setBackgroundResource(R.drawable.bg_item_video_host);
//            } else {
//                v.setBackgroundResource(R.drawable.bg_item_video_nor);
//            }
            VideoDecodeViewTV video = (VideoDecodeViewTV) v.findViewById(R.id.item_video_video);
            video.setFitsSystemWindows(true);
            video.setClipToPadding(true);
            UserBean bean = userCommon.getUser(uid);
            if (bean.getDevice() == UserCommon.DEVICE_MOBILE){
                video.setMobile(true);
            }else {
                video.setMobile(false);
            }
            if (isHost)
                video.setCurSVCLvl(lp.width /*- 2 - 2 * p*/, lp.height /*- 2 - 2 * p*/);
            else
                video.setCurSVCLvl(lp.width, lp.height);

            TextView tv = (TextView) v.findViewById(R.id.item_video_tv);

            if (!isHost)
                tv.setText(name);
            else
                tv.setText(name + "(主持人)");

        } else {
            if (isFull && !isSpeakerMode && 2 == total) {
                //h = w*9/15+DensityUtil.dip2px(getContext(),8f);
                h = w*9/16;//+DensityUtil.dip2px(getContext(),8f);
                top = (rootH - h)/2;
            }
            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            lp.setMargins(left, top, right, bottom);
            lp.width = w;
            lp.height = h;
            LayoutInflater inflater3 = LayoutInflater.from(getActivity());
            FrameLayout fl = (FrameLayout) inflater3.inflate(R.layout.item_inconf_video_tv, null);
            fl.setLayoutParams(lp);
            fl.setTag(channelid);

//            if (isHost) {
//                fl.setBackgroundResource(R.drawable.bg_item_video_host);
//            } else {
//                fl.setBackgroundResource(R.drawable.bg_item_video_nor);
//            }

            TextView tv = (TextView) fl.findViewById(R.id.item_video_tv);
            if (!isHost)
                tv.setText(name);
            else
                tv.setText(name + "(主持人)");

            rlRoot.addView(fl);
            VideoDecodeViewTV video = (VideoDecodeViewTV) fl.findViewById(R.id.item_video_video);
            video.setFitsSystemWindows(true);
            video.setClipToPadding(true);
            UserBean bean = userCommon.getUser(uid);
            if (bean.getDevice() == UserCommon.DEVICE_MOBILE){
                  video.setMobile(true);
            }else {
                  video.setMobile(false);
            }
            video.setSvc(isSupportSvc);
            video.changeStatus(channelid, true);
            existingVideos.add(channelid);
        }
    }
    private void addVideo(){
//        int p = getResources().getDimensionPixelOffset(R.dimen.dp_2);
//        int w = rlRoot.getWidth();
//        int h = rlRoot.getHeight() +Utils.getNavigationBarHeight(getActivity()) ;
        int w = DensityUtil.getWindowWidth(getActivity())/4;
        int h = (DensityUtil.getWindowHeight(getActivity())+Utils.getNavigationBarHeight(getActivity()))/4;
        int top = 0;
        int left = DensityUtil.getWindowWidth(getActivity()) * 3/4;
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rlEncoder.getLayoutParams();
        RelativeLayout.LayoutParams params1 = (RelativeLayout.LayoutParams)localCamera1.getLayoutParams();
        if (totalSyncList.size() == 0){

            Log.d(TAG,"FragVs.addView: localCamera.setParams(1,1)");

            closeLocalVideo();
            params.width = 1;
            params.height = 1;
            rlEncoder.setLayoutParams(params);
            localCamera.setParams(1,1);
            localCamera1.setParams(ConferenceApplication.Root_W,ConferenceApplication.Root_H);
            localCamera1.setSharing(true);
        }else {
            if (3 == totalNum){
                params.width= w;
                params.height = h;
                params.setMargins(left,top,0,0);
                //localCamera.setStreamVideoSize(640,480);
            }else {
                params.width= 0;
                params.height = 0;
                params.setMargins(0,0,0,0);
            }
            params1.width = ConferenceApplication.Root_W * 3/4;
            params1.height = ConferenceApplication.Root_H;
            rlEncoder.setLayoutParams(params);
            localCamera.setParams(params.width,params.height);

            if (!localCamera.existCamera()) {
                if (tvNoCamera != null) tvNoCamera.setVisibility(View.VISIBLE);
                callParentView(ACTION_HIDE_VIDEO, null);
            }
            else
            {
                if (tvNoCamera != null) tvNoCamera.setVisibility(View.GONE);
            }

            localCamera1.setParams(params1.width,params1.height);
            if (3 == totalNum){
                localCamera.setSharing(true);
            }
            localCamera1.setSharing(true);
        }
        for (int i = 0; i < syncList.size(); i++) {
            VideoBean bean = syncList.get(i);
            UserBean user = userCommon.getUser(bean.getUid());
            String name = user.getUsername();
            if (3 == totalNum){
                top = (i+1)* h;
            }else {
                top = i * h;
            }
            boolean isHost = false;
            if (user.getRole() == UserCommon.ROLE_SPEAKER || user.getRole() == UserCommon.ROLE_HOST) {
                isHost = true;
            } else {
                isHost = false;
            }
            View v = rlRoot.findViewWithTag(bean.getChannelid());
            if (v != null) {
                RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) v.getLayoutParams();
                lp.setMargins(left, top, 0, 0);
                lp.width = w;
                lp.height = h;
                v.setLayoutParams(lp);
//                if (isHost) {
//                    v.setBackgroundResource(R.drawable.bg_item_video_host);
//                } else {
//                    v.setBackgroundResource(R.drawable.bg_item_video_nor);
//                }
                VideoDecodeViewTV video = (VideoDecodeViewTV) v.findViewById(R.id.item_video_video);
                video.setFitsSystemWindows(true);
                video.setClipToPadding(true);
                if (user.getDevice() == UserCommon.DEVICE_MOBILE){
                    video.setMobile(true);
                }else {
                    video.setMobile(false);
                }
                video.setCurSVCLvl(lp.width/*- 2 * p*/, lp.height/*- 2 * p*/);
                video.setFitsSystemWindows(true);
                video.setClipToPadding(true);
                TextView tv = (TextView) v.findViewById(R.id.item_video_tv);
                if (!isHost)
                    tv.setText(name);
                else
                    tv.setText(name + "(主持人)");
            } else {
                RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                lp.setMargins(left, top, 0, 0);
                lp.width = w;
                lp.height = h;
                LayoutInflater inflater3 = LayoutInflater.from(getActivity());
                FrameLayout fl = (FrameLayout) inflater3.inflate(R.layout.item_inconf_video_tv, null);
                fl.setLayoutParams(lp);
                fl.setTag(bean.getChannelid());

//                if (isHost) {
//                    fl.setBackgroundResource(R.drawable.bg_item_video_host);
//                } else {
//                    fl.setBackgroundResource(R.drawable.bg_item_video_nor);
//                }

                TextView tv = (TextView) fl.findViewById(R.id.item_video_tv);
                if (!isHost)
                    tv.setText(name);
                else
                    tv.setText(name + "(主持人)");
                rlRoot.addView(fl);
                VideoDecodeViewTV video = (VideoDecodeViewTV) fl.findViewById(R.id.item_video_video);
                video.setFitsSystemWindows(true);
                video.setClipToPadding(true);
                if (user.getDevice() == UserCommon.DEVICE_MOBILE){
                    video.setMobile(true);
                }else {
                    video.setMobile(false);
                }
                video.setSvc(isSupportSvc);
                video.changeStatus(bean.getChannelid(), true);
                video.setFitsSystemWindows(true);
                video.setClipToPadding(true);
                existingVideos.add(bean.getChannelid());
            }
        }
        //添加默认图
        if (syncList.size() > 0 || 3 == totalNum){
            for (int i = 0; i < totalNum - syncList.size(); i++){
                if (3 == totalNum){
                    top = (i+syncList.size()+1) * h;
                }else {
                    top = (i+syncList.size()) * h;
                }
                ImageView iv = new ImageView(getActivity());
                RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                lp.setMargins(left, top, 0, 0);
                lp.width = w;
                lp.height = h;
                iv.setLayoutParams(lp);
                iv.setScaleType(ImageView.ScaleType.FIT_XY);
                iv.setImageResource(R.drawable.video_default);
                rlRoot.addView(iv);
                existingDefault.add(iv);
            }
        }
    }
    private void addDefault(int total, int count, boolean isFull,boolean isSpeakerMode) {
        if (isFull) {
            if (total == 1) {
                return;
            }else if(total>4){
                int sum  = 0;
                if (total < 6){
                   sum = (total/6 + 1)*6;
                }
                while (count<=sum){
                    addDefault1(sum,count,isFull,isSpeakerMode);
                    count++;
                }
            }else if(isSpeakerMode){
                int sum = 0;
                if (total < 4){
                   sum = (total/4 + 1)*4;
                }
                while (count<=sum){
                    addDefault1(sum,count,isFull,isSpeakerMode);
                    count++;
                }
            }
        } else {
            int sum = 4;
            while (count<=sum){
                addDefault1(sum,count,isFull,isSpeakerMode);
                count++;
            }
        }
    }

    private void addDefault1(int total, int count, boolean isFull,boolean isSpeakerMode) {
        if(count>total)return;
//        int rootW = rlRoot.getWidth();
//        int rootH = rlRoot.getHeight() +Utils.getNavigationBarHeight(getActivity()) ;
        int rootW = DensityUtil.getWindowWidth(getActivity());
        int rootH = DensityUtil.getWindowHeight(getActivity())+ Utils.getNavigationBarHeight(getActivity());
        int left = 0, right = 0, top = 0, bottom = 0, w = 0, h = 0;
        if (isFull) {
            if(isSpeakerMode){
                if (total == 1) {
                    return;
                } else if (total > 4) {
                    left = rootW * 2 / 3 - (count / 5 + count / 6) * rootW / 3;
                    top = ((count - 1) / 3 + (count + 1) / 4) * rootH / 3;
                    right = 0;
                    bottom = 0;
                    w = rootW / 3;
                    h = rootH / 3;
                } else {
                    left = (count - 2) * rootW / 3;
                    top = rootH * 2 / 3;
                    right = 0;
                    bottom = 0;
                    w = rootW / 3;
                    h = rootH / 3;
                }
            }else{
                if (total == 1) {
                    return;
                } else if (total == 2) {
                    return;
                } else if (total == 3) {
                    return;
                } else if (total == 4) {
                    left = (count - 1) % 2 * rootW / 2 + ((count - 1) / 4) * rootW;
                    top = ((count - 1) % 4 / 2) * (rootH / 2);
                    right = 0;
                    bottom = 0;
                    w = rootW / 2;
                    h = rootH / 2;
                } else {
                    left = (count - 1) % 3 * rootW / 3 + ((count - 1) / 6) * rootW;
                    top = rootH / 6 + (count / 4) * rootH / 3;
                    right = 0;
                    bottom = 0;
                    w = rootW / 3;
                    h = rootH / 3;
                }
            }
        } else {
            left = 0;
            top = (count - 1) * rootH / 4;
            right = 0;
            bottom = 0;
            w = rootW;
            h = rootH / 4;
        }
        ImageView iv = new ImageView(getActivity());
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        lp.setMargins(left, top, right, bottom);
        lp.width = w;
        lp.height = h;
        iv.setLayoutParams(lp);
        iv.setScaleType(ImageView.ScaleType.FIT_XY);
        iv.setImageResource(R.drawable.video_default);
        rlRoot.addView(iv);
        existingDefault.add(iv);
    }

    private void removeDefault() {
        if (existingDefault != null && !existingDefault.isEmpty()) {
            Iterator<ImageView> it = existingDefault.iterator();
            while (it.hasNext()) {
                rlRoot.removeView(it.next());
            }
        }
    }

    private VideoDecodeViewTV findViewByChannelid(int channelId) {
        View v = rlRoot.findViewWithTag(channelId);
        if (v != null) {
            VideoDecodeViewTV video = (VideoDecodeViewTV) v.findViewById(R.id.item_video_video);
            return video;
        } else {
            return null;
        }
    }

    private TextView findWaitingViewByChannelid(int channelId) {
        View v = rlRoot.findViewWithTag(channelId);
        if (v != null) {
            TextView tvWaiting = (TextView) v.findViewById(R.id.item_waiting_tv);
            return tvWaiting;
        } else {
            return null;
        }
    }

    private void removeUselessView(Map<Integer, Integer> mm) {
        if (existingVideos != null && !existingVideos.isEmpty()) {
            Iterator<Integer> it = existingVideos.iterator();
            while (it.hasNext()) {
                int channelid = it.next();
                if (!mm.containsKey(channelid)) {
                    View v = rlRoot.findViewWithTag(channelid);
                    if (v != null) {
                        VideoDecodeViewTV video = (VideoDecodeViewTV) v.findViewById(R.id.item_video_video);
                        video.changeStatus(0, false);
                        rlRoot.removeView(v);
                    }
                    it.remove();
                }
            }
        }
    }

    private void removeAllSyncRemoteView()
    {
        if (videoCommon == null || videoCommon.getSyncMap() == null){
            return;
        }

        Map<Integer, Integer> mm = videoCommon.getSyncMap();
        if (mm == null || mm.isEmpty()) {
            return;
        }

        Iterator<Map.Entry<Integer, Integer>> it = mm.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<Integer, Integer> item = it.next();
            int cid = item.getKey();
            int uid = item.getValue();

            View v = rlRoot.findViewWithTag(cid);
            if (v != null) {

                Log.d(TAG,"===>FragVs.removeAllSyncRemoteView: " + cid);

                VideoDecodeViewTV video = v.findViewById(R.id.item_video_video);
                video.changeStatus(0, false);
                rlRoot.removeView(v);
            }
            it.remove();
        }
   }

    protected void closeLocalVideo() {
        //隐藏本地预览窗口
        localCamera.setSharing(false);
    }

    public void destroyCamera() {
        localCamera.destroyCamera();
        localCamera1.destroyCamera();
    }
    protected void openLocalVideo() {
        //Log.d(TAG,"FragVs.openLocalVideo");
        Log.d(TAG, "FragVs.openLocalVideo Begin");
        if (Camera.getNumberOfCameras() == 0) return;
        if (localCamera.isSharing() && localCamera.isPreviewStarted())
        {
            Log.d(TAG, "FragVs.openLocalVideo Ignored!");
            return;
        }

        if (true != localCamera.isPreviewStarted()) {
            localCamera.changeStatus(true);
        }
        else
        {
            Log.d(TAG, "(Preview is running)FragVs.openLocalVideo Ignored!");
        }

        localCamera.setSharing(true);
        Log.d(TAG, "FragVs.openLocalVideo End");
    }

    public boolean isEmpty() {
        if (videoCommon == null || videoCommon.getSyncMap() == null){
            return true;
        }
        Map<Integer, Integer> mm = videoCommon.getSyncMap();
        if (mm == null || mm.isEmpty()) {
            return true;
        } else if (mm.size() == 1 && mm.containsValue(userCommon.getOwnID())) {
            return false;
        } else {
            return false;
        }
    }

    public boolean isSecondCameraEmpty() {
        if (localCamera1 != null && localCamera1.isPreviewStarted())
            return false;
        return true;
    }

    public boolean isRemoteEmpty() {
        if (videoCommon == null || videoCommon.getSyncMap() == null){
            return true;
        }
        Map<Integer, Integer> mm = videoCommon.getSyncMap();
        if (mm == null || mm.isEmpty()) {
            return true;
        } else if (mm.size() == 1 && mm.containsValue(userCommon.getOwnID())) {
            return true;
        } else {
            return false;
        }
    }

    public void doSetView() {
        setViewHandler.sendEmptyMessage(0);
    }

    Handler setViewHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {
            Map<Integer, Integer> mm = videoCommon.getSyncMap();
            if (rlRoot.getWidth() < 10 || mm == null || mm.isEmpty()) {
                updateVideoShow(false);
            } else {
                updateVideoShow(true);
            }
        }
    };

    public void preLeave() {

        Log.d(TAG,"FragVs.preLeave");

        localCamera1.preLeave();
        localCamera.preLeave();

        removeAllSyncRemoteView();

        updateVideoShow(false);

        //Map<Integer, Integer> syncMap;
//        localCamera.setSharing(false);
//        this.closeLocalVideo();
//        localCamera.destroyCamera();
//        removeUselessView(new HashMap<Integer, Integer>());
//        removeDefault();
//        localChannelID = -1;
    }

    public void setMaxVoice(int uid) {
        if (videoCommon.getSyncLayout() == VideoCommon.LayoutMode.MODE_VOICE
                && curMaxVoiceUser != uid){

            Log.d("GLVideoEncodeViewEx",">>>>>> FragVs setMaxVoice: " + uid);

            Map<Integer, Integer> syncMap = videoCommon.getSyncMap();

            if (syncMap.containsValue(uid)) {
                this.curMaxVoiceUser = uid;

                if (userCommon.getSelf().getUid() == curMaxVoiceUser) {
                    videoCommon.setSpeakerSelfId(true);
                } else {
                    videoCommon.setSpeakerSelfId(false);
                }

                localCamera.AnalyzeEncodeSizeEx();

                updateVideoShowEnter();
            }
        }
    }
    public void restartCam() {
        if (localCamera != null){
            localCamera.destroyCamera();
            localCamera.startCamera();
        }
//        stopCollect();
    }

    @Override
    public void onDestroyView() {
        //Log.e("YYYYYY","碎片销毁");
        localCamera.destroyCamera();
        localCamera1.destroyCamera();
        super.onDestroyView();
    }
//    //停止采集
//    public void stopCollect(){
//        if (audioRecordUtils != null){
//            audioRecordUtils.pauseRecord();
//        }
//    }

    /**
     * 6路视频以上算法
     * <AUTHOR>
     * @param syncMap 同步视频路数
     * */
    private void updateVideos(Map<Integer, Integer> syncMap){
        if (totalSyncList.size() != 0){
            totalSyncList.clear();
        }
        if (noSpeakerList.size() != 0){
            noSpeakerList.clear();
        }

        for (Integer key : syncMap.keySet()){
            VideoBean bean = new VideoBean();
            bean.setChannelid(key);
            bean.setUid(syncMap.get(key));
            totalSyncList.add(bean);
        }
        int column = 3;
        //单元格的宽度
        int cell_width = 0;
        //单元的高度
        int cell_height = 0;
        //同步视频路数
        int size = syncMap.size();
        if (-1 == curSpeakerId && 0 == curMaxVoiceUser){
            /***
             * 非主讲模式
             * 平铺
             * */
            if (size > 6 && size < 10){
                column = 3;
            }else{
                column = 4;
            }
            cell_width = DensityUtil.getWindowWidth(getActivity())/column;
            cell_height = DensityUtil.getWindowHeight(getActivity())/column;
            for (int i = 0; i < size; i++){
                VideoBean bean = totalSyncList.get(i);
                if (userCommon.getSelf().getUid() == bean.getUid()) {
                    //重置分辨率
//                    if (size > 6){
//                        videoCommon.setResolution(cell_width,cell_height);
//                    }
                    //本地视频
                    addLocalVideo(cell_width, cell_height,(i/column) * cell_height,(i % column) * cell_width);
                }else {
                    //远端视频
                    addFarVideo(cell_width, cell_height,(i/column) * cell_height,(i % column) * cell_width,bean);
                }
            }
            //添加默认图
            for (int i = 0; i < column * column - size; i++){
                addImage(cell_width,cell_height,((i+size)/column) * cell_height,((i+size) % column) * cell_width);
            }
        }else {
            /***
             * 主讲模式
             * 非平铺
             * */
            for (VideoBean bean : totalSyncList){
                if (bean.getChannelid() != curSpeakerId && (bean.getUid() != curMaxVoiceUser || bean.getChannelid() != mainChannelId)){
                    noSpeakerList.add(bean);
                }
            }
            if (size < 14){
                column = 4;
            }else {
                column = 5;
            }
            cell_width = DensityUtil.getWindowWidth(getActivity())/column;
            cell_height = DensityUtil.getWindowHeight(getActivity())/column;
            for (int i = 0; i < size; i++){
                VideoBean bean = totalSyncList.get(i);
                if (userCommon.getSelf().getUid() == bean.getUid()){
                    if (curMaxVoiceUser == bean.getUid() || (-1 != curSpeakerId && syncMap.containsKey(curSpeakerId) &&
                            syncMap.get(curSpeakerId) == bean.getUid())){
                        if (size < 9 || size > 13){
                            //重置分辨率
//                            if (size > 6){
//                                videoCommon.setResolution(3 * cell_width,3 * cell_height);
//                            }
                            addLocalVideo(3 * cell_width, 3 * cell_height,0,0);
                        }else{
//                            if (size > 6){
//                                videoCommon.setResolution(2 * cell_width,2 * cell_height);
//                            }
                            addLocalVideo(2 * cell_width, 2 * cell_height,0,0);
                        }
                    }
                }else {
                    if (curMaxVoiceUser == bean.getUid() || (-1 != curSpeakerId && syncMap.containsKey(curSpeakerId) &&
                            syncMap.get(curSpeakerId) == bean.getUid())){
                        if (size < 9 || size > 13){
                            addFarVideo(3 * cell_width, 3 * cell_height,0,0,bean);
                        }else{
                            addFarVideo(2 * cell_width, 2 * cell_height,0,0,bean);
                        }
                    }
                }
            }
            //遍历非主讲人员
            for (int i = 0; i < noSpeakerList.size(); i++){
                VideoBean bean = noSpeakerList.get(i);
                if (column == 4){
                    if (size < 9){
                        //7+1
                        if (i < 3){
                            if (userCommon.getSelf().getUid() == bean.getUid()){
//                                if (size > 6){
//                                    videoCommon.setResolution(cell_width,cell_height);
//                                }
                                addLocalVideo(cell_width,cell_height,(i % column) * cell_height,3 * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,(i % column) * cell_height,3 * cell_width,bean);
                            }
                        }else {
                            if (userCommon.getSelf().getUid() == bean.getUid()){
//                                if (size > 6){
//                                    videoCommon.setResolution(cell_width,cell_height);
//                                }
                                addLocalVideo(cell_width,cell_height,3 * cell_height,(i%column) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,3 * cell_height,(i%column) * cell_width,bean);
                            }
                        }
                    }else {
                        //12+1
                        if (i < 4){
                            if (i < 2){
                                if (userCommon.getSelf().getUid() == bean.getUid()){
//                                    if (size > 6){
//                                        videoCommon.setResolution(cell_width,cell_height);
//                                    }
                                    addLocalVideo(cell_width,cell_height,0,((i % column)+2) * cell_width);
                                }else {
                                    addFarVideo(cell_width,cell_height,0,((i % column)+2) * cell_width,bean);
                                }
                            }else {
                                if (userCommon.getSelf().getUid() == bean.getUid()){
//                                    if (size > 6){
//                                        videoCommon.setResolution(cell_width,cell_height);
//                                    }
                                    addLocalVideo(cell_width,cell_height,cell_height,(i % column) * cell_width);
                                }else {
                                    addFarVideo(cell_width,cell_height,cell_height,(i % column) * cell_width,bean);
                                }
                            }
                        }else {
                            if (userCommon.getSelf().getUid() == bean.getUid()){
//                                if (size > 6){
//                                    videoCommon.setResolution(cell_width,cell_height);
//                                }
                                addLocalVideo(cell_width,cell_height,(i/column+1) * cell_height,(i % column) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,(i/column+1) * cell_height,(i % column) * cell_width,bean);
                            }
                        }
                    }
                }else {
                    //16+1
                    if (i < 6){
                        if (i < 2){
                            if (userCommon.getSelf().getUid() == bean.getUid()){
//                                if (size > 6){
//                                    videoCommon.setResolution(cell_width,cell_height);
//                                }
                                addLocalVideo(cell_width,cell_height,0,((i % column)+3) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,0,((i % column)+3) * cell_width,bean);
                            }
                        }else if (i < 4){
                            if (userCommon.getSelf().getUid() == bean.getUid()){
//                                if (size > 6){
//                                    videoCommon.setResolution(cell_width,cell_height);
//                                }
                                addLocalVideo(cell_width,cell_height,cell_height,((i % column)+3) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,cell_height,((i % column)+3) * cell_width,bean);
                            }
                        }else {
                            if (userCommon.getSelf().getUid() == bean.getUid()){
//                                if (size > 6){
//                                    videoCommon.setResolution(cell_width,cell_height);
//                                }
                                addLocalVideo(cell_width,cell_height,0,((i % column)+3) * cell_width);
                            }else {
                                addFarVideo(cell_width,cell_height,0,((i % column)+3) * cell_width,bean);
                            }
                        }
                    }else {
                        if (userCommon.getSelf().getUid() == bean.getUid()){
//                            if (size > 6){
//                                videoCommon.setResolution(cell_width,cell_height);
//                            }
                            addLocalVideo(cell_width,cell_height,(i/column+2) * cell_height,(i % column - 1) * cell_width);
                        }else {
                            addFarVideo(cell_width,cell_height,(i/column+2) * cell_height,(i % column - 1) * cell_width,bean);
                        }
                    }
                }
            }
            //添加默认图
            if (size < 9){
                int cellNum = column * column - 9 - noSpeakerList.size();
                for (int i = 0; i < cellNum; i++){
                    addImage(cell_width,cell_height,(column -1) * cell_height,((i+size-1)%column) * cell_width);
                }
            }else if (size > 9 && size < 13){
                //剩余的单元格
                int cellNum = column * column - 4 - noSpeakerList.size();
                for (int i = 0; i < cellNum; i++){
                    addImage(cell_width,cell_height,(column -1) * cell_height,((i+size-1) % column) * cell_width);
                }
            }else if (size > 13 && size < 17){
                int cellNum = column * column - 9 - noSpeakerList.size();
                for (int i = 0; i < cellNum; i++){
                    addImage(cell_width,cell_height,(column -1) * cell_height,((i+size-1) % column -1) * cell_width);
                }
            }
        }
    }
    //修改本地视频
    private void addLocalVideo(int w, int h, int top, int left){

        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rlEncoder.getLayoutParams();
        params.setMargins(left, top, 0, 0);
        params.width = w;
        params.height = h;
        rlEncoder.setLayoutParams(params);
        localCamera.setParams(w,h);
        if (!localCamera.existCamera()) {
            if (tvNoCamera != null) tvNoCamera.setVisibility(View.VISIBLE);
            callParentView(ACTION_HIDE_VIDEO, null);
        }
        else
        {
            if (tvNoCamera != null) tvNoCamera.setVisibility(View.GONE);
        }
//        localCamera.reStartLocalView();
    }
    //添加远端视频
    private void addFarVideo(int w, int h, int top, int left,VideoBean bean){
        //int p = getResources().getDimensionPixelOffset(R.dimen.dp_2);
        UserBean user = userCommon.getUser(bean.getUid());
        String name = user.getUsername();

        boolean isHost = false;
        if (user.getRole() == UserCommon.ROLE_SPEAKER || user.getRole() == UserCommon.ROLE_HOST) {
            isHost = true;
        } else {
            isHost = false;
        }

        View v = rlRoot.findViewWithTag(bean.getChannelid());
        if (v != null) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) v.getLayoutParams();
            lp.setMargins(left, top, 0, 0);
            lp.width = w;
            lp.height = h;
            v.setLayoutParams(lp);
            VideoDecodeViewTV video = (VideoDecodeViewTV) v.findViewById(R.id.item_video_video);

            if (user.getDevice() == UserCommon.DEVICE_MOBILE){
                video.setMobile(true);
            }else {
                video.setMobile(false);
            }

            video.setCurSVCLvl(lp.width/*- 2 * p*/, lp.height/*- 2 * p*/);
            TextView tv = (TextView) v.findViewById(R.id.item_video_tv);
            if (!isHost)
                tv.setText(name);
            else
                tv.setText(name + "(主持人)");
        } else {
            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            lp.setMargins(left, top, 0, 0);
            lp.width = w;
            lp.height = h;
            LayoutInflater inflater3 = LayoutInflater.from(getActivity());
            FrameLayout fl = (FrameLayout) inflater3.inflate(R.layout.item_inconf_video_tv, null);
            fl.setLayoutParams(lp);
            fl.setTag(bean.getChannelid());
            TextView tv = (TextView) fl.findViewById(R.id.item_video_tv);
            if (!isHost)
                tv.setText(name);
            else
                tv.setText(name + "(主持人)");
            rlRoot.addView(fl);
            VideoDecodeViewTV video = (VideoDecodeViewTV) fl.findViewById(R.id.item_video_video);

            if (user.getDevice() == UserCommon.DEVICE_MOBILE){
                video.setMobile(true);
            }else {
                video.setMobile(false);
            }

            video.setSvc(isSupportSvc);
            video.changeStatus(bean.getChannelid(), true);
            existingVideos.add(bean.getChannelid());
        }
    }
    //添加默认图
    private void addImage(int w, int h, int top, int left){

        ImageView iv = new ImageView(getActivity());
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        lp.setMargins(left, top, 0, 0);
        lp.width = w;
        lp.height = h;
        iv.setLayoutParams(lp);
        iv.setScaleType(ImageView.ScaleType.FIT_XY);
        iv.setImageResource(R.drawable.video_default);
        rlRoot.addView(iv);
        existingDefault.add(iv);
    }

    private boolean isKeyFrame(byte[] buffer) {

        if (buffer.length < 5) {
            return false;
        }

        //00 00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 0
                && buffer[3] == 1) {
            int nalType = buffer[4] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                //Log.d(TAG, ">>>>>>isKeyFrame: nalType = " + nalType);
                return true;
            }
        }

        //00 00 01
        if (buffer[0] == 0
                && buffer[1] == 0
                && buffer[2] == 1) {
            int nalType = buffer[3] & 0x1f;
            if (nalType == 0x07 || nalType == 0x05 || nalType == 0x08) {
                //Log.d(TAG, ">>>>>>isKeyFrame: nalType = " + nalType);
                return true;
            }
        }

        return false;
    }

    private void sendEncodedFrameEx(ByteBuffer outputBuffer, MediaCodec.BufferInfo bufferInfo, int width, int height, byte[] spsPpsInfo) {

        byte[] data = new byte[bufferInfo.size];  //delete []data;
        outputBuffer.get(data);

        if (spsPpsInfo != null) {

            if (isKeyFrame(data)) { //IDR frame

                //int len;
                //mMediaHead[4] = (byte) (mMediaHead[4] | (3 << 5));

                if (shareDtCommon != null)
                {
                    //Log.d(TAG,"I Frame PPS.SPS length: " + spsPpsInfo.length);
                    //Log.d(TAG,"I Frame PPS.SPS: " + Arrays.toString(spsPpsInfo));
                    shareDtCommon.sendScreenData(width, height, 24, spsPpsInfo, spsPpsInfo.length, true, true);
                }

                //data[4] = (byte) (data[4] | (3 << 5)); //nal_ref_idc = 3,IDR frame high priority
                //len = data.length;

                if (shareDtCommon != null)
                {
                    Log.d(TAG,"I Frame length: " + data.length);
                    Log.d(TAG,"I Frame resolution: " + width + "x" + height);
                    shareDtCommon.sendScreenData(width, height, 24, data, data.length, true, true);
                }

            } else { //P frame

                if (shareDtCommon != null)
                {
                    Log.d(TAG,"P Frame length: " + data.length);
                    Log.d(TAG,"P Frame resolution: " + width + "x" + height);
                    shareDtCommon.sendScreenData(width, height, 24, data, data.length, true, false);
                }
            }
        }
    }

    private void sendEncodedFrame(ByteBuffer outputBuffer, MediaCodec.BufferInfo bufferInfo, int width, int height) {

        byte[] data = new byte[bufferInfo.size];  //delete []data;
        outputBuffer.get(data);

        if (data.length <= 0) return;

        if (isKeyFrame(data)) { //IDR frame

            if (shareDtCommon != null)
            {
                Log.d(TAG,"I Frame length: " + data.length);
                Log.d(TAG,"I Frame resolution: " + width + "x" + height);
                shareDtCommon.sendScreenData(width, height, 24, data, data.length, true, true);
            }

        } else { //P frame

            if (shareDtCommon != null)
            {
                Log.d(TAG,"P Frame length: " + data.length);
                Log.d(TAG,"P Frame resolution: " + width + "x" + height);
                shareDtCommon.sendScreenData(width, height, 24, data, data.length, true, false);
            }
        }
    }

    public void onSelfRoleChange(int newRole) {

        Log.d("GLVideoEncodeViewEx","FragVs onSelfRoleChange: " + newRole);
        if (localCamera != null) localCamera.AnalyzeEncodeSizeEx();
    }


//    @Override
//    public void onGetEncodedAudioFrame(ByteBuffer bb, MediaCodec.BufferInfo bi) {
//
//    }
//
//    @Override
//    public void onGetEncodedVideoFrame(ByteBuffer es, MediaCodec.BufferInfo bi, int width, int height, byte[] spsPpsInfo) {
//        if (localCamera1.getVisibility() == View.VISIBLE && localCamera1.isShared()){
//            if (shareDtCommon != null){
//                if (bi.size < 4) return;
//
//                sendEncodedFrameEx(es, bi, width, height, spsPpsInfo);
//            }
//        }
//    }

    /**
     * 定义了所有activity必须实现的接口
     * 布局切换
     */
    public interface FragmentInteraction {
        /**
         * Fragment 向Activity传递指令，这个方法可以根据需求来定义
         *
         */
        void setBuJuType(int type);
        void getLimit(int limit);
    }

    /**
     * 当FRagmen被加载到activity的时候会被回调
     *
     * @param activity
     */
    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity instanceof FragMenu.FragmentInteraction) {
            listterner = (FragmentInteraction) activity;
        } else {
            throw new IllegalArgumentException("activity must implements FragmentInteraction");
        }
    }
    //隐藏本地预览窗口
    public void setCameraSize(){
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rlEncoder.getLayoutParams();
        params.setMargins(0, 0, 0, 0);
        params.width = 1;
        params.height = 1;
        rlEncoder.setLayoutParams(params);
        localCamera.setParams(1,1);
    }

    //恢复分辨率
    public void recoverRatio(){
        if (localCamera != null){
            //恢复分辨率
            //localCamera.setStreamVideoSize(SharedPreferencesUrls.getInstance().getInt("width",1280),
             //       SharedPreferencesUrls.getInstance().getInt("height",720));
        }
    }

    //测试RTMP推送临时代码
    /*
    private void handleException(Exception e) {
        try {
            Toast.makeText(getActivity(), e.getMessage(), Toast.LENGTH_SHORT).show();
            mPublisher.stopPublish();
            mPublisher.stopRecord();
        } catch (Exception e1) {
            //
        }
    }

    // Implementation of SrsRtmpListener.

    @Override
    public void onRtmpConnecting(String msg) {
        Toast.makeText(getActivity(), msg, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRtmpConnected(String msg) {
        Toast.makeText(getActivity(), msg, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRtmpVideoStreaming() {
    }

    @Override
    public void onRtmpAudioStreaming() {
    }

    @Override
    public void onRtmpStopped() {
        Toast.makeText(getActivity(), "Stopped", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRtmpDisconnected() {
        Toast.makeText(getActivity(), "Disconnected", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRtmpVideoFpsChanged(double fps) {
        Log.i(TAG, String.format("Output Fps: %f", fps));
    }

    @Override
    public void onRtmpVideoBitrateChanged(double bitrate) {
        int rate = (int) bitrate;
        if (rate / 1000 > 0) {
            Log.i(TAG, String.format("Video bitrate: %f kbps", bitrate / 1000));
        } else {
            Log.i(TAG, String.format("Video bitrate: %d bps", rate));
        }
    }

    @Override
    public void onRtmpAudioBitrateChanged(double bitrate) {
        int rate = (int) bitrate;
        if (rate / 1000 > 0) {
            Log.d(TAG, String.format("Audio bitrate: %f kbps", bitrate / 1000));
        } else {
            Log.d(TAG, String.format("Audio bitrate: %d bps", rate));
        }
    }

    @Override
    public void onRtmpSocketException(SocketException e) {
        handleException(e);
    }

    @Override
    public void onRtmpIOException(IOException e) {
        handleException(e);
    }

    @Override
    public void onRtmpIllegalArgumentException(IllegalArgumentException e) {
        handleException(e);
    }

    @Override
    public void onRtmpIllegalStateException(IllegalStateException e) {
        handleException(e);
    }

    // Implementation of SrsRecordHandler.

    @Override
    public void onRecordPause() {
        Toast.makeText(getActivity(), "Record paused", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRecordResume() {
        Toast.makeText(getActivity(), "Record resumed", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRecordStarted(String msg) {
        Toast.makeText(getActivity(), "Recording file: " + msg, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRecordFinished(String msg) {
        Toast.makeText(getActivity(), "MP4 file saved: " + msg, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRecordIOException(IOException e) {
        handleException(e);
    }

    @Override
    public void onRecordIllegalArgumentException(IllegalArgumentException e) {
        handleException(e);
    }

    // Implementation of SrsEncodeHandler.

    @Override
    public void onNetworkWeak() {
        Toast.makeText(getActivity(), "Network weak", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onNetworkResume() {
        Toast.makeText(getActivity(), "Network resume", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onEncodeIllegalArgumentException(IllegalArgumentException e) {
        handleException(e);
    }*/

    public boolean existCamera() {
        if (localCamera != null) return localCamera.existCamera();
        return false;
    }
}