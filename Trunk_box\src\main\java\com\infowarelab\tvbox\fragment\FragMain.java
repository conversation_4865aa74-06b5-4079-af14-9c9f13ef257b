package com.infowarelab.tvbox.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.hardware.Camera;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;

import java.io.IOException;

@SuppressLint("ValidFragment")
public class FragMain extends BaseFragment implements SurfaceHolder.Callback{

    private View rootView;

    private TextView titleText;
    private Spinner spinner;
    private SurfaceView tvMain;
    private VideoCommonImpl videoCommon;
    private Camera mCamera;
    private SurfaceHolder mHolder;//
    private Camera.Parameters parameters;//
    private int cameraPosition = 0; // 0代表前置摄像头，1代表后置摄像头

    public FragMain(ICallParentView iCallParentView) {
        super(iCallParentView);
    }
    public void setCameraPosition(int cameraPosition) {
        this.cameraPosition = cameraPosition;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.frag_main, container, false);
        return rootView;
    }
    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initView();
    }

    private void initView(){

        titleText = (TextView)rootView.findViewById(R.id.titleText);
        if (0 == cameraPosition){
            titleText.setText(getResources().getString(R.string.main));
        }else {
            titleText.setText(getResources().getString(R.string.fu));
        }
        videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        SharedPreferences sharedPreferences =getActivity().getSharedPreferences("main", Context.MODE_PRIVATE);
        final SharedPreferences.Editor editor = sharedPreferences.edit();
        spinner = (Spinner)rootView.findViewById(R.id.sp);
//        String[] items = {"一般", "标清", "高清", "超清"};
        String[] items = {"一般", "标清", "高清"};
        ArrayAdapter<String> stringArrayAdapter = new ArrayAdapter<>(getContext(), R.layout.item_select, items);
        stringArrayAdapter.setDropDownViewResource(R.layout.item_drop);
        spinner.setAdapter(stringArrayAdapter);
        spinner.requestFocus();

        tvMain = (SurfaceView)rootView.findViewById(R.id.tv_main);
        mHolder = tvMain.getHolder();
        mHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {

//                Camera.Parameters parameters = mCamera.getParameters();

                switch (position) {
                    case 0:
                        editor.putInt("width", 320);
                        editor.putInt("height", 180);
                        mHolder.setFixedSize(320,240);
//                        parameters.setPreviewSize(320, 240);
//                        mCamera.setParameters(parameters);
                        break;
                    case 1:
                        editor.putInt("width", 640);
                        editor.putInt("height", 360);
                        mHolder.setFixedSize(640,480);
//                        parameters.setPreviewSize(640, 480);
//                        mCamera.setParameters(parameters);
                        break;
                    case 2:
                        editor.putInt("width", 1280);
                        editor.putInt("height", 720);
                        mHolder.setFixedSize(1280,720);
//                        parameters.setPreviewSize(1280, 720);
//                        mCamera.setParameters(parameters);
                        break;
//                    case 3:
//                        editor.putInt("width", 1920);
//                        editor.putInt("height", 1080);
//                        mHolder.setFixedSize(1920,1080);
//                        break;
                    default:
                        break;
                }
                editor.putInt("position", position);
                editor.commit();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });
        spinner.setSelection(sharedPreferences.getInt("position", 0));
        mHolder.setFixedSize(sharedPreferences.getInt("width",320),sharedPreferences.getInt("height",240));
        mHolder.addCallback(this);
    }
    //相机的启动和Activity的生命周期进行绑定
    @Override
    public void onResume() {
        super.onResume();
        if (mCamera == null) {
            mCamera = getCamera();
            if (mHolder != null) {
                setStartPrevicw(mCamera, mHolder);
                if (Build.MODEL.toUpperCase().startsWith("Lenovo".toUpperCase())){
                    mCamera.setDisplayOrientation(0);
                }
                setCamera();
            }
        }
    }
    //相机的启动和Activity的生命周期进行绑定
    @Override
    public void onPause() {
        super.onPause();
        releaseCamera();
    }
    /**
     * 打开摄像头
     *
     * @return
     */
    public Camera getCamera() {
        Camera camera;
        try {
            camera = Camera.open();
        } catch (Exception e) {
            camera = null;
            e.printStackTrace();
        }
        return camera;
    }

    /**
     * 开始预览相机内容
     */
    private void setStartPrevicw(Camera camera, SurfaceHolder holder) {
        try {
            camera.setPreviewDisplay(holder);
            //打开摄像头
            camera.startPreview();
        } catch (IOException e) {
            e.printStackTrace();
            //释放摄像头
            releaseCamera();
        }
    }

    /**
     * 释放相机资源
     */
    public void releaseCamera() {
        if (mCamera != null) {
            mCamera.setPreviewCallback(null);
            mCamera.stopPreview();
            mCamera.release();
            mCamera = null;
        }
    }
    //切换摄像头
    private void setCamera(){
        //切换前后摄像头
        int cameraCount = 0;
        Camera.CameraInfo cameraInfo = new Camera.CameraInfo();
        cameraCount = Camera.getNumberOfCameras();//得到摄像头的个数
        for (int i = 0; i < cameraCount; i++) {
            Camera.getCameraInfo(i, cameraInfo);//得到每一个摄像头的信息
            if (cameraPosition == 0) {
                //现在是后置，变更为前置
                if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
                    //代表摄像头的方位，CAMERA_FACING_FRONT前置 CAMERA_FACING_BACK后置
                    mCamera.stopPreview();//停掉原来摄像头的预览
                    mCamera.release();//释放资源
                    mCamera = null;//取消原来摄像头
                    mCamera = Camera.open(i);//打开当前选中的摄像头
                    try {
                        mCamera.setPreviewDisplay(mHolder);//通过surfaceview显示取景画面
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    setStartPrevicw(mCamera, mHolder);
                    break;
                }
            } else {
                //现在是前置， 变更为后置
                if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_BACK) {
                    //代表摄像头的方位，CAMERA_FACING_FRONT前置 CAMERA_FACING_BACK后置
                    mCamera.stopPreview();//停掉原来摄像头的预览
                    mCamera.release();//释放资源
                    mCamera = null;//取消原来摄像头
                    mCamera = Camera.open(i);//打开当前选中的摄像头
                    try {
                        mCamera.setPreviewDisplay(mHolder);//通过surfaceview显示取景画面
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    setStartPrevicw(mCamera, mHolder);
                    break;
                }
            }
        }
    }
    //在开始的时候创建画面显示的东西
    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        setStartPrevicw(mCamera, mHolder);
    }
    //当屏幕发生变化时候要做的事儿
    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        mCamera.stopPreview();
        setStartPrevicw(mCamera, mHolder);
    }
    //当界面销毁的时候要处理的事儿
    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        Log.e("Test","释放成功");
        releaseCamera();
    }
}
