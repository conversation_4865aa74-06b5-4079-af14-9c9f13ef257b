package com.infowarelab.tvbox.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.base.BaseViewAdapter;
import com.infowarelab.tvbox.base.BaseViewHolder;
import com.infowarelab.tvbox.modle.TerminalsListBean;

/**
 * Created by xiaor on 2019/12/25.
 */

public class TerminalAdapter extends BaseViewAdapter<TerminalsListBean>{

    public TerminalAdapter(Context context) {
        super(context);
    }
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (null == convertView) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_terminal_list, null);
        }
        LinearLayout itemview = BaseViewHolder.get(convertView,R.id.item_terminalList_itemview);
        ImageView checkBoxImage = BaseViewHolder.get(convertView,R.id.item_terminalList_checkBoxImage);
        TextView titleText = BaseViewHolder.get(convertView,R.id.item_terminalList_titleText);
        TerminalsListBean bean = getDatas().get(position);
        if (bean.isFouse()){
            itemview.setBackgroundResource(R.drawable.invate_list_bg);
        }else {
            itemview.setBackgroundResource(R.drawable.selected);
        }
        if (bean.isSelected() && bean.isFouse()){
            checkBoxImage.setImageResource(R.drawable.checkbox_sel_icon);
        }else if (bean.isSelected() && !bean.isFouse()){
            checkBoxImage.setImageResource(R.drawable.checkbox_selected_icom);
        }else if (!bean.isSelected() && bean.isFouse()){
            checkBoxImage.setImageResource(R.drawable.checkbox_select_icon);
        }else {
            checkBoxImage.setImageResource(R.drawable.checkbox_nor_icon);
        }
        titleText.setText(bean.getName());
        return convertView;
    }
}
