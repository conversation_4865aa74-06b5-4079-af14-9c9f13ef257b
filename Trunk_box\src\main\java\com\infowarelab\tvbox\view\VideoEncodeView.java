package com.infowarelab.tvbox.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.ImageFormat;
import android.hardware.Camera;
import android.hardware.Camera.CameraInfo;
import android.hardware.Camera.Parameters;
import android.hardware.Camera.PreviewCallback;
import android.os.Build;
import android.util.AttributeSet;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceHolder.Callback;
import android.view.SurfaceView;
import android.widget.RelativeLayout;

import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.video.AvcHardEncoder;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

////import org.apache.log4j.Logger;

/**
 * PreviewCallback回到接口，用于显示预览框
 */
@SuppressLint("NewApi")
public class VideoEncodeView extends SurfaceView implements PreviewCallback,
        Callback {
    private Context activity;

    private boolean isWriteFile = false;
    private File _fr = null;
    private FileOutputStream _out = null;

    public static int cameraWidth = 0;
    public static int cameraHeight = 0;
    private SurfaceHolder mPreviewHolder = null;
    private SurfaceHolder holder;
    private boolean initVideo = true;
    private boolean isSharing = false;
    private boolean isPortrait = true;
    private VideoCommonImpl videoCommon = (VideoCommonImpl) CommonFactory
            .getInstance().getVideoCommon();
    private ShareDtCommonImpl shareDtCommon = (ShareDtCommonImpl)CommonFactory.getInstance().getSdCommon();
    private int degrees = 90;
    private boolean mPreviewStarted = false;
    private byte[] yv12buf;
    private Camera camera = null;
    static int numOfCamera = Camera.getNumberOfCameras();
    static int currentCamera = CameraInfo.CAMERA_FACING_FRONT;
    //    static int currentCamera = CameraInfo.CAMERA_FACING_BACK;
    private AvcHardEncoder h264HwEncoderImpl;
    private boolean isHardCodec = true;
    //private Logger logger = Logger.getLogger("VideoEncodeView");
    //是否是辅流
    private boolean isFlow = false;
    //是否旋转
    private boolean isDegrees = true;
    private int mFrameLength = 0;

    private LinkedBlockingQueue<byte[]> mVideoFrameQueue = new LinkedBlockingQueue<>();
    private int mQueueCapacity = 10;
    private Thread mEncodingThread = null;
    private String TAG = "InfowareLab.Debug";
    private long mLastCheckTime = 0;
    private boolean mSendFrameByThread = false;
    private int mViewWidth = 0;
    private int mViewHeight = 0;
    private boolean support4K = false;

    public void setDegrees(boolean degrees) {
        isDegrees = degrees;
    }

    public void setFlow(boolean flow) {
        isFlow = flow;
    }

    public VideoEncodeView(Context context) {
        super(context);
        activity = context;
        init();
    }

    public VideoEncodeView(Context context, AttributeSet attrs) {
        super(context, attrs);
        activity = context;
        init();
    }

    public void init() {
        if (holder == null) {
            holder = this.getHolder();
            //Log.d("InfowareLab.Debug","VideoEncodeView.getHolder(1)="+holder);
        }
        holder.addCallback(this);
        holder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        if (null == h264HwEncoderImpl)
            h264HwEncoderImpl = new AvcHardEncoder();
        isHardCodec = AvcHardEncoder.IsSupportHardEncode();

        //Log.d("InfowareLab.Debug","VideoEncodeView.IsSupportHardEncode="+isHardCodec);

//        if (isWriteFile) {
//            _fr = new File("/sdcard/PreviewFrame.nv21");
//            try {
//                _out = new FileOutputStream(_fr);
//            } catch (FileNotFoundException e) {
//                // TODO Auto-generated catch block
//                e.printStackTrace();
//            }
//        }
    }

    private boolean encodeAndSendVideoFrame() {

        if (mVideoFrameQueue.size() <= 0) return false;

        byte[] videoFrame = null;

        videoFrame = mVideoFrameQueue.poll();

        if (videoFrame == null) {
            Log.d(TAG, "VideoEncodeView.encodeAndSendVideoFrame: videoFrame = NULL");
            return false;
        }

        if (isPortrait) {//竖屏

            if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16) {
                if (h264HwEncoderImpl.GetMediaEncoder() == null)
                    h264HwEncoderImpl.initEncoder(cameraHeight, cameraWidth);

                yv12buf = h264HwEncoderImpl.getAdapterYv12bufPortrait(videoFrame, cameraWidth, cameraHeight, currentCamera);
                h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
            } else //soft ware encoding
            {
                if (currentCamera == CameraInfo.CAMERA_FACING_BACK) {
                    yv12buf = rotateYUV420SPBackfacing(videoFrame, cameraWidth, cameraHeight);
                } else {
                    yv12buf = rotateYUV420SPFrontfacing(videoFrame, cameraWidth, cameraHeight);
                }

                videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraHeight, cameraWidth, false);
            }
            //end
        } else {//横屏
            if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16)//hardware encode.....
            {
                if (h264HwEncoderImpl.GetMediaEncoder() == null)
                    h264HwEncoderImpl.initEncoder(cameraWidth, cameraHeight);

                yv12buf = h264HwEncoderImpl.getAdapterYv12bufLandscape(videoFrame, cameraWidth, cameraHeight, degrees);
                h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
            } else  //software encode
            {
                if (degrees == 0) {
                    yv12buf = changeYUV420SP2P(videoFrame, cameraWidth, cameraHeight);
                } else {
                    yv12buf = Rotate180YUV420SP2P(videoFrame, cameraWidth, cameraHeight);
                }
                videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraWidth, cameraHeight, false);
            }
        }

        return true;
    }


    @Override
    public void onPreviewFrame(byte[] data, Camera camera) {

        if (data == null) {
            return;
        }

//        if (isWriteFile) {
//            try {
//                _out.write(data);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }

         //防止花屏

        if (data[4] == 0x61){
            data[4] = 0x41;
        }

        if (data.length != mFrameLength)
        {
            Log.d("InfowareLab.Debug", "VideoEncodeView.onPreviewFrame: wrong data length: " + data.length + "<" + mFrameLength);
            camera.addCallbackBuffer(data);
            return;
        }

        if (false == isSharing)
        {
            camera.addCallbackBuffer(data);
            return;
        }

        if (isSharing) {

            if (mSendFrameByThread) {
                if (mEncodingThread == null) {
                    mEncodingThread = new Thread() {
                        @Override
                        public void run() {
                            while (!isInterrupted() && isSharing) {
                                long timeCost = System.currentTimeMillis() - mLastCheckTime;

                                if (timeCost < 20) {
                                    long needToSleep = 20 - timeCost;

                                    //Log.d(TAG, "VideoEncodeView.mEncodingThread.needToSleep = " + needToSleep);

                                    if (needToSleep > 0) {
                                        try {
                                            sleep(needToSleep);
                                        } catch (InterruptedException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }

                                mLastCheckTime = System.currentTimeMillis();
                                encodeAndSendVideoFrame();
                            }
                            super.run();

                            Log.d(TAG, "VideoEncodeView.mEncodingThread is exit normally");

                            mEncodingThread = null;
                        }
                    };
                    mEncodingThread.start();
                }

                if (mVideoFrameQueue.size() >= mQueueCapacity) {
                    byte[] oldVideoFrame = mVideoFrameQueue.poll();
                    if (oldVideoFrame.length == data.length) {
                        System.arraycopy(data, 0, oldVideoFrame, 0, oldVideoFrame.length);
                        mVideoFrameQueue.offer(oldVideoFrame);
                        //Log.d("InfowareLab.Debug", "VideoEncodeView.onPreviewFrame: used OLD video frame buffer.");
                        camera.addCallbackBuffer(data);
                        return;
                    } else {
                        byte[] newVideoFrame = new byte[data.length];
                        System.arraycopy(data, 0, newVideoFrame, 0, newVideoFrame.length);
                        mVideoFrameQueue.offer(newVideoFrame);
                        //Log.d("InfowareLab.Debug", "VideoEncodeView.onPreviewFrame: used NEW video frame buffer: " + oldVideoFrame.length + " vs " + data.length);
                        camera.addCallbackBuffer(data);
                        return;
                    }
                }

                byte[] videoFrame = new byte[data.length];
                System.arraycopy(data, 0, videoFrame, 0, videoFrame.length);
                mVideoFrameQueue.offer(videoFrame);
                //Log.d("InfowareLab.Debug", "VideoEncodeView.onPreviewFrame: create video frame buffer: queue size=" + mVideoFrameQueue.size());
                camera.addCallbackBuffer(data);
                return;
            }
            else
            {
                if (isPortrait) {//竖屏

                    if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16) {
                        if (h264HwEncoderImpl.GetMediaEncoder() == null)
                            h264HwEncoderImpl.initEncoder(cameraHeight, cameraWidth);

                        yv12buf = h264HwEncoderImpl.getAdapterYv12bufPortrait(data, cameraWidth, cameraHeight, currentCamera);
                        h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
                    } else //soft ware encoding
                    {
                        if (currentCamera == CameraInfo.CAMERA_FACING_BACK) {
                            yv12buf = rotateYUV420SPBackfacing(data, cameraWidth, cameraHeight);
                        } else {
                            yv12buf = rotateYUV420SPFrontfacing(data, cameraWidth, cameraHeight);
                        }

                        videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraHeight, cameraWidth, false);
                    }
                    //end
                } else {//横屏
                    if (isHardCodec && Integer.parseInt(Build.VERSION.SDK) >= 16)//hardware encode.....
                    {
                        if (h264HwEncoderImpl.GetMediaEncoder() == null)
                            h264HwEncoderImpl.initEncoder(cameraWidth, cameraHeight);

                        yv12buf = h264HwEncoderImpl.getAdapterYv12bufLandscape(data, cameraWidth, cameraHeight, degrees);
                        h264HwEncoderImpl.offerEncoderAndSend(yv12buf, videoCommon);
                    } else  //software encode
                    {
                        if (degrees == 0) {
                            yv12buf = changeYUV420SP2P(data, cameraWidth, cameraHeight);
                        } else {
                            yv12buf = Rotate180YUV420SP2P(data, cameraWidth, cameraHeight);
                        }
                        videoCommon.sendMyVideoData(yv12buf, yv12buf.length, false, cameraWidth, cameraHeight, false);
                    }
                }
            }
        }

        camera.addCallbackBuffer(data);
    }

    public void startCamera() {

        Log.d("InfowareLab.Debug","VideoEncodeView.startCamera");

        openCamera();
        if (camera == null) {
            Log.d("InfowareLab.Debug","VideoEncodeView: open camera failed !!!");
            return;
        }

        setCameraParameters(degrees);
        if (Integer.parseInt(Build.VERSION.SDK) >= 16 && isHardCodec && initVideo) {
            int ret;
            if (h264HwEncoderImpl == null){
                h264HwEncoderImpl = new AvcHardEncoder();
            }
            if (isPortrait)
                ret = h264HwEncoderImpl.initEncoder(cameraHeight, cameraWidth);
            else
                ret = h264HwEncoderImpl.initEncoder(cameraWidth, cameraHeight);
            if (ret == 1)
                isHardCodec = false;
        }
        //if (initVideo)
        //    camera.setPreviewCallback(this);

        mFrameLength = ((cameraWidth * cameraHeight) * ImageFormat.getBitsPerPixel(ImageFormat.NV21)) / 8;

        //sharedInputBuff = new byte[mFrameLength];
        //privateInputBuff = new byte[mFrameLength];
        camera.addCallbackBuffer(new byte[mFrameLength]);
        camera.setPreviewCallbackWithBuffer(this);

        if (mPreviewHolder != null) {
            try {
                //mPreviewHolder.setFixedSize(cameraWidth, cameraHeight);
                camera.setPreviewDisplay(mPreviewHolder);
            } catch (IOException e) {
                e.printStackTrace();
            }

            changePreview(true);
        }
        else
        {
            Log.d("InfowareLab.Debug", "VideoEncoderView1: Surface is NOT ready.");
        }

        setBackgroundColor(0);
    }

    public void openCamera() {

        Log.d("InfowareLab.Debug","VideoEncodeView.openCamera");
        if (camera != null) return;

        if (Integer.parseInt(Build.VERSION.SDK) > 8) {
            if (numOfCamera == 1) {
                currentCamera = CameraInfo.CAMERA_FACING_BACK;
                try {
                    camera = Camera.open(currentCamera);
                }catch (Exception e){
                    Log.d("InfowareLab.Debug","VideoEncodeView.openCamera Error: " + e.getMessage());
                    //Log.e("YYYYYY","异常:::"+e);
                    e.printStackTrace();
                    //重新打开
                    //reStartLocalView();
                }
            } else {
                if (isFlow){
                    currentCamera = CameraInfo.CAMERA_FACING_BACK;
                }else {
                    currentCamera = CameraInfo.CAMERA_FACING_FRONT;
                }
                try {
                    camera = Camera.open(currentCamera);
                } catch (Exception e) {
                    Log.d("InfowareLab.Debug","VideoEncodeView.openCamera Error(2): " + e.getMessage());
                    e.printStackTrace();
                    //重新打开
                    //reStartLocalView();
                }
            }
        } else {
            try {
                camera = Camera.open(currentCamera);
            }catch (Exception e){
                e.printStackTrace();
                Log.d("InfowareLab.Debug","VideoEncodeView.openCamera Error(3): " + e.getMessage());
                //重新打开
                //reStartLocalView();
            }
        }
        if (Build.MODEL.toUpperCase().startsWith("Lenovo".toUpperCase())){
            camera.setDisplayOrientation(0);
        }
    }

    private void setCameraOrientation(int degrees, Parameters p) {
        if (Integer.parseInt(Build.VERSION.SDK) >= 8)
            setDisplayOrientation(camera, degrees);
        else {
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                p.set("orientation", "portrait");
                p.set("rotation", degrees);
            }
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
                p.set("orientation", "landscape");
                p.set("rotation", degrees);
            }
        }
    }

    private void setDisplayOrientation(Camera mCamera, int angle) {
        Method downPolymorphic;
        try {
            downPolymorphic = mCamera.getClass().getMethod(
                    "setDisplayOrientation", new Class[]{int.class});
            if (downPolymorphic != null)
                downPolymorphic.invoke(mCamera, new Object[]{angle});
        } catch (Exception e1) {
        }
    }

    /*
     * 设置相机属性
     */
    private void setCameraParameters(int degrees) {

        Log.d("InfowareLab.Debug","VideoEncodeView.setCameraParameters");

        if (videoCommon == null) {
            Log.d("InfowareLab.Debug","VideoEncodeView.setCameraParameters: videoCommon == null");
            return;
        }

        Parameters parameters = camera.getParameters();
        List<Camera.Size> previewSizes = parameters.getSupportedPreviewSizes();
        List<int[]> rates = parameters.getSupportedPreviewFpsRange();
        cameraWidth = 0;
        cameraHeight = 0;

        if (videoCommon.getWidth() <= 0 || videoCommon.getHeight() <= 0) {

            int width = SharedPreferencesUrls.getInstance().getInt("width", 1280);
            int height = SharedPreferencesUrls.getInstance().getInt("height", 720);

            if (videoCommon != null) {
                videoCommon.setWidth(width);
                videoCommon.setHeight(height);
                //Log.d("InfowareLab.Debug", ">>>>>>FragVs.local resolution: " + width + "x" + height);
            }
        }

        Log.d(TAG,"VideoEncodeView: Preferred Size: " + videoCommon.getWidth() + "x" + videoCommon.getHeight());

        support4K = false;

        // 取比设定值小的像素中最大的ss
        if (!isFlow){
            for (Camera.Size size : previewSizes) {
                if (size.width * size.height <= videoCommon.getWidth()
                        * videoCommon.getHeight()
                        && size.height >= 0) {

                    Log.d("InfowareLab.Debug","VideoEncodeView.PreviewSize: " + size.width + "x" + size.height);

                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width * size.height >= cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }

                if (size.width >= 4096 || size.height >= 2160)
                    support4K = true;
            }
            // 如果设定值实在太小，取所支持的最小像素
            if (cameraWidth == 0) {
                for (Camera.Size size : previewSizes) {
                    if (size.height >= 0) {
                        if (cameraWidth == 0) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                        if (size.width * size.height <= cameraWidth * cameraHeight) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                    }
                }
            }
        }else {
            //保存的值
            int with = SharedPreferencesUrls.getInstance().getInt("width1",1280);
            int height = SharedPreferencesUrls.getInstance().getInt("height1",720);
            for (Camera.Size size : previewSizes) {
                if (size.width * size.height <= with *height
                        && size.height >= 0) {
                    if (cameraWidth == 0) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                    if (size.width * size.height >= cameraWidth * cameraHeight) {
                        cameraWidth = size.width;
                        cameraHeight = size.height;
                    }
                }
            }
            // 如果设定值实在太小，取所支持的最小像素
            if (cameraWidth == 0) {
                for (Camera.Size size : previewSizes) {
                    if (size.height >= 0) {
                        if (cameraWidth == 0) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                        if (size.width * size.height <= cameraWidth * cameraHeight) {
                            cameraWidth = size.width;
                            cameraHeight = size.height;
                        }
                    }
                }
            }
        }

        int minimum = 0;
        int maximum = 0;
        int retMininum = 0;
        int retMaximum = 0;
        if (rates.size() > 0) {
            minimum = rates.get(0)[0];
            maximum = rates.get(0)[1];
            retMininum = rates.get(0)[0];
            retMaximum = rates.get(0)[1];
            for (int[] fps : rates){
                if (minimum < fps[0]){
                    minimum = fps[0];
                }
                if (maximum < fps[1]){
                    maximum = fps[1];
                }
            }
        }

        if (initVideo) {
            videoCommon
                    .initializeMyVideo(cameraWidth, cameraHeight, 15);
            Log.d("InfowareLab.Debug","videoCommon.initializeMyVideo: " + cameraWidth + "x" + cameraHeight);
            //videoCommon
            //    .initializeMyVideo(cameraWidth, cameraHeight, 30);
            //initVideo = false;
        }

        if (isDegrees){
            Log.d("InfowareLab.Debug","VideoEncodeView.setCameraOrientation: " + degrees);
            setCameraOrientation(degrees, parameters);
        }else {
            Log.d("InfowareLab.Debug","VideoEncodeView.setCameraOrientation: 0");
            setCameraOrientation(0, parameters);
        }
        //holder.setFixedSize(cameraWidth,cameraHeight);
        parameters.setPreviewSize(cameraWidth, cameraHeight);// 设置预览的高度和宽度,单位为像素
        Log.d("InfowareLab.Debug","VideoEncodeView.setPreviewSize: " + cameraWidth + "x" + cameraHeight);
        parameters.setPreviewFpsRange(minimum, maximum);// 设置图片预览的帧速。
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){
                if (minimum > 0 && maximum > 0){
                    //Log.d("InfowareLab.Debug","parameters.setPreviewFpsRange: " + minimum + "~" + maximum);
                    parameters.setPreviewFpsRange(minimum,maximum);
                }
            }else {
                if (retMininum > 0 && retMaximum > 0){
                    //Log.d("InfowareLab.Debug","parameters.setPreviewFpsRange(OLD): " + minimum + "~" + maximum);
                    parameters.setPreviewFpsRange(retMininum,retMaximum);
                }
            }
        }catch (RuntimeException e){
            if (retMininum > 0 && retMaximum > 0){
                Log.d("InfowareLab.Debug","parameters.setPreviewFpsRange(EXCEPTION): " + minimum + "~" + maximum);
                parameters.setPreviewFpsRange(retMininum,retMaximum);
            }
        }
        camera.setParameters(parameters);
    }

    public void changeCameraFacing() {
        destroyCamera();
        currentCamera = CameraInfo.CAMERA_FACING_BACK;
//        currentCamera = (currentCamera + 1) % numOfCamera;
        startCamera();
    }

    public void flushEncoder() {
        //if (isHardCodec && h264HwEncoderImpl.GetMediaEncoder() != null) {
        //   h264HwEncoderImpl.flushEncoder();
        //}
    }

    public void reStartLocalView() {

        Log.d("InfowareLab.Debug", "VideoEncodeView.reStartLocalView");
        if (camera == null) {
            changeStatus(true);
        } else {
            if (null != videoCommon && initVideo){
                if ((this.cameraWidth != videoCommon.getWidth()) || (this.cameraHeight != videoCommon.getHeight())) {
                    destroyCamera();
                    startCamera();
                    videoCommon.exChange(cameraHeight, cameraWidth);
                }
                else {
                    Log.d("InfowareLab.Debug", "No need to restart the second camera, ignored!");
                }
            }
        }
    }

    public void changeStatus(boolean isOpenCamera) {

        if (isOpenCamera) {
            if (camera == null) {
                Log.d("InfowareLab.Debug","VideoEncodeView.changeStatus(init and start camera): isOpenCamera=" + isOpenCamera);
                invalidate();
                init();
                startCamera();
            }
        } else {
            if (camera != null) {
                Log.d("InfowareLab.Debug","VideoEncodeView.changeStatus(destroy camera): isOpenCamera=" + isOpenCamera);
                destroyCamera();
            }
        }
    }

    public void destroyCamera() {

//        if (isWriteFile){
//            if (_fr != null) _fr.
//        }

        if (camera != null) {

            Log.d("InfowareLab.Debug", "VideoEncodeView.destroyCamera");
            //isSharing = false;
            camera.setPreviewCallback(null);
            camera.stopPreview();
            camera.release();
            h264HwEncoderImpl.releaseEncoder();
            mVideoFrameQueue.clear();
            h264HwEncoderImpl = null;
            camera = null;
        }
    }

    private void changePreview(boolean state) {
        Log.d("Infoware.Lab", "VideoEncodeView.changePreview state=" + state);
        try {
            if (state) {
                camera.startPreview();
                mPreviewStarted = true;
            } else {
                camera.stopPreview();
                mPreviewStarted = false;
            }
        } catch (Exception e) {
            Log.d("Inforware.Lab", "VideoEncodeView.changePreview Error=" + e.getMessage());
            mPreviewStarted = false;
        }
    }

    public boolean isPreviewStarted() { return mPreviewStarted; }

    public void changeOrietation(Configuration newConfig) {
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // 横向
            setPortrait(false);
            setCameraLandscape();
        } else {
            // 竖向
            setPortrait(true);
            setCameraPortrait();
        }
    }

    public void setPortrait(boolean isPortrait) {
        if (isDegrees){
            if (isPortrait) {
                videoCommon.exChange(cameraHeight, cameraWidth);
            } else {
                videoCommon.exChange(cameraWidth, cameraHeight);
            }
        }
    }

    public void setCameraLandscape() {
        degrees = 0;
        isPortrait = false;
        if (camera != null) {
            destroyCamera();
            openCamera();
            if (camera == null) {
                return;
            }

            setCameraParameters(0);
            camera.setPreviewCallback(this);
            if (mPreviewHolder != null) {
                try {
                    //mPreviewHolder.setFixedSize(cameraWidth, cameraHeight);
                    camera.setPreviewDisplay(mPreviewHolder);
                } catch (IOException e) {
                    e.printStackTrace();
                }

                changePreview(true);
            }
            else
            {
                Log.d("InfowareLab.Debug", "VideoEncoderView1: Surface is NOT ready.");
            }
        }
    }

    public void setCameraPortrait() {
        degrees = 90;
        isPortrait = true;
        if (camera != null) {
            destroyCamera();
            openCamera();
            if (camera == null) {
                return;
            }

            setCameraParameters(90);
            camera.setPreviewCallback(this);
            if (mPreviewHolder != null) {
                try {
                    //mPreviewHolder.setFixedSize(cameraWidth, cameraHeight);
                    camera.setPreviewDisplay(mPreviewHolder);
                } catch (IOException e) {
                    e.printStackTrace();
                }

                changePreview(true);
            }
            else
            {
                Log.d("InfowareLab.Debug", "VideoEncoderView1: Surface is NOT ready.");
            }
        }
    }

    public void setInitVideo(boolean initVideo) {
        this.initVideo = initVideo;
    }

    public boolean isSharing() {
        return isSharing;
    }

    public void setSharing(boolean isSharing) {
        this.isSharing = isSharing;
        mVideoFrameQueue.clear();
    }

    public int getDegrees() {
        return degrees;
    }

    public void setDegrees(int degrees) {
        this.degrees = degrees;
    }

    public boolean isCameraNUll() {
        return camera != null;
    }

    private byte[] rotateYUV420SPFrontfacing(byte[] src, int width, int height) {
        byte[] des = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;
        // 旋转Y
        int k = 0;
        for (int i = width - 1; i >= 0; i--) {
            for (int j = 0; j < height; j++) {
                des[k++] = src[width * j + i];
            }
        }
        for (int i = width - 2; i >= 0; i -= 2) {
            for (int j = 0; j < height / 2; j++) {
                des[k] = src[wh + width * j + i + 1];
                des[k + uv] = src[wh + width * j + i];
                k++;
            }
        }
        return des;
    }

    public static byte[] rotateYUV420SPBackfacing(byte[] src, int width,
                                                  int height) {
        byte[] des = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;
        // 旋转Y
        int k = 0;
        for (int i = 0; i < width; i++) {
            for (int j = height - 1; j >= 0; j--) {
                des[k] = src[width * j + i];
                k++;
            }
        }

        for (int i = 0; i < width; i += 2) {
            for (int j = height / 2 - 1; j >= 0; j--) {
                des[k] = src[wh + width * j + i + 1];
                des[k + uv] = src[wh + width * j + i];
                k++;
            }
        }

        return des;

    }

    private byte[] changeYUV420SP2P(byte[] src, int width, int height) {
        System.gc();
        byte[] yv12buf = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;

        System.arraycopy(src, 0, yv12buf, 0, wh);

        int k = 0;
        for (int i = 0; i < wh / 2; i += 2) {
            yv12buf[wh + k] = src[wh + i + 1];
            yv12buf[wh + uv + k] = src[wh + i];
            k++;
        }
        return yv12buf;
    }

    private byte[] Rotate180YUV420SP2P(byte[] src, int width, int height) {
        byte[] yv12buf = new byte[src.length];
        int wh = width * height;
        int uv = wh / 4;
        int k = 0;

        for (int i = 0; i < wh; i++) {
            yv12buf[k++] = src[wh - i];
        }

        for (int i = wh * 3 / 2 - 1; i >= wh; i -= 2) {
            yv12buf[k] = src[i];
            yv12buf[uv + k] = src[i - 1];
            k++;
        }
        return yv12buf;
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        Log.d("InfowareLab.Debug", "VideoEncodeView.surfaceCreated");

        mPreviewHolder = holder;

        if (!mPreviewStarted && camera != null){
            try {
                //mPreviewHolder.setFixedSize(cameraWidth, cameraHeight);
                camera.setPreviewDisplay(mPreviewHolder);
            } catch (IOException e) {
                e.printStackTrace();
            }
            changePreview(true);
        }
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width,
                               int height) {

        Log.d("InfowareLab.Debug", "VideoEncodeView.surfaceChanged: " + width + "x" + height);
        mPreviewHolder = holder;
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        Log.d("InfowareLab.Debug", "VideoEncodeView.surfaceDestroyed");
        mPreviewHolder = null;
        destroyCamera();
    }


    public void setParams(int width, int height) {

        Log.d(TAG, "VideoEncodeView.setParams: " + width + "x" + height);

        if (mViewWidth == width && mViewHeight == height)
            return;

        mViewWidth = width;
        mViewHeight = height;

        if (width > 1 && camera == null) {
            reStartLocalView();
        }

        if (width <= 1) {
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = 1;
            params.height = 1;
            setLayoutParams(params);
        } else {

            int h = height;
            int w = width;
            if (degrees % 180 == 0) {
                if ((1.0f * cameraWidth / cameraHeight) > (1.0f * width / height)) {
                    h = (int) ((1.0f * cameraHeight / cameraWidth) * width);
                } else {
                    w = (int) ((1.0f * cameraWidth / cameraHeight) * height);
                }
            } else {
                if ((1.0f * cameraHeight / cameraWidth) > (1.0f * width / height)) {
                    h = (int) ((1.0f * cameraWidth / cameraHeight) * width);
                } else {
                    w = (int) ((1.0f * cameraHeight / cameraWidth) * height);
                }
            }
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getLayoutParams();
            params.width = width;
            params.height = height;
//            params.setMargins((width - w) / 2, (height - h) / 2, 0, 0);
            params.setMargins(0, 0, DensityUtil.dip2px(getContext(),1.0f), 0);
            Log.d("InfowareLab.Debug","setParams: " + width + "x" + height);
            //if (mPreviewHolder != null) mPreviewHolder.setFixedSize(width,height);
            setLayoutParams(params);

        }
    }
    //修改分辨率
    public void setStreamVideoSize(int width, int height ){
        if (videoCommon != null){
            videoCommon.setWidth(width);
            videoCommon.setHeight(height);
        }
        reStartLocalView();
    }

    public boolean isSupport4K() {
        return support4K;
    }
}