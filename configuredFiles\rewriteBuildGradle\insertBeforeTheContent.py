#encoding=gbk
'''
Created on 2018-6-4

@author: Sing <PERSON>
'''
import os
ks='dependencies {'
bks = str.encode(ks)
f = open('build.gradle','rb')
f_readlines = f.readlines()
for f_readline in range(len(f_readlines)):
    if bks in f_readlines[f_readline]:
        print ("The "+'"'+ '}' +'"'+" line number is "+ str(f_readline-1))
        print ("The "+'"'+ ks +'"'+" line number is "+ str(f_readline+1))
        break
else:
    print ('sorry! There are no corresponding rows: '+ '"'+ ks +'".')
f.close()
sbg_file = open('build.gradle', 'r+')
scg_fileadd = open('signingConfigs.gradle', 'r+')
content = sbg_file.read()
contentadd = scg_fileadd.read()
sbg_file.close()
scg_fileadd.close()
always = content.split('\n')
always.insert(f_readline-1, contentadd)
shinn = '\n'.join(always)
fp = open('build.gradle', 'r+')
fp.write(str(shinn))
fp.close()
