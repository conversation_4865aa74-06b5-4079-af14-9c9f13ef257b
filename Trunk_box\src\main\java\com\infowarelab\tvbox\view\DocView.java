package com.infowarelab.tvbox.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.os.Handler;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnTouchListener;
import android.widget.ImageView;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.FileUtils;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.domain.AnnotationBean;
import com.infowarelabsdk.conference.domain.DocBean;
import com.infowarelabsdk.conference.domain.PageBean;
import com.infowarelabsdk.conference.domain.Point;
import com.infowarelabsdk.conference.domain.UserBean;
import com.infowarelabsdk.conference.shareDoc.DocCommon;
import com.infowarelabsdk.conference.util.ColorUtil;
import com.infowarelabsdk.conference.util.Constants;

////import org.apache.log4j.Logger;

import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 文档视图
 *
 * <AUTHOR>
 */
public class DocView extends ImageView implements OnTouchListener {
    //private Logger log = Logger.getLogger(getClass());

    protected static  float eraserOffset= 1;
    private static final float ANIMATION_WIDTH_OFFSET = 0.35010f;
    private static final float ANIMATION_HEIGHT_OFFSET = 0.32953f;

    private DocBean doc;

    /**
     * 文档高,宽
     */
    private int docWidth = 0;
    private int docHeight = 0;
    private int viewHeight = 0;
    private int viewWidth = 0;
    private int offsetHeight = 0;
    private int rootHeight = 0;


    private DocCommonImpl docCommon = (DocCommonImpl) CommonFactory.getInstance().getDocCommon();


    private Handler handler;

    private Bitmap bm = null;

    /**
     * 图像缩放比例
     */
    private Matrix matrix = new Matrix();
    private Matrix savedMatrix = new Matrix();
    private float[] matrixValues = new float[9];

    /**
     * 中心点
     */
    private PointF mid = new PointF();

    /**
     * 缩放限制
     */
    private static float maxZoom = 3f;
    private static float minZoom = 0.2f;

    private static float densityRatio = 1.0f;

    private boolean scaleIsZero = false;
    private boolean isOffset = true;

    protected static final int POINTER = 1;
    protected static final int NORMAL = 2;

    /** 开始注释 */
    protected boolean isBeginAnno = false;

    protected AnnotationBean annotation;

    /** 是否开始橡皮擦功能 */
    protected boolean isBeginClean = false;

    private boolean isPad =false;


    public DocView(Context context) {
        super(context);
        this.setOnTouchListener(this);
    }

    public DocView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.setOnTouchListener(this);
    }

    public DocView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        this.setOnTouchListener(this);
    }

    public void setDoc(DocBean doc, int pw, int ph, int oh) {
        viewWidth = pw > 0 ? pw : (getWidth() > 0 ? getWidth() : ConferenceApplication.Root_W);
        viewHeight = ph > 0 ? ph : (getHeight() > 0 ? getHeight() : ConferenceApplication.Root_H);
        offsetHeight = oh >= 0 ? oh : ConferenceApplication.StateBar_H;
        setDoc(doc);
    }

    public void setDensityRatio(float ratio) { densityRatio = ratio; }

    /**
     * 设置显示文档内容
     *
     * @param doc
     */
    float scale = 0;
    float top = 0;
    public void setDoc(DocBean doc) {
        this.isOffset = android.os.Build.VERSION.SDK_INT >= 24 ? false : true;
        if (null == doc)
            return;
        try {
            this.doc = doc;
            matrix.reset();
            if (null == doc.getPage())
                return;

            PageBean page = doc.getPage();
            int len = page.getLength();
            if (len == 1) {
                bm = Bitmap.createBitmap(1, 1,
                        Bitmap.Config.ARGB_8888);
                bm.eraseColor(Color.parseColor("#FFFFFF"));
                docHeight = page.getHeight();
                docWidth = page.getWidth();
                int diff = isOffset ? offsetHeight : 0;
                minZoom = (float) viewHeight / this.docHeight;
                minZoom = minZoom > ((float) viewWidth / this.docWidth) ? ((float) viewWidth / this.docWidth)
                        : minZoom;
//                if (scale >= 1.0f * viewHeight / this.docHeight) {
//                    scale = 1.0f * viewHeight / this.docHeight;
//                }
                if ("ppt".equals(FileUtils.getExtensionName(doc.getTitle()).substring(0,3).toLowerCase())
                        && (((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).getSyncMap().size() == 0)){
                    scale = 1.0f * viewHeight / this.docHeight;
                }else {
                    scale = 1.0f * viewWidth / this.docWidth;
                }
                if (scale == 0) {
                    scale = 1;
                }
                if (scale != 0) {
                    float left = (viewWidth - docWidth * scale) / 2;
                    if ("ppt".equals(FileUtils.getExtensionName(doc.getTitle()).substring(0,3).toLowerCase()) &&
                            0 == (((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).getSyncMap().size())){
                        top = (viewHeight - docHeight * scale) / 2 + diff;
                    }else {
                        top = (viewHeight - docHeight * scale) > 0 ? (viewHeight - docHeight * scale) / 2 : 1;
                    }
                    matrix.postScale(scale*densityRatio, scale*densityRatio);
                    matrix.postTranslate(left, top);
                    scaleIsZero = false;
                } else {
                    scaleIsZero = true;
                }
                matrix.getValues(matrixValues);
                requestLayout();
                invalidate();
            } else if (len > 0) {

                bm = page.getData();
                docHeight = bm.getHeight();
                docWidth = bm.getWidth();

                float docRatio = (float)docWidth/docHeight;
                float viewRatio = (float)viewWidth/viewHeight;

                if (viewRatio < docRatio) {

                    int diff = isOffset ? offsetHeight : 0;

                    minZoom = (float) viewHeight / this.docHeight;
                    minZoom = minZoom > ((float) viewWidth / this.docWidth) ? ((float) viewWidth / this.docWidth)
                            : minZoom;

                    //                if (scale >= 1.0f * viewHeight / this.docHeight) {
                    //                    scale = 1.0f * viewHeight / this.docHeight;
                    //                }

                    String extensionName = FileUtils.getExtensionName(doc.getTitle());

                    if (extensionName.length() >= 3 && "ppt".equals(extensionName.substring(0, 3).toLowerCase())
                            && viewHeight > viewWidth) {
                        scale = 1.0f * viewHeight / this.docHeight;
                    } else {
                        scale = 1.0f * viewWidth / this.docWidth;
                    }
                    if (scale == 0) {
                        scale = 1;
                    }

                    if (scale != 0) {
                        float left = (viewWidth - docWidth * scale) / 2;
                        if (extensionName.length() >= 3 && "ppt".equals(extensionName.substring(0, 3).toLowerCase())) {
                            top = (viewHeight - docHeight * scale) / 2 + diff;
                        } else {
                            top = (viewHeight - docHeight * scale) > 0 ? (viewHeight - docHeight * scale) / 2 : 0;
                        }

                        Log.d("InfowareLab.DocView", "postScale = " + scale*densityRatio);

                        matrix.postScale(scale*densityRatio, scale*densityRatio);
                        matrix.postTranslate(left, top);
                        scaleIsZero = false;
                    } else {
                        scaleIsZero = true;
                    }
                }
                else
                {
                    int diff = isOffset ? offsetHeight : 0;

                    minZoom = (float) viewHeight / this.docHeight;
                    minZoom = minZoom > ((float) viewWidth / this.docWidth) ? ((float) viewWidth / this.docWidth)
                            : minZoom;

                    String extensionName = FileUtils.getExtensionName(doc.getTitle());

                    if (extensionName.length() >= 3 && "ppt".equals(extensionName.substring(0, 3).toLowerCase())
                            && viewHeight > viewWidth) {
                        scale = 1.0f * viewHeight / this.docHeight;
                    } else {
                        scale = 1.0f * viewHeight / this.docHeight;
                    }
                    if (scale == 0) {
                        scale = 1;
                    }

                    if (scale != 0) {
                        float left = (viewWidth - docWidth * scale) / 2;
                        if (extensionName.length() >= 3 && "ppt".equals(extensionName.substring(0, 3).toLowerCase())) {
                            top = (viewHeight - docHeight * scale) / 2 + diff;
                        } else {
                            top = (viewHeight - docHeight * scale) > 0 ? (viewHeight - docHeight * scale) / 2 : 1;
                        }
                        matrix.postScale(scale*densityRatio, scale*densityRatio);
                        matrix.postTranslate(left, top);
                        scaleIsZero = false;
                    } else {
                        scaleIsZero = true;
                    }
                }
                matrix.getValues(matrixValues);
                requestLayout();
                invalidate();

            } else {
//                getRootView().findViewById(R.id.shareNoDoc).setVisibility(View.VISIBLE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void refresh() {
        if (doc != null) {
            invalidate();
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (scaleIsZero) {
            setDoc(doc);
        } else {
            draw2Canvas(canvas, true);
        }
    }

    /**
     * 将内容画到Canvas中
     *
     * @param canvas
     */
    private void draw2Canvas(Canvas canvas, boolean drawbm) {

        if (drawbm) {
            canvas.setMatrix(matrix);
            if (bm != null && !bm.isRecycled()) {
                if (bm.getWidth() == 1 && bm.getHeight() == 1) {
                    //Log.e("ldy","111111111111");
                    Paint p = new Paint();
                    p.setColor(Color.WHITE);
                    p.setStyle(Paint.Style.FILL);
                    canvas.drawRect(0, 0, 4200, 5940, p);
                } else {
                    //Log.e("ldy","22222222");
//                    int startX = (int) ((viewWidth - docWidth * scale)/2);
//                    int startY = (int) ((viewHeight - docHeight * scale)/2);
//                    if (startX < 0) startX = 0;
//                    if (startY < 0) startY = 0;
                    canvas.drawBitmap(bm, 0, 0, null);
                }
            }
        }
        if (doc != null && null != doc.getPage()) {
            drawAnnotation(canvas, doc.getPage());
        }

    }


    /**
     * 图片缩放
     *
     * @param scale
     */
    public void zoom(float scale) {
        if (bm != null && !bm.isRecycled()) {
            float[] values = new float[9];
            matrix.getValues(values);
            if (values[Matrix.MSCALE_X] >= maxZoom && scale > 1) {
                return;
            }
            doZoom(scale, scale);
            savedMatrix.set(matrix);
            invalidate();
        }
    }

    /**
     * 显示接收的注释
     *
     * @param canvas
     * @param page
     */
    private void drawAnnotation(Canvas canvas, PageBean page) {
        if (page == null || page.getPageID() == 0)
            return;
        ArrayList<AnnotationBean> annotations = (ArrayList<AnnotationBean>) page.getAnnotations().clone();
        if (annotations == null || annotations.isEmpty())
            return;
        try {
            for (AnnotationBean bean : annotations) {
                try {
                    drawAnnotation(canvas, bean);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 显示本地操作的注释
     *
     * @param canvas
     * @param annotation
     */
    private void drawAnnotation(Canvas canvas, AnnotationBean annotation) {
        try {
            if (annotation != null) {
                float scaleX, scaleY;
                if (DocCommonImpl.isAnimation) {
                    scaleY = (float) docHeight / (float) annotation.getHeight() * ANIMATION_HEIGHT_OFFSET;
                    scaleX = (float) docWidth / (float) annotation.getWidth() * ANIMATION_WIDTH_OFFSET;
                } else {
                    scaleY = (float) docHeight / (float) annotation.getHeight();
                    scaleX = (float) docWidth / (float) annotation.getWidth();
                }
                Paint mPaint = new Paint();
                if (annotation.getColor() != null) {
                    mPaint.setColor(Color.parseColor(annotation.getColor()));
                } else {
                    mPaint.setColor(Color.RED);
                }
                mPaint.setTextSize(annotation.getLineWidth());
                if (annotation.getFilltype() == Constants.FILL_FLAG) {
                    mPaint.setStyle(Paint.Style.FILL);
                } else {
                    mPaint.setStyle(Paint.Style.STROKE);
                }
                int wid = annotation.getLineWidth();
                matrix.getValues(matrixValues);

                if ((wid * matrixValues[Matrix.MSCALE_X]) < 1) {
                    wid = (int) Math.ceil((1 / matrixValues[Matrix.MSCALE_X]));
                }
                mPaint.setStrokeWidth(wid);
//				mPaint.setStrokeWidth(annotation.getLineWidth());
                mPaint.setAntiAlias(true);
                if (Constants.SHAPE_RECT.equals(annotation.getAnnoPattern())) {
                    Rect r = new Rect();
                    r.set((int) (annotation.getPoint(0).getX() * scaleX),
                            (int) (annotation.getPoint(0).getY() * scaleY),
                            (int) (annotation.getPoint(1).getX() * scaleX),
                            (int) (annotation.getPoint(1).getY() * scaleY));
                    canvas.drawRect(r, mPaint);
                } else if (Constants.SHAPE_ELLIPSE.equals(annotation.getAnnoPattern())) {
                    RectF rectF = new RectF(annotation.getPoint(0).getX() * scaleX, annotation.getPoint(0).getY()
                            * scaleY, annotation.getPoint(1).getX() * scaleX, annotation.getPoint(1).getY() * scaleY);
                    canvas.drawOval(rectF, mPaint);
                } else if (Constants.SHAPE_POLY_LINE.equals(annotation.getAnnoPattern())) {
                    List<Point> points = annotation.getPoints();
                    if (points != null) {
                        if (annotation.getPolygonPattern().equals(Constants.STROKE_HILIGHT)) {
                            mPaint.setAlpha(128);
                        }
                        Path path = new Path();
                        boolean isFrist = true;
                        for (int i = 0; i < points.size(); i++) {
                            float x = points.get(i).getX();
                            float y = points.get(i).getY();
                            if (android.os.Build.VERSION.SDK_INT > 20 && android.os.Build.VERSION.SDK_INT < 23) {
                                if (x * scaleX >= docWidth) x = (float) docWidth / scaleX - 1;
                                if (y * scaleY * matrixValues[Matrix.MSCALE_Y] >= viewHeight)
                                    y = (float) viewHeight / (scaleY * matrixValues[Matrix.MSCALE_Y]) - 1;
                                if (x <= 0) x = 1;
                                if (y <= 0) y = 1;
                            } else {
                                if (x * scaleX >= docWidth) x = (float) docWidth / scaleX - 1;
                                if (y * scaleY >= docHeight) y = (float) docHeight / scaleY - 1;
                                if (x <= 0) x = 1;
                                if (y <= 0) y = 1;
                            }
                            if (isFrist) {
                                path.moveTo(x * scaleX, y * scaleY);
                                isFrist = false;
                            } else {
                                path.lineTo(x * scaleX, y * scaleY);
                            }
                        }
                        if (Constants.SHAPE_CLOSE.equals(annotation.getPolygonPattern()) && !isFrist) {
                            path.close();
                        }
                        if (!isFrist)
                            canvas.drawPath(path, mPaint);
                    }
                } else if (Constants.SHAPE_LINE.equals(annotation.getAnnoPattern())) {
                    float x = annotation.getPoint(0).getX();
                    float y = annotation.getPoint(0).getY();
                    float x1 = annotation.getPoint(1).getX();
                    float y1 = annotation.getPoint(1).getY();
                    //log.info("scaleX:" + scaleX + " scaleY:" + scaleY);
                    //log.info("receive point: (" + x + "," + y + ") (" + x1 + "," + y1 + ")");
                    //log.info("phone point: (" + x * scaleX + "," + y * scaleY + ") (" + x1 * scaleX + "," + y1 * scaleY + ")");
                    canvas.drawLine(annotation.getPoint(0).getX() * scaleX, annotation.getPoint(0).getY() * scaleY,
                            annotation.getPoint(1).getX() * scaleX, annotation.getPoint(1).getY() * scaleY, mPaint);

                } else if (Constants.SHAPE_POINTER.equals(annotation.getAnnoPattern())) {
                    float x = annotation.getPoint(0).getX() * scaleX;
                    float y = annotation.getPoint(0).getY() * scaleY;
                    float lineLong = getResources().getDimension(R.dimen.height_101_80) / matrixValues[Matrix.MSCALE_X];
                    float txtSize = getResources().getDimension(R.dimen.height_101_80) / matrixValues[Matrix.MSCALE_X];
                    float height = getResources().getDimension(R.dimen.height_102_80) / matrixValues[Matrix.MSCALE_X];
                    float lineWidth = getResources().getDimension(R.dimen.line1) / matrixValues[Matrix.MSCALE_X];
                    int words = 2;
                    String name = "";
                    if (annotation.getRoleOrname() != null) {
                        name = annotation.getRoleOrname().trim();
                    }
                    if (name == null || name.equals("")) name = "Attender";
                    try {
                        name = idgui(name, 10);
                        words = getWords(name, 2);
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }

                    mPaint.setStrokeWidth(lineWidth);
                    canvas.drawLine(x, y, x + lineLong + (height / 10), y + lineLong + (height / 10), mPaint);

                    //画圆角矩形
                    mPaint.setStyle(Paint.Style.FILL);//充满
                    mPaint.setAntiAlias(true);// 设置画笔的锯齿效果
                    RectF oval3 = new RectF(x + lineLong, y + lineLong, x + txtSize * words / 2 + lineLong + height, y + height + lineLong);// 设置个新的长方形
                    canvas.drawRoundRect(oval3, height / 2, height / 2, mPaint);//第二个参数是x半径，第三个参数是y半径

                    Paint textPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DEV_KERN_TEXT_FLAG);
                    textPaint.setTextSize(txtSize);
                    textPaint.setTypeface(Typeface.DEFAULT);
                    textPaint.setColor(Color.WHITE);
                    canvas.drawText(name, x + height / 2 + lineLong, y + (height + 2 * txtSize) / 3 + lineLong, textPaint);

                } else if (Constants.SHAPE_POLYGON.equals(annotation.getAnnoPattern())) {
                    List<Point> points = annotation.getPoints();
                    if (points != null) {
                        if (annotation.getPolygonPattern().equals(Constants.STROKE_HILIGHT)) {
                            mPaint.setAlpha(128);
                        }
                        Path path = new Path();
                        boolean isFrist = true;
                        for (int i = 0; i < points.size(); i++) {
                            float x = points.get(i).getX();
                            float y = points.get(i).getY();
                            if (android.os.Build.VERSION.SDK_INT > 20 && android.os.Build.VERSION.SDK_INT < 23) {
                                if (x * scaleX >= docWidth) x = (float) docWidth / scaleX - 1;
                                if (y * scaleY * matrixValues[Matrix.MSCALE_Y] >= viewHeight)
                                    y = (float) viewHeight / (scaleY * matrixValues[Matrix.MSCALE_Y]) - 1;
                                if (x <= 0) x = 1;
                                if (y <= 0) y = 1;
                            } else {
                                if (x * scaleX >= docWidth) x = (float) docWidth / scaleX - 1;
                                if (y * scaleY >= docHeight) y = (float) docHeight / scaleY - 1;
                                if (x <= 0) x = 1;
                                if (y <= 0) y = 1;
                            }
                            if (isFrist) {
                                path.moveTo(x * scaleX, y * scaleY);
                                isFrist = false;
                            } else {
                                path.lineTo(x * scaleX, y * scaleY);
                            }
                        }
                        if (Constants.SHAPE_CLOSE.equals(annotation.getPolygonPattern()) && !isFrist) {
                            path.close();
                        }
                        if (!isFrist)
                            canvas.drawPath(path, mPaint);
                    }
                } else if (Constants.SHAPE_WRONG.equals(annotation.getAnnoPattern())) {
                    Point start = annotation.getPoint(0);
                    Point end = annotation.getPoint(1);
                    canvas.drawLine(start.x * scaleX, start.y * scaleY, end.x * scaleX, end.y * scaleY, mPaint);
                    canvas.drawLine(start.x * scaleX, end.y * scaleY, end.x * scaleX, start.y * scaleY, mPaint);
                } else if (Constants.SHAPE_RIGHT.equals(annotation.getAnnoPattern())) {
                    Point start = annotation.getPoint(0);
                    Point end = annotation.getPoint(1);
                    Path path = new Path();
                    path.moveTo(start.x * scaleX, end.y * scaleY - (end.y * scaleY - start.y * scaleY) / 3);
                    path.lineTo((start.x * scaleX + end.x * scaleX) / 2, end.y * scaleY);
                    path.lineTo(end.x * scaleX, start.y * scaleY);
                    canvas.drawPath(path, mPaint);
                } else if (Constants.SHAPE_TEXT.equals(annotation.getAnnoPattern())) {
                    float x = annotation.getPoint(0).getX() * scaleX;
                    float y = annotation.getPoint(0).getY() * scaleY;

                    Paint textPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DEV_KERN_TEXT_FLAG);
                    textPaint.setTextSize(Math.abs(annotation.getAnnText().height) * scaleY);
                    textPaint.setTypeface(Typeface.DEFAULT);
                    textPaint.setFakeBoldText(annotation.getAnnText().weight == 700 ? true : false);
                    textPaint.setTextSkewX(annotation.getAnnText().italic == 0 ? 0 : (float) -0.25);
                    textPaint.setStrikeThruText(annotation.getAnnText().strikeOut == 1 ? true : false);
                    textPaint.setUnderlineText(annotation.getAnnText().underline == 1 ? true : false);
                    if (annotation.getColor() != null) {
                        textPaint.setColor(Color.parseColor(annotation.getColor()));
                    } else {
                        textPaint.setColor(Color.BLACK);
                    }

                    Paint.FontMetrics fontMetrics = textPaint.getFontMetrics();
                    float top = fontMetrics.ascent;


                    canvas.drawText(annotation.getAnnText().txt, x, y - top, textPaint);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //获取当前doc X、Y方向上的缩放比例
    private float[] getDocXYScale(DocBean doc) {
        float[] scaleXY = new float[2];
        //X轴变化量
        scaleXY[0] = 1.0f * doc.getPageWidth() / docWidth;
        //Y轴变化量
        scaleXY[1] = 1.0f * doc.getPageHeight() / docHeight;
        return scaleXY;
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        //检测点击时是否菜单弹出
//		getHandler().sendEmptyMessage(DOC_DISMISS_MENU);
        if (doc != null) {
            /**
             * 指示
             */
            if (DocCommonImpl.isStartPointer) {
                drawAnnotation(event, POINTER);
            } else {
                /**
                 * 非指示状态
                 */
                // if(docCommon.getAnnotation() == null){
                // return pointerAction(event);
                // }

                if (docCommon.getAnnotation().isPainting()) {// 画笔工具画图
//					//log.info("Polygon event = " + event.getAction());
                    drawAnnotation(event, NORMAL);
                    return true;
                } else if (docCommon.getEraser().isEraserClean()) {
//					Log.e(TAG, "eraser onTouch");
                    drawEraser(event);
                    return true;
                } else {// 其他工具画图
                    if(doc.getPage() != null
                            && docCommon.getPageCount(doc.getDocID()) > 1){
//                        showSeekbar();
//                        dismissSeekbar();
                    }
//                    switchFileList(event);
//                    return pointerAction(event);
                }
            }
        }else {
//            switchFileList(event);
        }
        return true;

    }
    /**
     * 缩放图片
     *
     * @param scaleX
     * @param scaleY
     */
    private void doZoom(float scaleX, float scaleY) {
        matrix.postScale(scaleX, scaleY, mid.x, mid.y);
        matrix.getValues(matrixValues);
        boolean canZoom = false;
        matrix.set(savedMatrix);
        canZoom = scaleX >= 1f ? (matrixValues[Matrix.MSCALE_X] > maxZoom ? false : true)
                : (matrixValues[Matrix.MSCALE_X] < minZoom ? false : true);
        if (this.docHeight * matrixValues[Matrix.MSCALE_Y] < getHeight()) {
            mid.y = getHeight() / 2 + (rootHeight - getHeight());
        }
        if (this.docWidth * matrixValues[Matrix.MSCALE_Y] < getWidth()) {
            mid.x = getWidth() / 2;
        }
        // 正常缩放
        if (canZoom || (matrixValues[Matrix.MSCALE_X] <= maxZoom && matrixValues[Matrix.MSCALE_X] >= minZoom)) {
            // 放大倍数
            matrix.postScale(scaleX, scaleY, mid.x, mid.y);
        }
        // 小于最低缩放倍数
        else if (matrixValues[Matrix.MSCALE_X] < minZoom) {
            matrix.getValues(matrixValues);
            matrix.postScale(minZoom / matrixValues[Matrix.MSCALE_X], minZoom / matrixValues[Matrix.MSCALE_Y], mid.x,
                    mid.y);
        }
        // 大于最大放大倍数
        else {
            matrix.getValues(matrixValues);
            matrix.postScale(maxZoom / matrixValues[Matrix.MSCALE_X], maxZoom / matrixValues[Matrix.MSCALE_X], mid.x,
                    mid.y);
        }
        matrix.getValues(matrixValues);
        // 缩放控制图片位置
        // 图片高度小于屏幕高度
        if (this.docHeight * matrixValues[Matrix.MSCALE_Y] <= viewHeight) {
            matrix.postTranslate(0, (viewHeight - this.docHeight * matrixValues[Matrix.MSCALE_Y]) / 2
                            + (rootHeight - viewHeight) - matrixValues[Matrix.MTRANS_Y]
                    /*+ getResources().getDimensionPixelSize(R.dimen.height_6_80)*/);
        } else {
            // capline
            if (matrixValues[Matrix.MTRANS_Y] > (rootHeight - viewHeight)) {
                matrix.postTranslate(0, (rootHeight - viewHeight) - matrixValues[Matrix.MTRANS_Y]
                        /*+ getResources().getDimensionPixelSize(R.dimen.height_6_80)*/);
            }
            // baseline
            else if (matrixValues[Matrix.MTRANS_Y] + this.docHeight * matrixValues[Matrix.MSCALE_Y] < rootHeight) {
                matrix.postTranslate(0, rootHeight - matrixValues[Matrix.MTRANS_Y] - this.docHeight
                                * matrixValues[Matrix.MSCALE_Y]
                        /*+ getResources().getDimensionPixelSize(R.dimen.height_6_80)*/);
            }
        }

        // 图片宽度小于屏幕宽度
        if (this.docWidth * matrixValues[Matrix.MSCALE_X] <= getWidth()) {
            matrix.postTranslate((getWidth() - this.docWidth * matrixValues[Matrix.MSCALE_X]) / 2
                    - matrixValues[Matrix.MTRANS_X], 0);
        } else {
            // rightline
            if (matrixValues[Matrix.MTRANS_X] + this.docWidth * matrixValues[Matrix.MSCALE_X] <= getWidth()) {
                matrix.postTranslate(getWidth()
                        - (matrixValues[Matrix.MTRANS_X] + this.docWidth * matrixValues[Matrix.MSCALE_X]), 0);
            }
            // leftline
            else if (matrixValues[Matrix.MTRANS_X] > 0) {
                matrix.postTranslate(0 - matrixValues[Matrix.MTRANS_X], 0);
            }
        }
    }


    public void setHandler(Handler handler) {
        this.handler = handler;
    }

    public Handler getHandler() {
        return this.handler;
    }

    private int getWords(String s, int minSize) throws Exception {
        int changdu = s.getBytes("GBK").length;
        if (changdu > minSize) {
            return changdu;
        } else {
            return minSize;
        }
    }

    private String idgui(String s, int num) throws Exception {
        int changdu = s.getBytes("GBK").length;
        if (changdu > num) {
            s = s.substring(0, s.length() - 1);
            s = idgui2(s, num) + "…";
        }
        return s;
    }

    private String idgui2(String s, int num) throws Exception {
        int changdu = s.getBytes("GBK").length;
        if (changdu > num) {
            s = s.substring(0, s.length() - 1);
            s = idgui2(s, num);
        }
        return s;
    }

    public void postMove(int x, int y) {
        y = y * this.docHeight / 10000;
        float top = 0;
        matrix.getValues(matrixValues);
        if (matrixValues[Matrix.MSCALE_X] > 1.0f * viewHeight / this.docHeight) {
            if (matrixValues[Matrix.MSCALE_Y] * y + viewHeight > this.docHeight * matrixValues[Matrix.MSCALE_Y]) {
                top = viewHeight - docHeight * matrixValues[Matrix.MSCALE_Y] - matrixValues[Matrix.MTRANS_Y];
            } else {
                top = 0 - matrixValues[Matrix.MSCALE_Y] * y - matrixValues[Matrix.MTRANS_Y];
            }
        }

        matrix.postTranslate(0, top);
        invalidate();
    }

    //初始化annotation,把第一个点放进annotation里
    private AnnotationBean initAnnotation(DocBean doc, DocCommon docCommon, Point point, String annoType){


        if(doc.getPage() != null){
            UserBean self = ((UserCommonImpl)CommonFactory.getInstance().getUserCommon()).getSelf();
            AnnotationBean annotation = new AnnotationBean();
            annotation.addPoint(point);
            annotation.setFilltype(Constants.STROKE_FLAG);
            annotation.setDocID(doc.getDocID());
            annotation.setPageID(doc.getPage().getPageID());
            annotation.setUserId(self.getUid());
            annotation.setLineWidth(Integer.valueOf(Constants.STROKE_LINE1));
            annotation.setAnnoPattern(annoType);
            if(Constants.SHAPE_POINTER.equals(annoType)){
                annotation.setBmpId(self.getUid() % 5);
                annotation.setColor(ColorUtil.getHex(annotation.getBmpId()));
            }else{
                annotation.setColor(this.docCommon.getAnnotation().getJsonField(this.docCommon.getAnnotation().getCurrentColor()));
            }

            String username = self.getUsername();
            int usernameLenght = username.length();

            annotation.setWidth(doc.getPageWidth());
            annotation.setHeight(doc.getPageHeight());

            annotation.setRoleOrname(username.substring(0,usernameLenght));

            annotation.setPointerAnnt(true);
            return annotation;
        }else{
            return null;
        }

    }

    //移动过程中，把点加入Annotation中
    private void addPoint2Annotation(AnnotationBean annotation, Point point){
        if (Constants.SHAPE_POLY_LINE.equals(annotation.getAnnoPattern())) {
            annotation.addPoint(point);
        } else {
            annotation.replacePoint(1, point);
        }
    }
    //发送本地注释到服务器
    private void sendAnnotation2Service(final AnnotationBean annotation, final int operType){
        new Thread() {
            @Override
            public void run() {
                if(operType == DocCommon.ANNOTATION_OPT_TYPE_ADD){
                    if(null != doc && null != doc.getPage()){
                        docCommon.removeOneAnno(doc.getPage().getMyPreAnnotation());
                    }
                    int annotationId = docCommon.createPointerAnnt(annotation);
                    System.out.println("return aid:"+annotationId);
                    annotation.setAnnotationID(annotationId);
                }else if (operType == DocCommon.ANNOTATION_OPT_TYPE_DEL){

                    docCommon.removeOneAnno(annotation);
					/*ConferenceJni.sendAnnotation(handler, convertJson(annotation, operType)
							.getBytes().length, convertJson(annotation, operType).getBytes());*/
                }
//				Log.i("DocView", "annotation : " + annotation);
            }
        }.start();
    }
    private boolean doDrawAnno(MotionEvent event, Point point,int type){
        if(type == POINTER){
            return drawPointer(event, point);
        }else{
            return drawPolygon(event, point);
        }
    }
    //画注释
    private boolean drawPolygon(MotionEvent event, Point point){
//		//log.info("Polygon1 event = "+event.getAction());
        switch (event.getAction()) {

            case MotionEvent.ACTION_DOWN:
                //log.info("Polygon DOWN ");
                isBeginAnno = true;
                annotation = initAnnotation(doc, docCommon, point,Constants.SHAPE_POLY_LINE);
                if(null == annotation){
                    break;
                }
                float scaleX, scaleY;
                if(DocCommonImpl.isAnimation){
                    scaleY = (float) viewHeight / (float) annotation.getHeight() * ANIMATION_HEIGHT_OFFSET;
                    scaleX = (float) viewWidth / (float) annotation.getWidth() * ANIMATION_WIDTH_OFFSET;
                }else{
                    scaleY = (float) viewHeight / (float) annotation.getHeight();
                    scaleX = (float) viewWidth / (float) annotation.getWidth();
                }
                int width ;
                width = (int)(annotation.getLineWidth()/scaleX/4);
                if (width == 0) {
                    width =1;
                }

                annotation.setLineWidth(width);
                break;

            case MotionEvent.ACTION_MOVE:
                //log.info("Polygon MOVE ");
                if (!isBeginAnno|| annotation == null)
                    return false;
                if(null != annotation){
                    addPoint2Annotation(annotation, point);
                    if(!isPad){
                        invalidate();
                    }
                }
                break;

            case MotionEvent.ACTION_UP:
                //log.info("Polygon UP　");
                if (isBeginAnno) {
                    isBeginAnno = false;

                    if(null != annotation){
                        annotation.addPoint(point);
                        int annotationID = docCommon.createPolyLine(annotation);
                        // //log.info("createPolygon annoID = "+annotationID);
                        annotation.setAnnotationID(annotationID);
                        if (null != doc && null != doc.getPage()) {
                            doc.getPage().addAnnotation(annotation);
                        }
                    }
                }
                invalidate();
                break;
            case MotionEvent.ACTION_CANCEL:
                //log.info("Polygon cancel");
                if (isBeginAnno) {
                    isBeginAnno = false;

                    if(null != annotation){
                        annotation.addPoint(point);
                        int annotationID = docCommon.createPolyLine(annotation);
//						//log.info("createPolygon annoID = "+annotationID);
                        annotation.setAnnotationID(annotationID);
                        if(null != doc && null != doc.getPage()){
                            doc.getPage().addAnnotation(annotation);
                        }
                    }
                }
                invalidate();
                break;
        }

        return true;
    }
    //画标注
    private boolean drawPointer(MotionEvent event, Point point){
        switch (event.getAction()) {

            case MotionEvent.ACTION_DOWN:
                break;

            case MotionEvent.ACTION_MOVE:
                break;

            case MotionEvent.ACTION_UP:
                AnnotationBean annotation = initAnnotation(doc, docCommon, point,Constants.SHAPE_POINTER);
                if(annotation != null && !docCommon.isStartToShowPage()){
                    //sendAnnotation2Service(annotation, DocCommon.ANNOTATION_OPT_TYPE_ADD);
                    if(null != doc && null != doc.getPage()){
                        docCommon.removeOneAnno(doc.getPage().getMyPreAnnotation());
                    }else{
                        docCommon.removeOneAnno(annotation);
                    }
                    int annotationId = docCommon.createPointerAnnt(annotation);
                    System.out.println("return aid:"+annotationId);
                    annotation.setAnnotationID(annotationId);

                    if(doc.getPage() != null){
                        doc.getPage().addAnnotation(annotation);
                        doc.getPage().setPreAnnotation(annotation);
                        this.annotation = annotation;
                        invalidate();
                    }else{
                        docCommon.removeOneAnno(annotation);
                    }

                }

                break;
        }

        return true;
    }

    /**
     * 本地画注释
     *
     * @param event
     * @return
     */
    private boolean drawAnnotation(MotionEvent event,int type) {
        matrix.getValues(matrixValues);
        float[] scaleXY = getDocXYScale(doc);
        Point point = transCuttentPoint2AbsolutePoint(event, matrixValues, scaleXY);
//		Point point = new Point(event.getRawX(), event.getRawY());
        if (!DocCommonImpl.isWhiteBoard &&point.getX() >= 0 && point.getY() >= 0 && point.getX() * ANIMATION_WIDTH_OFFSET <= doc.getPageWidth()
                && point.getY() * ANIMATION_HEIGHT_OFFSET <= doc.getPageHeight()) {
//			//log.info("Polygon UP 1　point.getX()="+point.getX()+"point.getY()="+point.getY()+"doc.getPageWidth()="+doc.getPageWidth()+"doc.getPageHeight()="+doc.getPageHeight());
            doDrawAnno(event, point,type);
        } else if (point.getX() >= 0 && point.getY() >= 0 && point.getX() <= doc.getPageWidth()
                && point.getY() <= doc.getPageHeight()) {
//			//log.info("Polygon UP 2　point.getX()="+point.getX()+"point.getY()="+point.getY()+"doc.getPageWidth()="+doc.getPageWidth()+"doc.getPageHeight()="+doc.getPageHeight());
            doDrawAnno(event, point,type);
        }else{
            if (isBeginAnno && annotation != null) {
                isBeginAnno = false;

                annotation.addPoint(point);
                int annotationID = docCommon.createPolyLine(annotation);
                //log.info("createPolygon annoID = "+annotationID);
                annotation.setAnnotationID(annotationID);
                if(null != doc && null != doc.getPage()){
                    doc.getPage().addAnnotation(annotation);
                }
            }
            invalidate();
        }
        return true;
    }
    //根据获取的Point和Annotation中的值去对比，相同的话就删除page对应的Annotation
    private synchronized ArrayList<AnnotationBean> delAnnoFromPage(Point point, DocBean doc){
//		ArrayList<AnnotationBean> al = new ArrayList<AnnotationBean>();
//		ArrayList<AnnotationBean> annos = (ArrayList<AnnotationBean>) doc.getPage().getAnnotations();
//		Iterator<AnnotationBean> it = annos.iterator();
//		int offset = (int) ((ConferenceApplication.DENSITY*160/2.54)*SCALE);
//		while(it.hasNext()){
//			boolean flag = true;	//flag放这防止IllegalStateException，否则it.remove执行两次以上就要报这个错
//			AnnotationBean anno = it.next();
//			for(Point p : anno.getPoints()){
//				Log.i("zhushi", "poin:x-"+point.getX()+",y-"+point.getY()+";p:x-"+p.getX()+",y-"+p.getY()+";offset="+ConferenceApplication.SCREEN_HEIGHT*ConferenceApplication.DENSITY/SCALE+",offsetX="+Math.abs(p.getX()-point.getX())+",offsetY="+Math.abs(p.getY()-point.getY()));
//				if(Math.abs(p.getX()-point.getX())<ConferenceApplication.SCREEN_HEIGHT*ConferenceApplication.DENSITY/SCALE && Math.abs(p.getY()-point.getY())<ConferenceApplication.SCREEN_HEIGHT*ConferenceApplication.DENSITY/SCALE&& flag){//加减一百是因为给的图片尺寸是几千的，太小就选不到点了
////					al.add(anno);
//					flag = false;
//				}
//			}
//		}
        return deAnn(point,doc);
    }

    private void doDrawEraser(MotionEvent event, Point point){
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_MOVE:
            case MotionEvent.ACTION_UP:
                isBeginClean = true;
                ArrayList<AnnotationBean> al = delAnnoFromPage(point, doc);
                if(al == null || al.isEmpty()){
                    //log.info("can not find anno");
                }
                for(AnnotationBean anno : al){
                    sendAnnotation2Service(anno, DocCommon.ANNOTATION_OPT_TYPE_DEL);
                }
                invalidate();
                break;
        }
    }

    //本地画橡皮擦，相当于在画透明的线
    private boolean drawEraser(MotionEvent event){
        matrix.getValues(matrixValues);
        float[] scaleXY = getDocXYScale(doc);

        Log.e("TAG", "event.getRawX = " + event.getRawX() + ", matrixValues[Matrix.MTRANS_X] = " + matrixValues[Matrix.MTRANS_X]
                + ", matrixValues[Matrix.MSCALE_X] = " + matrixValues[Matrix.MSCALE_X] + ", scaleXY[0] = " + scaleXY[0] + ", event.getRawY() = " + event.getRawY() + ", matrixValues[Matrix.MTRANS_Y] = "
                + matrixValues[Matrix.MTRANS_Y] + ", matrixValues[Matrix.MSCALE_Y] = " + matrixValues[Matrix.MSCALE_Y] + ", scaleXY[1] = " + scaleXY[1]);

        Point point = transCuttentPoint2AbsolutePoint(event, matrixValues, scaleXY);
//		Point point = new Point(event.getRawX(), event.getRawY());

        eraserOffset =(float) ((ConferenceApplication.DENSITY*160/(3.5*2.54))/ matrixValues[Matrix.MSCALE_X] * scaleXY[0]);

        if (DocCommonImpl.isAnimation && point.getX() >= 0 && point.getY() >= 0 && point.getX() * ANIMATION_WIDTH_OFFSET <= doc.getPageWidth()
                && point.getY() * ANIMATION_HEIGHT_OFFSET <= doc.getPageHeight()) {
            doDrawEraser(event, point);
        } else if (point.getX() >= 0 && point.getY() >= 0 && point.getX() <= doc.getPageWidth()
                && point.getY() <= doc.getPageHeight()) {
            doDrawEraser(event, point);
        }
        return true;
    }

    /**
     * 把在当前在图片上获取的点     转换成     原始图片上对应的点
     * @param event	点击事件
     * @param matrixValues	Matrix对象的值被存放在这里
     * @param scaleXY	page的长宽和doc长宽的比值
     * @return
     */
    private Point transCuttentPoint2AbsolutePoint(MotionEvent event, float[] matrixValues, float[] scaleXY){
        Point point;

        if(isOffset){
            if(DocCommonImpl.isAnimation){
                point = new Point((int) (((event.getRawX() - matrixValues[Matrix.MTRANS_X])
                        / matrixValues[Matrix.MSCALE_X] * scaleXY[0]) / ANIMATION_WIDTH_OFFSET) , (int) (((event.getRawY() - matrixValues[Matrix.MTRANS_Y])
                        / matrixValues[Matrix.MSCALE_Y] * scaleXY[1]) / ANIMATION_HEIGHT_OFFSET));
            }else{
                point = new Point((int) ((event.getRawX() - matrixValues[Matrix.MTRANS_X])
                        / matrixValues[Matrix.MSCALE_X] * scaleXY[0]), (int) ((event.getRawY() - matrixValues[Matrix.MTRANS_Y])
                        / matrixValues[Matrix.MSCALE_Y] * scaleXY[1]));
            }
        }else{
            if(DocCommonImpl.isAnimation){
                point = new Point((int) (((event.getX() - matrixValues[Matrix.MTRANS_X])
                        / matrixValues[Matrix.MSCALE_X] * scaleXY[0]) / ANIMATION_WIDTH_OFFSET) , (int) (((event.getY() - matrixValues[Matrix.MTRANS_Y])
                        / matrixValues[Matrix.MSCALE_Y] * scaleXY[1]) / ANIMATION_HEIGHT_OFFSET));
            }else{
                point = new Point((int) ((event.getX() - matrixValues[Matrix.MTRANS_X])
                        / matrixValues[Matrix.MSCALE_X] * scaleXY[0]), (int) ((event.getY() - matrixValues[Matrix.MTRANS_Y])
                        / matrixValues[Matrix.MSCALE_Y] * scaleXY[1]));
            }
        }

        return point;
    }
    /**
     * 添加注释
     *
     * @param annotation
     */
    public void addAnnotation(AnnotationBean annotation) {
        if(doc != null){
            //如果添加的是指示，删除page中其他指示
			/*if(annotation.isPointerAnnt()){
				doc.getPage().removeAnnotation(annotation, true);
			}*/

            //doc.getPage().getAnnotations().add(annotation);
            invalidate();
        }
    }

    /**
     * 删除注释
     *
     * @param annotationID
     */
    public void removeAnnotation(Integer annotationID) {
        if(doc != null){
            ArrayList<AnnotationBean> annotationBeans = doc.getPage().getAnnotations();
            if(annotationBeans != null){
                invalidate();
            }

        }

    }
    public void setPad(boolean isPad) {
        this.isPad = isPad;
    }

    private ArrayList<AnnotationBean> deAnn(Point point, DocBean doc){
        float[] scaleXY = getDocXYScale(doc);
//		float offset = SCALE * scaleXY[0];
        float offset =(float) (ConferenceApplication.DENSITY*160/(2*2.54));
//        Log.i("Always", "DocView delAnn offset="+offset+";scale="+SCALE+";scaleXY[0]="+scaleXY[0]);
        ArrayList<AnnotationBean> al = new ArrayList<AnnotationBean>();
        ArrayList<AnnotationBean> annos;
        if(doc!=null&&null != doc.getPage()){
            annos = (ArrayList<AnnotationBean>) doc.getPage().getAnnotations().clone();
        }else {
            annos = new ArrayList<AnnotationBean>();
        }
        Log.i("Always", "DocView verifyAnn myPoint:("+point.getX()+","+point.getY()+");offset="+eraserOffset);
        Iterator<AnnotationBean> it = annos.iterator();
        while(it.hasNext()){
            boolean flag = true;	//flag放这防止IllegalStateException，否则it.remove执行两次以上就要报这个错
            AnnotationBean anno = it.next();
            if(verifyAnn(point, anno,eraserOffset)){
                al.add(anno);
                flag = false;
            }
        }
        return al;
    }

    private boolean verifyAnn(Point myPoint,AnnotationBean anno,float offset){
        if(Constants.SHAPE_LINE.equals(anno.getAnnoPattern())){//直线
            List<Point>  points = anno.getPoints();
            float point1X = points.get(0).getX();
            float point1Y = points.get(0).getY();
            float point2X = points.get(1).getX();
            float point2Y = points.get(1).getY();
            float minX =(point1X > point2X)?point2X:point1X;
            float maxX =(point1X > point2X)?point1X:point2X;

            float minY =(point1Y > point2Y)?point2Y:point1Y;
            float maxY =(point1Y > point2Y)?point1Y:point2Y;
            if (Math.abs(point1X-point2X)<offset) {
                if (myPoint.getY()>minY && myPoint.getY()<maxY) {
                    float d = Math.abs(myPoint.getX() - point1X);
                    if (d<offset) {
                        return true;
//                        [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
                    }
                }
            }
            else if(Math.abs(point1Y-point2Y)<offset){
                if (myPoint.getX()>minX && myPoint.getX()<maxX) {
                    float  A= (point2Y-point1Y)/(point2X-point1X);
                    float  C= (point1X*point2Y - point2X*point1Y)/(point1X-point2X);
                    float  B = -1;
                    float d = (float) (Math.abs(A*myPoint.getX()+B*myPoint.getY()+C)/(Math.sqrt(Math.pow(A,2)+Math.pow(B,2))));
                    if (d<offset) {
                        return true;
//                        [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
                    }
                }
            }
            else if ((myPoint.getX()>minX && myPoint.getX()<maxX)&&(myPoint.getY()>minY && myPoint.getY()<maxY)) {
                float  A= (point2Y-point1Y)/(point2X-point1X);
                float  C= (point1X*point2Y - point2X*point1Y)/(point1X-point2X);
                float  B = -1;
                float d = (float) (Math.abs(A*myPoint.getX()+B*myPoint.getY()+C)/(Math.sqrt(Math.pow(A,2)+Math.pow(B,2))));
                if (d<offset) {
                    return true;
//                    [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
                }
            }
        }else if (Constants.SHAPE_POLY_LINE.equals(anno.getAnnoPattern())) {//自由笔
            List<Point>  points = anno.getPoints();
            for (int j=0; j<points.size(); j++) {
                if ((myPoint.getX()-offset)<points.get(j).getX()&& (myPoint.getX()+offset)>points.get(j).getX()&& (myPoint.getY()-offset)<points.get(j).getY()&& (myPoint.getY()+offset)>points.get(j).getY()) {
//                     [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
//                    break;
                    return true;
                }
            }
        }else if (Constants.SHAPE_RECT.equals(anno.getAnnoPattern())) {//矩形
            List<Point>  points = anno.getPoints();
            float point1X = points.get(0).getX();
            float point1Y = points.get(0).getY();
            float point2X = points.get(1).getX();
            float point2Y = points.get(1).getY();
            float minX =(point1X > point2X)?point2X:point1X;
            float maxX =(point1X > point2X)?point1X:point2X;

            float minY =(point1Y > point2Y)?point2Y:point1Y;
            float maxY =(point1Y > point2Y)?point1Y:point2Y;
            boolean isSearch =((myPoint.getX()>minX && myPoint.getX()<maxX)&&((myPoint.getY()>minY-offset&&myPoint.getY()<minY+offset)
                    ||(myPoint.getY()>maxY-offset&&myPoint.getY()<maxY+offset)))
                    || ((myPoint.getY()>minY && myPoint.getY()<maxY)&&((myPoint.getX()>minX-offset&&myPoint.getX()<minX+offset)
                    ||(myPoint.getX()>maxX-offset&&myPoint.getX()<maxX+offset)));
            if (isSearch) {
//                [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
                return true;
            }
        }else if (Constants.SHAPE_ELLIPSE.equals(anno.getAnnoPattern())) {//椭圆形
            List<Point>  points = anno.getPoints();
            float point1X = points.get(0).getX();
            float point1Y = points.get(0).getY();
            float point2X = points.get(1).getX();
            float point2Y = points.get(1).getY();

            //用来判断长轴是否使在Y方向上
            boolean isLongAxisY =Math.abs(point2Y-point1Y)>Math.abs(point2X-point1X)?true:false;

            //椭圆的长轴和短轴的焦点
            Point dot = new Point(Math.abs((point2X+point1X)/2), Math.abs((point2Y+point1Y)/2));
            float a,b;
            a = Math.abs(point2Y-point1Y)>Math.abs(point2X-point1X)?Math.abs(point2Y-point1Y):Math.abs(point2X-point1X);
            b= Math.abs(point2Y-point1Y)<Math.abs(point2X-point1X)?Math.abs(point2Y-point1Y):Math.abs(point2X-point1X);

            float d =(float) Math.sqrt(Math.pow(a/2,2)-Math.pow(b/2,2));

            Point pointLeft,pointRight;
            if (isLongAxisY) {
                pointLeft = new Point(dot.getX(), dot.getY()+d);
                pointRight = new Point(dot.getX(), dot.getY()-d);
            }
            else{
                pointLeft = new Point(dot.getX()-d, dot.getY());
                pointRight = new Point(dot.getX()+d, dot.getY());
            }
            double pLeft1=Math.pow(Math.abs(myPoint.getX()-pointLeft.x),2);
            double pLeft2 =Math.pow(Math.abs(myPoint.getY()-pointLeft.y),2);
            double pLeft = pLeft1+pLeft2;

            double  pRight1=Math.pow(Math.abs(myPoint.getX()-pointRight.x),2);
            double  pRight2 =Math.pow(Math.abs(myPoint.getY()-pointRight.y),2);
            double  pRight = pRight1+pRight2;

            double leftlong =Math.sqrt(pLeft);
            double rightlong=Math.sqrt(pRight);
            double pointToOtherPoint =leftlong +rightlong;

            if (pointToOtherPoint<a+2*offset && pointToOtherPoint>a-2*offset) {
//                [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
                return true;
            }
        }else if (Constants.SHAPE_POLYGON.equals(anno.getAnnoPattern())) {//多边形
            List<Point>  points = anno.getPoints();
            for (int j=0; j<points.size()-1; j++) {
                float point1X = points.get(j).getX();
                float point1Y = points.get(j).getY();
                float point2X = points.get(j+1).getX();
                float point2Y = points.get(j+1).getY();

                float minX =(point1X > point2X)?point2X:point1X;
                float maxX =(point1X > point2X)?point1X:point2X;

                float minY =(point1Y > point2Y)?point2Y:point1Y;
                float maxY =(point1Y > point2Y)?point1Y:point2Y;

                if ((myPoint.getX()>minX && myPoint.getX()<maxX)&&(myPoint.getY()>minY && myPoint.getY()<maxY)) {
                    float  A= (point2Y-point1Y)/(point2X-point1X);
                    float  C= (point1X*point2Y - point2X*point1Y)/(point1X-point2X);
                    float  B = -1;
                    //                float y = k*myPoint.getX() + b;
                    float d = (float) (Math.abs(A*myPoint.getX()+B*myPoint.getY()+C)/(Math.sqrt(Math.pow(A,2)+Math.pow(B,2))));
                    if (d<offset) {
                        return true;
//                         [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
                    }
                }
            }
        }else if (Constants.STROKE_HILIGHT.equals(anno.getAnnoPattern())) {//荧光笔
            List<Point>  points = anno.getPoints();
            for (int j=0; j<points.size(); j++) {
                if ((myPoint.getX()-offset)<points.get(j).getX()&& (myPoint.getX()+offset)>points.get(j).getX()&& (myPoint.getY()-offset)<points.get(j).getY()&& (myPoint.getY()+offset)>points.get(j).getY()) {
//                     [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
//                    break;
                    return true;
                }
            }
        }else if (Constants.SHAPE_POINTER.equals(anno.getAnnoPattern())) {//指示

        }else if (Constants.SHAPE_RIGHT.equals(anno.getAnnoPattern())) {//对勾
            List<Point>  points = anno.getPoints();
            float point1X = points.get(0).getX();
            float point1Y = points.get(0).getY();
            float point2X = points.get(1).getX();
            float point2Y = points.get(1).getY();

            float minX = (point1X>point2X) ? point2X : point1X;
            float minY = (point1Y>point2Y) ? point2Y :point1Y;
            float maxX = (point1X>point2X) ? point1X : point2X;
            float maxY = (point1Y>point2Y) ? point1Y :point2Y;


            Point point1 = new Point(minX, (float) (maxY+ (minY - maxY)*2.0/3.0));
            Point point2 = new Point((minX + maxX) / 2, minY);
            Point point3 = new Point(maxX, maxY);



            float min1X = (point1.getX()>point2.getX()) ? point2.getX() : point1.getX();
            float min1Y = (point1.getY()>point2.getY()) ? point2Y :point1.getY();
            float max1X = (point1.getX()>point2.getX()) ? point1.getX() : point2.getX();
            float max1Y = (point1.getY()>point2.getY()) ? point1.getY() :point2.getY();

            if ((myPoint.getX()>min1X && myPoint.getX()<max1X)&&(myPoint.getY()>min1Y && myPoint.getY()<max1Y)) {
                float  A1= (point2.y-point1.y)/(point2.x-point1.x);
                float  C1= (point1.x*point2.y - point2.x*point1.y)/(point1.x-point2.x);
                float  B1 = -1;
                float  d1 = (float) (Math.abs(A1*myPoint.getX()+B1*myPoint.getY()+C1)/(Math.sqrt(Math.pow(A1,2)+Math.pow(B1,2))));
                if (d1<offset) {
//                        [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
                    return true;
                }
            }



            float min2X = (point2.x>point3.x) ? point3.x : point2.x;
            float min2Y = (point2.y>point3.y) ? point3.y : point1.y;
            float max2X = (point2.x>point3.x) ? point2.x : point3.x;
            float max2Y = (point2.y>point3.y) ? point2.y : point3.y;

            if ((myPoint.getX()>min2X && myPoint.getX()<max2X)&&(myPoint.getY()>min2Y && myPoint.getY()<max2Y)) {
                float  A2= (point3.y-point2.y)/(point3.x-point2.x);
                float  C2= (point2.x*point3.y - point3.x*point2.y)/(point2.x-point3.x);
                float  B2 = -1;
                float  d2 = (float) (Math.abs(A2*myPoint.getX()+B2*myPoint.getY()+C2)/(Math.sqrt(Math.pow(A2,2)+Math.pow(B2,2))));
                if (d2<offset) {
//                        [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
//                        return;
                    return true;
                }
            }


        }else if (Constants.SHAPE_WRONG.equals(anno.getAnnoPattern())) {//叉叉
            List<Point>  points = anno.getPoints();
            float point1X = points.get(0).getX();
            float point1Y = points.get(0).getY();
            float point2X = points.get(1).getX();
            float point2Y = points.get(1).getY();
            float minX = (point1X>point2X) ? point2X : point1X;
            float minY = (point1Y>point2Y) ? point2Y :point1Y;
            float maxX = (point1X>point2X) ? point1X : point2X;
            float maxY = (point1Y>point2Y) ? point1Y :point2Y;

            Point point1 = new Point(point1X, point1Y);
            Point point2 = new Point(point2X, point2Y);
            Point point3 = new Point(point1X, point2Y);
            Point point4 = new Point(point2X, point1Y);

            if ((myPoint.getX()>minX && myPoint.getX()<maxX)&&(myPoint.getY()>minY && myPoint.getY()<maxY)) {
                float  A1= (point2.y-point1.y)/(point2.x-point1.x);
                float  C1= (point1.x*point2.y - point2.x*point1.y)/(point1.x-point2.x);
                float  B1 = -1;
                float d1 = Math.abs(A1*myPoint.getX()+B1*myPoint.getY()+C1)/((float)Math.sqrt(Math.pow(A1,2)+Math.pow(B1,2)));
                if (d1<offset) {
//                    [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
//                    return;
                }

                float  A2= (point4.y-point3.y)/(point4.x-point3.x);
                float  C2= (point3.x*point4.y - point4.x*point3.y)/(point3.x-point4.x);
                float  B2 = -1;
                float d2 = Math.abs(A2*myPoint.getX()+B2*myPoint.getY()+C2)/((float)Math.sqrt(Math.pow(A2,2)+Math.pow(B2,2)));
                if (d2<offset) {
//                    [[DocumentShare sharedDocumentShare] removeOneAnn:docID LinePageID:pageID Annotation:m_temAnno.iAnnID];
//                    return;
                    return true;
                }

            }
        }
        return false;


    }
}
