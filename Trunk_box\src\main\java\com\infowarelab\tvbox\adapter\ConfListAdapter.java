package com.infowarelab.tvbox.adapter;

import android.content.Context;
import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.domain.ConferenceBean;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ConfListAdapter extends BaseAdapter implements BaseAdapterImpl {

    private Context mContext;
    private LayoutInflater mInflater;
    private int position = -1;
    private int lastPosition = -1;
    protected List<ConferenceBean> mData;

    public ConfListAdapter(Context context, List<ConferenceBean> data) {
        this.mContext = context;
        mData = data;
        mInflater = LayoutInflater.from(context);
    }

    @Override
    public View getView(int index, View convertView, ViewGroup group) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = mInflater.inflate(R.layout.conference_item, null);
            holder = new ViewHolder();
            holder.tvRoomTitle = (TextView) convertView.findViewById(R.id.item_room_title);
            holder.tvRoomDate = (TextView) convertView.findViewById(R.id.item_room_date);
            holder.tvRoomId = (TextView) convertView.findViewById(R.id.item_room_id);
            holder.tvRoomStatus = (TextView) convertView.findViewById(R.id.item_room_status);
            holder.ivJoin = (ImageView) convertView.findViewById(R.id.iv_join);
            holder.rlRoot = convertView.findViewById(R.id.conf_item_root);
            convertView.setTag(holder);

            //convertView.setFocusable(true);


//            holder.rlRoot.setFocusable(true);
//
//            convertView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
//                @Override
//                public void onFocusChange(View v, boolean hasFocus) {
//                    if (hasFocus){
//                        focusItem(v, index);
//                    }
//                    else
//                    {
//                        normalItem(v, index);
//                    }
//                }
//            });

        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        if (mData.size() > index)
        {
//            if (index == lastPosition){
//                holder.rlRoot.clearFocus();
//                holder.rlRoot.setBackgroundColor(Color.TRANSPARENT);
//                //holder.tvRoomTitle.setTextColor(R.color.black);
//            }

            ConferenceBean conferenceBean = mData.get(index);

            if (conferenceBean != null)
            {
                holder.tvRoomTitle.setText(conferenceBean.getName());
                holder.tvRoomDate.setText(dateToString(conferenceBean.getStartDate()));

                String pre = conferenceBean.getId().substring(0, 4);
                String end = conferenceBean.getId().substring(4);

                holder.tvRoomId.setText(pre + " " + end);

                if(conferenceBean.getStatus().equals("1") || conferenceBean.getStatus().equals("4")){
                    holder.tvRoomStatus.setVisibility(View.VISIBLE);
                }
                else
                {
                    holder.tvRoomStatus.setVisibility(View.GONE);
                }

                if (index == position){
                    holder.ivJoin.setImageResource(R.drawable.hs_arrow_2);
                }
                else
                {
                    holder.ivJoin.setImageResource(R.drawable.hs_arrow_1);
                }
            }
        }

        return convertView;
    }

    private void focusItem(View v, int index) {

        Log.d("InfowareLab.ConfList", " >>>>>> focusItem");
//        RelativeLayout rlConfItemRoot = (RelativeLayout) v.findViewById(R.id.conf_item_root);
//
//        if (rlConfItemRoot != null){
//            rlConfItemRoot.setBackgroundResource(R.drawable.bg_conf_item_fos);
//        }
    }

    private void normalItem(View v, int index) {

        Log.d("InfowareLab.ConfList", " >>>>>> normalItem");
//        RelativeLayout rlConfItemRoot = (RelativeLayout) v.findViewById(R.id.conf_item_root);
//
//        if (rlConfItemRoot != null){
//            rlConfItemRoot.setBackgroundResource(R.drawable.bg_conf_item_nor);
//        }
    }


    @Override
    public int getCount() {
        return mData.size();
    }

    @Override
    public ConferenceBean getItem(int position) {
        return mData.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public void setSelectPosition(int position) {

        if (position < 0) lastPosition = this.position;
        this.position = position;
        notifyDataSetChanged();
    }

    @Override
    public void setSecondPosition(int position) {
        this.lastPosition = position;
        notifyDataSetChanged();
    }

    public void recoverPosition() {
        if (this.position < 0)
            this.position = lastPosition;
        notifyDataSetChanged();
    }

    private class ViewHolder {
        RelativeLayout rlRoot;
        TextView tvRoomTitle;
        TextView tvRoomDate;
        TextView tvRoomId;
        TextView tvRoomStatus;
        ImageView ivJoin;
    }

    private String dateToString(Date startDate) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            return format.format(startDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void update(List<ConferenceBean> data) {

        this.mData = data;

        notifyDataSetChanged();
    }

}
