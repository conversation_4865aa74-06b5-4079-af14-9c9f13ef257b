package com.infowarelab.tvbox.fragment;

import android.os.Bundle;
import android.os.Message;
import androidx.annotation.Nullable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.adapter.AttendersAdapter;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.confctrl.UserCommon;
import com.infowarelabsdk.conference.domain.UserBean;


public class FragAttender extends BaseFragment {
    private View attView;

    private LinearLayout llAtts;
    private ListView lvAtts;
    private TextView tvAttsSum;

    private UserCommonImpl userCommon;

    private AttendersAdapter attsAdapter;

    private boolean isIniting = true;

    public FragAttender(ICallParentView iCallParentView) {
        super(iCallParentView);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        attView = inflater.inflate(R.layout.frag_inconf_att, container, false);
        initData();
        initView();
        initAttsView();
        return attView;
    }
    private void initView() {
        isIniting = true;
        llAtts = (LinearLayout) attView.findViewById(R.id.frag_att_ll_atts);
        lvAtts = (ListView) attView.findViewById(R.id.frag_att_lv_atts);
        tvAttsSum = (TextView) attView.findViewById(R.id.frag_att_tv_atts_sum);
        llAtts.setVisibility(View.VISIBLE);

        attView.postDelayed(new Runnable() {
            @Override
            public void run() {
                isIniting = false;
            }
        }, 5000);
    }

    private void initAttsView() {
        if (null == userCommon)
            userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
        if (null == userCommon)
            return;
        attsAdapter = new AttendersAdapter(getActivity(), userCommon.getUsersList());
        lvAtts.setAdapter(attsAdapter);
    }

    private void initData() {
        userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
    }
    public void doUserCallback(Message msg) {
        if (!isAdded()) return;
        switch (msg.what) {
            case UserCommon.CHAT_RECEIVE:
                break;
            case UserCommon.ACTION_USER_ADD:
                refreshAttenders();
                if (!isIniting && msg.obj instanceof UserBean) {
                    showBottomRightToast(((UserBean) msg.obj).getUsername() + " " + getResources().getString(R.string.attenders_joined));
                }
                break;
            case UserCommon.ACTION_USER_REMOVE:
                refreshAttenders();
                if (msg.obj instanceof UserBean) {
                    showBottomRightToast(((UserBean) msg.obj).getUsername() + " " + getResources().getString(R.string.attenders_leaved));
                }
                break;
            case UserCommon.ACTION_USER_MODIFY:
                refreshAttenders();
                break;
            case UserCommon.ROLEUPDATE:
                refreshAttenders();
                break;
            case UserCommon.CHAT_PRIVATE_ON:
                break;
            case UserCommon.CHAT_PRIVATE_OFF:
                break;
            case UserCommon.CHAT_PUBLIC_ON:
                break;
            case UserCommon.CHAT_PUBLIC_OFF:
                break;
            default:
                break;
        }
    }

    private void refreshAttenders() {
        attsAdapter.update(userCommon.getUsersList(), tvAttsSum);
    }
    public void showBottomRightToast(String s) {
        Toast toast = new Toast(getActivity());
        View v = getActivity().getLayoutInflater().inflate(R.layout.view_toast_rightbottom, null);
        TextView tv = (TextView) v.findViewById(R.id.view_toast_tv);
        tv.setText(s);
        toast.setDuration(Toast.LENGTH_SHORT);
        toast.setGravity(Gravity.BOTTOM | Gravity.RIGHT, 0, 0);
        toast.setView(v);
        toast.show();
    }

}
