package com.infowarelab.tvbox.activity;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.AESUtil;
import com.infowarelab.tvbox.utils.PublicWay;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelab.tvbox.utils.XMLUtils;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.AudioCommonImpl;
import com.infowarelabsdk.conference.common.impl.ChatCommomImpl;
import com.infowarelabsdk.conference.common.impl.ConfManageCommonImpl;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.confctrl.ConferenceCommon;
import com.infowarelabsdk.conference.domain.AnnotationResource;
import com.infowarelabsdk.conference.domain.AnnotationType;
import com.infowarelabsdk.conference.domain.ConferenceBean;
import com.infowarelabsdk.conference.domain.LoginBean;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.NetUtil;
import com.infowarelabsdk.conference.util.StringUtil;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import pub.devrel.easypermissions.EasyPermissions;


public class ActLogo extends BaseFragmentActivity implements EasyPermissions.PermissionCallbacks {
    private static final int RC_PERMISSIONS = 0;
    private static boolean flag = true;
    private String default_site;
    public static final String CONF_ID = "confKey";
    public static final String USER_NAME = "nickname";
    public static final String PASSWORD = "confPWD";
    public static final String SITE = "siteURL";
    public static final String LOGIN_NAME = "userName";
    public static final String LOGIN_PASS = "userPWD";
    private RelativeLayout llRoot;

    public static final int FINISH = 4;
    public static final int LOGINFAILED = 6;
    public static final int MEETINGINVALIDATE = 7;
    public static final int MEETINGNOTJOINBEFORE = 8;
    public static final int HOSTERROR = 9;
    public static final int SPELLERROR = 10;
    public static final int GET_ERROR_MESSAGE = 11;
    public static final int JOIN_CONFERENCE = 12;
    public static final int CREATECONF_ERROR = 15;
    public static final int READY_JOINCONF = 16;
    public static final int NEED_LOGIN = 1001;
    public static final int NO_CONFERENCE = 1002;
    protected static final int INIT_SDK_FAILED = 102;
    protected static final int CONF_CONFLICT = -5;
    //需要密码
    protected static final int NEED_PASSWORD = 10086;

    private String mRoomId = "";
    private String mRoomPwd = "";
    private int mActionMode = -1;
    private ConferenceBean mConfBean = null;
    private LoginBean mLoginBean = null;
    private Intent mConfIntent = null;
    private boolean logined = false;
    private boolean enterFromIntent = false;
    private int mRecordDuration = -1;
    private int mRecordInterval = -1;
    private String mUploadSite = null;
    private String mAttachInfo = null;
    private String mGroupId = null;

    //动画
    private TextView launchText;
    //是否已经进入首页
    private boolean isFlag = true;
    //定时器
    private int count = 0;
    private Timer timer = new Timer();
    private TimerTask task = new TimerTask() {
        @Override
        public void run() {
            if (hasPermission)
                doAsReady();
        }
    };

    private Handler handler = new Handler(Looper.getMainLooper());
    private Intent mHomeIntent = null;
    private boolean hasPermission = false;

    private AlertDialog dialog;
    private AlertDialog settingDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d("InfowareLab.Debug","ActLogo.onCreate");
        setContentView(R.layout.act_logo);

        requestPermissions();

        SharedPreferencesUrls.getInstance().putString("Model",Build.MODEL);
        flag = true;
        default_site = getResources().getString(R.string.comm_defaultsite);
        if (!default_site.startsWith("http://")) {
            default_site = "http://" + default_site;
        }
        if (savedInstanceState == null)
            initView();
        checkAnnotype();
    }

    private void initView() {
        Log.d("InfowareLab.Debug","ActLogo.initView");
        if (getIntent() != null && getIntent().getData() != null) {
            Config.Site_URL = getIntent().getData().getHost();
        } else {
            SharedPreferences preferences = getPreferences(Activity.MODE_PRIVATE);
            Config.Site_URL = preferences.getString(Constants.SITE, "");
        }

        try {
            PackageManager manager = this.getPackageManager();
            PackageInfo info = manager.getPackageInfo(this.getPackageName(), 0);
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        if (CommonFactory.getInstance().getChatCommom() == null) {
            CommonFactory.getInstance().setConfManageCommon(new ConfManageCommonImpl())
                    .setAudioCommon(new AudioCommonImpl()).setConferenceCommon(new ConferenceCommonImpl())
                    .setDocCommon(new DocCommonImpl()).setSdCommon(new ShareDtCommonImpl())
                    .setUserCommon(new UserCommonImpl()).setVideoCommon(new VideoCommonImpl()).setChatCommom(new ChatCommomImpl());
            //LocalCommonFactory.getInstance().setContactDataCommon(new ContactDataCommonImpl());
        }
        ConferenceCommonImpl ConfCommon = (ConferenceCommonImpl)CommonFactory.getInstance().getConferenceCommon();
        if (ConfCommon != null) ConfCommon.setLogPathEx(((ConferenceApplication)this.getApplication()).getFilePath("hslog"));

        DocCommonImpl.mWidth = getWindowManager().getDefaultDisplay().getWidth();
        DocCommonImpl.mHeight = getWindowManager().getDefaultDisplay().getHeight();

        launchText = (TextView)findViewById(R.id.act_logo_launchText);

        llRoot = (RelativeLayout) findViewById(R.id.ll_logo_root);
        llRoot.post(new Runnable() {
            @Override
            public void run() {
                getScreenWidthHeight();
//                doAsReady();
                if (timer == null) {
                    timer = new Timer();
                }
                if (checkNetwork()){
                    launchText.setText("系统正在启动...");
                    timer.schedule(task, 0);
                }
                else
                //Log.d("InfowareLab.Debug","ActLogo.task");
                    timer.schedule(task, 1000, 1000);
            }
        });
    }


    private void getScreenWidthHeight() {
        DisplayMetrics dm = new DisplayMetrics();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            getWindowManager().getDefaultDisplay().getRealMetrics(dm);
        } else {
            getWindowManager().getDefaultDisplay().getMetrics(dm);
        }
        ConferenceApplication.DENSITY = dm.density;
        if (dm.widthPixels > dm.heightPixels) {
            ConferenceApplication.SCREEN_WIDTH = dm.widthPixels;
            ConferenceApplication.SCREEN_HEIGHT = dm.heightPixels;
            ConferenceApplication.PPI_WIDTH = dm.xdpi;
            ConferenceApplication.PPI_HEIGHT = dm.ydpi;
        } else {
            ConferenceApplication.SCREEN_WIDTH = dm.heightPixels;
            ConferenceApplication.SCREEN_HEIGHT = dm.widthPixels;
            ConferenceApplication.PPI_WIDTH = dm.ydpi;
            ConferenceApplication.PPI_HEIGHT = dm.xdpi;
        }
        ConferenceApplication.Screen_W = ConferenceApplication.SCREEN_WIDTH;
        ConferenceApplication.Screen_H = ConferenceApplication.SCREEN_HEIGHT;
        ConferenceApplication.Root_W = llRoot.getWidth() > 0 ? llRoot.getWidth() : ConferenceApplication.Screen_W;
        ConferenceApplication.Root_H = llRoot.getHeight() > 0 ? llRoot.getHeight() : ConferenceApplication.Screen_H;
        int[] location = new int[2];
        llRoot.getLocationOnScreen(location);
        if (location[1] > 0) {
            ConferenceApplication.StateBar_H = location[1];
            ConferenceApplication.NavigationBar_H = ConferenceApplication.Screen_H - ConferenceApplication.Root_H - ConferenceApplication.StateBar_H;
        }

        ConferenceApplication.Top_H = 0;
        ConferenceApplication.Bottom_H = 0;

        Log.d("InfowareLab.Logo", "Logo ScreenW=" + ConferenceApplication.Screen_W
                + " ScreenH=" + ConferenceApplication.Screen_H
                + " RootW=" + ConferenceApplication.Root_W
                + " RootH=" + ConferenceApplication.Root_H
                + " StateH=" + ConferenceApplication.StateBar_H
                + " KeyH=" + ConferenceApplication.NavigationBar_H
                + " Density=" + ConferenceApplication.DENSITY
                + " Top_H=" + ConferenceApplication.Top_H
                + " Bottom_H=" + ConferenceApplication.Bottom_H);

    }

    public Handler confHandler = new Handler() {
        public void handleMessage(Message msg) {

            switch (msg.what) {

                case ConferenceCommon.RESULT_SUCCESS:
                    Log.d("InfowareLab.Debug","(ActLogo)conf.Handler: RESULT_SUCCESS");
                    SharedPreferencesUrls.getInstance().putBoolean("isPopup1",false);
//                    //通知是box

                    if (null != mConfIntent){
                        Log.d("InfowareLab.Debug","(ActLogo)Ignored for ActConf is already launched.");
                        break;
                    }

                    Log.d("InfowareLab.Debug","(ActLogo)conf.Handler: Launch ActConf");

                    ConferenceCommonImpl ConfCommon = (ConferenceCommonImpl)CommonFactory.getInstance().getConferenceCommon();
                    if (ConfCommon != null) ConfCommon.setHandler(null);

                    mConfIntent = new Intent(ActLogo.this, ActConf.class);

                    mConfIntent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                    if (mActionMode != -1) {
                        mConfIntent.putExtra("actionMode", mActionMode);
                        mConfIntent.putExtra("meeting_id", mConfBean.getId());
                        mConfIntent.putExtra("confName", mConfBean.getName());
                        mConfIntent.putExtra("joinName", mLoginBean.getUsername());
                        mConfIntent.putExtra("recordVideoTime", mRecordDuration);
                        mConfIntent.putExtra("recordInterval", mRecordInterval);
                        mConfIntent.putExtra("groupId", mGroupId);
                        if (mUploadSite != null) mConfIntent.putExtra("uploadUrl", mUploadSite);
                        if (mAttachInfo != null) mConfIntent.putExtra("attachInfo", mAttachInfo);
                    }

                    startActivity(mConfIntent);

                    finish();

                    break;
                case ConferenceCommon.BEYOUNGMAXCOUNT:
                    //hideLoading();
                    toastInThread(R.string.item_meetings_err_5);

                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case ConferenceCommon.BEYOUNGJIAMI:
                    //hideLoading();
                    toastInThread(R.string.item_meetings_err_6);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case ConferenceCommon.LEAVE:
                    break;
                case ConferenceCommon.CLOUDRECORD:
                    break;
                case ConferenceCommon.LICENSE_ERR:
                    //hideLoading();
                    toastInThread(R.string.item_meetings_err_6);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case NEED_LOGIN:
                    toastInThread(R.string.item_meetings_err_1);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case NEED_PASSWORD:
                    toastInThread(R.string.item_meetings_pwd1);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case NO_CONFERENCE:
                    toastInThread(R.string.item_meetings_err_3);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case MEETINGNOTJOINBEFORE:
                    toastInThread(R.string.item_meetings_err_7);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case HOSTERROR:
                    toastInThread(R.string.item_meetings_err_13);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case SPELLERROR:
                    toastInThread(R.string.item_meetings_err_8);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case LOGINFAILED:
                    toastInThread(R.string.item_meetings_err_9);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case CREATECONF_ERROR:
                    toastInThread(R.string.item_meetings_err_14);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case MEETINGINVALIDATE:
                    toastInThread(R.string.item_meetings_err_2);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    break;
                case CONF_CONFLICT:
                    toastInThread(R.string.item_meetings_err_9);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);

                    break;
                default:
                    Log.d("InfowareLab.Debug", "(ActLogo) default: "+ msg.what);

                    if (msg.what < 0){
                        toastInThread(R.string.item_meetings_err_6);
//                        post(new Runnable() {
//                            @Override
//                            public void run() {
//                                showLongToast(R.string.item_meetings_err_6);
//                            }
//                        });
                        postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                returnWithResultCode(RESULT_CANCELED);
                            }
                        }, 3 * 1000);
                    }

                    break;
            }
        }

    };

    private LoginBean getLoginBean(String pwd) {
        LoginBean loginBean = new LoginBean();
        String showName = FileUtil.readSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
        int uid = FileUtil.readSharedPreferencesInt(ActLogo.this, Constants.SHARED_PREFERENCES,Constants.USER_ID);
        loginBean = new LoginBean(mConfBean.getId(), showName, pwd);
        loginBean.setType(mConfBean.getType());
        loginBean.setUid(uid);
        return loginBean;
    }

    private void startConf(){

        ConferenceCommonImpl ConfCommon = (ConferenceCommonImpl)CommonFactory.getInstance().getConferenceCommon();
        if (ConfCommon == null || mConfBean == null || mLoginBean == null) return;

        ConfCommon.setLogPath(((ConferenceApplication)this.getApplication()).getFilePath("hslog"));
        ConfCommon.initSDK();
        ConfCommon.setHandler(confHandler);

        //LoginBean bean = new LoginBean();
        mLoginBean.setConferenceId(mRoomId);
        mLoginBean.setPassword(mRoomPwd);

        XMLUtils.CONFIGID = mConfBean.getId();
        XMLUtils.CONFIGNAME = mConfBean.getName();
        XMLUtils.CONFERENCEPATTERN = mConfBean.getConferencePattern();

//        String userName =  FileUtil.readSharedPreferences(
//                ActLogo.this, Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
//        if (!TextUtils.isEmpty(userName)){
//            mLoginBean.setUsername(userName);
//        }
//
//        String nickName = "";
//        if (!TextUtils.isEmpty(FileUtil.readSharedPreferences(ActLogo.this,
//                Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME))){
//            nickName = FileUtil.readSharedPreferences(ActLogo.this,
//                    Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME);
//        }

        String nickName = mLoginBean.getUsername();

        //更新登录用户的信息
        //((ConferenceCommonImpl) CommonFactory.getInstance().getConferenceCommon())
        //        .checkUser(getLoginBean(mRoomPwd));

        if (logined && (nickName.equals(mConfBean.getHostName()) || nickName.equals(mConfBean.getCreatorName()))) {
            new Thread(new Runnable() {
                @Override
                public void run() {

                    int uid = FileUtil.readSharedPreferencesInt(ActLogo.this,Constants.SHARED_PREFERENCES, Constants.USER_ID);
                    String siteId = FileUtil.readSharedPreferences(ActLogo.this,
                            Constants.SHARED_PREFERENCES, Constants.SITE_ID);

                    if (!Config.startConf(uid, nickName, siteId, mConfBean).equals("-1:error")){
                        ConfCommon.setMeetingBox();

                        boolean existCamera = ConferenceApplication.existCamera();
                        boolean existMicrophone = ConferenceApplication.existMicrophone(getApplicationContext());

                        Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
                        ConfCommon.setDeviceStatus(existMicrophone, existCamera);

                        ConfCommon.joinConference(ConfCommon.getParam(getLoginBean(mRoomPwd), true));
                    }
                    else
                    {
                        toastInThread(R.string.item_meetings_err_14);
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                returnWithResultCode(RESULT_CANCELED);
                            }
                        }, 3 * 1000);
                    }
                }
            }).start();
        }else {

            String param = Config.getConfigParam(mLoginBean, Config.MEETING);
            ConfCommon.setMeetingBox();

            boolean existCamera = ConferenceApplication.existCamera();
            boolean existMicrophone = ConferenceApplication.existMicrophone(getApplicationContext());

            Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
            ConfCommon.setDeviceStatus(existMicrophone, existCamera);

            ConfCommon.joinConference(param);
        }
    }


    private Handler toastTandler = new Handler(Looper.getMainLooper());

    public void toastInThread(int resId){
        toastTandler.post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(ActLogo.this, resId, Toast.LENGTH_LONG).show();
            }
        });
    }


    private void doAsReady() {
        Config.Site_URL = FileUtil.readSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES, Constants.SITE);
        Config.SiteName = FileUtil.readSharedPreferences(ActLogo.this, Constants.SHARED_PREFERENCES, Constants.SITE_NAME);
        Config.HAS_LIVE_SERVER = FileUtil.readSharedPreferences(ActLogo.this, Constants.SHARED_PREFERENCES, Constants.HAS_LIVE_SERVER).equals("true");
        if (flag) {
            new Thread() {
                @Override
                public void run() {
                    mHomeIntent = null;

                    Config.Site_URL = FileUtil.readSharedPreferences(ActLogo.this,
                            Constants.SHARED_PREFERENCES, Constants.SITE);

                    if (!checkIntentInput()) return;

                    if (checkNetwork()) {
                        destroyTimer();
//                        try {
//                            Thread.sleep(1 * 1000);
//                        } catch (InterruptedException e) {
//                            e.printStackTrace();
//                        }
                        if (Config.Site_URL == null || Config.Site_URL.equals("")) {
                            //此处走了很多次,一个值过滤一下
                            if (isFlag) {
                                isFlag = false;
                                Log.d("InfowareLab.Debug", "Start ActHome with empty site URL(Init)");

                                if (mHomeIntent == null) {
                                    mHomeIntent = new Intent(ActLogo.this, ActHome.class);
                                    mHomeIntent.setAction("Init");
                                    mHomeIntent.setData(getIntent().getData());
                                    startActivity(mHomeIntent);
                                }

                                finish();
                            }
                        } else {
//                            if (!TextUtils.isEmpty(userName)){
                                //启动子线程网络请求
                                new Thread(new Runnable() {
                                    @Override
                                    public void run() {

                                        PublicWay.setContext(getApplicationContext());

                                        PublicWay.getIpAndPort(ActLogo.this,Config.Site_URL,FileUtil.readSharedPreferences(ActLogo.this,
                                        Constants.SHARED_PREFERENCES, Constants.SITE_ID));

                                        Config.Site_URL = FileUtil.readSharedPreferences(ActLogo.this,
                                                Constants.SHARED_PREFERENCES, Constants.SITE);
                                        FileUtil.saveSharedPreferences(ActLogo.this,
                                                Constants.SHARED_PREFERENCES,
                                                Constants.SITE_NAME, Config.getSiteName(Config.Site_URL));
                                        String id = FileUtil.readSharedPreferences(ActLogo.this,
                                                Constants.SHARED_PREFERENCES, Constants.SITE_ID);

                                        if (Config.SiteType <= 1){
                                            handler.post(new Runnable() {
                                                @Override
                                                public void run() {
                                                    Toast.makeText(ActLogo.this, R.string.set_user_err8, Toast.LENGTH_LONG).show();
                                                }
                                            });
                                            return;
                                        }

                                        loginIfNeed();

                                        int userId = getApplicationContext().getSharedPreferences(Constants.SHARED_PREFERENCES,
                                                getApplicationContext().MODE_WORLD_READABLE).getInt(Constants.USER_ID, 0);
                                        if (TextUtils.isEmpty(id)){
                                            id = "0";
                                        }
                                        
//                                        if (Config.terminateRegist(DeviceIdFactory.getUUID1(ActLogo.this), FileUtil.readSharedPreferences(ActLogo.this,
//                                                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME), Integer.parseInt(id),userId).equals("0")) {
//                                            handler.post(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    Toast.makeText(ActLogo.this, R.string.regist_success, Toast.LENGTH_SHORT).show();
//                                                }
//                                            });
//                                        } else if (Config.terminateRegist(DeviceIdFactory.getUUID1(ActLogo.this), FileUtil.readSharedPreferences(ActLogo.this,
//                                                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME), Integer.parseInt(id),userId).equals("-1")) {
//                                            handler.post(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    Toast.makeText(ActLogo.this, R.string.regist_fail, Toast.LENGTH_SHORT).show();
//                                                }
//                                            });
//                                        }
                                    }
                                }).start();
//                            }
                            //此处走了很多次,一个值过滤一下
                            if (isFlag){
                                isFlag = false;
                                //Log.d("InfowareLab.Debug", "Start ActHome with site URL(List)");
                                if (mActionMode > -1 && mRoomId != null && mRoomId.length() > 0){
                                    startConf();
                                }
                                else {
                                    Log.d("InfowareLab.Debug", "Start ActHome with site URL(List)");

                                    if (mHomeIntent == null) {
                                        mHomeIntent = new Intent(ActLogo.this, ActHome.class);
                                        mHomeIntent.setAction("List");
                                        mHomeIntent.setData(getIntent().getData());
                                        //intent.putExtra("enterFromIntent", enterFromIntent);
                                        startActivity(mHomeIntent);
                                    }

                                    finish();
                                }
                                //intent = new Intent(ActLogo.this, ActHome.class);
                                //intent.setAction("List");
                            }
                        }
                    } else {
                        count++;
                        if (count >= 40){
                            destroyTimer();

                            if (mHomeIntent == null) {
                                mHomeIntent = new Intent(ActLogo.this, ActNet.class);
                                mHomeIntent.setData(getIntent().getData());
                                startActivity(mHomeIntent);
                                startActivity(mHomeIntent);
                            }

                            finish();
                        }
                    }
                }
            }.start();
        }
    }

    private void loginIfNeed() {

        Log.d("InfowareLab.Debug", "loginIfNeed");

        Config.Site_URL = FileUtil.readSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES, Constants.SITE);

        if (Config.Site_URL == null || Config.Site_URL.equals("")){
            Log.d("InfowareLab.Debug", "loginIfNeed Error: " + "Config.Site_URL is NULL");
            return;
        }

        String userName =  FileUtil.readSharedPreferences(
                ActLogo.this, Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME);

        if (userName == null || userName.equals("")){
            Log.d("InfowareLab.Debug", "loginIfNeed Error: " + "login Name is NULL");
            return;
        }

        ConferenceCommonImpl confCommon = (ConferenceCommonImpl)CommonFactory.getInstance().getConferenceCommon();

        String password =  FileUtil.readSharedPreferences(
                ActLogo.this, Constants.SHARED_PREFERENCES, Constants.LOGIN_PASS);


        LoginBean loginBean = new LoginBean();
        loginBean.setUsername(userName);
        loginBean.setPassword(password);

        loginBean = confCommon.checkUser(loginBean);

        if (loginBean != null) {
            //FileUtil.saveSharedPreferences(this, Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME, loginBean.getUsername());
            //FileUtil.saveSharedPreferences(this, Constants.SHARED_PREFERENCES, Constants.LOGIN_PASS, loginBean.getPassword());
            FileUtil.saveSharedPreferences(this, Constants.SHARED_PREFERENCES, Constants.USER_ID, loginBean.getUid());
            FileUtil.saveSharedPreferences(this, Constants.SHARED_PREFERENCES, Constants.LOGIN_ROLE, loginBean.getCreateConfRole());
            FileUtil.saveSharedPreferences(this, Constants.SHARED_PREFERENCES, Constants.LOGIN_NICKNAME, loginBean.getUsername());
            FileUtil.saveSharedPreferences(this, Constants.SHARED_PREFERENCES, Constants.LOGIN_EXNAME, loginBean.getRealname());
            //FileUtil.saveSharedPreferences(this, Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME, loginBean.getUsername());

        }
        else
        {
            handler.post(new Runnable() {
                @Override
                public void run() {
                    Toast.makeText(ActLogo.this, R.string.set_user_login_fail, Toast.LENGTH_SHORT).show();
                }
            });
        }

    }

    @Override
    protected void onDestroy() {
        isFlag = true;
        super.onDestroy();
        destroyTimer();
        try {
            if (PublicWay.socketBinder != null && PublicWay.mConnection != null){
                unbindService(PublicWay.mConnection);
            }
        }catch (IllegalArgumentException e){

        }
    }
    //销毁定时器
    private void destroyTimer(){
        count = 0;
        if (task != null) {
            task.cancel();
            task = null;
        }
        if (timer != null){
            timer.purge();
            timer.cancel();
            timer = null;
        }
    }

    private void returnWithResultCode(int resultCode){

        //数据是使用Intent返回
        Intent intent = new Intent();
        //把返回数据存入Intent
        intent.putExtra("result", "0");
        //设置返回数据
        ActLogo.this.setResult(resultCode, intent);
        //关闭Activity
        ActLogo.this.finish();
    }

    private boolean checkIntentInput() {
        Log.d("InfowareLab.Debug","ActLogo.checkIntentInput");

        Intent intent = getIntent();
        if (intent == null) return true;

//        String key0 = "60E81DB8-1D44-F9BA-F4D3-A9600F28";
          String key = "70E81DB8-1D44-F9BA-F4D3-A9600F28";

//        intent.putExtra("userName", "Fred");
//        String userPwd0 = "123456";
//
//        String encryptedPwd = null;
//        encryptedPwd = AESUtil.encryptECB(userPwd0, key);
//        intent.putExtra("userPwd", encryptedPwd);
//        intent.putExtra("svrPort", "80");
//        intent.putExtra("svrAddress", "*************");

//        intent.putExtra("userName", "Fred");
//
//        String userPwd0 = "123456";
//
//        String encryptedPwd = null;
//        encryptedPwd = AESUtil.encryptECB(userPwd0, key);
//        intent.putExtra("userPwd", encryptedPwd);
//        intent.putExtra("svrPort", "80");
//        intent.putExtra("svrAddress", "*************");
//        intent.putExtra("roomId", "06563593");
//        String confPwd = "";
//        String EncryptedConfPwd = null;
//
//        EncryptedConfPwd = AESUtil.encryptECB(confPwd,key);
//        intent.putExtra("roomPwd", EncryptedConfPwd);
//        intent.putExtra("actionMode", 0);

//        intent.putExtra("userName", "admin");
//        String userPwd0 = "123456";
//        String encryptedPwd = null;
//        encryptedPwd = AESUtil.encryptECB(userPwd0, key);
//        intent.putExtra("userPwd", encryptedPwd);
//        intent.putExtra("svrPort", "80");
//        intent.putExtra("svrAddress", "*************");
//        intent.putExtra("roomId", "97176675");
//        intent.putExtra("groupId", "1");
//        intent.putExtra("recordVideoTime", 25);
//        intent.putExtra("recordInterval", 1);
//        intent.putExtra("uploadUrl", "http://hnyxzhdj.mgtekj.com/file/uploadvideo");
//        intent.putExtra("attachInfo", "%7B%22caId%22%3A%228731203589039238%22%2C%22mid%22%3A126926918270326780%2C%22date%22%3A%222020-07-25%2018%3A58%3A00%22%2C%22orgCode%22%3A%5Bnull%5D%2C%22token%22%3A%22bf787144cb3c8bbdc937fa53e7c27227%22%7D");

//        String confPwd = "3296";
//        String EncryptedConfPwd = null;
////
//        EncryptedConfPwd = AESUtil.encryptECB(confPwd,key);
//        intent.putExtra("roomPwd", EncryptedConfPwd);
//          intent.putExtra("actionMode", 0);
//
//        intent.putExtra("nickName", "Frank");
//        intent.putExtra("roomId", "14754157");
//        String confPwd0 = "3296";
//        String EncryptedConfPwd = null;
//        EncryptedConfPwd = AESUtil.encryptECB(confPwd0,key);
//        intent.putExtra("roomPwd", EncryptedConfPwd);
//        intent.putExtra("svrPort", "80");
//        intent.putExtra("svrAddress", "*************");
//        intent.putExtra("actionMode", 1);

//        intent.putExtra("nickName", "Frank");
//        intent.putExtra("roomId", "14754157");
//        String confPwd = "3296";
//        String EncryptedConfPwd = null;
//        EncryptedConfPwd = AESUtil.encryptECB(confPwd,key);
//        intent.putExtra("roomPwd", EncryptedConfPwd);
//        intent.putExtra("svrPort", "80");
//        intent.putExtra("svrAddress", "*************");
//        intent.putExtra("actionMode", 1);

        String svrAddress = intent.getStringExtra("svrAddress");
        if (svrAddress == null) return true;

        String svrPort = intent.getStringExtra("svrPort");
        if (svrPort == null) svrPort = "80";

        svrAddress += ":";
        svrAddress += svrPort;

        if (!checkSite(svrAddress)){
            toastInThread(R.string.set_user_err3);
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    returnWithResultCode(RESULT_CANCELED);
                }
            }, 3 * 1000);
            return false;
        }

        enterFromIntent = true;
        ConferenceApplication.enterFromIntent = true;

        Config.Site_URL = FileUtil.readSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES, Constants.SITE);

        String userName = intent.getStringExtra("userName");
        String userPwd = intent.getStringExtra("userPwd");

        if (userPwd != null && userPwd.length() >= 0) {
            //userPwd = AESCrypt.decrypt(key, userPwd);
            userPwd = AESUtil.decryptECB(userPwd, key);
            //Log.d("InfowareLab.Debug", "pwdAfterDecrypt=" + userPwd);
        }

        String nickName = "";
        logined = false;

        if (userName != null || userPwd != null){
            if (!login(userName, userPwd, Config.Site_URL))
            {
                logined = false;
                //Toast.makeText(ActLogo.this, R.string.set_user_err6, Toast.LENGTH_LONG).show();
                toastInThread(R.string.set_user_err6);
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        returnWithResultCode(RESULT_CANCELED);
                    }
                }, 3 * 1000);
                return false;
            }
            else
            {
                logined = true;
                toastInThread(R.string.set_user_login_suc);
                //Toast.makeText(ActLogo.this, R.string.set_user_login_suc, Toast.LENGTH_LONG).show();
            }
        }
        else
        {
            nickName = intent.getStringExtra("nickName");
            if (nickName != null){
                if (!StringUtil.isNullOrBlank(nickName) && StringUtil.checkInput(nickName, Constants.PATTERN)) {
                    FileUtil.saveSharedPreferences(ActLogo.this,
                            Constants.SHARED_PREFERENCES,
                            Constants.LOGIN_JOINNAME, nickName);

                    logined = false;

                    if (mLoginBean == null){
                        mLoginBean = new LoginBean();
                        mLoginBean.setUsername(nickName);
                    }

                    //UDPHelper.getInstance(getActivity()).reSetSocket(joinnameText, Config.Site_URL, Config.SiteId);
                } else {

                    logined = false;

                    toastInThread(R.string.set_user_err5);
                    //Toast.makeText(ActLogo.this, R.string.set_user_err5, Toast.LENGTH_LONG).show();
                    handler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            returnWithResultCode(RESULT_CANCELED);
                        }
                    }, 3 * 1000);
                    return false;
                    //UDPHelper.getInstance(getActivity()).reSetSocket(getResources().getString(R.string.comm_defaultname) + "_" + DeviceIdFactory.getDeviceId(getActivity()), Config.Site_URL, Config.SiteId);
                }
            }
        }

        mActionMode = intent.getIntExtra("actionMode", -1);
        if (mActionMode < 0) return true;

        mRoomId = intent.getStringExtra("roomId");
        mRoomPwd = intent.getStringExtra("roomPwd");

        if (mRoomId == null || mRoomId.length()<=0) {

            toastInThread(R.string.item_meetings_err_2);
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    returnWithResultCode(RESULT_CANCELED);
                }
            }, 3 * 1000);
            return false;
        }

        if (mRoomPwd == null) mRoomPwd = "";

        if (mRoomPwd != null && mRoomPwd.length() > 0) {
            //mRoomPwd = AESCrypt.decrypt(key, mRoomPwd);
            mRoomPwd = AESUtil.decryptECB(mRoomPwd, key);
            //Log.d("InfowareLab.Debug", "confPwdAfterDecrypt=" + mRoomPwd);
        }

        mConfBean = Config.getConferenceByNumber(mRoomId);

        if (mConfBean == null){
            toastInThread(R.string.item_meetings_err_2);
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    returnWithResultCode(RESULT_CANCELED);
                }
            }, 3 * 1000);
            return false;
        }

        mGroupId = intent.getStringExtra("groupId");

        mRecordDuration = intent.getIntExtra("recordVideoTime", -1);
        mRecordInterval = intent.getIntExtra("recordInterval", -1);
        mUploadSite = intent.getStringExtra("uploadUrl");
        mAttachInfo = intent.getStringExtra("attachInfo");

        if (mLoginBean != null && !logined){
            mLoginBean.setUsername(nickName);
            mLoginBean.setPassword(mRoomPwd);
            mLoginBean.setUid(0);
            mLoginBean.setConferenceId(mRoomId);
        }

        Log.d("InfowareLab.Debug","ActLogo.checkIntentInput: confId=" + mRoomId + "; ActionMode=" + mActionMode);

        return true;
    }

    public String doEncode(String s) {
        String s1 = URLEncoder.encode(s);
        return s1;
    }

    /**
     * 检测出入站点IP的格式
     *
     * @return
     */
    private boolean checkSite(String siteText) {

        if (siteText.trim().length() <= 0 || siteText.trim().equals("")) {
            toastInThread(R.string.set_user_err7);
            return false;
        } else if (siteText.length() > 0) {
            if (!siteText.contains("http://")) {
                if (!siteText.contains("https://") && !StringUtil.isIP(siteText)) {
                    // text="http://"+InputEncode.doEncode(text);
                    String[] ss = siteText.split(":");
                    String s = "";
                    for (String string : ss) {
                        s = s + ":" + doEncode(string);
                    }
                    s = s.replaceFirst(":", "");
                    siteText = "http://" + s;
                }
            } else {
                String s = siteText.replace("http://", "");
                String[] ss = s.split(":");
                s = "";
                for (String string : ss) {
                    s = s + ":" + doEncode(string);
                }
                s = s.replaceFirst(":", "");
                siteText = "http://" + s;
            }
            String regex = "^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]";
            Pattern patt = Pattern.compile(regex);
            Matcher matcher = patt.matcher(siteText.trim());
            boolean isMatch = matcher.matches();
            if (!isMatch) {
                toastInThread(R.string.set_user_err1);
                return false;
            }
        }

        Config.SiteName = Config.getSiteName(siteText);
        if (Config.SiteName.equals("")
                || Config.SiteName == null) {
            toastInThread(R.string.set_user_err3);
            return false;
        } else {
            Config.Site_URL = siteText;
            // 保存SiteName、SiteId信息
            FileUtil.saveSharedPreferences(ActLogo.this,
                    Constants.SHARED_PREFERENCES,
                    Constants.SITE_NAME, Config.SiteName);
            FileUtil.saveSharedPreferences(ActLogo.this,
                    Constants.SHARED_PREFERENCES,
                    Constants.SITE_ID, Config.SiteId);
            FileUtil.saveSharedPreferences(ActLogo.this,
                    Constants.SHARED_PREFERENCES,
                    Constants.SITE, Config.Site_URL);
            FileUtil.saveSharedPreferences(ActLogo.this,
                    Constants.SHARED_PREFERENCES,
                    Constants.HAS_LIVE_SERVER, ""
                            + Config.HAS_LIVE_SERVER);

            return true;
        }

    }


    private boolean login(String userName, String userPwd, String svrAddress) {

        ConferenceCommonImpl ConfCommon = (ConferenceCommonImpl)CommonFactory.getInstance().getConferenceCommon();

        if (null == ConfCommon) return false;

        if (userName == null || userPwd == null) return false;

        if (mLoginBean == null)
            mLoginBean = new LoginBean();

        mLoginBean.setUsername(userName);
        mLoginBean.setPassword(userPwd);

        mLoginBean = ConfCommon.checkUser(mLoginBean);

        if (mLoginBean == null) return false;

        FileUtil.saveSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME,
                userName);
        FileUtil.saveSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES, Constants.LOGIN_PASS,
                userPwd);

        FileUtil.saveSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES, Constants.SITE, Config.Site_URL);

        FileUtil.saveSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES,
                Constants.SITE_NAME, Config.getSiteName(Config.Site_URL));

        FileUtil.saveSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES,
                Constants.USER_ID, mLoginBean.getUid());

        FileUtil.saveSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES,
                Constants.LOGIN_JOINNAME, userName);

        FileUtil.saveSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES,
                Constants.LOGIN_NICKNAME, userName);

        FileUtil.saveSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES,
                Constants.LOGIN_EXNAME, userName);

        FileUtil.saveSharedPreferences(ActLogo.this,
                Constants.SHARED_PREFERENCES,
                Constants.LOGIN_ROLE, mLoginBean.getCreateConfRole());

        return true;
    }

    private boolean checkNet() {
        Runtime runtime = Runtime.getRuntime();
        try {
            java.lang.Process ipProcess = runtime.exec("ping **********");
            int result = ipProcess.waitFor();
            return (result == 0);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 检查网络状态
     *
     * @return
     */
    private boolean checkNetwork() {
        if (NetUtil.isNetworkConnected(this)) {
            return true;
        } else {
            return false;
        }
    }

    private void checkAnnotype() {
        if (((DocCommonImpl) CommonFactory.getInstance().getDocCommon())
                .getAnnotation() == null) {
            ((DocCommonImpl) CommonFactory.getInstance().getDocCommon())
                    .setAnnotation(new AnnotationType(new AnnotationResource(
                            R.id.annotationColorRed, R.id.annotationPen,
                            initRes(), initJson())));
        }
    }

    private Map<Integer, Integer> initRes() {
        Map<Integer, Integer> res = new HashMap<Integer, Integer>();
        res.put(R.id.annotationPen, R.drawable.pen);
        // res.put(R.id.annotationColorBlack, R.drawable.colorblack);
        // res.put(R.id.annotationColorBlue, R.drawable.colorblue);
        res.put(R.id.annotationColorGreen, R.drawable.colorgreen);
        res.put(R.id.annotationColorRed, R.drawable.colorred);
        res.put(R.id.annotationColorYellow, R.drawable.coloryellow);
        // res.put(R.id.annotationColorWhite, R.drawable.colorwhite);
        return res;
    }

    private Map<Integer, String> initJson() {
        Map<Integer, String> jsons = new HashMap<Integer, String>();
        jsons.put(R.id.annotationPen, Constants.PAINT_TYPE_PEN);
        jsons.put(R.id.annotationColorGreen, Constants.COLOR_GREEN);
        jsons.put(R.id.annotationColorRed, Constants.COLOR_RED);
        jsons.put(R.id.annotationColorYellow, Constants.COLOR_YELLOW);
        return jsons;
    }

    public void requestPermissions() {

//        if (android.os.Build.VERSION.SDK_INT >= 31) {
//            String[] perms = {Manifest.permission.MANAGE_EXTERNAL_STORAGE, Manifest.permission.CAMERA,
//                    Manifest.permission.RECORD_AUDIO, Manifest.permission.REORDER_TASKS};
//
////            if (!Environment.isExternalStorageManager()) {
////                Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
////                startActivity(intent);
////                return;
////            }
//
//            if (EasyPermissions.hasPermissions(this, perms)) {
//                // Already have permission, do the thing
//                // ...
//                Log.d("ConfAPI.debug", "already have permission");
//                hasPermission = true;
//                return;
//            } else {
//                // Do not have permissions, request them now
//                Log.d("ConfAPI.debug", "requestPermissions start ... ");
//                EasyPermissions.requestPermissions(this, getString(R.string.permission_rationale),
//                        RC_PERMISSIONS, perms);
//            }
//        }
        if (android.os.Build.VERSION.SDK_INT >= 23)
        {
            String[] perms = {Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.CAMERA,
                    Manifest.permission.RECORD_AUDIO, Manifest.permission.REORDER_TASKS};


            if (EasyPermissions.hasPermissions(this, perms)) {
                // Already have permission, do the thing
                // ...
                Log.d("ConfAPI.debug", "already have permission");
                hasPermission = true;
                return;
            } else {
                // Do not have permissions, request them now
                Log.d("ConfAPI.debug", "requestPermissions start ... ");
                EasyPermissions.requestPermissions(this, getString(R.string.permission_rationale),
                        RC_PERMISSIONS, perms);
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Log.d("ConfAPI.debug", "onRequestPermissionsResult");
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);

    }

    @Override
    public void onPermissionsGranted(int requestCode, @NonNull List<String> perms) {
        Log.d("ConfAPI.debug", "onPermissionsGranted done");

        hasPermission = true;

        doAsReady();
        //initLog4j();
    }

    @Override
    public void onPermissionsDenied(int requestCode, @NonNull List<String> perms) {
        Log.d("ConfAPI.debug", "onPermissionsDenied done");
        showSettingDialog();
    }

    private void showSettingDialog() {
        if (settingDialog == null) {
            settingDialog = new AlertDialog.Builder(this).create();
            settingDialog.setMessage(getString(R.string.string_open_setting_tip));
            settingDialog.setButton(DialogInterface.BUTTON_POSITIVE, getString(R.string.string_goto), new DialogInterface
                    .OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    Intent intent = new Intent(Settings.ACTION_SETTINGS);
                    startActivity(intent);
                    finish();
                }
            });
            settingDialog.setButton(DialogInterface.BUTTON_NEGATIVE, getString(R.string.string_not_goto), new DialogInterface
                    .OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    finish();
                }
            });
            settingDialog.setCancelable(false);
            settingDialog.setCanceledOnTouchOutside(false);
            settingDialog.show();
        } else {
            settingDialog.show();
        }
    }
}
