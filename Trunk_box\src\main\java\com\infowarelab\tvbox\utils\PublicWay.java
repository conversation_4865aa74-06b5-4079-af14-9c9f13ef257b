package com.infowarelab.tvbox.utils;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.util.Xml;

import androidx.annotation.RequiresApi;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.activity.ActConf;
import com.infowarelab.tvbox.activity.ActHome;
import com.infowarelab.tvbox.activity.ActMain;
import com.infowarelab.tvbox.activity.ActSound;
import com.infowarelab.tvbox.activity.MicSetActivity;
import com.infowarelab.tvbox.modle.TerminalsListBean;
import com.infowarelab.tvbox.view.CustomDialog;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.domain.ConferenceBean;
import com.infowarelabsdk.conference.domain.LoginBean;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.MessageEvent;
import com.infowarelabsdk.conference.util.NetUtil;
import com.infowarelabsdk.conference.util.ToastUtil;

import org.greenrobot.eventbus.EventBus;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

/**
 * Created by xiaor on 2019/11/18.
 */

public class PublicWay {
    //公共参数
    public static double latitude = 0.00;
    public static double longitude = 0.00;
    public static String IP = "";
    public static int PORT = -1;
    //关于会议
    private static ConferenceCommonImpl conferenceCommon;
    private static Activity mActivity;

    private static Context mContext;

    private static ConferenceBean confBean;
    //弹窗是否正在显示
    public static CustomDialog dialog = null;
    //是否通知了
    private static boolean isInform = false;
    //socket是否连接
    public static boolean isConnect = false;

    public static List<TerminalsListBean> terminalsData = new ArrayList<>();

    private static boolean inviteServerEnabled = true;

    /**
     * 判断服务是否运行
     */
    public static boolean isServiceRunning(Activity activity, final String className) {
        ActivityManager activityManager = (ActivityManager) activity.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningServiceInfo> info = activityManager.getRunningServices(Integer.MAX_VALUE);
        if (info == null || info.size() == 0) return false;
        for (ActivityManager.RunningServiceInfo aInfo : info) {
            if (className.equals(aInfo.service.getClassName())) return true;
        }
        return false;
    }

    /*获取ip,port接口*/
    public static SocketService.SocketBinder socketBinder;

    public static boolean getIpAndPort(Activity mActivity1, String url, String siteId) {

        if (!inviteServerEnabled)
            return true;

        mActivity = mActivity1;
        String result = "-1";
        final StringBuffer m_url = new StringBuffer(url + "/meeting/remoteServlet?funcName=getInvitingServer&siteId=" + siteId);
        try {
            String response = NetUtil.doGet(m_url.toString());
            if (response != null && !response.equals("")) {
                DocumentBuilderFactory domfac = DocumentBuilderFactory
                        .newInstance();
                DocumentBuilder dombuilder = domfac.newDocumentBuilder();
                Document doc = dombuilder.parse(new InputSource(
                        new StringReader(response)));
                Element root = doc.getDocumentElement();
                if (root == null) {
                    return false;
                }
                if (root.getElementsByTagName("return").item(0).getFirstChild().getNodeValue().equals("0")) {
                    result = root.getElementsByTagName("result").item(0).getTextContent();
                    String[] ss = result.split(":");
                    if (ss.length == 1) {
                        IP = ss[0];
                        PORT = 10002;
                        Log.d("InfowareLab.Debug", "Inviting Server:" + IP + "; port:" + PORT);
                        return true;
                    } else if (ss.length > 1) {
                        IP = ss[0];
                        PORT = Integer.parseInt(ss[1]);
                        Log.d("InfowareLab.Debug", "Inviting Server::" + IP + "; port:" + PORT);
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            /*先判断 Service是否正在运行 如果正在运行  给出提示  防止启动多个service*/
            //Log.e("UUUUUU","IP:::"+IP);
            //Log.e("UUUUUU","PORT:::"+PORT);
            if (PublicWay.isServiceRunning(mActivity, "com.infowarelab.tvbox.utils.SocketService")) {
                if (socketBinder != null) {
//                    socketBinder.getService().count = 0;
                    //socketBinder.getService().isReConnect = true;
                    //socketBinder.getService().bReleaseSocket = false;
                    socketBinder.getService().reconnectSocket();
                } else {
                    Intent intent = new Intent(mActivity, SocketService.class);
                    mActivity.bindService(intent, mConnection, mActivity.BIND_ABOVE_CLIENT);
                    mActivity.startService(intent);
                }
                return true;
            } else {
                /*启动service*/
                Intent intent = new Intent(mActivity, SocketService.class);
                mActivity.bindService(intent, mConnection, mActivity.BIND_ABOVE_CLIENT);
                mActivity.startService(intent);
            }
        }
        return false;
    }

    public static ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder binder) {
            socketBinder = (SocketService.SocketBinder) binder;
            if (socketBinder != null) {
                socketBinder.getService().setOnReceiveDataListener(new SocketService.OnReceiveDataListener() {
                    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
                    @Override
                    public void onReceiveData(String str) {
//                        String[] strBuffer = str.split("\n");
//                        for(int i = 0; i < strBuffer.length; i ++) {
//                            formatXML(0, strBuffer[i]);
//                        }
                        Log.d("InfowareLab.Socket", ">>>socketBinder.getService().onReceiveData:" + str);
                        formatXML(0, str);
                    }
                });
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
        }
    };
    static ConferenceBean tpConfBean = null;

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static void formatXML(int type, String xml) {

        Log.d("InfowareLab.Socket", "formatXML： type = " + type);
        Log.d("InfowareLab.Socket", "formatXML： xml = " + xml);

        XmlPullParser parser = Xml.newPullParser();
        try {
            parser.setInput(new StringReader(xml.trim()));
        } catch (XmlPullParserException e) {
            e.printStackTrace();
        }
        try {
            int event = parser.getEventType();
            while (event != XmlPullParser.END_DOCUMENT) {
                switch (event) {
                    case XmlPullParser.START_DOCUMENT:
                        break;
                    case XmlPullParser.START_TAG:

                        String tagName = parser.getName();
                        Log.d("InfowareLab.Socket", "formatXML： tag = " + tagName);

                        if ("Pack".equalsIgnoreCase(tagName)) {
                            event = parser.next();
                            continue;
                        }

                        String nextText = "";

                        try {
                            nextText = parser.nextText();
                        } catch (XmlPullParserException e) {
                            Log.d("InfowareLab.Socket", "formatXML XmlPullParserException(nextText)：" + e.getMessage());
                            e.printStackTrace();
                            event = parser.next();
                            continue;
                        }

                        Log.d("InfowareLab.Socket", "formatXML： nextText = " + nextText);

                        if (0 == type) {
                            if ("Action".equalsIgnoreCase(tagName)
                                    && "ping_response".equalsIgnoreCase(nextText)) {

                                Log.d("InfowareLab.Socket", ">>>>>> got ping response >>>>>>");
                                //Log.d("InfowareLab.Socket", "xml = " + xml);

                                //formatXML(3, xml);
                                AnalyzeShareCodeMessage(xml);

                                return;

                            } else if ("Action".equalsIgnoreCase(tagName)
                                    && "invite".equalsIgnoreCase(nextText)) {

                                Log.d("InfowareLab.Socket", ">>>>>> got invitation message >>>>>>");
                                Log.d("InfowareLab.Socket", "xml = " + xml);

                                //formatXML(2, xml);

                                AnalyzeInviteMessage(xml);

                                return;
                            } else if ("Action".equalsIgnoreCase(tagName)
                                    && "terminals_response".equalsIgnoreCase(nextText)) {

                                AnalyzeTerminalListMessage(xml);

                                return;
                            }
                            else
                            {
                                return;
                            }

                            /*else if ("Action".equalsIgnoreCase(tagName)
                                    && !"invite".equalsIgnoreCase(nextText)) {

//                                formatXML(1, xml);
//                                if (parser.getEventType() != XmlPullParser.END_TAG) {
//                                    if ("invite_response".equals(parser.nextText().toLowerCase())) {
//                                        return;
//                                    }
//                                }
                                return;

                            } else if ("ConfID".equals(tagName)) {
                                String confID = nextText;
                                isInform = false;
                                XMLUtils.CONFIGID = confID.substring(0, 8);
                                XMLUtils.CONFIGNAME = confID.substring(8, confID.length()).replace("会议", "").trim();
                                CommonFactory commonFactory = CommonFactory.getInstance();
                                conferenceCommon = (ConferenceCommonImpl) commonFactory.getConferenceCommon();
                                Thread thread = new Thread() {
                                    public void run() {
                                        tpConfBean = Config.getConferenceByNumber(XMLUtils.CONFIGID);
                                    }

                                    ;
                                };
                                try {
                                    thread.start();
                                    thread.join();
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                                if (tpConfBean != null) {
                                    confBean = tpConfBean;
                                    XMLUtils.CONFERENCEPATTERN = tpConfBean.getConferencePattern();
                                    SharedPreferencesUrls.getInstance().putBoolean("isPopup", false);
                                    SharedPreferencesUrls.getInstance().putBoolean("isPopup1", false);
                                    if (ActHome.mActivity != null) {
                                        mActivity = ActHome.mActivity;
                                        showDialog(mActivity.getResources().getString(R.string.zd_invite_title),
                                                String.format(ActHome.mActivity.getResources().getString(R.string.dialog_content), XMLUtils.CONFIGNAME));
                                    } else if (ActConf.mActivity != null) {
//                                    mActivity = ActConf.mActivity;
//                                    showDialog(ActConf.mActivity .getResources().getString(R.string.zd_invite_title),
//                                            String.format(ActConf.mActivity .getResources().getString(R.string.dialog_content),XMLUtils.configName));
                                    } else if (ActMain.mActivity != null) {
                                        mActivity = ActMain.mActivity;
                                        showDialog(mActivity.getResources().getString(R.string.zd_invite_title),
                                                String.format(ActMain.mActivity.getResources().getString(R.string.dialog_content), XMLUtils.CONFIGNAME));
                                    } else if (MicSetActivity.mActivity != null) {
                                        mActivity = MicSetActivity.mActivity;
                                        showDialog(mActivity.getResources().getString(R.string.zd_invite_title),
                                                String.format(MicSetActivity.mActivity.getResources().getString(R.string.dialog_content), XMLUtils.CONFIGNAME));
                                    } else if (ActSound.mActivity != null) {
                                        mActivity = ActSound.mActivity;
                                        showDialog(mActivity.getResources().getString(R.string.zd_invite_title),
                                                String.format(ActSound.mActivity.getResources().getString(R.string.dialog_content), XMLUtils.CONFIGNAME));
                                    }
                                } else {
                                    if (tpConfBean == null) {
                                        handler.post(new Runnable() {
                                            @Override
                                            public void run() {
                                                ToastUtil.showMessage(mActivity, "邀请入会错误：无法获取会议信息。", 5 * 1000);
                                            }
                                        });
                                    }
                                }
                            } else if ("InviteID".equals(parser.getName())) {
                                XMLUtils.inviteID = parser.nextText();
                            }
                            handler.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    if (socketBinder != null) {
                                        String name = (mActivity.getSharedPreferences(Constants.SHARED_PREFERENCES,
                                                Context.MODE_WORLD_READABLE)).getString(Constants.LOGIN_JOINNAME, "");
                                        String siteId = FileUtil.readSharedPreferences(mActivity, Constants.SHARED_PREFERENCES,
                                                Constants.SITE_ID);
                                        if (!isInform &&
                                                !TextUtils.isEmpty(XMLUtils.inviteID)) {
                                            socketBinder.getService().sendOrderEx(XMLUtils.getMsg("" + ConferenceApplication.currentTimeMillis(), name,
                                                    DeviceIdFactory.getUUID1(mActivity), XMLUtils.CONFIGID, siteId, XMLUtils.inviteID));
                                            isInform = true;
                                        }
                                    }
                                }
                            }, 500);*/
                        }
                        break;
                    case XmlPullParser.END_TAG:
                        break;
                    default:
                        break;
                }
                event = parser.next();
            }
        } catch (XmlPullParserException e) {
            Log.d("InfowareLab.Socket", "formatXML XmlPullParserException：" + e.getMessage());
            e.printStackTrace();
        } catch (IOException e) {
            Log.d("InfowareLab.Socket", "formatXML IOException：" + e.getMessage());
            e.printStackTrace();
        }
//        catch (InterruptedException e) {
//            e.printStackTrace();
//        }
    }

    private static void AnalyzeTerminalListMessage(String xml) {

        Log.d("InfowareLab.Socket", "AnalyzeTerminalListMessage");

        if (terminalsData.size() != 0) {
            terminalsData.clear();
        }
        //解析xml
        try {
            TerminalsListBean bean = null;
            XmlPullParserFactory pullParserFactory = XmlPullParserFactory.newInstance();
            //获取XmlPullParser的实例
            XmlPullParser xmlPullParser = pullParserFactory.newPullParser();
            //设置输入流  xml文件
            xmlPullParser.setInput(new ByteArrayInputStream(xml.getBytes()), "UTF-8");
            //开始
            int eventType = xmlPullParser.getEventType();
            while (eventType != XmlPullParser.END_DOCUMENT) {
                String nodeName = xmlPullParser.getName();
                switch (eventType) {
                    case XmlPullParser.START_DOCUMENT:
                        //文档开始
                        break;
                    case XmlPullParser.START_TAG:

                        //开始节点
                        if ("Terminal".equals(nodeName)) {
                            bean = new TerminalsListBean();
                        } else if ("ID".equals(nodeName)) {
                            bean.setID(xmlPullParser.nextText());
                        } else if ("Name".equals(nodeName)) {
                            bean.setName(xmlPullParser.nextText());
                        } else if ("InnerSvrIP".equals(nodeName)) {
                            bean.setInnerSvrIP(xmlPullParser.nextText());
                        } else if ("InnerSvrPort".equals(nodeName)) {
                            bean.setInnerSvrPort(xmlPullParser.nextText());
                        } else if ("longitude".equals(nodeName)) {
                            bean.setLongitude(xmlPullParser.nextText());
                        } else if ("latitude".equals(nodeName)) {
                            bean.setLatitude(xmlPullParser.nextText());
                        } else if ("Online".equals(nodeName)) {
                            if ("true".equals(xmlPullParser.nextText())) {
                                bean.setOnline(true);
                            } else {
                                bean.setOnline(false);
                            }
                        }
                        break;
                    //结束节点
                    case XmlPullParser.END_TAG:
                        if ("Terminal".equals(nodeName)) {
                            terminalsData.add(bean);
                            bean = null;
                        }
                        break;
                    default:
                        break;
                }
                eventType = xmlPullParser.next();
            }
            //Log.e("UUUUUU:","数据量::"+terminalsData.size());
            //刷新列表
            MessageEvent messageEvent = new MessageEvent();
            messageEvent.setType(8);
            EventBus.getDefault().postSticky(messageEvent);
        } catch (Exception e) {
            //Log.e("UUUUUU","异常::"+e);
            //Log.e("UUUUUU","异常::"+e);
            e.printStackTrace();
            //ToastUtil.showMessage(mActivity,"获取列表失败，请重试",2 * 1000);
        }
    }

    private static void AnalyzeShareCodeMessage(String xml) {

        //解析xml
        try {

            XmlPullParserFactory pullParserFactory = XmlPullParserFactory.newInstance();
            //获取XmlPullParser的实例
            XmlPullParser xmlPullParser = pullParserFactory.newPullParser();
            //设置输入流  xml文件
            xmlPullParser.setInput(new ByteArrayInputStream(xml.getBytes()), "UTF-8");
            //开始
            int eventType = xmlPullParser.getEventType();
            while (eventType != XmlPullParser.END_DOCUMENT) {
                String nodeName = xmlPullParser.getName();
                switch (eventType) {
                    case XmlPullParser.START_DOCUMENT:
                        //文档开始
                        break;
                    case XmlPullParser.START_TAG:
                        //开始节点
                        if ("ShareCode".equals(nodeName)) {
                            String shareCode = xmlPullParser.nextText();
                            Log.d("InfowareLab.Socket", "AnalyzeShareCodeMessage: >>>>>> shareCode = " + shareCode);
                            if (!TextUtils.isEmpty(shareCode)) {
                                DeviceIdFactory.setShareCode(shareCode);
                                MessageEvent messageEvent = new MessageEvent();
                                messageEvent.setType(22);
                                EventBus.getDefault().postSticky(messageEvent);
                            }
                        }
                        break;
                    //结束节点
                    case XmlPullParser.END_TAG:
                        break;
                    default:
                        break;
                }
                eventType = xmlPullParser.next();
            }
        } catch (Exception e) {
            Log.d("InfowareLab.Socket", "Failed to get share code: " + e.getMessage());
            e.printStackTrace();
            //ToastUtil.showMessage(mActivity,"获取列表失败，请重试",2 * 1000);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private static void AnalyzeInviteMessage(String xml) {

        String fromName = "Unknown";
        String fromID = "Unknown";
        String confID = null;

        //解析xml
        try {
            XmlPullParserFactory pullParserFactory = XmlPullParserFactory.newInstance();
            //获取XmlPullParser的实例
            XmlPullParser xmlPullParser = pullParserFactory.newPullParser();
            //设置输入流  xml文件
            xmlPullParser.setInput(new ByteArrayInputStream(xml.getBytes()), "UTF-8");
            //开始
            int eventType = xmlPullParser.getEventType();
            while (eventType != XmlPullParser.END_DOCUMENT) {
                String nodeName = xmlPullParser.getName();
                switch (eventType) {
                    case XmlPullParser.START_DOCUMENT:
                        //文档开始
                        break;
                    case XmlPullParser.START_TAG:

                        //开始节点
                        if ("From".equals(nodeName)) {
                            Log.d("InfowareLab.Socket", "AnalyzeInviteMessage >>>>>> got From Tag");
                        } else if ("Terminal".equals(nodeName)) {
                            Log.d("InfowareLab.Socket", "AnalyzeInviteMessage >>>>>> got Terminal Tag");
                        } else if ("Name".equals(nodeName)) {
                            fromName = xmlPullParser.nextText();
                            Log.d("InfowareLab.Socket", "AnalyzeInviteMessage >>>>>> got From Name Tag: fromName = " + fromName);
                        } else if ("ID".equals(nodeName)) {
                            fromID = xmlPullParser.nextText();
                            Log.d("InfowareLab.Socket", "AnalyzeInviteMessage >>>>>> got From ID Tag: fromID = " + fromID);
                        } else if ("ConfID".equals(nodeName)) {
                            confID = xmlPullParser.nextText();
                            //confID = confID.substring(0,8);
                            Log.d("InfowareLab.Socket", "AnalyzeInviteMessage >>>>>> got Conf ID Tag: confID = " + confID);
                        } else if ("InviteID".equals(nodeName)) {
                            XMLUtils.inviteID = xmlPullParser.nextText();
                            Log.d("InfowareLab.Socket", "AnalyzeInviteMessage >>>>>> got Conf ID Tag: inviteID = " + XMLUtils.inviteID);

                        }
                        break;
                    //结束节点
                    case XmlPullParser.END_TAG:
                        break;
                    default:
                        break;
                }
                eventType = xmlPullParser.next();
            }

        } catch (Exception e) {

            Log.d("InfowareLab.Socket", "AnalyzeInviteMessage: Failed to analyze invite xml: " + e.getMessage());

            e.printStackTrace();
            //ToastUtil.showMessage(mActivity,"获取列表失败，请重试",2 * 1000);
        }

        if (confID == null) {
            Log.d("InfowareLab.Socket", "AnalyzeInviteMessage:Failed to analyze invite xml: NULL confID");
            return;
        }

        isInform = false;

        if (confID.length() > 8) {
            XMLUtils.CONFIGID = confID.substring(0, 8);
            XMLUtils.CONFIGNAME = confID.substring(8, confID.length()).replace("会议", "").trim();
        }
        else
            XMLUtils.CONFIGID = confID;

        CommonFactory commonFactory = CommonFactory.getInstance();
        conferenceCommon = (ConferenceCommonImpl) commonFactory.getConferenceCommon();
        Thread thread = new Thread() {
            public void run() {
                tpConfBean = Config.getConferenceByNumber(XMLUtils.CONFIGID);
            }
        };
        try {
            thread.start();
            thread.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        if (tpConfBean != null) {

            Log.d("InfowareLab.Socket", "AnalyzeInviteMessage: >>>>>> got the conference information");

            confBean = tpConfBean;
            XMLUtils.CONFERENCEPATTERN = tpConfBean.getConferencePattern();
            SharedPreferencesUrls.getInstance().putBoolean("isPopup", false);
            SharedPreferencesUrls.getInstance().putBoolean("isPopup1", false);

            if (!ActConf.isInMeeting && ActHome.mActivity != null) {
                mActivity = ActHome.mActivity;
                showDialog(mActivity.getResources().getString(R.string.zd_invite_title),
                        String.format(ActHome.mActivity.getResources().getString(R.string.dialog_content), XMLUtils.CONFIGNAME));
            } else if (ActConf.mActivity != null) {

                Log.d("InfowareLab.Socket", ">>>>>> IGNORED THE INVITATION SINCE IN MEETING (0) <<<<<<");
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.showMessage(PublicWay.mContext, "当前会议还没有退出，无法接收屏幕共享，请稍后重试。", 5 * 1000);
                    }
                });
                //showDialog(ActConf.mActivity .getResources().getString(R.string.zd_invite_title),
                //        String.format(ActConf.mActivity .getResources().getString(R.string.dialog_content),XMLUtils.configName));
            }
            else {
                Log.d("InfowareLab.Socket", ">>>>>> IGNORED THE INVITATION SINCE IN MEETING (1) <<<<<<");
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.showMessage(PublicWay.mContext, "此界面无法接收屏幕共享，请退回会议列表重试。", 5 * 1000);
                    }
                });
            }
//            else if (ActMain.mActivity != null) {
//                //mActivity = ActMain.mActivity;
//                //showDialog(mActivity.getResources().getString(R.string.zd_invite_title),
//                //        String.format(ActMain.mActivity.getResources().getString(R.string.dialog_content), XMLUtils.CONFIGNAME));
//            } else if (MicSetActivity.mActivity != null) {
//                //mActivity = MicSetActivity.mActivity;
//                //showDialog(mActivity.getResources().getString(R.string.zd_invite_title),
//                //        String.format(MicSetActivity.mActivity.getResources().getString(R.string.dialog_content), XMLUtils.CONFIGNAME));
//            } else if (ActSound.mActivity != null) {
//                //mActivity = ActSound.mActivity;
//                //showDialog(mActivity.getResources().getString(R.string.zd_invite_title),
//                //        String.format(ActSound.mActivity.getResources().getString(R.string.dialog_content), XMLUtils.CONFIGNAME));
//            }
        } else {
            if (tpConfBean == null) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.showMessage(mActivity, "邀请入会错误：无法获取会议信息。", 5 * 1000);
                    }
                });
            }
        }

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (socketBinder != null) {
                    String name = (mActivity.getSharedPreferences(Constants.SHARED_PREFERENCES,
                            Context.MODE_WORLD_READABLE)).getString(Constants.LOGIN_JOINNAME, "");
                    String siteId = FileUtil.readSharedPreferences(mActivity, Constants.SHARED_PREFERENCES,
                            Constants.SITE_ID);
                    if (!isInform &&
                            !TextUtils.isEmpty(XMLUtils.inviteID)) {
                        socketBinder.getService().sendOrderEx(XMLUtils.getMsg("" + ConferenceApplication.currentTimeMillis(), name,
                                DeviceIdFactory.getUUID1(mActivity), XMLUtils.CONFIGID, siteId, XMLUtils.inviteID));
                        isInform = true;
                    }
                }
            }
        }, 500);
    }

    private static void startConf() {

        if (conferenceCommon == null) {
            handler.post(new Runnable() {
                @Override
                public void run() {
                    ToastUtil.showMessage(mActivity, "邀请入会错误：系统没有准备好，请稍候重试。", 5 * 1000);
                }
            });
        }

        conferenceCommon.setLogPath(((ConferenceApplication) mActivity.getApplication()).getFilePath("hslog"));
        conferenceCommon.initSDK();
        LoginBean bean = new LoginBean();
        bean.setConferenceId(XMLUtils.CONFIGID);
        bean.setUid(0);
        String userName = FileUtil.readSharedPreferences(
                mActivity, Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
        if (TextUtils.isEmpty(userName)) {
            userName = "NO_NAME";
        }
        bean.setUsername(userName);
        String nickName = "";
        if (!TextUtils.isEmpty(FileUtil.readSharedPreferences(mActivity,
                Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME))) {
            nickName = FileUtil.readSharedPreferences(mActivity,
                    Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME);
        }
        //更新登录用户的信息
        ((ConferenceCommonImpl) CommonFactory.getInstance().getConferenceCommon())
                .checkUser(getLoginBean(confBean.getConfPassword()));
        if (nickName.equals(confBean.getHostName()) || nickName.equals(confBean.getCreatorName())) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    SharedPreferences preferences = mActivity.getSharedPreferences(
                            Constants.SHARED_PREFERENCES, Context.MODE_WORLD_READABLE);
                    int uid = preferences.getInt(Constants.USER_ID, 0);
                    String siteId = mActivity.getSharedPreferences(Constants.SHARED_PREFERENCES,
                            mActivity.MODE_WORLD_READABLE).getString(Constants.SITE_ID, "");
                    //Log.d("999999","会议Id::"+XMLUtils.CONFIGID);
                    if (!Config.startConf(uid, FileUtil.readSharedPreferences(
                            mActivity, Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME), siteId, confBean).equals("-1:error")) {
                        conferenceCommon.setMeetingBox();

                        boolean existCamera = ConferenceApplication.existCamera();
                        boolean existMicrophone = ConferenceApplication.existMicrophone(mActivity.getApplicationContext());

                        Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
                        conferenceCommon.setDeviceStatus(existMicrophone, existCamera);
                        conferenceCommon.setAcceptSharingDesktop(true);
                        conferenceCommon.joinConference(conferenceCommon.getParam(getLoginBean(""), true));
                    }
                }
            }).start();
        } else {
            //Log.e("ldy","测试会议加入configId::"+XMLUtils.CONFIGID);
            bean.setPassword(confBean.getConfPassword());
            String param = Config.getConfigParam(bean, Config.MEETING);
            //Log.e("tttttt","会议参数::"+parme);
            conferenceCommon.setMeetingBox();

            boolean existCamera = ConferenceApplication.existCamera();
            boolean existMicrophone = ConferenceApplication.existMicrophone(mActivity.getApplicationContext());

            Log.d("InfowareLab.Debug","FragConfList.setDeviceStatus: existMicrophone = " + existMicrophone + "; existCamera = " + existCamera);
            conferenceCommon.setDeviceStatus(existMicrophone, existCamera);
            conferenceCommon.setAcceptSharingDesktop(true);
            conferenceCommon.joinConference(param);
        }
    }

    static Handler handler = new Handler(Looper.getMainLooper());

    public static void showDialog(String title, String content) {

        boolean autoAccept = FileUtil.readSharedPreferencesBoolean(mActivity, Constants.SHARED_PREFERENCES, Constants.AUTO_ACCEPT, true);

        if (autoAccept) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                if (ActConf.mActivity == null && !ActConf.isInMeeting) {

                    startConf();

                    if (dialog != null && dialog.isShowing()) {
                        dialog.dismiss();
                        dialog = null;
                    }
                } else {

                    Log.d("InfowareLab.Socket", ">>>>>> IGNORED THE INVITATION SINCE IN MEETING <<<<<<");

//                    ActConf.mActivity.exit();
//                    ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getUsersList().clear();
//                    new Handler().postDelayed(new Runnable() {
//                        @Override
//                        public void run() {
//                            startConf();
//                        }
//                    }, 2500);

                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtil.showMessage(mActivity, "当前会议还没有退出，无法接收屏幕共享，请稍后重试。", 5 * 1000);
                        }
                    });

                    return;
                }
            }
            handler.post(new Runnable() {
                @Override
                public void run() {
                    ToastUtil.showMessage(mActivity, "您已加入" + XMLUtils.CONFIGNAME + "的会议", 5 * 1000);
                }
            });
            return;
        }

        if (dialog == null) {
            dialog = new CustomDialog(mActivity, 0);
            //dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            dialog.setTitle(title);
            dialog.setContent(content);
            dialog.setOnResultListener(new CustomDialog.OnResultListener() {
                @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
                @Override
                public void doYes() {
                    // TODO Auto-generated method stub
                    if (ActConf.mActivity == null) {
                        startConf();
                        dialog.dismiss();
                        dialog = null;
                    } else {
                        ActConf.mActivity.exit();
                        ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getUsersList().clear();
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                startConf();
                            }
                        }, 2500);
                    }
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtil.showMessage(mActivity, "您已加入" + XMLUtils.CONFIGNAME + "的会议", 5 * 1000);
                        }
                    });
                }

                @Override
                public void doNo() {
                    // TODO Auto-generated method stub
                    XMLUtils.CONFIGID = "";
                    XMLUtils.CONFIGNAME = "";
                    XMLUtils.CONFERENCEPATTERN = 1;
                    isInform = true;
                    SharedPreferencesUrls.getInstance().putString("configId", "");
                }
            });
            if (!dialog.isShowing()) {
                dialog.show();
            }
        } else {
            dialog.setTitle(title);
            dialog.setContent(content);
            if (!dialog.isShowing()) {
                dialog.show();
            }
        }
    }

    private static LoginBean getLoginBean(String pwd) {
        LoginBean loginBean = new LoginBean();
        String showName = FileUtil.readSharedPreferences(mActivity,
                Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
        SharedPreferences preferences = mActivity.getSharedPreferences(
                Constants.SHARED_PREFERENCES, Context.MODE_WORLD_READABLE);
        int uid = preferences.getInt(Constants.USER_ID, 0);
        loginBean = new LoginBean(confBean.getId(), showName, pwd);
        loginBean.setType(confBean.getType());
        loginBean.setUid(uid);
        return loginBean;
    }

    public static Context getContext() {
        return mContext;
    }

    public static void setContext(Context mContext) {
        PublicWay.mContext = mContext;
    }
}