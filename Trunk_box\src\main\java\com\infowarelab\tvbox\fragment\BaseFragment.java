package com.infowarelab.tvbox.fragment;





import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import androidx.fragment.app.Fragment;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Toast;

import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;

public class BaseFragment extends Fragment{
	public static final String ACTION_SHOWLOADING = "action_showloading";
	public static final String ACTION_HIDELOADING = "action_hideloading";
	public static final String ACTION_LOGOUT = "action_logout";
	public static final String ACTION_UPDATELIST = "action_updatelist";
	public static final String ACTION_SETFINISH = "action_setfinish";
	public static final String ACTION_DSSHARED = "action_dsshared";
	public static final String ACTION_ASSHARED = "action_asshared";
	public static final String ACTION_VSSHARED = "action_vsshared";

	public static final String ACTION_JOIN_CONF = "action_join_conf";
	public static final String ACTION_SHARE_SCREEN = "action_share_screen";
	public static final String ACTION_SETTINGS = "action_settings";
	public static final String ACTION_SHOWHOME = "action_show_home";
	public static final String ACTION_RETURN_DESKTOP = "action_return_desktop";
	public static final String ACTION_RETURN = "action_return";
	public static final String ACTION_CONF_PWD = "action_conf_pwd";

	public static final String ACTION_HIDE_VIDEO = "action_hide_video";
	public static final String ACTION_HIDE_AUDIO = "action_hide_audio";

	//无参构造器
	public BaseFragment(){
		super();
	}
	@SuppressLint("ValidFragment")
	public BaseFragment(ICallParentView iCallParentView) {
		super();
		this.iCallParentView = iCallParentView;
	}

	protected void hideInput(){
		InputMethodManager inputMethodManager = (InputMethodManager) getActivity()
				.getSystemService(Context.INPUT_METHOD_SERVICE);
		if(null!=getActivity().getCurrentFocus().getWindowToken()){
			inputMethodManager.hideSoftInputFromWindow(getActivity()
					.getCurrentFocus().getWindowToken(),
					InputMethodManager.HIDE_NOT_ALWAYS);
		}
	}
	protected void hideInput(View v) {
		InputMethodManager inputMethodManager = (InputMethodManager) getActivity()
				.getSystemService(Context.INPUT_METHOD_SERVICE);
		if (null != getActivity().getCurrentFocus()&&null != getActivity().getCurrentFocus().getApplicationWindowToken()) {
			inputMethodManager.hideSoftInputFromWindow(getActivity().getCurrentFocus()
					.getApplicationWindowToken(),
					InputMethodManager.HIDE_NOT_ALWAYS);
		}
	}
	public void showInput(View v){
		((InputMethodManager) getActivity().getSystemService(getActivity().INPUT_METHOD_SERVICE)).showSoftInput(v, 0);
	}

	public void showLongToast(int resId) {
		Toast.makeText(getActivity(), resId, Toast.LENGTH_LONG).show();
	}

	public void showShortToast(int resId) {
		Toast.makeText(getActivity(), getResources().getString(resId), Toast.LENGTH_SHORT).show();
	}
	public void showShortToast(String s) {
		Toast.makeText(getActivity(), s, Toast.LENGTH_SHORT).show();
	}
	
	protected void showLoading(){
		callParentView(ACTION_SHOWLOADING,"");
	}
	
	protected void hideLoading(){
		callParentView(ACTION_HIDELOADING,"");
	}
	protected void logout(){
		callParentView(ACTION_LOGOUT,"");
	}

	@Override
	public void onHiddenChanged(boolean hidden) {
		// TODO Auto-generated method stub
		callParentView(ACTION_HIDELOADING,"");
		super.onHiddenChanged(hidden);
	}

	@Override
	public void onDestroyView() {
		// TODO Auto-generated method stub
		callParentView(ACTION_HIDELOADING,"");
		super.onDestroyView();
	}

	protected boolean isLogin(){
		String username = FileUtil.readSharedPreferences(getActivity(),
				Constants.SHARED_PREFERENCES, Constants.LOGIN_NAME);
		if (username.equals("")) {
			return false;
		}else {
			return true;
		}
	}
	protected int getUid(){
		SharedPreferences preferences = getActivity().getSharedPreferences(Constants.SHARED_PREFERENCES,
				Context.MODE_PRIVATE);
		int uid = preferences.getInt(Constants.USER_ID, 0);
		return uid;
	}

	private ICallParentView iCallParentView = null;

	public void setiCallParentView(ICallParentView iCallParentView){
		this.iCallParentView = iCallParentView;
	}

	public interface ICallParentView{
		void onCallParentView(String msg,Object obj);
	}

	protected void callParentView(String msg,Object obj){
		if(iCallParentView!=null){
			iCallParentView.onCallParentView(msg,obj);
		}
	}
}
