package com.infowarelab.tvbox.utils;

import android.content.Context;
import android.util.Log;
import android.util.Xml;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.base.ResInvite;
import com.infowarelab.tvbox.base.ResPing;
import com.infowarelab.tvbox.base.ResTerminals;
import com.infowarelab.tvbox.base.Terminal;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.NetUtil;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;
import java.io.StringReader;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.List;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;


public class UDPHelper {

    public Boolean IsThreadDisable = false;//指示监听线程是否终止
    private String url;
    private String ip;
    private int port;
    private String name;
    private String deviceId;
    private String siteId;
    private IUDP iudp;
    private DatagramSocket socket;
    private static final String REQHEAD_PING = "001";
    private static final String REQHEAD_INVITE = "002";
    private static final String REQHEAD_LIST = "003";
    private static final String ACTION_INVITE = "invite";
    private static final String ACTION_INVITE_RES = "invite_response";
    private static final String ACTION_LIST_RES = "terminals_response";
    private static final String ACTION_LIST = "get_terminals";
    private static final String ACTION_PING = "ping";
    private static final String ACTION_PING_RES = "request_status";
    private long threadTag = 0;
    private static UDPHelper udpHelper;


    public static UDPHelper getInstance(Context context){
        if(udpHelper==null){
            String name = FileUtil.readSharedPreferences(context,
                    Constants.SHARED_PREFERENCES, Constants.LOGIN_JOINNAME);
            String url = FileUtil.readSharedPreferences(context,
                    Constants.SHARED_PREFERENCES, Constants.SITE_URL);
            String siteid = FileUtil.readSharedPreferences(context,
                    Constants.SHARED_PREFERENCES, Constants.SITE_ID);
            String deviceId = DeviceIdFactory.getDeviceId(context);

            udpHelper = new UDPHelper(name,deviceId,url,siteid,null);
        }
        return udpHelper;
    }


    private UDPHelper(String name, String deviceId, String url, String siteId, IUDP iudp) {
        this.name = name;
        this.deviceId = deviceId;
        this.url = url;
        this.siteId = siteId;
        this.iudp = iudp;
        this.threadTag = ConferenceApplication.currentTimeMillis();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public IUDP getIudp() {
        return iudp;
    }

    public void setIudp(IUDP iudp) {
        this.iudp = iudp;
    }

    public void sendDta(final String msg) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                if(!getSocket())return;
                try {
                    byte[] buffer = new byte[1024];
                    buffer = (msg).getBytes();
                    DatagramPacket dp = new DatagramPacket(buffer, buffer.length);
                    socket.send(dp);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }

    public void sendPing() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                if(!getSocket())return;
                try {
                    byte[] buffer = new byte[1024];
                    long thistag = threadTag;
                    while (!IsThreadDisable&&thistag==threadTag) {
                        buffer = (getPingXML(ConferenceApplication.currentTimeMillis() + "")).getBytes();
                        DatagramPacket dp = new DatagramPacket(buffer, buffer.length);
                        socket.send(dp);
                        Thread.sleep(4000);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    socket = null;
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    socket = null;
                }
            }
        }).start();
    }

    public void sendGetXML() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                if(!getSocket())return;
                try {
                    byte[] buffer = new byte[1024];
                    buffer = (getTerminalsXML(ConferenceApplication.currentTimeMillis() + "")).getBytes();
                    DatagramPacket dp = new DatagramPacket(buffer, buffer.length);
                    socket.send(dp);
                } catch (IOException e) {
                    e.printStackTrace();
                    socket = null;
                }
            }
        }).start();
    }

    public void sendInvite(final List<String> list, final String confId, final String confTheme) {
        new Thread(new Runnable() {
            @Override
            public void run() {
//                if (socket == null) {
//                    try {
//                        InetAddress ia = InetAddress.getByName(ip);
//                        socket = new DatagramSocket(port);
//                        socket.connect(ia, port);
//                        startReceive();
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
                if(!getSocket())return;
                try {
                    byte[] buffer = new byte[22312];
                    buffer = (getInviteXML(ConferenceApplication.currentTimeMillis() + "", confId + confTheme, list)).getBytes();
                    DatagramPacket dp = new DatagramPacket(buffer, buffer.length);
                    socket.send(dp);
                } catch (IOException e) {
                    e.printStackTrace();
                    socket = null;
                }
            }
        }).start();
    }

    public void sendReply(final String msg) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (socket == null) return;
                try {
                    byte[] buffer = new byte[22312];
                    buffer = msg.getBytes();
                    DatagramPacket dp = new DatagramPacket(buffer, buffer.length);
                    socket.send(dp);
                } catch (IOException e) {
                    e.printStackTrace();
                    socket = null;
                }
            }
        }).start();
    }

    public void startReceive() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long thistag = threadTag;
                    while (!IsThreadDisable&&thistag==threadTag) {
                        DatagramPacket dp1 = new DatagramPacket(new byte[22312], 22312);
                        socket.receive(dp1);
                        String strMsg = new String(dp1.getData()).trim();
                        formatXML(strMsg);
//                        Log.d("UDP Demo", "UDP接收数据:" + strMsg);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }


    private void onReceive(int code, Object obj) {
        if (this.iudp != null) {
            iudp.onReceive(code, obj);
        }
    }

    private void onSend(String msg) {
        if (this.iudp != null) {
            iudp.onSend(0);
        }
    }


    private void formatXML(String xml) {
        XmlPullParser parser = Xml.newPullParser();
        try {
            parser.setInput(new StringReader(xml));
        } catch (XmlPullParserException e) {
            e.printStackTrace();
        }
        try {
            int action = 0;
            String act = "";
            //ping
            ResPing resPing = new ResPing();
            //list
            ResTerminals resTerminals = new ResTerminals();
            List<Terminal> terminals = new ArrayList<Terminal>();
            Terminal terminal = new Terminal();
            //invite
            ResInvite resInvite = new ResInvite();

            int event = parser.getEventType();
            while (event != XmlPullParser.END_DOCUMENT) {
                switch (event) {
                    case XmlPullParser.START_DOCUMENT:
                        break;
                    case XmlPullParser.START_TAG:
                        if ("Action".equals(parser.getName())) {
                            act = parser.nextText();
                            if (act.equals(ACTION_PING_RES)) {
                                action = 1;
                                resPing = new ResPing();
                                resPing.setAction(ACTION_PING_RES);
                            } else if (act.equals(ACTION_PING)) {
                                action = 2;
                            } else if (act.equals(ACTION_LIST_RES)) {
                                action = 3;
                                resTerminals = new ResTerminals();
                                resTerminals.setAction(ACTION_LIST_RES);
                            } else if (act.equals(ACTION_LIST)) {
                                action = 4;
                            } else if (act.equals(ACTION_INVITE_RES)) {
                                action = 5;
                            } else if (act.equals(ACTION_INVITE)) {
                                action = 6;
                                resInvite = new ResInvite();
                                resInvite.setAction(ACTION_INVITE);
                            }
                        } else if (action == 1) {

                            if ("RequestID".equals(parser.getName())) {
                                resPing.setRequestId(parser.nextText());
                            } else if ("Status".equals(parser.getName())) {
                                resPing.setStatus(parser.nextText());
                            } else if ("Request".equals(parser.getName())) {
                                resPing.setRequest(parser.nextText());
                            } else if ("Reason".equals(parser.getName())) {
                                resPing.setReason(parser.nextText());
                            }
                        } else if (action == 3) {
                            if ("Terminals".equals(parser.getName())) {
                                terminals = new ArrayList<Terminal>();
                            } else if ("Terminal".equals(parser.getName())) {
                                terminal = new Terminal();
                            } else if ("Name".equals(parser.getName())) {
                                terminal.setName(parser.nextText());
                            } else if ("ID".equals(parser.getName())) {
                                terminal.setDeviceId(parser.nextText());
                            } else if ("Online".equals(parser.getName())) {
                                terminal.setIsOnline(parser.nextText());
                            }

                        } else if (action == 6) {
                            if ("RequestID".equals(parser.getName())) {
                                resInvite.setRequestId(parser.nextText());
                            } else if ("Terminal".equals(parser.getName())) {
                                terminal = new Terminal();
                            } else if ("Name".equals(parser.getName())) {
                                terminal.setName(parser.nextText());
                            } else if ("ID".equals(parser.getName())) {
                                terminal.setDeviceId(parser.nextText());
                            } else if ("ConfID".equals(parser.getName())) {
                                String c = parser.nextText();
                                if (c.length() > 8) {
                                    resInvite.setConfId(c.substring(0, 8));
                                    resInvite.setConfTitle(c.substring(8));
                                }
                            } else if ("SiteID".equals(parser.getName())) {
                                resInvite.setSiteId(parser.nextText());
                            } else if ("InviteID".equals(parser.getName())) {
                                resInvite.setInviteId(parser.nextText());
                            }
                        }
                        break;
                    case XmlPullParser.END_TAG:
                        if (action == 1) {

                        } else if (action == 3) {
                            if ("Terminal".equals(parser.getName())) {
                                terminals.add(terminal);
                            } else if ("Terminals".equals(parser.getName())) {
                                resTerminals.setTerminals(terminals);
                            }
                        } else if (action == 6) {
                            if ("Terminal".equals(parser.getName())) {
                                resInvite.setFromTerminal(terminal);
                            }
                        }
                        break;
                }
                event = parser.next();
            }

            if (action == 1) {
                onReceive(1, resPing);
            } else if (action == 3) {
                onReceive(3, resTerminals);
            } else if (action == 6) {
                onReceive(6, resInvite);
                sendReply(getReplyXML(ACTION_INVITE, resInvite.getRequestId()));
                sendReply(getInviteResponseXML(resInvite.getInviteId(), resInvite.getConfId() + resInvite.getConfTitle()));
            }
        } catch (XmlPullParserException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    private String getTestXML() {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" +
                "<Pack>" +
                "<Action>testing</Action>" +
                "</Pack>";
    }

    private String getPingXML(String RequestID) {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" +
                "<Pack>" +
                "<Action>ping</Action>" +
                "<RequestID>" + RequestID + "</RequestID>" +
                "<TerminalName>" + name + "</TerminalName>" +
                "<TerminalID>" + deviceId + "</TerminalID>" +
                "<SiteID>" + siteId + "</SiteID>" +
                "</Pack>";
    }

    private String getInviteXML(String requestID, String confId, List<String> list) {
        StringBuffer sb = new StringBuffer();
        if (list.size() > 0) {
            sb.append("<To>");
            for (String s : list) {
                sb.append("<Terminal>")
                        .append("<ID>")
                        .append(s)
                        .append("</ID>")
                        .append("</Terminal>")
                ;

            }
            sb.append("</To>");
        }

        return "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" +
                "<Pack>" +
                "<Action>invite</Action>" +
                "<RequestID>" + requestID + "</RequestID>" +
                "<InviteID>" + requestID + "</InviteID>" +
                "<From>" +
                "<Terminal>" +
                "<Name>" + name + "</Name>" +
                "<ID>" + deviceId + "</ID>" +
                "</Terminal>" +
                "</From>" +

                sb.toString() +

                "<ConfID>" + confId + "</ConfID>" +
                "<SiteID>" + siteId + "</SiteID>" +
                "</Pack>";
    }

    private String getInviteResponseXML(String inviteID, String confId) {

        return "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" +
                "<Pack>" +
                "<Action>invite_response</Action>" +
                "<InviteID>" + inviteID + "</InviteID>" +
                "<Result>arrived</Result>" +
                "<From>" +
                "<Terminal>" +
                "<Name>" + name + "</Name>" +
                "<ID>" + deviceId + "</ID>" +
                "</Terminal>" +
                "</From>" +
                "<ConfID>" + confId + "</ConfID>" +
                "<SiteID>" + siteId + "</SiteID>" +
                "</Pack>";
    }

    private String getTerminalsXML(String requestID) {

        return "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" +
                "<Pack>" +
                "<Action>get_terminals</Action>" +
                "<RequestID>" + requestID + "</RequestID>" +
                "<From>" + deviceId + "</From>" +
                "<SiteID>" + siteId + "</SiteID>" +
                "</Pack>";
    }

    private String getReplyXML(String action, String requestId) {

        return "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" +
                "<Pack>" +
                "<Action>request_status</Action>" +
                "<RequestID>" + requestId + "</RequestID>" +
                "<Status>successed</Status>" +
                "<Request>" + action + "</Request>" +
                "<Reason>reasons</Reason>" +
                "</Pack>";
    }


    public boolean getSocket() {
        if(socket==null){
            if(getIpAndPort(url,siteId)){
                try {
                    InetAddress ia = InetAddress.getByName(ip);
                    socket = new DatagramSocket(port);
                    socket.connect(ia, port);
                    startReceive();
                    return true;
                } catch (IOException e) {
                    e.printStackTrace();
                    return false;
                }
            } else {
                return false;
            }
        }else {
            return true;
        }
    }
    public boolean reSetSocket(String name,String url, String siteId) {
        this.name = name;
        this.url = url;
        this.siteId = siteId;
        if(socket==null){
            if(getIpAndPort(url,siteId)){
                try {
                    InetAddress ia = InetAddress.getByName(ip);
                    socket = new DatagramSocket(port);
                    socket.connect(ia, port);
                    startReceive();
                    return true;
                } catch (IOException e) {
                    e.printStackTrace();
                    return false;
                }
            } else {
                return false;
            }
        }else {
            threadTag = ConferenceApplication.currentTimeMillis();
            socket.close();
            socket = null;
            if(getIpAndPort(url,siteId)){
                try {
                    InetAddress ia = InetAddress.getByName(ip);
                    socket = new DatagramSocket(port);
                    socket.connect(ia, port);
                    startReceive();
                    return true;
                } catch (IOException e) {
                    e.printStackTrace();
                    return false;
                }
            } else {
                return false;
            }
        }
    }


    public boolean getIpAndPort(String url, String siteId) {
        String result = "-1";
        final StringBuffer m_url = new StringBuffer(url + "/meeting/remoteServlet?funcName=getInvitingServer&siteId=" + siteId);

        Log.d("UDP Demo", "getIpAndPort:" + m_url.toString());
        try {
            String response = NetUtil.doGet(m_url.toString());
            Log.d("UDP Demo", "getIpAndPort:" + response);
            if(response!=null&&!response.equals("")){
                DocumentBuilderFactory domfac = DocumentBuilderFactory
                        .newInstance();
                DocumentBuilder dombuilder = domfac.newDocumentBuilder();
                Document doc = dombuilder.parse(new InputSource(
                        new StringReader(response)));
                Element root = doc.getDocumentElement();
                if(root == null){
                    return false;
                }
                if(root.getElementsByTagName("return").item(0).getFirstChild().getNodeValue().equals("0")){
                    result = root.getElementsByTagName("result").item(0).getTextContent();
                    String[] ss = result.split(":");
                    if(ss.length==1){
                        this.ip = ss[0];
                        this.port = 10001;
                        Log.d("UDP Demo", "ip:" + this.ip+"; port:"+this.port);
                        return true;
                    }else if(ss.length>1){
                        this.ip = ss[0];
                        this.port = Integer.parseInt(ss[1]);
                        Log.d("UDP Demo", "ip:" + this.ip+"; port:"+this.port);
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

}
