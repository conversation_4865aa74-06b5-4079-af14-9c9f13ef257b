package com.infowarelab.tvbox.activity;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import com.infowarelab.tvbox.BuildConfig;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.base.BaseApplication;
import com.infowarelab.tvbox.recorderlib.RecordManager;
import com.infowarelab.tvbox.recorderlib.recorder.RecordConfig;
import com.infowarelab.tvbox.recorderlib.recorder.RecordHelper;
import com.infowarelab.tvbox.recorderlib.recorder.listener.RecordFftDataListener;
import com.infowarelab.tvbox.recorderlib.recorder.listener.RecordResultListener;
import com.infowarelab.tvbox.recorderlib.recorder.listener.RecordSoundSizeListener;
import com.infowarelab.tvbox.recorderlib.recorder.listener.RecordStateListener;
import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.FileUtils;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelab.tvbox.view.AudioView;
import com.infowarelabsdk.conference.util.ToastUtil;
import com.infowarelabsdk.conference.util.Utils;

import java.io.File;
import java.io.IOException;

public class MicSetActivity extends BaseFragmentActivity implements View.OnClickListener {

    public static MicSetActivity mActivity = null;

    private Context mContext;

    private RelativeLayout btnLayout;
    private RelativeLayout.LayoutParams params;
    private LinearLayout seebBarLayout;
    private AudioView audioView;
    private SeekBar seekBar;
    private RadioButton recordBtn;
    private RadioButton stopRecordBtn;
    private RadioButton startBtn;
    private TextView stateText;

    //调节音量的大小
    private int maxVolume = 50; // 最大音量值
    private int curVolume = 20; // 当前音量值
    private int stepVolume = 0; // 每次调整的音量幅度
    private AudioManager audioMgr = null; // Audio管理器，用了控制音量

    //录制保存的路径
    private String voicePath = "";
    private String dirPath = Environment.getExternalStorageDirectory().getPath() + "/AudioRecord/";

    private boolean isStart = false;
    private boolean isPause = false;
    private final RecordManager recordManager = RecordManager.getInstance();

    private MediaPlayer mediaPlayer;

    //录制的状态
    private int recordState = 5;
    private LinearLayout llCancel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        setContentView(R.layout.ui_record);

//        Window window = getWindow();
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//
//            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN;
//
//            window.getDecorView().setSystemUiVisibility(uiOptions);
//            window.setStatusBarColor(Color.TRANSPARENT);
//        }
//        else {
//            View v = this.getWindow().getDecorView();
//            v.setSystemUiVisibility(View.GONE);
//        }

        mContext = this;
        initView();
    }

    private void initView(){

        btnLayout = (RelativeLayout) findViewById(R.id.ui_record_btnLayout);
        seebBarLayout = (LinearLayout)findViewById(R.id.ui_record_seebBarLayout);
        audioView = (AudioView)findViewById(R.id.ui_record_audioView);
        seekBar = (SeekBar) findViewById(R.id.ui_record_micSeekBar);
        recordBtn = (RadioButton)findViewById(R.id.ui_record_recordBtn);
        stopRecordBtn = (RadioButton)findViewById(R.id.ui_record_stopRecordBtn);
        startBtn = (RadioButton)findViewById(R.id.ui_record_startBtn);
        stateText = (TextView)findViewById(R.id.ui_record_stateText);


        seekBar.setFocusable(true);
        seekBar.requestFocus();

        //params = (RelativeLayout.LayoutParams) btnLayout.getLayoutParams();

        audioMgr = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        // 获取最大音乐音量
        maxVolume = audioMgr.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        // 初始化音量大概为最大音量的1/2
        curVolume = SharedPreferencesUrls.getInstance().getInt("video_progress",maxVolume * 4/5);
        // 每次调整的音量大概为最大音量的1/6
        stepVolume = maxVolume / 6;
        audioMgr.setStreamVolume(AudioManager.STREAM_MUSIC, 5, AudioManager.FLAG_SHOW_UI);

        seekBar.setMax(maxVolume);
        seekBar.setProgress(curVolume);
        adjustVolume();
        initRecord();
//        AndPermission.with(this)
//                .runtime()
//                .permission(new String[]{Permission.READ_EXTERNAL_STORAGE, Permission.WRITE_EXTERNAL_STORAGE,
//                        Permission.RECORD_AUDIO})
//                .start();

        llCancel = (LinearLayout) findViewById(R.id.ll_frag_set_cancel);

        llCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }
    private void initRecord() {

        mediaPlayer = new MediaPlayer();

        recordManager.init(BaseApplication.getInstance(), BuildConfig.DEBUG);
        recordManager.changeFormat(RecordConfig.RecordFormat.WAV);
//        if (Build.MODEL.indexOf("MV200") == -1
//                && Build.MODEL.indexOf("MRUT33") == -1) {
//            recordManager.changeRecordConfig(recordManager.getRecordConfig().setSampleRate(44100));
//        }else {
//            recordManager.changeRecordConfig(recordManager.getRecordConfig().setSampleRate(44100));
//        }
        recordManager.changeRecordConfig(recordManager.getRecordConfig().setEncodingConfig(AudioFormat.ENCODING_PCM_16BIT));
        recordManager.changeRecordDir(dirPath);
        //进来停止录制
        doStop();
        initRecordEvent();
    }
    private void initRecordEvent() {

        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                curVolume = progress;
                adjustVolume();
            }
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });

        recordBtn.setOnClickListener(this);
        stopRecordBtn.setOnClickListener(this);
        startBtn.setOnClickListener(this);

        recordManager.setRecordStateListener(new RecordStateListener() {
            @Override
            public void onStateChange(RecordHelper.RecordState state) {

                switch (state) {
                    case PAUSE:
//                        tvState.setText("暂停中");
                        stateText.setText("暂停中");
                        recordState = 0;
                        audioView.setVisibility(View.GONE);
                        //seebBarLayout.setPadding(0, DensityUtil.dip2px(mContext,35),0,0);
                        //params.height = DensityUtil.dip2px(mContext,550);
                        break;
                    case IDLE:
//                        tvState.setText("空闲中");
                        stateText.setText("录音结束");
                        recordState = 1;
                        audioView.setVisibility(View.GONE);
                        //seebBarLayout.setPadding(0, DensityUtil.dip2px(mContext,35),0,0);
                        //params.height = DensityUtil.dip2px(mContext,550);
                        break;
                    case RECORDING:
//                        tvState.setText("录音中");
                        stateText.setText("录音中");
                        recordState = 2;
                        audioView.setVisibility(View.VISIBLE);
                        //seebBarLayout.setPadding(0, 0,0,0);
                        //params.height = DensityUtil.dip2px(mContext,550);
                        break;
                    case STOP:
//                        tvState.setText("停止");
                        stateText.setText("停止");
                        recordState = 3;
                        audioView.setVisibility(View.GONE);
                        //seebBarLayout.setPadding(0, DensityUtil.dip2px(mContext,35),0,0);
                        //params.height = DensityUtil.dip2px(mContext,550);
                        break;
                    case FINISH:
//                        tvState.setText("录音结束");
//                        tvSoundSize.setText("---");
                        stateText.setText("录音结束");
                        recordState = 4;
                        audioView.setVisibility(View.GONE);
                        //seebBarLayout.setPadding(0, DensityUtil.dip2px(mContext,35),0,0);
                        //params.height = DensityUtil.dip2px(mContext,550);
                        break;
                    default:
                        break;
                }

                //btnLayout.setLayoutParams(params);
            }

            @Override
            public void onError(String error) {
                Log.d("InfowareLab.Debug", ">>>>>>MicSetActivity.recordManager.onError" + error);
            }
        });
        recordManager.setRecordSoundSizeListener(new RecordSoundSizeListener() {
            @Override
            public void onSoundSize(int soundSize) {
            }
        });
        recordManager.setRecordResultListener(new RecordResultListener() {
            @Override
            public void onResult(File result) {
                voicePath = result.getAbsolutePath();
            }
        });
        recordManager.setRecordFftDataListener(new RecordFftDataListener() {
            @Override
            public void onFftData(byte[] data) {
                audioView.setWaveData(data);
            }
        });

        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                //播放完毕
                recordState = 5;
            }
        });
    }
    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.ui_record_recordBtn:
                if (Utils.isFastClick())return;
                //录制
                if (recordState == 2){
                    ToastUtil.showMessage(mContext,"正在录音",5 * 1000);
                    return;
                }
                if ((mediaPlayer != null && mediaPlayer.isPlaying()) &&  5 != recordState){
                    ToastUtil.showMessage(mContext,"正在播放中,无法录音",5 * 1000);
                    return;
                }
                doPlay();
                break;
            case R.id.ui_record_stopRecordBtn:
                //停止录制
                if (Utils.isFastClick())return;
                if (recordState != 2){
                    ToastUtil.showMessage(mContext,"已停止录音",5 * 1000);
                    return;
                }
                doStop();
                break;
            case R.id.ui_record_startBtn:
                //开始播放
                if (Utils.isFastClick())return;
                if (2 == recordState){
                    ToastUtil.showMessage(mContext,"正在录音，无法播放",5 * 1000);
                    return;
                }
                if (TextUtils.isEmpty(voicePath)){
                    ToastUtil.showMessage(mContext,"您暂未进行录音,无法进行播放",5 * 1000);
                    return;
                }
                try {
                    if (mediaPlayer == null){
                        mediaPlayer = new MediaPlayer();
                    }
                    mediaPlayer.reset();
                    mediaPlayer.setDataSource(voicePath);
                    mediaPlayer.prepare();
                    mediaPlayer.start();

                } catch (IOException e) {
                    e.printStackTrace();
                    ToastUtil.showMessage(mContext,"您暂未录音,无法进行播放",5 * 1000);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_VOLUME_UP:
            case KeyEvent.KEYCODE_PLUS:
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                addVolume();
                break;
            case KeyEvent.KEYCODE_VOLUME_DOWN:
            case KeyEvent.KEYCODE_MINUS:
            case KeyEvent.KEYCODE_DPAD_LEFT:
                reduceVolume();
                break;
            case KeyEvent.KEYCODE_BACK:
                SharedPreferencesUrls.getInstance().putInt("video_progress",curVolume);
                break;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        SharedPreferencesUrls.getInstance().putInt("video_progress",curVolume);
    }

    /**
     * 调整音量
     */
    private void adjustVolume() {
        audioMgr.setStreamVolume(AudioManager.STREAM_MUSIC, curVolume,
                AudioManager.FLAG_PLAY_SOUND);
    }
    //增加音量
    private void addVolume(){
        curVolume += stepVolume;
        if (curVolume >= maxVolume) {
            curVolume = maxVolume;
        }
        seekBar.setProgress(curVolume);
        adjustVolume();
    }
    //降低音量
    private void reduceVolume(){
        curVolume -= stepVolume;
        if (curVolume <= 0) {
            curVolume = 0;
        }
        seekBar.setProgress(curVolume);
        adjustVolume();
    }

    private void doStop() {
        recordManager.stop();
        isPause = false;
        isStart = false;
    }

    private void doPlay() {
        if (isStart) {
            recordManager.pause();
            isPause = true;
            isStart = false;
        } else {
            if (isPause) {
                recordManager.resume();
            } else {
                recordManager.start();
            }
            isStart = true;
        }
    }

    @Override
    protected void onDestroy() {
        //销毁页面时删除录音文件
        FileUtils.DeleteFolder(Environment.getExternalStorageDirectory().getPath() + "/AudioRecord/");
        if (mediaPlayer != null && mediaPlayer.isPlaying()){
            mediaPlayer.stop();
            mediaPlayer.release();
            mediaPlayer = null;
        }
        recordState = 5;
        mActivity = null;
        super.onDestroy();
    }
    @Override
    protected void onResume() {
        mActivity = MicSetActivity.this;
        super.onResume();
    }
}
