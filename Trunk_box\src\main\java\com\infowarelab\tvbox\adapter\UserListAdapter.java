package com.infowarelab.tvbox.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.base.BaseViewAdapter;
import com.infowarelab.tvbox.base.BaseViewHolder;
import com.infowarelab.tvbox.modle.FacilityListBean;
import com.infowarelab.tvbox.view.ToggleButton;
import com.infowarelabsdk.conference.domain.UserBean;

/**
 * Created by xiaor on 2019/12/30.
 */

public class UserListAdapter extends BaseViewAdapter<FacilityListBean>{

    private OnClickListener onClickListener;

    public UserListAdapter(Context context,OnClickListener onClickListener) {
        super(context);
        this.onClickListener = onClickListener;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (null == convertView) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_user_list, null);
        }
        TextView titleText = BaseViewHolder.get(convertView,R.id.item_userList_titleText);
        ToggleButton toggleButton = BaseViewHolder.get(convertView,R.id.item_userList_toggleBtn);
        FacilityListBean bean = getDatas().get(position);
        titleText.setText(bean.getUsername());

        //Log.e("gggg","通道::"+bean.getChannelId());
        if (bean.isSelected()){
            toggleButton.setToggleOn();
        }else {
            toggleButton.setToggleOff();
        }
        toggleButton.setOnToggleChanged(new ToggleButton.OnToggleChanged() {
            @Override
            public void onToggle(boolean on) {
                onClickListener.onClickListener(position,on);
            }
        });
        return convertView;
    }
    public interface OnClickListener {
        void onClickListener(int position,boolean on);
    }
}
