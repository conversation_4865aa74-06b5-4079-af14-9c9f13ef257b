package com.infowarelab.tvbox.render;

import android.content.Context;
import android.opengl.GLSurfaceView;
import android.util.AttributeSet;

public abstract class BaseVideoEncodeView extends GLSurfaceView {
    public BaseVideoEncodeView(Context context) {
        super(context);
    }

    public BaseVideoEncodeView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public abstract void AnalyzeEncodeSizeEx();

    public abstract void reStartLocalView();

    public abstract void flushEncoder();

    public abstract void AnalyzeEncodeSize(int arg1, int arg2);

    public abstract void setParams(int w, int h);

    public abstract void setSharing(boolean b);

    public abstract void destroyCamera();

    public abstract boolean isSharing();

    public abstract boolean isPreviewStarted();

    public abstract void changeStatus(boolean b);

    public abstract void preLeave();

    public abstract void startCamera();

    public abstract boolean existCamera();
}
