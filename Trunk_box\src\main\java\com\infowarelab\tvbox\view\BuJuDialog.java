package com.infowarelab.tvbox.view;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.RadioButton;

import com.infowarelab.tvbox.R;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;

public class BuJuDialog extends AlertDialog implements View.OnClickListener{

    VideoCommonImpl videoCommon;
    private RadioButton dengfenBtn;
    private RadioButton zhuciBtn;
    private RadioButton shujuBtn;
    private RadioButton yuyinBtn;

    private OnResultListener onResultListener;
    //焦点得落点
    private int type = 0;

    public void setType(int type) {
        this.type = type;
    }

    public void setOnResultListener(OnResultListener onResultListener) {
        this.onResultListener = onResultListener;
    }

    public BuJuDialog(Context context,VideoCommonImpl videoCommon) {
        super(context, R.style.dialog);
        this.videoCommon = videoCommon;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_distribution);
        setCanceledOnTouchOutside(true);

        dengfenBtn = (RadioButton) findViewById(R.id.dialog_distribution_dengfenBtn);
        zhuciBtn = (RadioButton) findViewById(R.id.dialog_distribution_zhuciBtn);
        shujuBtn = (RadioButton) findViewById(R.id.dialog_distribution_shujuBtn);
        yuyinBtn = (RadioButton) findViewById(R.id.dialog_distribution_yuyinBtn);

        dengfenBtn.setOnClickListener(this);
        zhuciBtn.setOnClickListener(this);
        shujuBtn.setOnClickListener(this);
        yuyinBtn.setOnClickListener(this);
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    public void show() {
        super.show();
        switch (videoCommon.getSyncLayout()){
            case MODE_PLAIN:
                dengfenBtn.setFocusable(true);
                dengfenBtn.setChecked(true);
                dengfenBtn.requestFocus();

                zhuciBtn.setChecked(false);
                zhuciBtn.setFocusable(true);

                shujuBtn.setChecked(false);
                shujuBtn.setFocusable(true);

                yuyinBtn.setChecked(false);
                yuyinBtn.setFocusable(true);

                break;
            case MODE_VOICE:
                yuyinBtn.setFocusable(true);
                yuyinBtn.setChecked(true);
                yuyinBtn.requestFocus();

                dengfenBtn.setChecked(false);
                dengfenBtn.setFocusable(true);

                zhuciBtn.setChecked(false);
                zhuciBtn.setFocusable(true);

                shujuBtn.setChecked(false);
                shujuBtn.setFocusable(true);
                break;
            case MODE_SPEAKER:
                zhuciBtn.setFocusable(true);
                zhuciBtn.setChecked(true);
                zhuciBtn.requestFocus();

                dengfenBtn.setChecked(false);
                dengfenBtn.setFocusable(true);

                shujuBtn.setChecked(false);
                shujuBtn.setFocusable(true);

                yuyinBtn.setChecked(false);
                yuyinBtn.setFocusable(true);
                break;
            default:
                shujuBtn.setFocusable(true);
                shujuBtn.setChecked(true);
                shujuBtn.requestFocus();

                dengfenBtn.setChecked(false);
                dengfenBtn.setFocusable(true);

                zhuciBtn.setChecked(false);
                zhuciBtn.setFocusable(true);

                yuyinBtn.setChecked(false);
                yuyinBtn.setFocusable(true);
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.dialog_distribution_dengfenBtn:
                if (onResultListener != null){
                    onResultListener.selectType(0);
                }
                dismiss();
                break;
            case R.id.dialog_distribution_zhuciBtn:
                if (onResultListener != null){
                    onResultListener.selectType(1);
                }
                dismiss();
                break;
            case R.id.dialog_distribution_shujuBtn:
                if (onResultListener != null){
                    onResultListener.selectType(2);
                }
                dismiss();
                break;
            case R.id.dialog_distribution_yuyinBtn:
                if (onResultListener != null){
                    onResultListener.selectType(3);
                }
                dismiss();
                break;
            default:
                dismiss();
                break;
        }
    }

    public interface OnResultListener {
        void selectType(int type);
    }
}
