package com.infowarelab.tvbox.fragment;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.okhttp.abc.bean.VersionInfo;
import com.infowarelab.tvbox.okhttp.abc.constant.HttpConstant;
import com.infowarelab.tvbox.okhttp.abc.download.DownloadListener;
import com.infowarelab.tvbox.okhttp.abc.request.RequestCenter;
import com.infowarelab.tvbox.okhttp.listener.DisposeDataListener;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;

import java.io.File;

/**
 * Created by sdvye on 2019/6/11.
 */

public class FragVersion extends BaseFragment implements View.OnClickListener {
    private View versionView;
    private Button btnUpdate;

    private TextView tv_type;
    private TextView tv_verison;
    private static String TAG = "InfowareLab.update";
    private LinearLayout llCancel;

    public FragVersion(ICallParentView iCallParentView) {
        super(iCallParentView);
    }

    //无参构造器
    public FragVersion(){
        super();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        versionView = inflater.inflate(R.layout.frag_version, container, false);
        initView();
        initData();
        return versionView;
    }

    private void initView() {
        btnUpdate = (Button) versionView.findViewById(R.id.btn_update);
        tv_type = (TextView)versionView.findViewById(R.id.tv_type);
        tv_verison = (TextView)versionView.findViewById(R.id.tv_verison);
        btnUpdate.setOnClickListener(this);
        if (Build.MODEL.indexOf("YT-500") != -1 || Build.MODEL.indexOf("I-5300") != -1){
            tv_type.setText("HSYP-YT-185");
        }else if (Build.MODEL.indexOf("MV200") != -1){
            tv_type.setText("HSYP-YT-200");
        }else if (Build.MODEL.indexOf("MRUT33") != -1){
            tv_type.setText("HSYP-YT-100");
        }else if (Build.MODEL.indexOf("YT-300") != -1){
            tv_type.setText("HSYP-YT-150");
        }else if (Build.MODEL.indexOf("HDX2") != -1){
            tv_type.setText("HSYP-YT-150");
        }else
            tv_type.setText("HSYP-UNKNOWN");

//        if (TextUtils.isEmpty(getVersionName(getContext()))){
//            tv_verison.setText(getActivity().getResources().getString(R.string.vers));
//        }else {
//            tv_verison.setText("TV "+getVersionName(getContext()));
//        }

        llCancel = (LinearLayout) versionView.findViewById(R.id.ll_frag_set_cancel);

        llCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                callParentView(ACTION_RETURN, null);
            }
        });

        llCancel.requestFocus();
    }

    private void initData() {
        getVersionInfo();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_update:
                downApk((String) v.getTag());
                break;
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            getVersionInfo();
        }
    }

    private void getVersionInfo() {
        String downUrl =  Config.Site_URL = FileUtil.readSharedPreferences(getContext(),
                Constants.SHARED_PREFERENCES, Constants.SITE);
        if (TextUtils.isEmpty(downUrl)){
            downUrl = HttpConstant.BASEURL;
        }else {
            if (!downUrl.startsWith("http://")){
                downUrl = "http://"+ downUrl;
            }
        }
        String medurl = "";
        if (!"box".equals(Config.SiteName)){
            medurl = HttpConstant.MEDURL+Config.SiteName+"/";
        }else {
            medurl = HttpConstant.MEDURL;
        }
        RequestCenter.getVersionInfo(downUrl+ medurl + HttpConstant.VERSION, new DisposeDataListener() {
            @Override
            public void onSuccess(Object responseObj) {
                if (responseObj instanceof VersionInfo) {
                    VersionInfo versionInfo = (VersionInfo) responseObj;
                    try {
                        PackageInfo packageInfo = getActivity()
                                .getApplicationContext()
                                .getPackageManager()
                                .getPackageInfo(getActivity().getPackageName(), 0);
                        int v = packageInfo.versionCode;
                        if (versionInfo.getVersionCode() > v) {
//                            btnUpdate.setVisibility(View.VISIBLE);
                            btnUpdate.setVisibility(View.GONE);
                            btnUpdate.setTag(versionInfo.getFileName());
                        } else {
                            btnUpdate.setVisibility(View.GONE);
                        }
                    } catch (PackageManager.NameNotFoundException e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onFailure(Object responseObj) {
                btnUpdate.setVisibility(View.GONE);
            }
        });
    }

    private void downApk(String fileName) {
        String downUrl =  Config.Site_URL = FileUtil.readSharedPreferences(getContext(),
                Constants.SHARED_PREFERENCES, Constants.SITE);
        if (TextUtils.isEmpty(downUrl)){
            downUrl = HttpConstant.BASEURL;
        }else {
            if (!downUrl.startsWith("http://")){
                downUrl = "http://"+ downUrl;
            }
        }
        String medurl = "";
        if (!"box".equals(Config.SiteName)){
            medurl = HttpConstant.MEDURL+Config.SiteName+"/";
        }else {
            medurl = HttpConstant.MEDURL;
        }
        RequestCenter.downloadFile(downUrl + medurl, fileName, new DownloadListener() {
            @Override
            public void start(long max) {
                Message m = new Message();
                m.what = 0;
                m.arg1 = 0;
                m.arg2 = (int) max;
                setParaHandler.sendMessage(m);
            }

            @Override
            public void loading(int cur, int total) {
                Message m = new Message();
                m.what = 0;
                m.arg1 = cur;
                m.arg2 = total;
                setParaHandler.sendMessage(m);
            }

            @Override
            public void complete(String path) {
                Log.d(TAG, "downloadFile path = " + path);
                installApk(getActivity(), path);
            }

            @Override
            public void fail(int code, String message) {

                Log.d(TAG, "downloadFile failed = " + message);
            }

            @Override
            public void loadfail(String message) {

            }
        });
    }

    Handler setParaHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == 0) {
                btnUpdate.setText("正在下载...(" + msg.arg1 + "/" + msg.arg2 + ")");
            }
        }
    };

    public static void installApk(Context context, String downloadApk) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        File file = new File(downloadApk);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Uri apkUri = FileProvider.getUriForFile(context, context.getPackageName() + ".fileProvider", file);
//            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        } else {
//            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            Uri uri = Uri.fromFile(file);
            intent.setDataAndType(uri, "application/vnd.android.package-archive");
        }
        context.startActivity(intent);

    }


    private String getVersionName(Context context) {
        PackageManager manager = context.getPackageManager();
        String name = "";
        try {
            PackageInfo info = manager.getPackageInfo(context.getPackageName(), 0);
            name = info.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        return name;
    }
}
