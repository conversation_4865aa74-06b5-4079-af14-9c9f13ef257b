package com.infowarelab.tvbox.adapter;

import android.content.Context;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.modle.TerminalsListBean;
import com.infowarelab.tvbox.utils.LogUtil;
import com.infowarelab.tvbox.widget.CustomRecyclerView;
import com.infowarelabsdk.conference.domain.ConferenceBean;
import com.infowarelabsdk.conference.domain.ConferenceBean;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ConferenceAdapter extends CustomRecyclerView.CustomAdapter<Integer> {

    private int type;

    public ConferenceAdapter(Context context, List<ConferenceBean> data) {
        super(context, data);
    }

    @Override
    protected RecyclerView.ViewHolder onSetViewHolder(View view) {
        return new ConferenceListViewHolder(view);
    }

    @NonNull
    @Override
    protected int onSetItemLayout() {
        return R.layout.conference_item;
    }

    private String dateToString(Date startDate) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            return format.format(startDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    protected void onSetItemData(RecyclerView.ViewHolder viewHolder, int position) {
        ConferenceListViewHolder holder = (ConferenceListViewHolder) viewHolder;

        if (holder == null) return;
        if (holder.tvRoomTitle == null) return;

        if (mData.size() > position)
        {
            ConferenceBean conferenceBean = mData.get(position);

            if (conferenceBean != null)
            {
                holder.tvRoomTitle.setText(conferenceBean.getName());
                holder.tvRoomDate.setText(dateToString(conferenceBean.getStartDate()));
                holder.tvRoomId.setText(conferenceBean.getId());

                if(conferenceBean.getStatus().equals("1") || conferenceBean.getStatus().equals("4")){
                    holder.tvRoomStatus.setVisibility(View.VISIBLE);
                }
                else
                {
                    holder.tvRoomStatus.setVisibility(View.GONE);
                }
            }
        }
    }


    @Override
    protected void onItemFocus(View itemView, int position) {

        RelativeLayout rlConfItemRoot = (RelativeLayout) itemView.findViewById(R.id.conf_item_root);

        if (rlConfItemRoot != null){
            rlConfItemRoot.setBackgroundResource(R.drawable.bg_conf_item_fos);
        }

        //TextView tvRoomTitle = (TextView) itemView.findViewById(R.id.item_room_title);
        //TextView tvRoomDate = (TextView) itemView.findViewById(R.id.item_room_name);
        //LinearLayout llRoonName = (LinearLayout) itemView.findViewById(R.id.ll_room_name);
//
//        tvRoomTitle.setVisibility(View.VISIBLE);
//        llRoonName.setVisibility(View.VISIBLE);

//        if (Build.VERSION.SDK_INT >= 21) {
//            //抬高Z轴
//            ViewCompat.animate(itemView).scaleX(1.10f).scaleY(1.10f).translationZ(1).start();
//        } else {
//            ViewCompat.animate(itemView).scaleX(1.10f).scaleY(1.10f).start();
//            ViewGroup parent = (ViewGroup) itemView.getParent();
//            parent.requestLayout();
//            parent.invalidate();
//        }
    }

    @Override
    protected void onItemClicked(View itemView, int position) {

        RelativeLayout rlConfItemRoot = (RelativeLayout) itemView.findViewById(R.id.conf_item_root);

        if (rlConfItemRoot != null) {
            rlConfItemRoot.setBackgroundResource(R.drawable.bg_conf_item_clk);
        }
    }

    @Override
    protected void onItemGetNormal(View itemView, int position) {

        RelativeLayout rlConfItemRoot = (RelativeLayout) itemView.findViewById(R.id.conf_item_root);

        if (rlConfItemRoot != null){
            rlConfItemRoot.setBackgroundResource(R.drawable.bg_conf_item_nor);
        }

//        TextView tvRoomTitle = (TextView) itemView.findViewById(R.id.item_room_title);
//        TextView tvRoomDate = (TextView) itemView.findViewById(R.id.item_room_name);
//        LinearLayout llRoomName = (LinearLayout) itemView.findViewById(R.id.ll_room_name);
//
//        tvRoomTitle.setVisibility(View.VISIBLE);
//
//        if (type == 2)
//            llRoomName.setVisibility(View.VISIBLE);
//        else
//            llRoomName.setVisibility(View.GONE);

//        if (Build.VERSION.SDK_INT >= 21) {
//            ViewCompat.animate(itemView).scaleX(1.0f).scaleY(1.0f).translationZ(0).start();
//        } else {
//            LogUtil.i(this, "ControlAdapter.normalStatus.scale build version < 21");
//            ViewCompat.animate(itemView).scaleX(1.0f).scaleY(1.0f).start();
//            ViewGroup parent = (ViewGroup) itemView.getParent();
//            parent.requestLayout();
//            parent.invalidate();
//        }
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    public void update(List<ConferenceBean> data) {

        this.mData = data;

        notifyDataSetChanged();
    }

    public void setType(int type) {
        this.type = type;
    }

    public ConferenceBean getItem(int position) {

        if (mData == null || mData.size() <= position) return null;

        return mData.get(position);
    }

    private class ConferenceListViewHolder extends RecyclerView.ViewHolder {

        TextView tvRoomTitle;
        TextView tvRoomDate;
        TextView tvRoomId;
        TextView tvRoomStatus;

        ConferenceListViewHolder(View itemView) {
            super(itemView);
            tvRoomTitle = (TextView) itemView.findViewById(R.id.item_room_title);
            tvRoomDate = (TextView) itemView.findViewById(R.id.item_room_date);
            tvRoomId = (TextView) itemView.findViewById(R.id.item_room_id);
            tvRoomStatus = (TextView) itemView.findViewById(R.id.item_room_status);
        }
    }

    public void setSelect(int pos) {
        notifyItemChanged(pos);
    }

}
