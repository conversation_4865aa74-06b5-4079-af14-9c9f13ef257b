package com.infowarelab.tvbox.base;

import java.io.Serializable;

/**
 * Created by Always on 2017/10/24.
 */

public class ResPing implements Serializable {
        private String action;
        private String requestId;
        private String status;
        private String request;
        private String reason;

    public String getAction() {
        return action;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return "ResPing{" +
                "action='" + action + '\'' +
                ", requestId='" + requestId + '\'' +
                ", status='" + status + '\'' +
                ", request='" + request + '\'' +
                ", reason='" + reason + '\'' +
                '}';
    }
}
