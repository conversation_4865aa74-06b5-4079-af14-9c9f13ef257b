package com.infowarelab.tvbox.adapter;


import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.utils.PublicWay;
import com.infowarelab.tvbox.view.SquareLinearLayout;
import com.infowarelabsdk.conference.domain.ConferenceBean;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.StringUtil;

import java.util.List;

public class GridAdapter extends BaseAdapter {

	private int selectId = -1;
	private LayoutInflater mInflater;
	private OnSelectListener onSelectListener;
	private List<ConferenceBean> data;
	private Context context;
	private int lineHeight;
	private int mGridViewRows;
	private ViewHolder viewHolder = null;

	public GridAdapter(Context context, List<ConferenceBean> data, int lineHeight) {
		this.context = context;
		this.data = data;
		this.lineHeight = lineHeight;
		mInflater = LayoutInflater.from(context);
		setRows();
	}

	@Override
	public int getViewTypeCount() {
		return 1;
	}

	public void update(List<ConferenceBean> data){
        this.data = data;
        setRows();
        notifyDataSetChanged();
    }

	private void setRows() {
		int remainder = data.size() % 4;
		mGridViewRows =(remainder == 0)? (this.data.size() > 0 ?  this.data.size() / 4:0) : (data.size() / 4) +1;
	}
	public int getNumRows() {

		Log.d("InfowareLab.Debug","GridAdapter.getNumRows: " + mGridViewRows);
		return mGridViewRows;
	}

	public int getNumColumns(){
		return 4;
	}

	@Override
	public int getCount() {

		//Log.d("InfowareLab.Debug","GridAdapter.getCount: " + data.size());
		return data.size();
	}

	@Override
	public ConferenceBean getItem(int position) {

		//Log.d("InfowareLab.Debug","GridAdapter.getItem: " + position);
		return data.get(position);
	}

	@Override
	public long getItemId(int position) {
		return position;
	}
	
	public void updateBySelect(int id){
		this.selectId = id;
		this.notifyDataSetChanged();
	}
	@Override
	public View getView(int position, View convertView, ViewGroup parent) {
		if (convertView == null) {
			convertView = mInflater.inflate(
					R.layout.view_item_grid, null);
			viewHolder = new ViewHolder();
			viewHolder.ll = (SquareLinearLayout) convertView.findViewById(R.id.item_ll);
			viewHolder.tvTitle = (TextView) convertView.findViewById(R.id.item_meetings_tv_title);
			viewHolder.tvState = (TextView) convertView.findViewById(R.id.item_meetings_tv_state);
			viewHolder.tvType = (TextView) convertView.findViewById(R.id.item_meetings_tv_type);
			viewHolder.tvHost = (TextView) convertView.findViewById(R.id.item_meetings_tv_host);
			viewHolder.tvDo = (TextView) convertView.findViewById(R.id.item_meetings_tv_do);
			viewHolder.idText = (TextView)convertView.findViewById(R.id.item_meetings_tv_idText);
			viewHolder.createMeeting = (SquareLinearLayout)convertView.findViewById(R.id.item_createMeeting_ll);
			convertView.setTag(viewHolder);
		} else {
			viewHolder = (ViewHolder)convertView.getTag();
		}
		final ConferenceBean conferenceBean = data.get(position);
		if (position == 0 && FileUtil.readSharedPreferences(context, Constants.SHARED_PREFERENCES,
				Constants.LOGIN_ROLE).contains("meeting.role.init.host;") /*&& PublicWay.isConnect*/){
			viewHolder.createMeeting.setVisibility(View.VISIBLE);
			viewHolder.ll.setVisibility(View.GONE);
		}else {
			viewHolder.createMeeting.setVisibility(View.GONE);
			viewHolder.ll.setVisibility(View.VISIBLE);
			viewHolder.tvTitle.setText(conferenceBean.getName());
			viewHolder.tvState.setText(conferenceBean.getStatus().equals("1") || conferenceBean.getStatus().equals("4")?context.getResources().getString(R.string.item_meetings_state_opened):context.getResources().getString(R.string.item_meetings_state_closed));
			viewHolder.tvType.setText(StringUtil.getDate(conferenceBean.getStartTime()));
			viewHolder.tvHost.setText(conferenceBean.getHostName());
			viewHolder.idText.setText(conferenceBean.getId());
			if(conferenceBean.getStatus().equals("1") || conferenceBean.getStatus().equals("4")){
				viewHolder.tvDo.setText(context.getResources().getString(R.string.item_meetings_join));
				viewHolder.tvDo.setBackgroundResource(R.drawable.bg_rc_green_b_nor);
			}else if (conferenceBean.isMine()){
				viewHolder.tvDo.setText(context.getResources().getString(R.string.item_meetings_open));
				viewHolder.tvDo.setBackgroundResource(R.drawable.bg_rc_blue_b_nor);
			}else {
				viewHolder.tvDo.setText(context.getResources().getString(R.string.item_meetings_closed));
				viewHolder.tvDo.setBackgroundResource(R.drawable.bg_rc_gray_b_nor);
			}
		}
		viewHolder.ll.setOnClickListener(new OnClickListener() {
			@Override
			public void onClick(View v) {
				// TODO Auto-generated method stub
				if (TextUtils.isEmpty(conferenceBean.getId()) || 0 == Integer.parseInt(conferenceBean.getId())) {
					onSelectListener.doCreateMeeting();
				}else {
					// TODO Auto-generated method stub
					doSelect(conferenceBean);
				}
			}
		});
		viewHolder.createMeeting.setOnClickListener(new OnClickListener() {
			@Override
			public void onClick(View v) {
				if (TextUtils.isEmpty(conferenceBean.getId()) || 0 == Integer.parseInt(conferenceBean.getId())) {
					onSelectListener.doCreateMeeting();
				}else {
					// TODO Auto-generated method stub
					doSelect(conferenceBean);
				}
			}
		});
		return convertView;
	}


	private static class ViewHolder {
		public SquareLinearLayout createMeeting;
		public SquareLinearLayout ll;
		public TextView tvTitle;
		public TextView tvState;
		public TextView tvType;
		public TextView tvHost;
		public TextView tvDo;
		public TextView idText;
	}
	public interface OnSelectListener {
		void doSelect(ConferenceBean conferenceBean);
		void doCreateMeeting();
	}
	public void setOnSelectListener(OnSelectListener onSelectListener) {
		this.onSelectListener = onSelectListener;
	}
	public void doSelect(ConferenceBean conferenceBean) {
		if (onSelectListener != null) {
			onSelectListener.doSelect(conferenceBean);
		}
	}
	public void doCreat() {
		if (onSelectListener != null) {
			onSelectListener.doCreateMeeting();
		}
	}
}
