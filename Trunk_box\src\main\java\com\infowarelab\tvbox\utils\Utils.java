package com.infowarelab.tvbox.utils;

import android.app.Activity;
import android.content.res.Resources;
import android.graphics.Point;
import android.os.Build;
import android.view.Display;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.ViewConfiguration;

/**
 * 
 * @<NAME_EMAIL>
 *
 */
public class Utils {

	/**
	 * 获取SDK版本
	 */
	public static int getSDKVersion() {
		int version = 0;
		try {
			version = Integer.valueOf(android.os.Build.VERSION.SDK);
		} catch (NumberFormatException e) {
		}
		return version;
	}
	public static String getDeviceInfo(){
		StringBuffer sb =new StringBuffer();
		sb.append("主板："+ Build.BOARD);
		sb.append("\n系统启动程序版本号："+ Build.BOOTLOADER);
		sb.append("\n系统定制商："+Build.BRAND);
		sb.append("\ncpu指令集："+Build.CPU_ABI);
		sb.append("\ncpu指令集2："+Build.CPU_ABI2);
		sb.append("\n设置参数："+Build.DEVICE);
		sb.append("\n显示屏参数："+Build.DISPLAY);
		sb.append("\n无线电固件版本："+Build.getRadioVersion());
		sb.append("\n硬件识别码："+Build.FINGERPRINT);
		sb.append("\n硬件名称："+Build.HARDWARE);
		sb.append("\nHOST:"+Build.HOST);
		sb.append("\n修订版本列表："+Build.ID);
		sb.append("\n硬件制造商："+Build.MANUFACTURER);
		sb.append("\n版本："+Build.MODEL);
		sb.append("\n硬件序列号："+Build.SERIAL);
		sb.append("\n手机制造商："+Build.PRODUCT);
		sb.append("\n描述Build的标签："+Build.TAGS);
		sb.append("\nTIME:"+Build.TIME);
		sb.append("\nbuilder类型："+Build.TYPE);
		sb.append("\nUSER:"+Build.USER);
		return sb.toString();
	}

	public static int getNavigationBarHeight(Activity activity) {

		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
			Display display = activity.getWindowManager().getDefaultDisplay();
			Point size = new Point();
			Point realSize = new Point();
			display.getSize(size);
			display.getRealSize(realSize);
			Resources resources = activity.getResources();
			int resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android");
			int height = resources.getDimensionPixelSize(resourceId);
			//超出系统默认的导航栏高度以上，则认为存在虚拟导航
			if ((realSize.y - size.y) > (height - 10)) {
				return height;
			}

			return 0;
		} else {
			boolean menu = ViewConfiguration.get(activity).hasPermanentMenuKey();
			boolean back = KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_BACK);
			if (menu || back) {
				return 0;
			} else {
				Resources resources = activity.getResources();
				int resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android");
				int height = resources.getDimensionPixelSize(resourceId);
				return height;
			}
		}

	}
}
