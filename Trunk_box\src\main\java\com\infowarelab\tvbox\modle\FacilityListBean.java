package com.infowarelab.tvbox.modle;

import java.io.Serializable;

/**
 * Created by xiaor on 2020/1/9.
 */

public class FacilityListBean implements Serializable{
    private int uid = 0;
    //是否选中
    private boolean isSelected = false;
    //是否聚焦
    private boolean isFouse = false;
    private boolean videoOpen = false;
    private String username = "";

    private int channelId;

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public boolean isFouse() {
        return isFouse;
    }

    public void setFouse(boolean fouse) {
        isFouse = fouse;
    }

    public boolean isVideoOpen() {
        return videoOpen;
    }

    public void setVideoOpen(boolean videoOpen) {
        this.videoOpen = videoOpen;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    public int getChannelId() {
        return channelId;
    }

    public void setChannelId(int channelId) {
        this.channelId = channelId;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }
}
