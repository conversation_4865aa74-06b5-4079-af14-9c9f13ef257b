package com.infowarelab.tvbox.okhttp.abc.request;


import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;


import com.infowarelab.tvbox.okhttp.CommonOkHttpClient;
import com.infowarelab.tvbox.okhttp.abc.bean.VersionInfo;
import com.infowarelab.tvbox.okhttp.abc.constant.HttpConstant;
import com.infowarelab.tvbox.okhttp.abc.download.DownloadListener;
import com.infowarelab.tvbox.okhttp.abc.download.DownloadResponseBody;
import com.infowarelab.tvbox.okhttp.listener.DisposeDataHandle;
import com.infowarelab.tvbox.okhttp.listener.DisposeDataListener;
import com.infowarelab.tvbox.okhttp.request.CommonRequest;
import com.infowarelab.tvbox.okhttp.request.RequestParams;
import com.infowarelab.tvbox.utils.FileUtils;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class RequestCenter {
    private static final String key ="info-cooperation-abc123!@#";
    //根据参数发送所有的get请求
    private static void getRequest(String url, RequestParams params,
                                   DisposeDataListener listener,
                                   Class<?> clazz) {
        CommonOkHttpClient.get(CommonRequest.createGetRequest(url, params),
                new DisposeDataHandle(listener, clazz));
    }

    private static void postRequest(String url, RequestParams params,
                                    DisposeDataListener listener,
                                    Class<?> clazz) {
        CommonOkHttpClient.post(CommonRequest.createPostRequest(url, params),
                new DisposeDataHandle(listener, clazz));
    }

    public static void getVersionInfo(String url,DisposeDataListener listener){
        RequestCenter.getRequest(url,null,listener,VersionInfo.class);
    }
    public static Call downloadFile(String url,String filename,DownloadListener listener) {
        Log.d("InfowareLab","downloadFile下载地址:"+url + filename);
        FileUtils.DeleteFolder(url + filename);
        return  RequestCenter.download(url+filename, null , listener);
    }
    public static void cancelDownloadFile(Call call) {
        if (call != null) call.cancel();
    }

    public static Call download(String url, final int modelId, final String siteId, FormBody body, final DownloadListener downloadListener, final long startsPoint) {
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .header("RANGE", "bytes=" + startsPoint + "-")//断点续传
                .build();

        Callback callback = new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {

            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                long length = response.body().contentLength();
                if (length == 0) {
                    // 说明文件已经下载完，直接跳转安装就好
//                    downloadListener.complete(String.valueOf(getFile().getAbsoluteFile()));
                    return;
                }
                String n = siteId + "_" + modelId + "." + getEndName(response);
                File file = getFile(n);

                if (file.exists()&&file.length()>=length) {
                    Log.i("downloadfile", "downloadfile filelength = " + file.length() + "\n" + "responselength = " +length);
                    downloadListener.complete(n);
                    return;
                } else {
                    file.createNewFile();
                }

                downloadListener.start(length + startsPoint);
                // 保存文件到本地
                InputStream is = null;
                RandomAccessFile randomAccessFile = null;
                BufferedInputStream bis = null;

                byte[] buff = new byte[2048];
                int len = 0;
                try {
                    is = response.body().byteStream();
                    bis = new BufferedInputStream(is);


                    // 随机访问文件，可以指定断点续传的起始位置
                    randomAccessFile = new RandomAccessFile(file, "rwd");
                    randomAccessFile.seek(startsPoint);
                    while ((len = bis.read(buff)) != -1) {
                        randomAccessFile.write(buff, 0, len);
                    }

                    // 下载完成
                    downloadListener.complete(n);
                } catch (Exception e) {
                    e.printStackTrace();
                    downloadListener.loadfail(e.getMessage());
                } finally {
                    try {
                        if (is != null) {
                            is.close();
                        }
                        if (bis != null) {
                            bis.close();
                        }
                        if (randomAccessFile != null) {
                            randomAccessFile.close();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        };


        // 重写ResponseBody监听请求
        Interceptor interceptor = new Interceptor() {
            @Override
            public Response intercept(Chain chain) throws IOException {
                Response originalResponse = chain.proceed(chain.request());
                return originalResponse.newBuilder()
                        .body(new DownloadResponseBody(originalResponse, startsPoint, downloadListener))
                        .build();
            }
        };

        OkHttpClient.Builder dlOkhttp = new OkHttpClient.Builder()
                .addNetworkInterceptor(interceptor);

        // 发起请求
        Call call = dlOkhttp.build().newCall(request);
        call.enqueue(callback);
        return call;
    }

    public static Call download(String url,  FormBody body, final DownloadListener downloadListener) {
        Request request = new Request.Builder()
                .url(url)
//                .post(body)
                .header("RANGE", "bytes=" + 0 + "-")//断点续传
                .build();

        Callback callback = new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {

            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                long length = response.body().contentLength();
                if (length == 0) {
                    // 说明文件已经下载完，直接跳转安装就好
//                    downloadListener.complete(String.valueOf(getFile().getAbsoluteFile()));
                    return;
                }

                String n = "box.apk";
                File file = getFile(n);

                if (file.exists()&&file.length()>=length) {
                    file.delete();
                }
                file.createNewFile();

                downloadListener.start(length);

                // 保存文件到本地
                InputStream is = null;
                RandomAccessFile randomAccessFile = null;
                BufferedInputStream bis = null;

                byte[] buff = new byte[2048];
                int len = 0;
                try {
                    is = response.body().byteStream();
                    bis = new BufferedInputStream(is);


                    // 随机访问文件，可以指定断点续传的起始位置
                    randomAccessFile = new RandomAccessFile(file, "rwd");
                    randomAccessFile.seek(0);
                    while ((len = bis.read(buff)) != -1) {
                        randomAccessFile.write(buff, 0, len);
                    }

                    // 下载完成
                    downloadListener.complete(file.getPath());
                } catch (Exception e) {
                    e.printStackTrace();
                    downloadListener.loadfail(e.getMessage());
                } finally {
                    try {
                        if (is != null) {
                            is.close();
                        }
                        if (bis != null) {
                            bis.close();
                        }
                        if (randomAccessFile != null) {
                            randomAccessFile.close();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        };


        // 重写ResponseBody监听请求
        Interceptor interceptor = new Interceptor() {
            @Override
            public Response intercept(Chain chain) throws IOException {
                Response originalResponse = chain.proceed(chain.request());
                return originalResponse.newBuilder()
                        .body(new DownloadResponseBody(originalResponse, 0, downloadListener))
                        .build();
            }
        };

        OkHttpClient.Builder dlOkhttp = new OkHttpClient.Builder()
                .addNetworkInterceptor(interceptor);

        // 发起请求
        Call call = dlOkhttp.build().newCall(request);
        call.enqueue(callback);
        return call;
    }

    private static File getFile(String name) {
        String root = Environment.getExternalStorageDirectory() + File.separator + "infowarelab" + File.separator + "apk" + File.separator;
        File file = new File(root, name);
        File fileParent = file.getParentFile();
        if (!fileParent.exists()) {
            fileParent.mkdirs();
        }
        return file;
    }
    private static File getTemporaryFile(String name) {
        String root = Environment.getExternalStorageDirectory() + File.separator + "infowarelab" + File.separator + "temporary" + File.separator;
        File file = new File(root, name);
        File fileParent = file.getParentFile();
        if (!fileParent.exists()) {
            fileParent.mkdirs();
        }
        return file;
    }

    private static long getFileStart() {
        String root = Environment.getExternalStorageDirectory().getPath();
        File file = new File(root, "updateDemo.apk");
        return file.length();
    }

    private static String getHeaderFileName(Response response) {
        String s = response.header("Content-Disposition");
        if (!TextUtils.isEmpty(s)) {
            s.replace("filename=", "");
//            dispositionHeader.replace("attachment;filename=", "");
            s.replace("filename*=utf-8", "");
            String[] strings = s.split("; ");
            if (strings.length > 1) {
                s = strings[1].replace("filename=", "");
                s = s.replace("\\\"", "");
                return s;
            }
            return "";
        }
        return "";
    }private static String getFileName(Response response) {
        String s = response.header("Content-Disposition");
        if (!TextUtils.isEmpty(s)) {
            String[] strings = s.split("=");
            if (strings.length > 1) {
                s = strings[strings.length - 1];
                return s;
            }
            return "";
        }
        return "";
    }

    private static String getEndName(Response response) {
        String endName = response.header("Content-Disposition");
        Log.i("sss", "sss" + endName);
        if (!TextUtils.isEmpty(endName)) {
            String[] strings = endName.split("\\.");
            if (strings.length > 1) {
                endName = strings[strings.length - 1];
                return endName;
            }
            return "";
        }
        return "";
    }
}
