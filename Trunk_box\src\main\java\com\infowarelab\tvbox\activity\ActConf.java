package com.infowarelab.tvbox.activity;

import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.hardware.display.DisplayManager;
import android.hardware.usb.UsbConfiguration;
import android.hardware.usb.UsbConstants;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbInterface;
import android.hardware.usb.UsbManager;
import android.media.AudioManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.Display;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.SurfaceView;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RadioButton;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.infowarelab.tvbox.ConferenceApplication;
import com.infowarelab.tvbox.R;
import com.infowarelab.tvbox.adapter.ChatContentAdapter;
import com.infowarelab.tvbox.fragment.BaseFragment;
import com.infowarelab.tvbox.fragment.DocListFragment;
import com.infowarelab.tvbox.fragment.FragAs;
import com.infowarelab.tvbox.fragment.FragAs2;
import com.infowarelab.tvbox.fragment.FragDs;
import com.infowarelab.tvbox.fragment.FragDs2;
import com.infowarelab.tvbox.fragment.FragMenu;
import com.infowarelab.tvbox.fragment.FragMic;
import com.infowarelab.tvbox.fragment.FragSound;
import com.infowarelab.tvbox.fragment.FragVs;
import com.infowarelab.tvbox.fragment.FragVs1;
import com.infowarelab.tvbox.modle.FacilityListBean;
import com.infowarelab.tvbox.utils.AudioRecordUtils;
import com.infowarelab.tvbox.utils.DensityUtil;
import com.infowarelab.tvbox.utils.DeviceIdFactory;
import com.infowarelab.tvbox.utils.FileUtils;
import com.infowarelab.tvbox.utils.PublicWay;
import com.infowarelab.tvbox.utils.SharedPreferencesUrls;
import com.infowarelab.tvbox.utils.XMLUtils;
import com.infowarelab.tvbox.view.BuJuDialog;
import com.infowarelab.tvbox.view.DocListDialog;
import com.infowarelab.tvbox.view.ExitDialog;
import com.infowarelab.tvbox.view.InviteDialog;
import com.infowarelab.tvbox.view.LoadingDialog;
import com.infowarelab.tvbox.view.PersonListDialog;
import com.infowarelab.tvbox.view.PromptDialog;
import com.infowarelab.tvbox.view.SubtitleView;
import com.infowarelab.tvbox.view.VideoListDialog;
import com.infowarelabsdk.conference.audio.AudioCommon;
import com.infowarelabsdk.conference.callback.CallbackManager;
import com.infowarelabsdk.conference.common.CommonFactory;
import com.infowarelabsdk.conference.common.impl.AudioCommonImpl;
import com.infowarelabsdk.conference.common.impl.ChatCommomImpl;
import com.infowarelabsdk.conference.common.impl.ConfManageCommonImpl;
import com.infowarelabsdk.conference.common.impl.ConferenceCommonImpl;
import com.infowarelabsdk.conference.common.impl.DocCommonImpl;
import com.infowarelabsdk.conference.common.impl.ShareDtCommonImpl;
import com.infowarelabsdk.conference.common.impl.UserCommonImpl;
import com.infowarelabsdk.conference.common.impl.VideoCommonImpl;
import com.infowarelabsdk.conference.confctrl.ConferenceCommon;
import com.infowarelabsdk.conference.confctrl.UserCommon;
import com.infowarelabsdk.conference.domain.MessageBean;
import com.infowarelabsdk.conference.domain.UserBean;
import com.infowarelabsdk.conference.shareDt.ShareDtCommon;
import com.infowarelabsdk.conference.transfer.Config;
import com.infowarelabsdk.conference.util.Constants;
import com.infowarelabsdk.conference.util.FileUtil;
import com.infowarelabsdk.conference.util.MessageEvent;
import com.infowarelabsdk.conference.util.StringUtil;
import com.infowarelabsdk.conference.util.ToastUtil;
import com.infowarelabsdk.conference.util.Utils;
import com.infowarelabsdk.conference.video.VideoCommon;
import com.mingri.uvc.Uvc;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import es.dmoral.toasty.Toasty;

@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
public class ActConf extends BaseFragmentActivity implements BaseFragment.ICallParentView, View.OnClickListener,
        FragMenu.FragmentInteraction, FragSound.FragmentInteraction, DocListFragment.FragmentInteraction, FragVs.FragmentInteraction,
        FragVs1.FragmentInteraction, View.OnFocusChangeListener {

    private static final String TAG = "InfowareLab.Debug";
    private LoadingDialog loadingDialog = null;

    public static int lWidth = 0;
    public static int lHeight = 0;

    public static int topHeight = 0;
    public static int stateHeight = 0;

    public static ActConf mActivity;
    //耳机的广播
    public static final String TAGLISTEN = "android.intent.action.HEADSET_PLUG";
    //usb线的广播
    private final static String TAGUSB = "android.hardware.usb.action.USB_STATE";
    //外设的广播
    public static final String TAGIN = "android.hardware.usb.action.USB_DEVICE_ATTACHED";
    public static final String TAGOUT = "android.hardware.usb.action.USB_DEVICE_DETACHED";
    private ExitDialog exitDialog;
    private RelativeLayout rlRoot;
    private FrameLayout flLeft, flRight;
    //音频状态
//    private ImageView voiceStateImage;

    private View cutline;
    private ConferenceCommonImpl conferenceCommon;
    private UserCommonImpl userCommon;
    private AudioCommonImpl audioCommon;
    private DocCommonImpl docCommon;
    private ShareDtCommonImpl sdCommon;
    private FragVs fragVs;
    private FragVs1 fragVs1;
    private FragDs fragDs;

    private FragAs fragAs;
    private FragAs2 fragAs2 = null; //for second screen display
    private FragDs2 fragDs2 = null;

    //    private FragAttender fragAtt;
//    private FragMenu fragMenu;
//    private DocListFragment fragDos;
    private FragmentManager fragmentManager;
    private AudioManager audioManager;
    private BroadcastReceiver audioReceiver;
    private int audioState = -1;
    private boolean BOOLEAN = false;
    private int type = 0;
    private int bujuType = 0;
    private int layoutType = 0;
    //private int lastLayoutType = 0;

    //记录上一个布局样式
//    private int lastLayout = 0;
//    private RightSlidingFrameLayout flAttender;
    //特定控制设置的碎片
    private int curPosition = -1;
    private FragMic fragMic;
    private FragSound fragSound;
    //    //会议Id
//    private String meeting_id = "";
//    private String meetingName = "";
//    private int conferencePattern = 1;
    //是否正在桌面共享
    public static boolean isShare = false;

    private VideoCommonImpl videoCommon;
    private int lastRole;
    private boolean isHost = false;
    //是否要翻页
    private boolean isRunPage = false;
    //弹窗
    private VideoListDialog dialog = null;

    public boolean isAcLeave = false;
    //    //采集工具
    private AudioRecordUtils audioRecordUtils;
    //操作按钮
    private View inflate;
    private Dialog menuDialog;
    private LinearLayout micLayout, docLayout, flowLayout, videoLayout, soundLayout,
            setVideoLayout, inviteLayout, distributionLayout;
    private RadioButton rbMic;
    private RadioButton rbSound;
    public RadioButton rbMicSet;
    private RadioButton rbSoundSet;
    private RadioButton docBtn, flowBtn, videoBtn;
    private TextView flowText;
    private RadioButton inviteBtn;
    private RadioButton canjiaBtn;
    private RadioButton chatBtn;
    private RadioButton logoutBtn;

    //聊天
//    private TextView tvChatName;
    private ListView lvChat;
    private EditText etChat;
    private ImageView btnChatBack;
    private RelativeLayout rlChatEdit;
    private ImageView btnSend;
    private UserBean chatingUser = new UserBean();
    private ChatContentAdapter chatAdatper = null;
    //    private LinearLayout llAttenders;
    //邀请
    private InviteDialog inviteDialog = null;
    //人员列表
    private PersonListDialog personListDialog;
    //菜单
    private ImageView menuBtn;

    private DocListDialog docListDialog;

    private BuJuDialog buJuDialog;

    //    public int model = 1;
    private int curModel = 0;
    public static final int MODEL_VS = 1;
    public static final int MODEL_DS = 2;
    public static final int MODEL_AS = 3;
    public static final int MODEL_DS_VS = 4;
    public static final int MODEL_AS_VS = 5;

    //是否弹消息
    private boolean isShow = true;

    private boolean conferenceExit = false;
//    //是否弹窗
//    private boolean isPopup = true;

    private Runnable curR;

    private Runnable curR1;

    private int limit = 9;
    //是否通知布局
    private boolean isInform = true;
    private String mConfId = null;
    private int mCurrentLayoutType = -10086;

    private SubtitleView mSubtitleView;

    private Timer netCheckTimer = null;
    private TimerTask netCheckTask = null;
    private ImageView mNetStatusView;
    private TextView tvConfId;
    //private boolean isSyncVideoSelf = false;
    //private boolean isSyncAudioSelf = false;

    public static boolean isInMeeting = false;

    private PromptDialog requestVideoDialog = null;
    private PromptDialog requestAudioDialog = null;

    private ConfManageCommonImpl confManageCommon;
    private int requestType = -1;
    private int requestData2 = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);

        Log.d("InfowareLab.Debug","ActConf.onCreate: savedInstanceState=" + savedInstanceState);

        conferenceExit = false;
        isInMeeting = true;

        CallbackManager.IS_FOECE_LEAVED = false;

        //Log.d("InfowareLab.Debug","ActConf.mConfId=" + mConfId);
        //SharedPreferencesUrls.getInstance().putString("confId", mConfId);

        //注册EventBus
        EventBus.getDefault().register(this);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        hideBottomUIMenu();
        setContentView(R.layout.act_inconf);

        if (null == loadingDialog)
            loadingDialog = new LoadingDialog(this);

        loadingDialog.setCancelable(false);
        loadingDialog.setCanceledOnTouchOutside(false);

        if (loadingDialog != null && !loadingDialog.isShowing()) {
            loadingDialog.setTitle("正在进入会议");
            loadingDialog.show();
        }

        mActivity = this;

//        meeting_id = getIntent().getExtras().getString("meeting_id");
//        Intent intent = getIntent();
//
//        mActionMode = getIntent().getIntExtra("actionMode", -1);
//        if (mActionMode > -1) {
//            mConfId = intent.getStringExtra("meeting_id");
//            mGroupId = intent.getStringExtra("groupId");
//
//            mConfName = intent.getStringExtra("confName");
//            mJoinName = intent.getStringExtra("joinName");
//
//            mRecordDuration = intent.getIntExtra("recordVideoTime", -1);
//            mRecordInterval = intent.getIntExtra("recordInterval", -1);
//            mUploadSite = intent.getStringExtra("uploadUrl");
//            mAttachInfo = intent.getStringExtra("attachInfo");

//            if (mRecordInterval > 0) {
//                mRecordInterval = mRecordInterval * 60;
//            }

//            Log.d("InfowareLab.Debug", "=> mRecordDuration = " + mRecordDuration);
//            Log.d("InfowareLab.Debug", "=> mRecordInterval = " + mRecordInterval);
//            Log.d("InfowareLab.Debug", "=> mUploadSite = " + mUploadSite);
//            Log.d("InfowareLab.Debug", "=> mAttachInfo = " + mAttachInfo);
//            Log.d("InfowareLab.Debug", "=> mConfName = " + mConfName);
//            Log.d("InfowareLab.Debug", "=> mJoinName = " + mJoinName);
//            Log.d("InfowareLab.Debug", "=> mConfId = " + mConfId);
//            Log.d("InfowareLab.Debug", "=> mGroupId = " + mGroupId);

//            RescheduleRecord();
//        }

        //if (null == savedInstanceState) {
        initData();
        initView();
        //}

        Intent intent = getIntent();
        mConfId = intent.getStringExtra("confId");

        Log.d("InfowareLab.Debug","ActConf.confId = " + mConfId);

        if (mConfId != null && mConfId.length() == 8){
            //String regex = "(.{4})";
            //mConfId = mConfId.replaceAll(regex,"$1 ");
            String subString = mConfId;
            String subString1 = subString.substring(0,4);
            subString = mConfId;
            String subString2 = subString.substring(4,8);

            tvConfId.setText(getString(R.string.item_meetings_id) + " " + subString1 + " " + subString2);
            tvConfId.setVisibility(View.VISIBLE);
        }

        exited = false;

    }

    private boolean detectUsbAudioDevice() {
        HashMap<String, UsbDevice> deviceHashMap = ((UsbManager) getSystemService(USB_SERVICE)).getDeviceList();
        for (Map.Entry entry : deviceHashMap.entrySet()) {
            UsbDevice device = (UsbDevice) entry.getValue();
            if (null != device) {
                for (int i = 0; i < device.getConfigurationCount(); i++) {
                    UsbConfiguration configuration = device.getConfiguration(i);
                    if (null != configuration) {
                        for (int j = 0; j < configuration.getInterfaceCount(); j++) {
                            UsbInterface usbInterface = configuration.getInterface(j);
                            if (null != usbInterface) {
                                if (UsbConstants.USB_CLASS_AUDIO == usbInterface.getInterfaceClass()) {
                                    Log.d(TAG, "has usb audio device");
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        Log.d(TAG, "have no usb audio device");
        return false;
    }

    public static boolean getMicrophoneAvailable(Context context) {
//        MediaRecorder recorder = new MediaRecorder();
//        recorder.setAudioSource(MediaRecorder.AudioSource.MIC);
//        recorder.setOutputFormat(MediaRecorder.OutputFormat.DEFAULT);
//        recorder.setAudioEncoder(MediaRecorder.AudioEncoder.DEFAULT);
//        recorder.setOutputFile(new File(context.getCacheDir(), "MediaUtil#micAvailTestFile").getAbsolutePath());
//        boolean available = true;
//        try {
//            recorder.prepare();
//        }
//        catch (IOException exception) {
//            available = false;
//        }
//        recorder.release();
//        return available;
        return false;
    }

    private void startCheckNetStatus() {

        if (netCheckTask != null){
            netCheckTask.cancel();
            netCheckTask = null;
        }

        if (netCheckTimer != null) {
            netCheckTimer.purge();
            netCheckTimer.cancel();
            netCheckTimer = null;
        }

        if (netCheckTask == null) {
            netCheckTask = new TimerTask() {
                @Override
                public void run() {
                    if (conferenceCommon != null && !isAcLeave) conferenceCommon.getNetSpeed();
                }
            };
        }

        if (netCheckTimer == null)
            netCheckTimer = new Timer();

        netCheckTimer.schedule(netCheckTask, 3 * 1000, 3 * 1000);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {

        Log.d("InfowareLab.Debug","ActConf.onConfigurationChanged: " + newConfig.toString());

        super.onConfigurationChanged(newConfig);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {

        Log.d("InfowareLab.Debug", "ActConf.onSaveInstanceState");
        //super.onSaveInstanceState(outState); //禁止保存Fragment数据
    }

    protected void hideBottomUIMenu() {
        //隐藏虚拟按键，并且全屏
//        if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) {
//            View v = this.getWindow().getDecorView();
//            v.setSystemUiVisibility(View.GONE);
//        } else if (Build.VERSION.SDK_INT >= 19) {
//            View decorView = getWindow().getDecorView();
//            //int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_IMMERSIVE;
//            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE ;
//            decorView.setSystemUiVisibility(uiOptions);
//
//            // status bar is hidden, so hide that too if necessary.
//            //ActionBar actionBar = getActionBar();
//            //actionBar.hide();
//        }

        //隐藏虚拟按键，并且全屏
        if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) { // lower api
            View v = this.getWindow().getDecorView();
            v.setSystemUiVisibility(View.GONE);
        } else if (Build.VERSION.SDK_INT >= 19) {

            Window _window = getWindow();
            WindowManager.LayoutParams params = _window.getAttributes();
            params.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION|View.SYSTEM_UI_FLAG_IMMERSIVE;
            _window.setAttributes(params);
        }
    }

    private void initView() {

        Log.d("InfowareLab.Debug","ActConf.initView");

        if (null == userCommon.getSelf()){

            Log.d("InfowareLab.Debug","ActConf.initView Error: userCommon.getSelf() == NULL");
            return;
        }

        videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
        docCommon = (DocCommonImpl) CommonFactory.getInstance().getDocCommon();
//        //去掉SVC
//        videoCommon.disableSVC();

        inviteDialog = new InviteDialog(this, 0);
        personListDialog = new PersonListDialog(ActConf.this);
        docListDialog = new DocListDialog(ActConf.this);
        buJuDialog = new BuJuDialog(ActConf.this, videoCommon);

        fragmentManager = getSupportFragmentManager();
        rlRoot = (RelativeLayout) findViewById(R.id.rl_inconf_root);
        flLeft = (FrameLayout) findViewById(R.id.fl_inconf_content_container_left);
        flRight = (FrameLayout) findViewById(R.id.fl_inconf_content_container_right);
        cutline = findViewById(R.id.view_inconf_content_cutline);

        cutline.setVisibility(View.GONE);
        flLeft.setVisibility(View.GONE);
        flRight.setVisibility(View.GONE);

        mSubtitleView = findViewById(R.id.subtitle_view);
        mSubtitleView.setVisibility(View.GONE);

        mNetStatusView = findViewById(R.id.iv_inconf_netState);

//      voiceStateImage =(ImageView) findViewById(R.id.fl_inconf_voiceStateImage);

        //聊天
        rlChatEdit = (RelativeLayout) findViewById(R.id.rl_inconf_attender_chat_bottom);
//        llAttenders = (LinearLayout)findViewById(R.id.ll_inconf_attender_all);
        //菜单
        menuBtn = (ImageView) findViewById(R.id.view_inconf_attender_menuBtn);

        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) menuBtn.getLayoutParams();
        params.width = DensityUtil.dip2px(this, 60);
        params.height = DensityUtil.dip2px(this, 60);
        params.setMargins(0, 0, DensityUtil.dip2px(this, 15), com.infowarelab.tvbox.utils.Utils.getNavigationBarHeight(ActConf.this)
                + DensityUtil.dip2px(this, 10));

        menuBtn.setOnClickListener(this);

        if (Build.MODEL.indexOf("YT-500") != -1 || Build.MODEL.indexOf("I-5300") != -1)
            menuBtn.setVisibility(View.VISIBLE);
        else
            menuBtn.setVisibility(View.GONE);

        rlRoot.post(new Runnable() {
            @Override
            public void run() {
                initFrag();
                if (audioCommon == null) {
                    audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
                }
                audioCommon.startReceive();
                bujuType = SharedPreferencesUrls.getInstance().getInt("" + XMLUtils.CONFIGID + "_bujuType", -10086);
                if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                        || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER){
                    bujuType = ConferenceCommon.COMPONENT_TYPE_VIDEO;
                    //平铺
                    conferenceCommon.setLayout(ConferenceCommon.COMPONENT_TYPE_VIDEO, ConferenceCommon.RT_CONF_UI_TYPE_LIST_VIDEO);
                    videoCommon.setVideoLayout(0);
                    videoCommon.setCurLayoutMode(VideoCommon.LayoutMode.MODE_PLAIN);
                }
                setViewHandler.sendEmptyMessage(0);
                initAttenderFrame();
                initConfHandler();
                //initSDHandler();
                initUserCallback();
                initAudioHandler();
                initAudio();
                initMenuDialog();

                if (loadingDialog != null && loadingDialog.isShowing()){
                    loadingDialog.dismiss();
                }

                String strSubtitle = conferenceCommon.getSubtitles();

                Log.d("InfowareLab.Subtitles","strSubtitle = " + strSubtitle);

                if (strSubtitle != null && strSubtitle.length() > 0 && !strSubtitle.isEmpty()){

                    Log.d("InfowareLab.Subtitles","show subtitle");

                    mSubtitleView.setText(strSubtitle);
                    mSubtitleView.setVisibility(View.VISIBLE);
                }
                else{
                    mSubtitleView.setText("");
                    mSubtitleView.setVisibility(View.GONE);
                }

                startCheckNetStatus();

//                if (!existMicrophone()){
//                    if (null != micLayout) micLayout.setVisibility(View.GONE);
//                }

                //Log.d("InfowareLab.ActConf","ActConf.check microphone: " + existMicrophone());
            }
        });

        personListDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                canjiaBtn.setChecked(false);
                canjiaBtn.setFocusable(true);
            }
        });

        docListDialog.setOnSelectListener(new DocListDialog.OnSelectListener() {
            @Override
            public void onDocItemListener(int docId) {
                ActConf.this.onDocItemListener(docId);
            }

            @Override
            public void onCloseListener() {
                ActConf.this.onCloseListener();
            }
        });

        buJuDialog.setOnResultListener(new BuJuDialog.OnResultListener() {
            @Override
            public void selectType(int type) {
                switch (type) {
                    case 0:
                        bujuType = ConferenceCommon.COMPONENT_TYPE_VIDEO;
                        mCurrentLayoutType = bujuType;
                        //平铺
                        conferenceCommon.setLayout(ConferenceCommon.COMPONENT_TYPE_VIDEO, ConferenceCommon.RT_CONF_UI_TYPE_LIST_VIDEO);
                        videoCommon.setVideoLayout(0);
                        setView(ConferenceCommon.COMPONENT_TYPE_VIDEO);
                        videoCommon.setCurLayoutMode(VideoCommon.LayoutMode.MODE_PLAIN);
                        break;
                    case 1:
                        //主次'
                        bujuType = ConferenceCommon.COMPONENT_TYPE_VIDEO;
                        mCurrentLayoutType = bujuType;
                        conferenceCommon.setLayout(ConferenceCommon.COMPONENT_TYPE_VIDEO, ConferenceCommon.RT_CONF_UI_TYPE_LIST_VIDEO);
                        setView(ConferenceCommon.COMPONENT_TYPE_VIDEO);
                        videoCommon.setCurLayoutMode(VideoCommon.LayoutMode.MODE_SPEAKER);
                        if (videoCommon.getSyncMap().containsValue(userCommon.getSelf().getUid())) {
                            for (Integer key : videoCommon.getSyncMap().keySet()) {
                                if (userCommon.getSelf().getUid() == videoCommon.getSyncMap().get(key)) {
                                    videoCommon.setVideoLayout(key);
                                }
                            }
                        } else {
                            for (Integer key : videoCommon.getSyncMap().keySet()) {
                                if (key != null) {
                                    videoCommon.setVideoLayout(key);
                                    break;
                                }
                            }
                        }
                        break;
                    case 2:

                        if (!existSecondScreen()) {
                            //数据
                            if ((fragAs == null || fragAs.isEmpty())
                                    && ((fragDs == null || fragDs.isEmpty()))) {
                                ToastUtil.showMessage(ActConf.this, "暂无任何共享,未能切换布局", 2 * 1000);
                                return;
                            }

                            conferenceCommon.setLayout(ConferenceCommon.COMPONENT_TYPE_DS, ConferenceCommon.RT_CONF_UI_TYPE_LEFT_TOP_VIDEO);
                            if (isShare || (fragAs != null && !fragAs.isEmpty())) {
                                bujuType = ConferenceCommon.COMPONENT_TYPE_AS;
                                mCurrentLayoutType = bujuType;
                                videoCommon.setVideoLayout(0);
                                setView(ConferenceCommon.COMPONENT_TYPE_AS);
                            } else if (fragDs != null && !fragDs.isEmpty()) {
                                bujuType = ConferenceCommon.COMPONENT_TYPE_DS;
                                mCurrentLayoutType = bujuType;
                                videoCommon.setVideoLayout(0);
                                setView(ConferenceCommon.COMPONENT_TYPE_DS);
                                if (menuDialog != null && menuDialog.isShowing()) {
                                    btnState();
                                }
                            }
                        }
                        else
                        {
                            //数据
                            if ((fragAs2 == null || fragAs2.isEmpty())
                                    && ((fragDs2 == null || fragDs2.isEmpty()))) {
                                ToastUtil.showMessage(ActConf.this, "暂无任何共享,未能切换布局", 2 * 1000);
                                return;
                            }

                            conferenceCommon.setLayout(ConferenceCommon.COMPONENT_TYPE_DS, ConferenceCommon.RT_CONF_UI_TYPE_LEFT_TOP_VIDEO);
                            if (isShare || (fragAs2 != null && !fragAs2.isEmpty())) {
                                bujuType = ConferenceCommon.COMPONENT_TYPE_AS;
                                mCurrentLayoutType = bujuType;
                                videoCommon.setVideoLayout(0);
                                setView(ConferenceCommon.COMPONENT_TYPE_AS);
                            } else if (fragDs2 != null && !fragDs2.isEmpty()) {
                                bujuType = ConferenceCommon.COMPONENT_TYPE_DS;
                                mCurrentLayoutType = bujuType;
                                videoCommon.setVideoLayout(0);
                                setView(ConferenceCommon.COMPONENT_TYPE_DS);
                                if (menuDialog != null && menuDialog.isShowing()) {
                                    btnState();
                                }
                            }
                        }

                        break;
                    case 3:
                        //语音激励
                        bujuType = ConferenceCommon.COMPONENT_TYPE_VIDEO;
                        mCurrentLayoutType = bujuType;
                        videoCommon.setCurLayoutMode(VideoCommon.LayoutMode.MODE_VOICE);
                        setView(ConferenceCommon.COMPONENT_TYPE_VIDEO);
                        conferenceCommon.setLayout(ConferenceCommon.COMPONENT_TYPE_VIDEO, ConferenceCommon.RT_CONF_UI_TYPE_LIST_VIDEO);
                        videoCommon.setVideoLayout(-1);
                        videoCommon.setCurLayoutMode(VideoCommon.LayoutMode.MODE_VOICE);
                        //取第一个元素
                        for (Integer key : videoCommon.getSyncMap().keySet()) {
                            uid = videoCommon.getSyncMap().get(key);
                            if (Build.MODEL.indexOf("MV200") == -1
                                    && Build.MODEL.indexOf("MRUT33") == -1) {
                                if (fragVs != null) fragVs.setMaxVoice(uid);
                            } else {
                                if (fragVs1 != null) fragVs1.setMaxVoice(uid);
                            }
                            return;
                        }
                        break;
                    default:
                        break;
                }
//                //去掉SVC
//                videoCommon.disableSVC();
            }
        });

        tvConfId = findViewById(R.id.iv_inconf_conf_id);

        if (chatAdatper == null){
            initChatView();
        }
    }

    private void initAttenderFrame() {
//        flAttender = (RightSlidingFrameLayout) findViewById(R.id.fl_inconf_attender);
//        RelativeLayout.LayoutParams p = (RelativeLayout.LayoutParams) flAttender.getLayoutParams();
//        p.width = ConferenceApplication.Root_W / 4;
//        flAttender.setViewWidth(ConferenceApplication.Root_W / 4);
//        flAttender.setLayoutParams(p);
//
//        FragmentTransaction ft;
//        ft = fragmentManager.beginTransaction();
//        fragAtt = (FragAttender) fragmentManager.findFragmentByTag("Att");
//        if (fragAtt != null)
//            ft.hide(fragAtt);
//        if (fragAtt == null) {
//            fragAtt = new FragAttender(this);
//            ft.add(R.id.fl_inconf_attender, fragAtt, "Att");
//        } else {
//            ft.show(fragAtt);
//        }
//        ft.commitAllowingStateLoss();
    }

    private void showRequestVideoDialog(int type, int requestData2, int userId, int channelId) {

        Log.d("InfowareLab.Request", "showRequestDialog: type = " +  type);
        Log.d("InfowareLab.Request", "showRequestDialog: userId = " +  userId);
        Log.d("InfowareLab.Request", "showRequestDialog: channelId = " +  channelId);
        Log.d("InfowareLab.Request", "showRequestDialog: requestData2 = " +  requestData2);

        if (requestVideoDialog == null)
            requestVideoDialog = new PromptDialog(this);

        if (requestVideoDialog.isShowing()) return;

        if (type == 1)
            requestVideoDialog.setContent(getResources().getString(R.string.hst_request_audio));
        else
            requestVideoDialog.setContent(getResources().getString(R.string.hst_request_video));

        requestVideoDialog.setCancelable(false);

        requestVideoDialog.setOnResultListener(new PromptDialog.OnResultListener() {
            @Override
            public void doYes() {
                //发送确认消息
                if (confManageCommon != null){
                    Log.d("InfowareLab.Request", "showRequestDialog: response(1) userId = " +  userId);
                    Log.d("InfowareLab.Request", "showRequestDialog: response(1) channelId = " +  channelId);
                    Log.d("InfowareLab.Request", "showRequestDialog: response(1) requestData2 = " +  requestData2);
                    confManageCommon.response(type, userId, channelId, 1, requestData2, "");
                }
            }

            @Override
            public void doNo() {
                //发送确认消息
                if (confManageCommon != null){
                    Log.d("InfowareLab.Request", "showRequestDialog: response(0) userId = " +  userId);
                    Log.d("InfowareLab.Request", "showRequestDialog: response(0) channelId = " +  channelId);
                    Log.d("InfowareLab.Request", "showRequestDialog: response(1) requestData2 = " +  requestData2);
                    confManageCommon.response(type, userId, channelId, 0, requestData2, "");
                }
            }
        });

        requestVideoDialog.show();
    }


    private void showRequestAudioDialog(int type, int requestData2, int userId, int channelId) {

        Log.d("InfowareLab.Request", "showRequestDialog: type = " +  type);
        Log.d("InfowareLab.Request", "showRequestDialog: userId = " +  userId);
        Log.d("InfowareLab.Request", "showRequestDialog: channelId = " +  channelId);
        Log.d("InfowareLab.Request", "showRequestDialog: requestData2 = " +  requestData2);

        if (requestAudioDialog == null)
            requestAudioDialog = new PromptDialog(this);

        if (requestAudioDialog.isShowing()) return;

        if (type == 1)
            requestAudioDialog.setContent(getResources().getString(R.string.hst_request_audio));
        else
            requestAudioDialog.setContent(getResources().getString(R.string.hst_request_video));

        requestAudioDialog.setCancelable(false);

        requestAudioDialog.setOnResultListener(new PromptDialog.OnResultListener() {
            @Override
            public void doYes() {
                //发送确认消息
                if (confManageCommon != null){
                    Log.d("InfowareLab.Request", "showRequestDialog: response(1) userId = " +  userId);
                    Log.d("InfowareLab.Request", "showRequestDialog: response(1) channelId = " +  channelId);
                    Log.d("InfowareLab.Request", "showRequestDialog: response(1) requestData2 = " +  requestData2);
                    confManageCommon.response(type, userId, channelId, 1, requestData2, "");
                }
            }

            @Override
            public void doNo() {
                //发送确认消息
                if (confManageCommon != null){
                    Log.d("InfowareLab.Request", "showRequestDialog: response(0) userId = " +  userId);
                    Log.d("InfowareLab.Request", "showRequestDialog: response(0) channelId = " +  channelId);
                    Log.d("InfowareLab.Request", "showRequestDialog: response(1) requestData2 = " +  requestData2);
                    confManageCommon.response(type, userId, channelId, 0, requestData2, "");
                }
            }
        });

        requestAudioDialog.show();
    }

    private void initData() {

        if (CommonFactory.getInstance().getUserCommon() == null) {
            CommonFactory.getInstance().setAudioCommon(new AudioCommonImpl()).setConferenceCommon(new ConferenceCommonImpl())
                    .setDocCommon(new DocCommonImpl()).setSdCommon(new ShareDtCommonImpl())
                    .setUserCommon(new UserCommonImpl()).setVideoCommon(new VideoCommonImpl()).setChatCommom(new ChatCommomImpl());
        }

        conferenceCommon = (ConferenceCommonImpl) CommonFactory.getInstance().getConferenceCommon();
        userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
        audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
        sdCommon = (ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon();
        confManageCommon = (ConfManageCommonImpl) CommonFactory.getInstance().getConfManageCommon();

        requestVideoDialog = null;
        requestAudioDialog = null;

//        boolean existMicrophone = existMicrophone();//getMicrophoneAvailable(this);
//        boolean existCamera = getCameraAvailable(this);
//
//        Log.d(TAG,"ActConf.existMicrophone = " + existMicrophone);
//        Log.d(TAG,"ActConf.existCamera = " + existCamera);
//
//        conferenceCommon.setDeviceStatus(existMicrophone, existCamera);

        if (!conferenceCommon.isAcceptSharingDesktop()) {

            boolean syncMyAudio = conferenceCommon.isMyAudioSync();

            Log.d("InfowareLab.Net", "initData: syncMyAudio = " + syncMyAudio);

            //        //通知是box
            //        conferenceCommon.setMeetingBox();
            if (userCommon != null && userCommon.getSelf() != null) {
                lastRole = userCommon.getSelf().getRole();

                if (audioCommon != null) {
                    if (syncMyAudio || userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        audioCommon.startSend();
                        SharedPreferencesUrls.getInstance().putBoolean("isOpenMic", true);
                    }
                } else {
                    audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
                    if (syncMyAudio || userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        audioCommon.startSend();
                        SharedPreferencesUrls.getInstance().putBoolean("isOpenMic", true);
                    }
                }
            } else {
                userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
                if (userCommon != null && userCommon.getSelf() != null) {
                    lastRole = userCommon.getSelf().getRole();
                    if (audioCommon == null) {
                        audioCommon = (AudioCommonImpl) CommonFactory.getInstance().getAudioCommon();
                    }
                    if (syncMyAudio || userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        audioCommon.startSend();
                    }
                }
            }
        }
    }


    private void initAudio() {
        audioCommon.createVoe(this);
        setVolumeControlStream(AudioManager.STREAM_VOICE_CALL);
        initAudioFilter();
    }

    private void initAudioFilter() {
        audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
        } else {
            audioManager.setMode(AudioManager.MODE_IN_CALL);
        }
        boolean on = SharedPreferencesUrls.getInstance().getBoolean("speakerphoneon", false);
        Log.d("InfowareLab.Audio", "initAudioFilter: audioManager.setSpeakerphoneOn:" + on);
        audioManager.setSpeakerphoneOn(on);//false走usb麦，true走hdmi

        if (!audioManager.isWiredHeadsetOn()
                && !audioManager.isBluetoothScoOn()) {
            audioState = 0;
        } else {
            audioState = 1;
        }
        audioReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                SharedPreferences sharedPreferences = getSharedPreferences("sound", Context.MODE_PRIVATE);
                SharedPreferences.Editor editor = sharedPreferences.edit();
                //判断外设
                if (action.equals(TAGIN)) {
                    Toast.makeText(context, "外设已经连接", Toast.LENGTH_SHORT).show();
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
                        audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
                    } else {
                        audioManager.setMode(AudioManager.MODE_IN_CALL);
                    }
                    Log.d("InfowareLab.Audio", "onReceive: audioManager.setSpeakerphoneOn: false");
                    //audioManager.setSpeakerphoneOn(false);//false走usb麦，true走hdmi
                    //SharedPreferencesUrls.getInstance().putBoolean("speakerphoneon", false);
                    //editor.putInt("position", 1);
                    //editor.commit();
                } else if (action.equals(TAGOUT)) {
                    if (BOOLEAN) {
                        Toast.makeText(context, "外设已经移除", Toast.LENGTH_SHORT).show();
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
                            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
                        } else {
                            audioManager.setMode(AudioManager.MODE_IN_CALL);
                        }
                        Log.d("InfowareLab.Audio", "onReceive: audioManager.setSpeakerphoneOn: true");
                        //audioManager.setSpeakerphoneOn(true);//false走usb麦，true走hdmi
                        //SharedPreferencesUrls.getInstance().putBoolean("speakerphoneon", true);
                        //editor.putInt("position", 0);
                        //editor.commit();
                    }
                }
                //判断存储usb
                if (action.equals(TAGUSB)) {
//                    boolean connected = intent.getExtras().getBoolean("connected");
//                    if (connected) {
//                        Toast.makeText(context, "USB 已经连接", Toast.LENGTH_SHORT).show();
//                        audioCommon.stopReceive();
//                        audioCommon.startReceive();
//                    } else {
//                        if (BOOLEAN) {
//                            Toast.makeText(context, "USB 断开", Toast.LENGTH_SHORT).show();
//                            audioCommon.stopReceive();
//                            audioCommon.startReceive();
//                        }
//
//                    }
                }
                //判断耳机
                if (action.equals(TAGLISTEN) || action.equals(Intent.ACTION_HEADSET_PLUG)) {
                    audioState = intent.getIntExtra("state", 2);
                    // state --- 0代表拔出，1代表插入
                    // name--- 字符串，代表headset的类型。
                    // microphone -- 1代表这个headset有麦克风，0则没有
                    // int i=intent.getIntExtra("",0);
                    if (audioState == 0) {
                        if (BOOLEAN) {
                            //showShortToast("拔出耳机");
                            Log.d("InfowareLab.Audio", "拔出耳机: audioManager.setSpeakerphoneOn: true");
                            audioManager.setSpeakerphoneOn(true);
                        }
                    } else if (audioState == 1) {
                        //showShortToast("耳机插入");
                        Log.d("InfowareLab.Audio", "耳机插入: audioManager.setSpeakerphoneOn: false");
                        audioManager.setSpeakerphoneOn(false);
                    }

                }
                BOOLEAN = true;
            }

        };
        IntentFilter filter = new IntentFilter();
        //筛选的条件
        filter.addAction(TAGIN);
        filter.addAction(TAGOUT);
        filter.addAction(TAGUSB);
        filter.addAction(Intent.ACTION_HEADSET_PLUG);
        //注册广播 动态注册
        registerReceiver(audioReceiver, filter);
    }

    @Override
    protected void onStart() {
        Log.d("InfowareLab.Debug", "ActConf.onStart");
        super.onStart();
    }

    @Override
    protected void onStop() {
        Log.d("InfowareLab.Debug", "ActConf.onStop");
        super.onStop();
    }

    @Override
    protected void onResume() {

        Log.d("InfowareLab.Debug", "ActConf.onResume");

//        if (mWakeLock == null) {
//            PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
//            mWakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK, "ActConf");
//            mWakeLock.acquire();
//        }

        if (userCommon != null) {
            isHost = userCommon.isHost1();
        }
        isShow = true;
        lHeight = DensityUtil.getWindowHeight(this) + com.infowarelab.tvbox.utils.Utils.getNavigationBarHeight(ActConf.this);
        lWidth = DensityUtil.getWindowWidth(this);
        super.onResume();
        //删除三天前得日志
        FileUtils.removeFileByTime(((ConferenceApplication) ActConf.this.getApplication()).getFilePath("Log"));
    }

    @Override
    protected void onRestart() {

        Log.d("InfowareLab.Debug", "ActConf.onRestart");

        super.onRestart();
        if (null != rlRoot) {
            rlRoot.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (Build.MODEL.indexOf("MV200") == -1
                            && Build.MODEL.indexOf("MRUT33") == -1) {
                        if (fragVs != null) {
                            fragVs.restartCam();
                        }
                    } else {
                        if (fragVs1 != null) {
                            fragVs1.restartCam();
                        }
                    }
                }
            }, 300);
        }
    }

    private void initSDHandler() {
        Handler sdHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);

                Log.d(TAG, ">>>>>sdHandler: msg.what="+msg.what);

                switch (msg.what) {
                    case ShareDtCommon.STOP_SHARE_DT:
                        //Log.d("InfowareLab.Debug", "===> ConferenceCommon.LEAVE");

                        if (isShare) {
                           shareDS(false);
                        }

                        break;
                    case ShareDtCommon.START_SHARE_DT:

                        if (isShare) {
                            shareDS(true);
                        }

                        break;

                    default:
                        break;
                }
            }
        };
        if (sdCommon == null) {
            sdCommon = (ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon();
        }
        sdCommon.setHandler(sdHandler);
    }

    private void initConfHandler() {
        Handler confHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);

                Log.d(TAG, ">>>>>confHandler: msg.what="+msg.what);

                switch (msg.what) {
                    case ConferenceCommon.LEAVE:
                        Log.d("InfowareLab.Debug", "===> ConferenceCommon.LEAVE");
                        isAcLeave = true;
                        CallbackManager.IS_FOECE_LEAVED = true;

//                        if (Build.MODEL.indexOf("MV200") == -1
//                                && Build.MODEL.indexOf("MRUT33") == -1) {
//                            if (fragVs != null) {
//                                fragVs.preLeave();
//                                fragVs.setCameraSize();
//                                fragVs.destroyCamera();
//                            }
//                        } else {
//                            if (fragVs1 != null) {
//                                fragVs1.preLeave();
//                                fragVs1.destroyCamera();
//                            }
//                        }
//                        if (fragAs != null) fragAs.preLeave();

                        //String message = "";

                        if ((Integer) msg.obj == ConferenceCommon.FORCELEAVE) {
                            showShortToast(R.string.leave_force);
                            mExitMessage = "您已被请出会议，请重新加入会议。";
                        } else if ((Integer) msg.obj == ConferenceCommon.HOSTCLOSECONF) {
                            showShortToast(R.string.leave_finish);
                            mExitMessage = "会议已结束，请重新加入会议。";
                        } else if ((Integer) msg.obj == 40100) {
                            exitedDueToNetworkIssue = true;
                            boolean isSyncVideoSelf = videoCommon.isSyncSelf();
                            boolean isSyncAudioSelf = audioCommon.isRecording();
                            Log.d("InfowareLab.Net", "===> exit due to network issue: isSyncVideoSelf = " + isSyncVideoSelf);
                            Log.d("InfowareLab.Net", "===> exit due to network issue: isSyncAudioSelf = " + isSyncAudioSelf);

                            conferenceCommon.saveMyVideoAudioSync(isSyncVideoSelf, isSyncAudioSelf);

                            showShortToast(R.string.leave_offline);
                            mExitMessage = "断开连接，请重新加入会议。";
                        }
                        else
                            mExitMessage = "";

                        if (mExitMessage.length() > 0){

                            conferenceExit = true;

                            // TODO Auto-generated method stub
                            SharedPreferencesUrls.getInstance().putBoolean("mic", false);
                            SharedPreferencesUrls.getInstance().putBoolean("sound", true);
                            if (isShare) {
                                if (audioRecordUtils != null) {
                                    audioRecordUtils.pauseRecord();
                                    audioRecordUtils = null;
                                }
                                ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).stopDesktopShare();
                                ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).setScreenInfo(0, 0, 0);
                                if (Build.MODEL.indexOf("MV200") == -1
                                        && Build.MODEL.indexOf("MRUT33") == -1) {
                                    if (fragVs != null) fragVs.recoverRatio();
                                } else {
                                    if (fragVs1 != null) fragVs1.recoverRatio();
                                }
                                isShare = false;
                            }
                            exit();
                        }
                        else {

                            if (exitDialog == null) {
                                exitDialog = new ExitDialog(ActConf.this, 0);
                                exitDialog.setContext(mExitMessage);
                                exitDialog.setClickListener(new ExitDialog.OnResultListener() {
                                    @Override
                                    public void doYes() {
                                        // TODO Auto-generated method stub
                                        SharedPreferencesUrls.getInstance().putBoolean("mic", false);
                                        SharedPreferencesUrls.getInstance().putBoolean("sound", true);
                                        if (isShare) {
                                            if (audioRecordUtils != null) {
                                                audioRecordUtils.pauseRecord();
                                                audioRecordUtils = null;
                                            }
                                            ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).stopDesktopShare();
                                            ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).setScreenInfo(0, 0, 0);
                                            if (Build.MODEL.indexOf("MV200") == -1
                                                    && Build.MODEL.indexOf("MRUT33") == -1) {
                                                if (fragVs != null) fragVs.recoverRatio();
                                            } else {
                                                if (fragVs1 != null) fragVs1.recoverRatio();
                                            }
                                            isShare = false;
                                        }
                                        exit();
                                    }

                                    @Override
                                    public void doNo() {
                                        logoutBtn.setFocusable(true);
                                        logoutBtn.setChecked(false);
                                        // TODO Auto-generated method stub
                                        if (fragAs != null) {
                                            fragAs.startAudio();
                                        }
                                    }
                                });
                                if (!exitDialog.isShowing()) {
                                    exitDialog.show();
                                }
                            } else {
                                exitDialog.setContext(mExitMessage);
                                if (exitDialog.isShowing()) {
                                    exitDialog.dismiss();
                                } else {
                                    exitDialog.show();
                                }
                            }
                        }
                        break;
                    case ConferenceCommon.CALLATT:
                        break;
                    case ConferenceCommon.CLOUDRECORD:
                        break;
                    case ConferenceCommon.RT_CONF_UI_TYPE_LIST_VIDEO:
                        type = msg.arg1;
                        setViewHandler.removeCallbacksAndMessages(null);
                        setViewHandler.sendEmptyMessage(0);
                        break;
                    case ConferenceCommon.RT_CONF_UI_TYPE_FULLSCREEN_VIDEO:
                        type = msg.arg1;
                        setViewHandler.removeCallbacksAndMessages(null);
                        setViewHandler.sendEmptyMessage(0);
                        break;
                    case ConferenceCommon.RT_CONF_UI_TYPE_LEFT_TOP_VIDEO:
                        type = msg.arg1;
                        setViewHandler.removeCallbacksAndMessages(null);
                        setViewHandler.sendEmptyMessage(0);
                        break;
                    case ConferenceCommon.RT_CONF_UI_TYPE_RIGHT_VIDEO:
                        type = msg.arg1;
                        setViewHandler.removeCallbacksAndMessages(null);
                        setViewHandler.sendEmptyMessage(0);
                        break;
                    case ConferenceCommon.RT_CONF_UI_TYPE_TOP_VIDEO:
                        type = msg.arg1;
                        setViewHandler.removeCallbacksAndMessages(null);
                        setViewHandler.sendEmptyMessage(0);
                        break;
                    case ConferenceCommon.RT_CONF_UI_TYPE_FULLSCREEN_NORMAL:
                        type = msg.arg1;
                        setViewHandler.removeCallbacksAndMessages(null);
                        setViewHandler.sendEmptyMessage(0);
                        break;
                    default:
                        break;
                }
            }
        };
        if (conferenceCommon == null) {
            conferenceCommon = (ConferenceCommonImpl) CommonFactory.getInstance().getConferenceCommon();
        }
        conferenceCommon.setHandler(confHandler);
    }


    //记录上个角色
    private void initUserCallback() {
        Handler userHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
//                if (fragAtt != null) fragAtt.doUserCallback(msg);
                //Log.d(TAG, "userHandler: msg = " + msg.what);

                if (msg.what == UserCommon.CHANGE_ROLE) {

                    Log.d(TAG, "userHandler: msg = CHANGE_ROLE");
                    int uid = msg.arg1;
                    int newRole = msg.arg2;
                    UserBean user = userCommon.getUser(uid);
                    if (user == null || userCommon.getSelf() == null) {
                        return;
                    }
                    if (uid == userCommon.getSelf().getUid()) {
                        if (lastRole != newRole) {
                            if (lastRole == UserCommon.ROLE_SPEAKER && newRole != UserCommon.ROLE_HOST) {
                                ToastUtil.showMessage(ActConf.this, "主持人取消了您的主讲人角色", 5 * 1000);
                                isHost = false;
                                //bujuType = -10086;
//                                setMicStatus(true);
                                if (menuDialog != null) {
                                    btnState();
                                }
                            } else if (newRole == UserCommon.ROLE_HOST) {
                                ToastUtil.showMessage(ActConf.this, "恭喜你,你已经是主持人啦", 5 * 1000);
                                audioCommon.startSend();
                                setMicStatus(false);
                                //bujuType =
                                if (menuDialog != null) {
                                    btnState();
                                }
                                isHost = true;
                            } else if (newRole == UserCommon.ROLE_SPEAKER) {
                                if (lastRole != UserCommon.ROLE_HOST) {
                                    ToastUtil.showMessage(ActConf.this, "恭喜你,主持人授权您为主讲人", 5 * 1000);
                                    setMicStatus(false);
                                }
                                isHost = true;
                                if (menuDialog != null) {
                                    btnState();
                                }
                            } else if (lastRole == UserCommon.ROLE_HOST) {
                                isHost = false;
                                ToastUtil.showMessage(ActConf.this, "您已被取消主持人角色", 5 * 1000);
//                                setMicStatus(true);
                                if (menuDialog != null && menuDialog.isShowing()) {
                                    btnState();
                                }
                            }
                            lastRole = newRole;

                            if (fragVs != null) {
                                fragVs.onSelfRoleChange(newRole);
                            }
                        }
                    }

//                    if (msg.what == UserCommon.ROLEUPDATE){
//                        if (personListDialog != null) {
//                            personListDialog.refreshAttenders();
//                        }
//                    }
                } else if (msg.what == UserCommon.CHAT_RECEIVE) {

                    if (chatAdatper == null){
                        initChatView();
                    }

                    if (null == chatingUser) chatingUser = new UserBean();

                    chatingUser.setUid(userCommon.ALL_USER_ID);

                    if (null != chatingUser) {

                        chatAdatper.notifyDataSetChanged();
                        lvChat.setSelection(chatAdatper.getCount() - 1);
                        // 这里做已阅读全部操作
                        userCommon.setReadAllMessage(chatingUser.getUid());
                        userCommon.getUser4Phone(chatingUser.getUid())
                                .setReadedMsg(true);
//                        findViewById(R.id.view_inconf_attender_chat)
//                                .setVisibility(View.GONE);
//                        chatBtn.setChecked(false);
//                        inviteBtn.setChecked(true);
//                        inviteBtn.requestFocus();
                    }
                } else if (msg.what == UserCommon.ACTION_USER_ADD || msg.what == UserCommon.ACTION_USER_REMOVE
                        || msg.what == UserCommon.ACTION_USER_MODIFY || msg.what == UserCommon.ROLEUPDATE) {

                    if (msg.what == UserCommon.ROLEUPDATE)
                        Log.d(TAG, "userHandler: msg = ROLEUPDATE");

                    if (personListDialog != null) {
                        personListDialog.refreshAttenders();
                    }
                } else if (msg.what == UserCommon.AS_PRIVILIGE_OFF){
                    if ( Build.MODEL.indexOf("MV200") != -1
                            || Build.MODEL.indexOf("YT-500") != -1 || Build.MODEL.indexOf("I-5300") != -1) {

                        if (menuDialog != null) {
                            btnState();
                        }
//                        if (flowLayout != null)
//                            flowLayout.setVisibility(View.GONE);
                    }

                } else if (msg.what == UserCommon.AS_PRIVILIGE_ON){
                    if ( Build.MODEL.indexOf("MV200") != -1
                            || Build.MODEL.indexOf("YT-500") != -1 || Build.MODEL.indexOf("I-5300") != -1) {

                        if (menuDialog != null) {
                            btnState();
                        }
//                        if (flowLayout != null)
//                            flowLayout.setVisibility(View.VISIBLE);
                    }
                }
            }
        };
        if (userCommon == null) {
            userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
        }
        userCommon.setHandler(userHandler);

    }

    //音频限制
    boolean isOpenFlag = true;
    boolean isClosedFlag = true;

    private void initAudioHandler() {
        Handler audioHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case AudioCommon.MIC_TURN_ON:
                        if (audioCommon == null) {
                            break;
                        }
                        if (audioCommon != null && isOpenFlag) {
                            micOn();
                            isOpenFlag = false;
                            isClosedFlag = true;
                        }
                        break;
                    case AudioCommon.MIC_TURN_OFF:
                        if (audioCommon == null) {
                            break;
                        }
                        if (isClosedFlag) {
                            micOff();
                            isClosedFlag = false;
                            isOpenFlag = true;
                        }
                        break;
                    case AudioCommon.MIC_ON:
                        if (isOpenFlag) {
                            micOn();
                            isOpenFlag = false;
                            isClosedFlag = true;
                        }
                        break;
                    case AudioCommon.MIC_OFF:
                        if (isClosedFlag) {
                            micOff();
                            isClosedFlag = false;
                            isOpenFlag = true;
                        }
                        break;
                    case AudioCommon.MIC_ENABLE:
                        break;
                    case AudioCommon.MIC_DISABLE:
                        break;
                    case AudioCommon.MIC_NOPERMISSION:
                        break;
                    case AudioCommon.MAX_VOICE:
                        Log.d("InfowareLab.Debug", ">>>>>>ActConf.AudioCommon.MAX_VOICE: uid=" + msg.arg1);
                        if (Build.MODEL.indexOf("MV200") == -1
                                && Build.MODEL.indexOf("MRUT33") == -1) {
                            if (fragVs != null) fragVs.setMaxVoice(msg.arg1);
                        } else {
                            if (fragVs1 != null) fragVs1.setMaxVoice(msg.arg1);
                        }
                        break;
                    default:
                        break;
                }
            }
        };
        audioCommon.setHandler(audioHandler);
    }

    private void initFrag() {

        Log.d("InfowareLab.Debug","ActConf.initFrag");

        fragmentManager = getSupportFragmentManager();
        FragmentTransaction ft;
        ft = fragmentManager.beginTransaction();
        if (Build.MODEL.indexOf("MV200") == -1
                && Build.MODEL.indexOf("MRUT33") == -1) {
            if (null == fragVs) {
//                fragVs = new FragVs(this);
                fragVs = new FragVs();
                fragVs.setiCallParentView(this);
            }
            ft.add(R.id.fl_inconf_content_container_right, fragVs, "Video");
        } else {
            if (null == fragVs1) {
//                fragVs1 = new FragVs1(this);
                fragVs1 = new FragVs1();
                fragVs1.setiCallParentView(this);
            }
            ft.add(R.id.fl_inconf_content_container_right, fragVs1, "Video");
        }

        Display display = getSecondScreen();
        if (display == null) {

            if (null == fragDs) {
//            fragDs = new FragDs(this);
                fragDs = new FragDs();
                fragDs.setiCallParentView(this);
            }
            ft.add(R.id.fl_inconf_content_container_left, fragDs, "Doc");


            if (null == fragAs) {
                //            fragAs = new FragAs(this);
                fragAs = new FragAs();
                fragAs.setiCallParentView(this);
            }

            ft.add(R.id.fl_inconf_content_container_left, fragAs, "AS");
            ft.hide(fragDs).show(fragAs);
        }
        else
        {
            if (null == fragAs2) {
                //            fragAs = new FragAs(this);
                fragAs2 = new FragAs2(getApplicationContext(), display);
                fragAs2.setiCallParentView(this);

                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                    fragAs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                else
                    fragAs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE

                //fragAs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;

                fragAs2.show();

//                Window window = fragAs2.getWindow();
//                WindowManager.LayoutParams lp = window.getAttributes();
//                lp.gravity = Gravity.CENTER; // 居中位置
//                lp.width = WindowManager.LayoutParams.MATCH_PARENT;
//                lp.height = WindowManager.LayoutParams.MATCH_PARENT;
//                window.setAttributes(lp);
//                window.setWindowAnimations(R.style.dialogWindowAnim);
            }

            if (null == fragDs2) {
                //            fragDs = new fragDs(this);
                fragDs2 = new FragDs2(getApplicationContext(), display);
                fragDs2.setiCallParentView(this);

                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                    fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                else
                    fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE

                fragDs2.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);

                fragDs2.show();
                fragDs2.hide();
            }
        }

        curLeftPage = 1;
        ft.commitAllowingStateLoss();
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
            if (!isShare && isRunPage && (-1 == curPosition)) {

                if (!existSecondScreen()) {
                    if (fragDs != null && fragDs.isAdded() && !fragDs.isHidden()) {
                        fragDs.downPage();
                    }
                }
                else  {
                    if (fragDs2 != null && fragDs2.isAdded() && !fragDs2.isHidden()) {
                        fragDs2.downPage();
                    }
                }

            }
        }
        if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
            if (!existSecondScreen()) {
                if (!isShare && isRunPage && (-1 == curPosition)) {
                    if (fragDs != null && fragDs.isAdded() && !fragDs.isHidden()) {
                        fragDs.upPage();
                    }
                }
            }
            else {
                if (!isShare && isRunPage && (-1 == curPosition)) {
                    if (fragDs2 != null && fragDs2.isAdded() && !fragDs2.isHidden()) {
                        fragDs2.upPage();
                    }
                }
            }
        }
        return super.onKeyUp(keyCode, event);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        boolean result = false;
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_LEFT:
                if (0 == curPosition) {
                    if (fragMic != null) {
                        fragMic.subtraction();
                    }
                } else if (1 == curPosition) {
                    if (fragSound != null) {
                        fragSound.subtraction();
                    }
                } else {
//                    if(!flAttender.isShow()&&(exitDialog==null||!exitDialog.isShowing())){
//                        flAttender.show();
//                        result = true;
//                    }
                }
                break;
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                if (0 == curPosition) {
                    if (fragMic != null) {
                        fragMic.add();
                    }
                } else if (1 == curPosition) {
                    if (fragSound != null) {
                        fragSound.add();
                    }
                } else {
//                    if(flAttender.isShow()&&(exitDialog==null||!exitDialog.isShowing())){
//                        flAttender.dismiss();
//                        result = true;
//                    }
                }
                break;
            case KeyEvent.KEYCODE_MENU:
                //if (Build.MODEL.indexOf("YT-500") != -1) {
                    if (menuDialog != null) {
                        if (menuDialog.isShowing()) {
                            menuDialog.dismiss();
                        } else {
                            menuDialog.show();
                            update();
                        }
                    }
                //}
                break;
            case KeyEvent.KEYCODE_HOME:
                result = true;
                break;
            case KeyEvent.KEYCODE_VOLUME_DOWN:
            case KeyEvent.KEYCODE_MINUS:
                if (0 == curPosition) {
                    if (fragMic != null) {
                        fragMic.subtraction();
                    }
                } else if (1 == curPosition) {
                    if (fragSound != null) {
                        fragSound.subtraction();
                    }
                }
                break;
            case KeyEvent.KEYCODE_VOLUME_UP:
            case KeyEvent.KEYCODE_PLUS:
                if (0 == curPosition) {
                    if (fragMic != null) {
                        fragMic.add();
                    }
                } else if (1 == curPosition) {
                    if (fragSound != null) {
                        fragSound.add();
                    }
                }
                break;
            //静音
            case KeyEvent.KEYCODE_F3:
                if (((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_HOST) {
                    if (Utils.isFastClick()) break;
                    micOpre(false);
                    SharedPreferencesUrls.getInstance().putBoolean("allAute", false);
//                    if (SharedPreferencesUrls.getInstance().getBoolean("isOpenMic", true)) {
//                        //关闭
//                        micOpre(false);
//                        SharedPreferencesUrls.getInstance().putBoolean("isOpenMic", false);
//                    } else {
//                        //打开
//                        micOpre(true);
//                        SharedPreferencesUrls.getInstance().putBoolean("isOpenMic", true);
//                    }
                }
                break;
            case KeyEvent.KEYCODE_F4:
//                if ((SharedPreferencesUrls.getInstance().getBoolean("bAS", false)) && Build.MODEL.indexOf("UT33") == -1
//                        && Build.MODEL.indexOf("HDX2") == -1) {
                if (Build.MODEL.indexOf("UT33") == -1
                        && Build.MODEL.indexOf("HDX2") == -1) {
                    if (fragAs != null && fragAs.isAdded() && !fragAs.isHidden() && !fragAs.isEmpty()){
                        ToastUtil.showMessage(ActConf.this,"对不起，正在桌面共享，请稍后再试", 5 * 1000);
                        break;
                    }
                    if (fragAs2 != null && fragAs2.isAdded() && !fragAs2.isHidden() && !fragAs2.isEmpty()){
                        ToastUtil.showMessage(ActConf.this,"对不起，正在桌面共享，请稍后再试", 5 * 1000);
                        break;
                    }
                    //发送辅流
                    FragmentTransaction ft = fragmentManager.beginTransaction();
                    if (!isShare) {
                        flLeft.setVisibility(View.GONE);
                        flRight.setVisibility(View.VISIBLE);
                        cutline.setVisibility(View.VISIBLE);
                        bujuType = ConferenceCommon.COMPONENT_TYPE_AS;
                        if (Build.MODEL.indexOf("MV200") == -1
                                && Build.MODEL.indexOf("MRUT33") == -1) {
                            if (fragVs != null) {
                                fragVs.setShare(true);
                                fragVs.retCamera();
                            }
                            if (fragAs != null && fragAs.isAdded() && !fragAs.isHidden()) {
                                ft.hide(fragAs);
                            }

                            if (fragAs2 != null && fragAs2.isAdded() && !fragAs2.isHidden()) {
                                fragAs2.dismiss();
                            }

                            if (fragVs == null) {
//                                fragVs = new FragVs(this);
                                fragVs = new FragVs();
                                fragVs.setiCallParentView(this);
                                ft.add(R.id.fl_inconf_content_container_right, fragVs, "Video");
                            } else {
                                ft.show(fragVs);
                            }
                        } else {
                            if (fragVs1 != null) {
                                fragVs1.setShare(true);
                                fragVs1.retCamera();
                            }
                            if (fragAs != null && fragAs.isAdded() && !fragAs.isHidden()) {
                                ft.hide(fragAs);
                            }
                            if (fragAs2 != null && fragAs2.isAdded() && !fragAs2.isHidden()) {
                                fragAs2.dismiss();
                            }
                            if (fragVs1 == null) {
//                                fragVs1 = new FragVs1(this);
                                fragVs1 = new FragVs1();
                                fragVs1.setiCallParentView(this);
                                ft.add(R.id.fl_inconf_content_container_right, fragVs1, "Video");
                            } else {
                                ft.show(fragVs1);
                            }
                        }
                        ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).startDesktopShare(15, 20);
                        isShare = true;
                    } else {
                        if (Build.MODEL.indexOf("MV200") == -1
                                && Build.MODEL.indexOf("MRUT33") == -1) {
                            if (fragVs != null) {
                                fragVs.setShare(false);
                                fragVs.retCamera();
                                fragVs.recoverRatio();
                            }
                        } else {
                            if (fragVs1 != null) {
                                fragVs1.setShare(false);
                                fragVs1.retCamera();
                                fragVs1.recoverRatio();
                            }
                        }
                        if (audioRecordUtils != null) {
                            audioRecordUtils.pauseRecord();
                            audioRecordUtils = null;
                        }
                        ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).stopDesktopShare();
                        ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).setScreenInfo(0, 0, 0);
                        setViewHandler.removeCallbacksAndMessages(null);
                        setViewHandler.sendEmptyMessage(0);
                        isShare = false;
                    }
                    ft.commitAllowingStateLoss();
                } else {
                    if (Build.MODEL.indexOf("MV200") != -1 || Build.MODEL.indexOf("YT-500") != -1 || Build.MODEL.indexOf("I-5300") != -1) {
                        if (isShare) {
                            if (audioRecordUtils != null) {
                                audioRecordUtils.pauseRecord();
                                audioRecordUtils = null;
                            }
                            ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).stopDesktopShare();
                            ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).setScreenInfo(0, 0, 0);
                            isShare = false;
                            if (Build.MODEL.indexOf("MV200") == -1
                                    && Build.MODEL.indexOf("MRUT33") == -1) {
                                if (fragVs != null) fragVs.recoverRatio();
                            } else {
                                if (fragVs1 != null) fragVs1.recoverRatio();
                            }
                        }
                    } else {
                        ToastUtil.showMessage(ActConf.this, "对不起，你的设备暂不支持该功能", 5 * 1000);
                    }
                }
                break;
            //静音按键
            case KeyEvent.KEYCODE_NUM_LOCK:
                if (Utils.isFastClick()) break;
                if (SharedPreferencesUrls.getInstance().getBoolean("mic", false)) {
                    micOff();
                    SharedPreferencesUrls.getInstance().putBoolean("mic", false);
                } else {
                    micOn();
                    SharedPreferencesUrls.getInstance().putBoolean("mic", true);
                }
                break;
            //静音
            case KeyEvent.KEYCODE_VOLUME_MUTE:
                if (rbSound.isChecked()) {
                    rbSound.setChecked(false);
                } else {
                    rbSound.setChecked(true);
                }
                break;
            default:
                break;
        }
        if (result) {
            return true;
        } else {
            return super.onKeyDown(keyCode, event);
        }
    }

    public void closeCam() {
        fragVs.destroyCamera();
    }

    @Override
    public void onBackPressed() {
        if (findViewById(R.id.view_inconf_attender_chat).getVisibility() == View.VISIBLE) {
            findViewById(R.id.view_inconf_attender_chat)
                    .setVisibility(View.GONE);

            if (menuDialog != null && !menuDialog.isShowing())
                menuDialog.show();

        } else {
            fragmentManager = getSupportFragmentManager();
            FragmentTransaction ft;
            ft = fragmentManager.beginTransaction();
            if (0 == curPosition) {
                if (fragMic != null && fragMic.isAdded() && !fragMic.isHidden()) {
                    fragMic.back();
                    ft.hide(fragMic);
                }
                ft.commitAllowingStateLoss();
                curPosition = -1;
            } else if (1 == curPosition) {
                if (fragSound != null && fragSound.isAdded() && !fragSound.isHidden()) {
                    fragSound.back();
                    fragSound.resetMediaPlay();
                    ft.hide(fragSound);
                }
                ft.commitAllowingStateLoss();
                curPosition = -1;
            } else if (2 == curPosition) {
                curPosition = -1;
            } else if (3 == curPosition) {
            } else if (4 == curPosition) {
                curPosition = -1;
            } else {
                if (isShare) {
                    if (Build.MODEL.indexOf("MV200") == -1
                            && Build.MODEL.indexOf("MRUT33") == -1) {
                        if (fragVs != null) {
                            fragVs.setShare(false);
                            fragVs.retCamera();
                            fragVs.recoverRatio();
                        }
                    } else {
                        if (fragVs1 != null) {
                            fragVs1.setShare(false);
                            fragVs1.retCamera();
                            fragVs1.recoverRatio();
                        }
                    }
                    setViewHandler.removeCallbacksAndMessages(null);
                    setViewHandler.sendEmptyMessage(0);
                    isShare = false;
                    if (audioRecordUtils != null) {
                        audioRecordUtils.pauseRecord();
                        audioRecordUtils = null;
                    }
                    if (flowText != null) {
                        flowText.setText(getResources().getString(R.string.flow_title));
                    }
                    ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).stopDesktopShare();
                    ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).setScreenInfo(0, 0, 0);
                } else {
                    if (menuDialog != null && menuDialog.isShowing()) {
                        if (Build.MODEL.indexOf("YT-500") != -1|| Build.MODEL.indexOf("I-5300") != -1)
                            menuDialog.dismiss();
                    } else {
                        if (fragAs != null) {
                            fragAs.destroyAudio();
                        }
                        if (fragAs2 != null) {
                            fragAs2.destroyAudio();
                        }
                        showExitDialog();
                    }
                }
            }
        }
    }

    public void showExitDialog() {
        mExitMessage = "";
        if (exitDialog == null) {
            exitDialog = new ExitDialog(this, 0);
            exitDialog.setContext("");
            exitDialog.setClickListener(new ExitDialog.OnResultListener() {
                @Override
                public void doYes() {
                    // TODO Auto-generated method stub
                    SharedPreferencesUrls.getInstance().putBoolean("mic", false);
                    SharedPreferencesUrls.getInstance().putBoolean("sound", true);
                    if (isShare) {
                        if (audioRecordUtils != null) {
                            audioRecordUtils.pauseRecord();
                            audioRecordUtils = null;
                        }
                        ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).stopDesktopShare();
                        ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).setScreenInfo(0, 0, 0);
                        if (Build.MODEL.indexOf("MV200") == -1
                                && Build.MODEL.indexOf("MRUT33") == -1) {
                            if (fragVs != null) fragVs.recoverRatio();
                        } else {
                            if (fragVs1 != null) fragVs1.recoverRatio();
                        }
                        isShare = false;
                    }
                    exit();
                }

                @Override
                public void doNo() {
                    logoutBtn.setFocusable(true);
                    logoutBtn.setChecked(false);
                    // TODO Auto-generated method stub
                    if (fragAs != null) {
                        fragAs.startAudio();
                    }
                    if (fragAs2 != null) {
                        fragAs2.startAudio();
                    }
                }
            });
            if (!exitDialog.isShowing()) {
                exitDialog.show();
            }
        } else {
            exitDialog.setContext("");
            if (exitDialog.isShowing()) {
                exitDialog.dismiss();
            } else {
                exitDialog.show();
            }
        }
    }

    public void exit() {
        
        stopGetNetStatus();

        if (loadingDialog != null && !loadingDialog.isShowing()) {
            loadingDialog.setTitle("正在退会");
            loadingDialog.show();
        }

        //if (!audioCommon.didStartSend) audioCommon.startSend();

        conferenceExit = true;

        isAcLeave = true;

        if (requestAudioDialog != null){
            requestAudioDialog.dismiss();
            requestAudioDialog = null;
        }

        if (requestVideoDialog != null){
            requestVideoDialog.dismiss();
            requestVideoDialog = null;
        }

        Log.d("InfowareLab.Leave", "======>check exit()");

        if (Build.MODEL.indexOf("MV200") == -1
                && Build.MODEL.indexOf("MRUT33") == -1) {
            if (fragVs != null) {
                fragVs.preLeave();
                fragVs.setCameraSize();
                fragVs.destroyCamera();
            }
        } else {
            if (fragVs1 != null) {
                fragVs1.preLeave();
                fragVs1.destroyCamera();
            }
        }
        if (fragAs != null) fragAs.preLeave();
        if (fragAs != null) {
            fragAs.destroyAudio();
        }

        if (fragAs2 != null) fragAs2.preLeave();

        if (fragAs2 != null) {
            fragAs2.destroyAudio();
            fragAs2.dismiss();
            fragAs2 = null;
        }

        //if (fragDs2 != null) fragDs2.preLeave();

        if (fragDs2 != null) {
            fragDs2.dismiss();
            fragDs2 = null;
        }

        audioCommon.stopSend();
        audioCommon.stopReceive();
        audioCommon.destroyVoe();

        Log.d("InfowareLab.Leave", "======>check 2");

        //清空视频路数
        //((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).clearMap();
        ((DocCommonImpl) CommonFactory.getInstance().getDocCommon()).clearDocs();
        if (isShare) {
            if (audioRecordUtils != null) {
                audioRecordUtils.pauseRecord();
                audioRecordUtils = null;
            }
            ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).stopDesktopShare();
            ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).setScreenInfo(0, 0, 0);
            if (Build.MODEL.indexOf("MV200") == -1
                    && Build.MODEL.indexOf("MRUT33") == -1) {

                if (fragVs != null) fragVs.recoverRatio();
            }
        }

        Log.d("InfowareLab.Leave", "======>check 3");

        exitHandler.sendEmptyMessage(0);

        exited = true;

        SharedPreferencesUrls.getInstance().putString("configId", "");
        SharedPreferencesUrls.getInstance().putString("confId", "");
    }

    private void stopGetNetStatus() {

        if (netCheckTask != null){
            netCheckTask.cancel();
            netCheckTask = null;
        }

        if (netCheckTimer != null) {
            netCheckTimer.purge();
            netCheckTimer.cancel();
            netCheckTimer = null;
        }
    }

    private boolean exited = false;
    private String mExitMessage = "";
    private boolean exitedDueToNetworkIssue = false;

    Handler exitHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {
            if (isAcLeave){
                //清空视频路数

                Log.d("InfowareLab.Leave", "======>check 4");

                //((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).clearMap();
                CommonFactory.getInstance().getConferenceCommon().leaveConference();
                Log.d("InfowareLab.Leave", "getConferenceCommon().leaveConference() end");
                isAcLeave = false;
            }

            Log.d("InfowareLab.Leave", "======>check 5");

            postDelayed(new Runnable() {
                @Override
                public void run() {
                    //CommonFactory.getInstance().getConferenceCommon().leaveConference();

                    if (loadingDialog != null && loadingDialog.isShowing()){
                        loadingDialog.dismiss();
                    }

                    Log.d("InfowareLab.Leave", "======>check 6");

                    Intent intent = new Intent(ActConf.this, ActHome.class);
                    if (mExitMessage.length() > 0) {
                        intent.putExtra("showExitMessage", true);
                        intent.putExtra("Message", mExitMessage);
                        intent.putExtra("exitedDueToNetworkIssue", exitedDueToNetworkIssue);
                        //intent.putExtra("isSyncVideoSelf", isSyncVideoSelf);
                        //intent.putExtra("isSyncAudioSelf", isSyncAudioSelf);
                        if (!exitedDueToNetworkIssue) conferenceCommon.saveMyVideoAudioSync(false, false);
                    }
                    else {
                        intent.putExtra("showExitMessage", false);
                        conferenceCommon.saveMyVideoAudioSync(false, false);
                    }

                    int actionMode = getIntent().getIntExtra("actionMode", -1);
                    if (actionMode < 0){
                        //Intent intent = new Intent(ActConf.this, ActHome.class);
                        startActivity(intent);
                    }

                    finish();
                }
            }, 500);

        }
    };

    @Override
    public void onCallParentView(String msg, Object obj) {
        switch (msg) {
            case BaseFragment.ACTION_VSSHARED:
                setViewHandler.removeCallbacksAndMessages(null);
                setViewHandler.sendEmptyMessage(0);
                break;
            case BaseFragment.ACTION_ASSHARED:
                setViewHandler.removeCallbacksAndMessages(null);
                setViewHandler.sendEmptyMessage(0);
                break;
            case BaseFragment.ACTION_DSSHARED:
                if (!existSecondScreen()) {
                    if (bujuType != ConferenceCommon.COMPONENT_TYPE_VIDEO) {
                        setViewHandler.removeCallbacksAndMessages(null);
                        setViewHandler.sendEmptyMessage(0);
                    }
                }
                else
                {
                    setViewHandler.removeCallbacksAndMessages(null);
                    setViewHandler.sendEmptyMessage(0);
                }
                break;
            case BaseFragment.ACTION_HIDE_VIDEO:
                if (setVideoLayout != null) {
                    UserBean self = userCommon.getSelf();
                    if (self != null) self.setHaveVideo(false);
                    setVideoLayout.setVisibility(View.GONE);
                }
                break;
            default:
                break;
        }
    }

    public Handler setViewHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {
            if (userCommon == null || userCommon.getSelf() == null || conferenceExit) {
                setView(-100);
                return;
            }
            if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                    || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                boolean isAsEmpty = fragAs == null || fragAs.isEmpty();
                if (bujuType != ConferenceCommon.COMPONENT_TYPE_VIDEO
                        || !isAsEmpty) {
                    setView(-100);
                } else {
                    boolean isDsEmpty = false;
                    if (!existSecondScreen()) {
                        validateFragDs();
                        isDsEmpty = fragDs == null || fragDs.isEmpty();
                    }
                    else
                    {
                        validateFragDs2();
                        isDsEmpty = fragDs2 == null || fragDs2.isEmpty();
                    }

                    if (!isAsEmpty) {
                        bujuType = ConferenceCommon.COMPONENT_TYPE_AS;
                    } else if (isDsEmpty) {
                        bujuType = ConferenceCommon.COMPONENT_TYPE_VIDEO;
                    }
                    if (Build.MODEL.indexOf("MV200") == -1
                            && Build.MODEL.indexOf("MRUT33") == -1) {
                        if (fragVs != null && !fragVs.isEmpty()) {
                            fragVs.retCamera();
                        }
                    } else {
                        if (fragVs1 != null && !fragVs1.isEmpty()) {
                            fragVs1.retCamera();
                        }
                    }
                    setView(bujuType);
                }
            } else {
                if (bujuType != ConferenceCommon.COMPONENT_TYPE_VIDEO || (fragAs != null && !fragAs.isEmpty())) {
                    setView(-100);
                } else {
                    setView(ConferenceCommon.COMPONENT_TYPE_VIDEO);
                }
            }
            hideLoading();
        }
    };
    Handler setPageHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {
            changeDSAS(msg.what);
            doSetHandler.sendEmptyMessage(20);
        }
    };

    Handler doSetHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {
            doSetHandler.removeCallbacksAndMessages(null);
            if (Build.MODEL.indexOf("MV200") == -1
                    && Build.MODEL.indexOf("MRUT33") == -1) {
                fragVs.doSetView();
            } else {
                fragVs1.doSetView();
            }

            Display display = getSecondScreen();
            if (display == null) {
                if (fragAs != null)
                    fragAs.doSetView();
                if (fragDs != null)
                    fragDs.doSetView();
            }
            else
            {
                if (fragAs2 != null)
                    fragAs2.doSetView();
                if (fragDs2 != null)
                    fragDs2.doSetView();
            }

        }
    };

    public void setView(int model) {
        if (isShare) return;

        if (conferenceExit) return;

        boolean flag = false;
        if (type == 3 || type == 5) {
            flag = true;
        } else {
            flag = false;
        }
        boolean isSingleVideo = false;
        boolean isVsEmpty = false;
        if (Build.MODEL.indexOf("MV200") == -1
                && Build.MODEL.indexOf("MRUT33") == -1) {
            if (fragVs != null) {
                isSingleVideo = fragVs.getMax();
            }
            isVsEmpty = fragVs == null || fragVs.isEmpty();
        } else {
            if (fragVs1 != null) {
                isSingleVideo = fragVs1.getMax();
            }
            isVsEmpty = fragVs1 == null || fragVs1.isEmpty();
        }

        boolean isDsEmpty = false;
        boolean isAsEmpty = false;
        if (SharedPreferencesUrls.getInstance().getBoolean("flag", false)) {
            isDsEmpty = false;
            isAsEmpty = false;
            if (!existSecondScreen()) {
                validateFragAs();
                validateFragDs();
                isAsEmpty = fragAs == null || fragAs.isEmpty();
                isDsEmpty = fragDs == null || fragDs.isEmpty() || flag;
            }
            else
            {
                validateFragAs2();
                validateFragDs2();
                isAsEmpty = fragAs2 == null || fragAs2.isEmpty();
                isDsEmpty = fragDs2 == null || fragDs2.isEmpty() || flag;
            }

            SharedPreferencesUrls.getInstance().putBoolean("flag", false);
        } else {
            if ((userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                    || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER)
                    && bujuType == ConferenceCommon.COMPONENT_TYPE_DS) {

                if (!existSecondScreen()) {
                    validateFragDs();
                    isDsEmpty = fragDs == null || fragDs.isEmpty();
                }
                else
                {
                    validateFragDs2();
                    isDsEmpty = fragDs2 == null || fragDs2.isEmpty();
                }

            }else {

                if (!existSecondScreen()) {
                    validateFragDs();
                    isDsEmpty = fragDs == null || fragDs.isEmpty() || flag || isSingleVideo;
                }
                else
                {
                    validateFragDs2();
                    isDsEmpty = fragDs2 == null || fragDs2.isEmpty() || flag || isSingleVideo;
                }
            }

            if (!existSecondScreen()) {
                validateFragAs();
                isAsEmpty = fragAs == null || fragAs.isEmpty() || isSingleVideo;
            }
            else
            {
                validateFragAs2();
                isAsEmpty = fragAs2 == null || fragAs2.isEmpty() || isSingleVideo;
            }
        }
        if (-100 == model) {
            if (userCommon == null || userCommon.getSelf() == null) {
                userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
            }
            if (isVsEmpty) {
                if (!isAsEmpty) {
                    model = MODEL_AS;
                    if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        bujuType = ConferenceCommon.COMPONENT_TYPE_AS;
                    }else {
                        bujuType = -10086;
                    }
                } else if (!isDsEmpty) {
                    model = MODEL_DS;
                    if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        bujuType = ConferenceCommon.COMPONENT_TYPE_DS;
                    }else {
                        bujuType = -10086;
                    }
                } else {
                    model = MODEL_VS;
                    if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        bujuType = ConferenceCommon.COMPONENT_TYPE_VIDEO;
                    }else {
                        bujuType = -10086;
                    }
                }
            } else {
                if (!isAsEmpty) {
                    model = MODEL_AS_VS;
                    if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        bujuType = ConferenceCommon.COMPONENT_TYPE_AS;
                    }else {
                        bujuType = -10086;
                    }
                } else if (!isDsEmpty) {
                    model = MODEL_DS_VS;
                    if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        bujuType = ConferenceCommon.COMPONENT_TYPE_DS;
                    }else {
                        bujuType = -10086;
                    }
                } else {
                    model = MODEL_VS;
                    if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        bujuType = ConferenceCommon.COMPONENT_TYPE_VIDEO;
                    }else {
                        bujuType = -10086;
                    }
                }
            }
            if (isAsEmpty && isDsEmpty) {
                model = MODEL_VS;
                if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                        || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                    bujuType = ConferenceCommon.COMPONENT_TYPE_VIDEO;
                }else {
                    bujuType = -10086;
                }
            }
        } else {
            if (model == ConferenceCommon.COMPONENT_TYPE_VIDEO) {
                model = MODEL_VS;
            } else if (model == ConferenceCommon.COMPONENT_TYPE_DS) {
                if (isVsEmpty) {
                    model = MODEL_DS;
                } else {
                    model = MODEL_DS_VS;
                }
            } else if (model == ConferenceCommon.COMPONENT_TYPE_AS) {
                if (isVsEmpty) {
                    model = MODEL_AS;
                } else {
                    model = MODEL_AS_VS;
                }
            }
        }


        if (menuDialog != null){
            btnState();
        }

        if (!isShare) {
            if (model == curModel) return;
        }

        Log.d(TAG, ">>>>>ActConf.setView: model=" + model);
        Log.d(TAG, ">>>>>ActConf.setView: bujuType=" + bujuType);

        if (userCommon.getSelf().getRole() != UserCommon.ROLE_HOST
                && userCommon.getSelf().getRole() != UserCommon.ROLE_SPEAKER) {
            if (model == MODEL_DS_VS && layoutType == ConferenceCommon.COMPONENT_TYPE_VIDEO){
                Log.d(TAG, ">>>>>ActConf.setView: revised to MODEL_VS");
                model = MODEL_VS;
            }
        }

        LinearLayout.LayoutParams pleft = (LinearLayout.LayoutParams) flLeft.getLayoutParams();
        LinearLayout.LayoutParams pright = (LinearLayout.LayoutParams) flRight.getLayoutParams();
        switch (model) {
            case MODEL_VS:

                Log.d(TAG, ">>>>>ActConf.setView: MODEL_VS");

                cutline.setVisibility(View.GONE);
                flLeft.setVisibility(View.GONE);
                flRight.setVisibility(View.VISIBLE);

                if (Build.MODEL.indexOf("MV200") != -1
                        || Build.MODEL.indexOf("MRUT33") != -1) {
                    if (fragVs1 != null) {
                        fragVs1.changeRatio(videoCommon.getSyncMap().size(), MODEL_VS);
                    }
                }

                if (existSecondScreen()){

                    validateFragAs2();

                    //fragAs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;

                    if (fragAs2 != null && !fragAs2.isEmpty()) {
                        fragAs2.show();
                        if (fragDs2 != null) {
                            fragDs2.hide();
                            if (fragDs2.isEmpty()) {
                                fragDs2.dismiss();
                                fragDs2 = null;
                            }
                        }
                    }
                    else {
                        if (fragDs2 != null && fragDs2.isEmpty())
                        {
                            fragAs2.show();
                            fragDs2.hide();
                            fragDs2.dismiss();
                            fragDs2 = null;
                        }
                        else
                        {
                            validateFragDs2();

                            fragDs2.setiCallParentView(this);
                            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                                fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                            else
                                fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE

                            fragDs2.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);

                            fragDs2.initConfData();

                            //fragDs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;
                            fragDs2.show();
                            fragDs2.initBuju();

                            if (fragAs2 != null && fragAs2.isAdded()) {
                                fragAs2.hide();
                            }
                        }
                    }

//                    Window window = fragAs2.getWindow();
//                    WindowManager.LayoutParams lp = window.getAttributes();
//                    lp.gravity = Gravity.CENTER; // 居中位置
//                    lp.width = WindowManager.LayoutParams.MATCH_PARENT;
//                    lp.height = WindowManager.LayoutParams.MATCH_PARENT;
//                    window.setAttributes(lp);
//                    window.setWindowAnimations(R.style.DialogAnimation);

                }

                break;
            case MODEL_DS:
                Log.d(TAG, ">>>>>ActConf.setView: MODEL_DS");
                if (!existSecondScreen()) {
                    cutline.setVisibility(View.GONE);
                    flLeft.setVisibility(View.VISIBLE);
                    flRight.setVisibility(View.GONE);
                }
                else {
                    cutline.setVisibility(View.GONE);
                    flLeft.setVisibility(View.GONE);
                    flRight.setVisibility(View.VISIBLE);
                }
                break;
            case MODEL_AS:
                Log.d(TAG, ">>>>>ActConf.setView: MODEL_AS");
                if (!existSecondScreen()) {
                    cutline.setVisibility(View.GONE);
                    flLeft.setVisibility(View.VISIBLE);
                    flRight.setVisibility(View.GONE);
                }
                else
                {
                    cutline.setVisibility(View.GONE);
                    flLeft.setVisibility(View.GONE);
                    flRight.setVisibility(View.VISIBLE);
                }
                break;
            case MODEL_AS_VS:
                Log.d(TAG, ">>>>>ActConf.setView: MODEL_AS_VS");
                if (isShare || existSecondScreen()) {
                    cutline.setVisibility(View.GONE);
                    flLeft.setVisibility(View.GONE);
                    flRight.setVisibility(View.VISIBLE);
                } else {
                    cutline.setVisibility(View.VISIBLE);
                    flLeft.setVisibility(View.VISIBLE);
                    flRight.setVisibility(View.VISIBLE);
                    pleft.weight = 1;
                    pright.weight = 3;
                    if (Build.MODEL.indexOf("MV200") != -1
                            || Build.MODEL.indexOf("MRUT33") != -1) {
                        if (fragVs1 != null) {
                            fragVs1.changeRatio(videoCommon.getSyncMap().size(), MODEL_AS_VS);
                        }
                    }
                }
                break;
            case MODEL_DS_VS:
                Log.d(TAG, ">>>>>ActConf.setView: MODEL_DS_VS");

                if (isShare || existSecondScreen()) {
                    cutline.setVisibility(View.GONE);
                    flLeft.setVisibility(View.GONE);
                    flRight.setVisibility(View.VISIBLE);
                } else {

                    if (fragAs != null && fragAs.isAdded() && !fragAs.isHidden()){
                        fragmentManager = getSupportFragmentManager();
                        FragmentTransaction ft;
                        ft = fragmentManager.beginTransaction();
                        ft.show(fragDs).hide(fragAs);
                        ft.commitAllowingStateLoss();
                    }

                    cutline.setVisibility(View.VISIBLE);
                    flLeft.setVisibility(View.VISIBLE);
                    flRight.setVisibility(View.VISIBLE);
                    pleft.weight = 1;
                    pright.weight = 3;
                    if (Build.MODEL.indexOf("MV200") != -1
                            || Build.MODEL.indexOf("MRUT33") != -1) {
                        if (fragVs1 != null) {
                            fragVs1.changeRatio(videoCommon.getSyncMap().size(), MODEL_DS_VS);
                        }
                    }
                }

                break;
            default:
                break;
        }

//        if (model == MODEL_AS) {
//            pleft.weight = 0;
//            pright.weight = 0;
//            pleft.width = ViewGroup.LayoutParams.MATCH_PARENT;
//        }

        flLeft.setLayoutParams(pleft);
        flRight.setLayoutParams(pright);
        if (model == MODEL_AS || model == MODEL_AS_VS) {
            isRunPage = false;
            setPageHandler.sendEmptyMessage(1);
        } else if (model == MODEL_DS || model == MODEL_DS_VS) {
            isRunPage = true;
            setPageHandler.sendEmptyMessage(2);
        } else {
            isRunPage = false;
            setPageHandler.sendEmptyMessage(0);
        }

    }


    private int curLeftPage = 1;

    private void changeDSAS(int page) {
        if (isShare || curLeftPage == 2) {
            if (page == curLeftPage) return;
        }
        fragmentManager = getSupportFragmentManager();
        FragmentTransaction ft;
        ft = fragmentManager.beginTransaction();
        if (page == 1) {

            Display display = getSecondScreen();

            if (display == null) {

                if (fragAs == null) {
                    //                fragAs = new FragAs(this);
                    fragAs = new FragAs();
                    fragAs.setiCallParentView(this);
                    ft.add(R.id.fl_inconf_content_container_left, fragAs, "As");
                } else {
                    fragAs.setPM();
                    ft.show(fragAs);
                }

                if (fragDs != null && fragDs.isAdded()) {
                    ft.hide(fragDs);
                }
            }
            else
            {
                if (null == fragAs2) {
                    //            fragAs = new FragAs(this);
                    fragAs2 = new FragAs2(getApplicationContext(), display);
                    fragAs2.setiCallParentView(this);
                    if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                        fragAs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                    else
                        fragAs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE

                    fragAs2.initConfData();

                    //fragAs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;

                    fragAs2.show();
                    fragAs2.setPM();
                }
                else {

                    fragAs2.setiCallParentView(this);
                    if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                        fragAs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                    else
                        fragAs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE

                    fragAs2.initConfData();

                    //fragAs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;
                    fragAs2.show();
                    fragAs2.setPM();
                }

                if (fragDs2 != null && fragDs2.isAdded()) {
                    fragDs2.hide();
                }
            }
            curLeftPage = 1;
        } else if (page == 2) {
//            if (fragAs != null && fragAs.isAdded()) {
//                ft.hide(fragAs);
//            }
//            if (fragAs2 != null && fragAs2.isAdded()) {
//                fragAs2.dismiss();
//            }
//
//            if (fragDs == null) {
////                fragDs = new FragDs(this);
//                fragDs = new FragDs();
//                fragDs.setiCallParentView(this);
//                ft.add(R.id.fl_inconf_content_container_left, fragDs, "Doc");
//            } else {
//                ft.show(fragDs);
//                fragDs.initBuju();
//            }

            Display display = getSecondScreen();

            if (display == null) {

                if (fragDs == null) {
    //                fragDs = new FragDs(this);
                    fragDs = new FragDs();
                    fragDs.setiCallParentView(this);
                    fragDs.initConfData();
                    ft.add(R.id.fl_inconf_content_container_left, fragDs, "Doc");
                } else {
                    ft.show(fragDs);
                    fragDs.initBuju();
                }

                if (fragAs != null && fragAs.isAdded()) {
                    ft.hide(fragAs);
                }
            }
            else
            {
                if (null == fragDs2) {
                    //            fragDs = new fragDs(this);
                    fragDs2 = new FragDs2(getApplicationContext(), display);
                    fragDs2.setiCallParentView(this);
                    if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                        fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                    else
                        fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE

                    fragDs2.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);

                    fragDs2.initConfData();

                    //fragDs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;
                    fragDs2.show();
                    fragDs2.initBuju();
                }
                else {

                    fragDs2.setiCallParentView(this);
                    if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                        fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                    else
                        fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE

                    fragDs2.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);

                    fragDs2.initConfData();

                    //fragDs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;
                    fragDs2.show();
                    fragDs2.initBuju();
                }

                if (fragAs2 != null && fragAs2.isAdded()) {
                    fragAs2.hide();
                }
            }

            curLeftPage = 2;
        }
//        ft.commitAllowingStateLoss();
        /**
         * 解决IllegalStateException: Can not perform this action after onSaveInstanceState
         * 如果在保存玩状态后再给它添加Fragment就会出错。解决办法就
         * 是把commit（）方法替换成 commitAllowingStateLoss()就行了，其效果是一样的
         * */
        ft.commitAllowingStateLoss();
    }

    @Override
    protected void onPause() {
        Log.d("InfowareLab.Debug", "ActConf.onPause");
        super.onPause();
    }

    @Override
    protected void onDestroy() {

        Log.d("InfowareLab.Debug", "ActConf.onDestroy");

        stopGetNetStatus();

        if (!exited && mConfId != null){
            Log.d("InfowareLab.Debug", "ActConf.destroy without exiting!!!!");
            SharedPreferencesUrls.getInstance().putString("confId", mConfId);
        }

        try {
            if (audioReceiver != null)
                unregisterReceiver(audioReceiver);
        }
        catch (java.lang.IllegalArgumentException exception){

        }

        SharedPreferencesUrls.getInstance().putInt("" + XMLUtils.CONFIGID + "_bujuType", -10086);
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismiss();
            loadingDialog = null;
        }
        if (menuDialog != null) {
            menuDialog.dismiss();
            menuDialog = null;
        }
        if (userCommon != null && userCommon.isHost()) {
            SharedPreferencesUrls.getInstance().putBoolean("mic", false);
        } else {
            SharedPreferencesUrls.getInstance().putBoolean("mic", true);
        }
        lastRole = -100;
        EventBus.getDefault().unregister(this);

        if (exited) {
            //清空视频路数
            if (videoCommon != null) videoCommon.clearMap();
            if (userCommon != null) userCommon.cleanUserList();
        }

        if (Build.MODEL.indexOf("MV200") == -1
                && Build.MODEL.indexOf("MRUT33") == -1) {
            if (fragVs != null) {
                fragVs.destroyCamera();
            }
        } else {
            if (fragVs1 != null) {
                fragVs1.destroyCamera();
            }
        }
        if (audioRecordUtils != null) {
            audioRecordUtils.pauseRecord();
            audioRecordUtils = null;
        }
        SharedPreferencesUrls.getInstance().putString("configId", "");
        mActivity = null;
        SharedPreferencesUrls.getInstance().putBoolean("sound", true);

        if (conferenceCommon != null)
            conferenceCommon.setAcceptSharingDesktop(false);

        isInMeeting = false;

        super.onDestroy();
    }

    @Override
    public void setMicListener() {
        curPosition = 0;
        FragmentTransaction ft = fragmentManager.beginTransaction();
        if (fragMic == null) {
//            fragMic = new FragMic(this);
            fragMic = new FragMic();
            fragMic.setiCallParentView(this);
            ft.add(R.id.fl_inconf_menu, fragMic, "Mic");
        } else {
            fragMic.initFouse();
            ft.show(fragMic);
        }
        ft.commitAllowingStateLoss();
    }

    @Override
    public void setSoundListener() {
        curPosition = 1;
        FragmentTransaction ft = fragmentManager.beginTransaction();
        if (fragSound == null) {
//            fragSound = new FragSound(this);
            fragSound = new FragSound();
            fragSound.setiCallParentView(this);
            ft.add(R.id.fl_inconf_menu, fragSound, "Sound");
        } else {
            fragSound.initFocus();
            ft.show(fragSound);
        }
        ft.commitAllowingStateLoss();
    }

    //关闭麦克风
    @Override
    public void micOff() {
        setMicStatus(true);
    }

    //打开麦克风
    @Override
    public void micOn() {
        setMicStatus(false);
    }

    @Override
    public void selectDoc() {
        curPosition = 2;
//        FragmentTransaction ft = fragmentManager.beginTransaction();
//        if (fragDos == null) {
//            fragDos = new DocListFragment(this);
//            ft.add(R.id.fl_inconf_menu, fragDos, "doc");
//        } else {
//            fragDos.onLosefocus();
//            ft.show(fragDos);
//            fragDos.update();
//        }
//        ft.commitAllowingStateLoss();
    }

    @Override
    public void shareDS(boolean isShare) {
        Log.d("InfowareLab.Debug","ActConf.shareDS: " + isShare);
        this.isShare = isShare;
        FragmentTransaction ft = fragmentManager.beginTransaction();
        curPosition = -1;
        if (isShare) {
            bujuType = ConferenceCommon.COMPONENT_TYPE_AS;
            flLeft.setVisibility(View.GONE);
            flRight.setVisibility(View.GONE);
            if (fragAs != null && fragAs.isAdded() && !fragAs.isHidden()) {
                ft.hide(fragAs);
            }
            if (fragAs2 != null && fragAs2.isAdded() && !fragAs2.isHidden()) {
                fragAs2.dismiss();
            }
            if (Build.MODEL.indexOf("MV200") == -1
                    && Build.MODEL.indexOf("MRUT33") == -1) {
                if (fragVs == null) {
//                    fragVs = new FragVs(this);
                    fragVs = new FragVs();
                    fragVs.setiCallParentView(this);
                    ft.add(R.id.fl_inconf_content_container_right, fragVs, "Video");
                } else {
                    ft.show(fragVs);
                }
                if (fragVs != null) {
                    fragVs.setShare(true);
                    fragVs.retCamera();
                    flRight.setVisibility(View.VISIBLE);
                }
            } else {
                if (fragVs1 == null) {
//                    fragVs1 = new FragVs1(this);
                    fragVs1 = new FragVs1();
                    fragVs1.setiCallParentView(this);
                    ft.add(R.id.fl_inconf_content_container_right, fragVs1, "Video");
                } else {
                    ft.show(fragVs1);
                }
                if (fragVs1 != null) {
                    fragVs1.setShare(true);
                    Runnable r = new Runnable() {
                        @Override
                        public void run() {
                            if (curR1 != this) return;
                            fragVs1.retCamera();
                            flRight.setVisibility(View.VISIBLE);
                        }
                    };
                    curR1 = r;

                    new Handler().postDelayed(r, 100);

                    //setViewHandler.postDelayed(r, 800);
                }
            }
//            if (audioRecordUtils == null) {
//                audioRecordUtils = new AudioRecordUtils(MediaRecorder.AudioSource.UNPROCESSED);
//                audioRecordUtils.startRecord();
//            }

            //lastLayoutType = conferenceCommon.getCurrentLayoutType();

            ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).startDesktopShare(15, 20);
        } else {
            if (Build.MODEL.indexOf("MV200") == -1
                    && Build.MODEL.indexOf("MRUT33") == -1) {
                if (fragVs != null) {
                    fragVs.setShare(false);
                    fragVs.retCamera();
                    fragVs.recoverRatio();
                }
            } else {
                if (fragVs1 != null) {
                    fragVs1.setShare(false);
                    fragVs1.retCamera();
                    fragVs1.recoverRatio();
                }
            }
//            if (audioRecordUtils != null) {
//                audioRecordUtils.pauseRecord();
//                audioRecordUtils = null;
//            }

            layoutType = ConferenceCommon.COMPONENT_TYPE_VIDEO;
            ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).stopDesktopShare();
            ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).setScreenInfo(0, 0, 0);
            setViewHandler.removeCallbacksAndMessages(null);
            setViewHandler.sendEmptyMessage(0);
        }
        ft.commitAllowingStateLoss();
    }

    //视频操作
    @Override
    public void videoPre() {
        curPosition = -1;
        showDialog();
    }

    public void setMicStatus(boolean isOff) {
        if (isOff) {
            if (SharedPreferencesUrls.getInstance().getBoolean("isOpenMic", false)) {
                showShortToast(R.string.mic_off_title);
                if (audioCommon != null) {
                    audioCommon.stopSend();
                }
                rbMic.setChecked(false);
                SharedPreferencesUrls.getInstance().putBoolean("isOpenMic", false);
            }
        } else {
            if (!SharedPreferencesUrls.getInstance().getBoolean("isOpenMic", false)) {
                rbMic.setChecked(true);
                showShortToast(R.string.mic_on_title);
                if (audioCommon != null) {
                    audioCommon.startReceive();
                    audioCommon.startSend();
                }
                SharedPreferencesUrls.getInstance().putBoolean("isOpenMic", true);
            }
        }
    }

    @Override
    public void allMak() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
        } else {
            audioManager.setMode(AudioManager.MODE_IN_CALL);
        }
        Log.d("InfowareLab.Audio", "allMak: audioManager.setSpeakerphoneOn: false");
        audioManager.setSpeakerphoneOn(false);//false走usb麦，true走hdmi
        SharedPreferencesUrls.getInstance().putBoolean("speakerphoneon", false);
    }

    @Override
    public void TVMak() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
        } else {
            audioManager.setMode(AudioManager.MODE_IN_CALL);
        }
        Log.d("InfowareLab.Audio", "TVMak: audioManager.setSpeakerphoneOn: true");
        audioManager.setSpeakerphoneOn(true);//false走usb麦，true走hdmi
        SharedPreferencesUrls.getInstance().putBoolean("speakerphoneon", true);
    }

    int uid = 0;
    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void Event(MessageEvent messageEvent) {
        switch (messageEvent.getType()) {
//            case 0:
//                Log.e("YYYYYY","视频桌面分享权限：："+messageEvent.isbAS());
//                SharedPreferencesUrls.getInstance().putBoolean("bAS", messageEvent.isbAS());
//                if (menuDialog != null) {
//                    btnState();
//                }
//                if (!messageEvent.isbAS() && isShare) {
//                    if (audioRecordUtils != null) {
//                        audioRecordUtils.pauseRecord();
//                        audioRecordUtils = null;
//                    }
//                    if (Build.MODEL.indexOf("MV200") == -1
//                            && Build.MODEL.indexOf("MRUT33") == -1) {
//                        if (fragVs != null) {
//                            fragVs.setShare(false);
//                            fragVs.retCamera();
//                            fragVs.recoverRatio();
//                        }
//                    } else {
//                        if (fragVs1 != null) {
//                            fragVs1.setShare(false);
//                            fragVs1.retCamera();
//                        }
//                    }
//                    ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).stopDesktopShare();
//                    ((ShareDtCommonImpl) CommonFactory.getInstance().getSdCommon()).setScreenInfo(0, 0, 0);
//                    isShare = false;
//                }
//                break;
            case 1:
                if (dialog != null) {
                    dialog.refreshData();
                }
                if (userCommon != null && userCommon.getSelf() != null ){
                    if (isInform && (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST
                            || userCommon.getSelf().getRole() == UserCommon.ROLE_SPEAKER)){
                        switch (bujuType){
                            case ConferenceCommon.COMPONENT_TYPE_VIDEO:
                                //平铺
                                conferenceCommon.setLayout(ConferenceCommon.COMPONENT_TYPE_VIDEO, ConferenceCommon.RT_CONF_UI_TYPE_LIST_VIDEO);
                                videoCommon.setVideoLayout(0);
                                videoCommon.setCurLayoutMode(VideoCommon.LayoutMode.MODE_PLAIN);
                                break;
                            default:
                                break;
                        }
                        isInform = false;
                    }
                }
                break;
            //公告
            case 2:

                String strText = messageEvent.getStrText();
                Log.d("InfowareLab.Subtitles","Event() => strText = " + strText);

                if (strText != null && strText.length() > 0){

                    Log.d("InfowareLab.Subtitles", "Event() => show subtitle");

                    mSubtitleView.setText(strText);
                    mSubtitleView.setVisibility(View.VISIBLE);
                }
                else{
                    mSubtitleView.setText("");
                    mSubtitleView.setVisibility(View.GONE);
                }

                break;
            //手否是第一次进会
            case 3:

                break;
            //主讲模式
            case 4:
                if (videoCommon == null) {
                    videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
                }

                if (messageEvent.getCurSpeakerId() > 0) {

                    //主讲模式
                    videoCommon.setCurLayoutMode(VideoCommon.LayoutMode.MODE_SPEAKER);
                    if (Build.MODEL.indexOf("MV200") == -1
                            && Build.MODEL.indexOf("MRUT33") == -1) {
                        if (fragVs != null) {
                            fragVs.setSpeak(messageEvent.getCurSpeakerId());
                            SharedPreferencesUrls.getInstance().putInt("curSpeakerId", -1);
                        } else {
                            SharedPreferencesUrls.getInstance().putInt("curSpeakerId", messageEvent.getCurSpeakerId());
                        }
                    } else {
                        if (fragVs1 != null) {
                            fragVs1.setSpeak(messageEvent.getCurSpeakerId());
                            SharedPreferencesUrls.getInstance().putInt("curSpeakerId", -1);
                        } else {
                            SharedPreferencesUrls.getInstance().putInt("curSpeakerId", messageEvent.getCurSpeakerId());
                        }
                    }
                } else if (messageEvent.getCurSpeakerId() == -1) {
                    //语音激励模式
                    //取第一个元素
                    videoCommon.setCurLayoutMode(VideoCommon.LayoutMode.MODE_VOICE);
                    for (Integer key : videoCommon.getSyncMap().keySet()) {
                        if (0 == uid) {
                            uid = videoCommon.getSyncMap().get(key);
                            setViewHandler.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    if (Build.MODEL.indexOf("MV200") == -1
                                            && Build.MODEL.indexOf("MRUT33") == -1) {
                                        if (fragVs != null) fragVs.setMaxVoice(uid);
                                    } else {
                                        if (fragVs1 != null) fragVs1.setMaxVoice(uid);
                                    }
                                }
                            }, 1500);
                            return;
                        }
                    }
                } else {
                    if (Build.MODEL.indexOf("MV200") == -1
                            && Build.MODEL.indexOf("MRUT33") == -1) {

                        if (fragVs != null) fragVs.retCamera();
                    } else {
                        if (fragVs1 != null) fragVs1.retCamera();
                    }
                }
                break;
            //轮循的路数
            case 5:
                int loopNum = messageEvent.getLoopNum();

                break;
            //socket连接状态
            case 6:
                break;
            //界面切换
            case 7:
                layoutType = messageEvent.getLayType();

                Log.d(TAG, ">>>>>layout change: layoutType="+layoutType);

                if (bujuType == layoutType) {
                    return;
                }
                bujuType = layoutType;
                mCurrentLayoutType = bujuType;

                SharedPreferencesUrls.getInstance().putInt("" + XMLUtils.CONFIGID + "_bujuType", messageEvent.getLayType());
//                setViewHandler.removeCallbacksAndMessages(null);
//                setViewHandler.sendEmptyMessage(0);
                break;
            //刷新设备列表
            case 8:
                if (mActivity != null && SharedPreferencesUrls.getInstance().getBoolean("isPopup1", false)) {
                    isShow = false;
                    if (loadingDialog != null && loadingDialog.isShowing()) {
                        loadingDialog.dismiss();
                    }
                    showInviteDialog();
                }
                break;
            //本地通道
            case 9:
                //保存本地通道
                Log.d("InfwareLab.Debug","ActConf.Event(Save Local Channel): " + messageEvent.getLocalChannelID());
                SharedPreferencesUrls.getInstance().putInt("localChannelID",messageEvent.getLocalChannelID());
//                if (userCommon == null)
//                    userCommon = (UserCommonImpl) CommonFactory.getInstance().getUserCommon();
//                if (videoCommon == null)
//                    videoCommon = (VideoCommonImpl) CommonFactory.getInstance().getVideoCommon();
//
//                if (userCommon.getSelf().getRole() == UserCommon.ROLE_HOST){
//                    TimerTask task = new TimerTask() {
//                        @Override
//                        public void run() {
//                            videoCommon.openVideo(messageEvent.getLocalChannelID(), null);
//                            videoCommon.setSyncVedio(messageEvent.getLocalChannelID(), true);
//                        }
//                    };
//                    Timer timer = new Timer();
//                    timer.schedule(task, 2 * 1000);
//                }
                break;
            case 11: //stop desktop sharing
                if (isShare) {
                    shareDS(false);
                }
                break;
//          case 12: //start desktop sharing
//                if (!isShare) {
//                    shareDS(true);
//                }
//                break;
            case 13: //net status update

                if (mNetStatusView != null) {
                    int netStatus = messageEvent.getNetStatus();

                    if (netStatus == 0)
                        mNetStatusView.setImageResource(R.drawable.net_good);
                    else if (netStatus == 1)
                        mNetStatusView.setImageResource(R.drawable.net_bad);
                    else if (netStatus == 2)
                        mNetStatusView.setImageResource(R.drawable.net_critical);
                }

                break;

            //主持人请求打开视音频
            case 22:
                requestType = messageEvent.getRequestType();
                requestData2 = messageEvent.getData2();
                Log.d("InfowareLab.Request", "onResponse: requestType = " +  requestType);
                Log.d("InfowareLab.Request", "onResponse: requestData2 = " +  requestData2);

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (requestType == 2)
                            showRequestVideoDialog(requestType, requestData2, messageEvent.getUserID(), messageEvent.getChannelID());
                        else if (requestType == 1)
                            showRequestAudioDialog(requestType, requestData2, messageEvent.getUserID(), messageEvent.getChannelID());

                    }
                });
                break;

            //收到请求打开视音频回应
            case 23:
                requestType = messageEvent.getRequestType();
                Log.d("InfowareLab.Request", "onResponse: requestType = " +  requestType);

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {

                        if (messageEvent.getAcceptSync() > 0){
                            if (requestType == 2){
                                videoCommon.openVideo(messageEvent.getChannelID(), (SurfaceView) null);
                                videoCommon.setSyncVedio(messageEvent.getChannelID(), true);

                                if (dialog != null) dialog.refreshStatus(messageEvent.getChannelID(), true);
//                                if (fragVideo != null) {
//                                    if (fragVideo.getRequestType() == 1)
//                                        fragVideo.syncVideo(messageEvent.getChannelID());
//                                    else
//                                        fragVideo.viewRemoteVideo(messageEvent.getChannelID());
//                                }
                            }
                            else if (requestType == 1){
                                if (audioCommon != null) audioCommon.openAudio(messageEvent.getUserID());
                            }
                        }
                        else
                        {
                            UserBean userBean = userCommon.getUser(messageEvent.getUserID());

                            if (userBean != null){
                                String userName = userBean.getUsername();
                                //showShortToast(R.string.hst_reject_message);
                                String message = userName + getResources().getString(R.string.hst_reject_video_message);

                                if (requestType == 1){
                                    message = userName + getResources().getString(R.string.hst_reject_audio_message);
                                }

                                Toast toast = Toasty.custom(ActConf.this, message, getResources().getDrawable(R.drawable.ic_check_white_24dp),
                                        getResources().getColor(R.color.black), getResources().getColor(R.color.errorColor),
                                        Toast.LENGTH_SHORT, false, true);

                                toast.setGravity(Gravity.CENTER, 0, 0);

                                toast.show();
                            }
                        }
                    }
                });
                break;
//            case 24:
//                int userId = messageEvent.getUserID();
//                boolean handUp = messageEvent.isHandUp();
//                Log.d("InfowareLab.Hand", "onHandUp: handUp = " +  handUp);
//
//                UserBean userBean = userCommon.getUser(userId);
//
//                if (userBean != null){
//
//                    if (userBean.isHandUp() != handUp) {
//                        userBean.setHandUp(handUp);
//
//                        if (fragAtt != null && fragAtt.isAdded()){
//                            fragAtt.refreshList();
//                        }
//                    }
//                }

            default:
                break;
        }
    }

    @Override
    public void onDocItemListener(int docId) {
        bujuType = ConferenceCommon.COMPONENT_TYPE_DS;

        Display display = getSecondScreen();

        if (display == null) {

            fragmentManager = getSupportFragmentManager();
            FragmentTransaction ft;
            ft = fragmentManager.beginTransaction();
            //        if (fragDos != null && fragDos.isAdded() && !fragDos.isHidden()) {
            //            ft.hide(fragDos);
            //        }
            if (fragDs == null) {
                //            fragDs = new FragDs(this);
                fragDs = new FragDs();
                fragDs.setiCallParentView(this);
                ft.add(R.id.fl_inconf_content_container_left, fragDs, "Doc");
            } else {
                ft.show(fragDs);
                fragDs.initBuju();
            }
            fragDs.switchDoc(docId);
            curLeftPage = 2;
            ft.commitAllowingStateLoss();
        }
        else {
            if (null == fragDs2) {
                //            fragDs = new fragDs(this);
                fragDs2 = new FragDs2(getApplicationContext(), display);
                fragDs2.setiCallParentView(this);
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                    fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                else
                    fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE

                fragDs2.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);

                fragDs2.initConfData();
                //fragDs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;
                fragDs2.show();
                fragDs2.initBuju();
            }
            else {

                fragDs2.setiCallParentView(this);
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                    fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                else
                    fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE

                fragDs2.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);

                fragDs2.initConfData();
                //fragDs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;
                fragDs2.show();
                fragDs2.initBuju();
            }

            fragDs2.switchDoc(docId);
            curLeftPage = 2;
        }

        curPosition = -1;
    }

    //关闭文档选择页
    @Override
    public void onCloseListener() {
//        fragmentManager = getSupportFragmentManager();
//        FragmentTransaction ft;
//        ft = fragmentManager.beginTransaction();
//        if (fragDos != null && fragDos.isAdded() && !fragDos.isHidden()) {
//            ft.hide(fragDos);
//        }
//        ft.commitAllowingStateLoss();
        curPosition = -1;
    }

    //刷新文档列表
    public void update() {
        if (menuDialog != null && menuDialog.isShowing()) {
            btnState();
        }
    }

    //刷新文档按钮
    private void btnState() {
        //Log.d("InfowareLab.Debug", "btnState()");
        if (docListDialog != null) {
            docListDialog.refreshDocList();
            if (((DocCommonImpl) CommonFactory.getInstance().getDocCommon()).getDocMapList().size() <= 1) {
                if (docListDialog.isShowing()) {
                    docListDialog.dismiss();
                }
            }
        }
        if (micLayout != null) {
            if ( ((AudioCommonImpl) CommonFactory.getInstance().getAudioCommon()).isMICWork()) {
                if (1 == XMLUtils.CONFERENCEPATTERN) {
                    micLayout.setVisibility(View.VISIBLE);
                } else {
                    if (((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_HOST
                            || ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                        micLayout.setVisibility(View.VISIBLE);
                    } else {
                        if (((AudioCommonImpl) CommonFactory.getInstance().getAudioCommon()).isMICWork())
                            micLayout.setVisibility(View.VISIBLE);
                        else
                            micLayout.setVisibility(View.GONE);
                    }
                }
            } else {
                micLayout.setVisibility(View.GONE);
            }
        }
        if (docLayout != null) {
            if (((DocCommonImpl) CommonFactory.getInstance().getDocCommon()).getDocMapList().size() > 1
                    && mCurrentLayoutType == ConferenceCommon.COMPONENT_TYPE_DS) {
                if (((DocCommonImpl) CommonFactory.getInstance().getDocCommon()).getPrivateShareDocPriviledge()) {
                    if (1 == XMLUtils.CONFERENCEPATTERN) {
                        Log.d(TAG, ">>>>>ActConf.btnState: show docLayout due to XMLUtils.CONFERENCEPATTERN = 1");
                        docLayout.setVisibility(View.VISIBLE);
                    } else {
                        if (((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_HOST
                                || ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                            docLayout.setVisibility(View.VISIBLE);
                        } else {
                            docLayout.setVisibility(View.GONE);
                        }
                    }
                } else {
                    docLayout.setVisibility(View.GONE);
                }
            } else {
                docLayout.setVisibility(View.GONE);
            }
        }
        if (flowLayout != null) {
            if ( Build.MODEL.indexOf("MV200") != -1
                    || Build.MODEL.indexOf("YT-500") != -1 || Build.MODEL.indexOf("I-5300") != -1) {
//                if (SharedPreferencesUrls.getInstance().getBoolean("bAS", false)) {
//                    flowLayout.setVisibility(View.VISIBLE);
//                    if (isShare){
//                        flowText.setText(getResources().getString(R.string.stop_share));
//                    }else {
//                        flowText.setText(getResources().getString(R.string.flow_title));
//                    }
//                } else {
//                    flowLayout.setVisibility(View.GONE);
//                }

//                if (userCommon != null && !userCommon.isASPriviledge()){
//                    flowLayout.setVisibility(View.GONE);
//                }
//                else{
                    //修改逻辑
                    if (isShare) {
                        if (userCommon != null && userCommon.isASPriviledge())
                        //if (((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).isVideoPreviewPriviledge())
                        {
                            flowLayout.setVisibility(View.VISIBLE);
                            flowText.setText(getResources().getString(R.string.stop_share));
                        } else {
                            shareDS(false);
                            flowText.setText(getResources().getString(R.string.flow_title));
                            flowLayout.setVisibility(View.GONE);
                        }

                    } else {

                        if (userCommon != null && userCommon.isASPriviledge())
                        //if (((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).isVideoPreviewPriviledge())
                        {
                            flowText.setText(getResources().getString(R.string.flow_title));

                            if (fragAs != null && fragAs.isAdded() && !fragAs.isHidden()
                                    && !fragAs.isEmpty()) {
                                flowLayout.setVisibility(View.GONE);
                            } else {
                                flowLayout.setVisibility(View.VISIBLE);
                            }
                        } else
                            flowLayout.setVisibility(View.GONE);
                    }
                //}
            } else {
                flowLayout.setVisibility(View.GONE);
            }
        }

        //

        if (videoLayout != null) {
            if (((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).isHost()) {
                videoLayout.setVisibility(View.VISIBLE);
            } else {
                videoLayout.setVisibility(View.GONE);
            }
        }
        if (soundLayout != null) {
            if (((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_HOST) {
                soundLayout.setVisibility(View.VISIBLE);
            } else {
                soundLayout.setVisibility(View.GONE);
            }
        }
        if (setVideoLayout != null) {
            if (1 == XMLUtils.CONFERENCEPATTERN) {
                if (((VideoCommonImpl) CommonFactory.getInstance().getVideoCommon()).isVideoPreviewPriviledge()) {
                    if (fragVs != null && fragVs.existCamera())
                        setVideoLayout.setVisibility(View.VISIBLE);
                    rbMicSet.setFocusable(true);
                    if (videoCommon.getSyncMap().containsValue(userCommon.getSelf().getUid())) {
                        rbMicSet.setChecked(true);
                    } else {
                        rbMicSet.setChecked(false);
                    }
                }
                else
                    setVideoLayout.setVisibility(View.GONE);
            } else {
                setVideoLayout.setVisibility(View.GONE);
            }
        }
        if (inviteLayout != null) {
            if (((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_HOST) {
                inviteLayout.setVisibility(View.VISIBLE);
            } else {
                inviteLayout.setVisibility(View.GONE);
            }
        }
        if (distributionLayout != null) {
            if (((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_HOST
                    || ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_SPEAKER) {
                distributionLayout.setVisibility(View.VISIBLE);
            } else {
                distributionLayout.setVisibility(View.GONE);
            }
        }
        if (logoutBtn != null) {
            if (micLayout == null || micLayout.getVisibility() == View.GONE) {
                logoutBtn.setFocusable(true);
                logoutBtn.setChecked(true);
                logoutBtn.requestFocus();
            } else {
                rbMic.setFocusable(true);
                rbMic.requestFocus();
                rbMic.setChecked(SharedPreferencesUrls.getInstance().getBoolean("isOpenMic", true));
            }
        }
        //麦克风左右焦点
        if (micLayout != null && micLayout.getVisibility() == View.VISIBLE) {
            //左焦点
            rbMic.setNextFocusLeftId(R.id.frag_menu_logoutBtn);
            //右焦点
            if (soundLayout != null && soundLayout.getVisibility() == View.VISIBLE) {
                rbMic.setNextFocusRightId(R.id.rb_sound);
                rbSound.setNextFocusLeftId(R.id.rb_mic);
            } else if (setVideoLayout != null && setVideoLayout.getVisibility() == View.VISIBLE) {
                rbMic.setNextFocusRightId(R.id.rb_mic_set);
                rbMicSet.setNextFocusLeftId(R.id.rb_mic);
            } else if (distributionLayout != null && distributionLayout.getVisibility() == View.VISIBLE) {
                rbMic.setNextFocusRightId(R.id.rb_sound_set);
                rbSoundSet.setNextFocusLeftId(R.id.rb_mic);
            } else if (docLayout != null && docLayout.getVisibility() == View.VISIBLE) {
                rbMic.setNextFocusRightId(R.id.frag_menu_docBtn);
                docBtn.setNextFocusLeftId(R.id.rb_mic);
            } else if (flowLayout != null && flowLayout.getVisibility() == View.VISIBLE) {
                rbMic.setNextFocusRightId(R.id.frag_menu_flowBtn);
                flowBtn.setNextFocusLeftId(R.id.rb_mic);
            } else if (videoLayout != null && videoLayout.getVisibility() == View.VISIBLE) {
                rbMic.setNextFocusRightId(R.id.frag_menu_videoBtn);
                videoBtn.setNextFocusLeftId(R.id.rb_mic);
            } else {
                rbMic.setNextFocusRightId(R.id.frag_menu_chatBtn);
                chatBtn.setNextFocusLeftId(R.id.rb_mic);
            }
        }
        //全体静音左右焦点
        if (soundLayout != null && soundLayout.getVisibility() == View.VISIBLE) {
            //左焦点
            if (micLayout != null && micLayout.getVisibility() == View.VISIBLE) {
                rbSound.setNextFocusLeftId(R.id.rb_mic);
                rbMic.setNextFocusRightId(R.id.rb_sound);
            } else {
                rbSound.setNextFocusLeftId(R.id.frag_menu_logoutBtn);
                logoutBtn.setNextFocusRightId(R.id.rb_sound);
            }
            //右焦点
            if (setVideoLayout != null && setVideoLayout.getVisibility() == View.VISIBLE) {
                rbSound.setNextFocusRightId(R.id.rb_mic_set);
                rbMicSet.setNextFocusLeftId(R.id.rb_sound);
            } else if (distributionLayout != null && distributionLayout.getVisibility() == View.VISIBLE) {
                rbSound.setNextFocusRightId(R.id.rb_sound_set);
                rbSoundSet.setNextFocusLeftId(R.id.rb_sound);
            } else if (docLayout != null && docLayout.getVisibility() == View.VISIBLE) {
                rbSound.setNextFocusRightId(R.id.frag_menu_docBtn);
                docBtn.setNextFocusLeftId(R.id.rb_sound);
            } else if (flowLayout != null && flowLayout.getVisibility() == View.VISIBLE) {
                rbSound.setNextFocusRightId(R.id.frag_menu_flowBtn);
                flowBtn.setNextFocusLeftId(R.id.rb_sound);
            } else if (videoLayout != null && videoLayout.getVisibility() == View.VISIBLE) {
                rbSound.setNextFocusRightId(R.id.frag_menu_videoBtn);
                videoBtn.setNextFocusLeftId(R.id.rb_sound);
            } else {
                rbSound.setNextFocusRightId(R.id.frag_menu_chatBtn);
                chatBtn.setNextFocusLeftId(R.id.rb_sound);
            }
        }
        //视频按钮左右焦点
        if (setVideoLayout != null && setVideoLayout.getVisibility() == View.VISIBLE) {
            //左焦点
            if (soundLayout != null && soundLayout.getVisibility() == View.VISIBLE) {
                rbMicSet.setNextFocusLeftId(R.id.rb_sound);
                rbSound.setNextFocusRightId(R.id.rb_mic_set);
            } else if (micLayout != null && micLayout.getVisibility() == View.VISIBLE) {
                rbMicSet.setNextFocusLeftId(R.id.rb_mic);
                rbMic.setNextFocusRightId(R.id.rb_mic_set);
            } else {
                rbMicSet.setNextFocusLeftId(R.id.frag_menu_logoutBtn);
                logoutBtn.setNextFocusRightId(R.id.rb_mic_set);
            }
            //右焦点
            if (distributionLayout != null && distributionLayout.getVisibility() == View.VISIBLE) {
                rbMicSet.setNextFocusRightId(R.id.rb_sound_set);
                rbSoundSet.setNextFocusLeftId(R.id.rb_mic_set);
            } else if (docLayout != null && docLayout.getVisibility() == View.VISIBLE) {
                rbMicSet.setNextFocusRightId(R.id.frag_menu_docBtn);
                docBtn.setNextFocusLeftId(R.id.rb_mic_set);
            } else if (flowLayout != null && flowLayout.getVisibility() == View.VISIBLE) {
                rbMicSet.setNextFocusRightId(R.id.frag_menu_flowBtn);
                flowBtn.setNextFocusLeftId(R.id.rb_mic_set);
            } else if (videoLayout != null && videoLayout.getVisibility() == View.VISIBLE) {
                rbMicSet.setNextFocusRightId(R.id.frag_menu_videoBtn);
                videoBtn.setNextFocusLeftId(R.id.rb_mic_set);
            } else {
                rbMicSet.setNextFocusRightId(R.id.frag_menu_chatBtn);
                chatBtn.setNextFocusLeftId(R.id.rb_mic_set);
            }
        }
        //布局按钮左右焦点
        if (distributionLayout != null && distributionLayout.getVisibility() == View.VISIBLE) {
            //左焦点
            if (setVideoLayout != null && setVideoLayout.getVisibility() == View.VISIBLE) {
                rbSoundSet.setNextFocusLeftId(R.id.rb_mic_set);
                rbMicSet.setNextFocusRightId(R.id.rb_sound_set);
            } else if (soundLayout != null && soundLayout.getVisibility() == View.VISIBLE) {
                rbSoundSet.setNextFocusLeftId(R.id.rb_sound);
                rbSound.setNextFocusRightId(R.id.rb_sound_set);
            } else if (micLayout != null && micLayout.getVisibility() == View.VISIBLE) {
                rbSoundSet.setNextFocusLeftId(R.id.rb_mic);
                rbMic.setNextFocusRightId(R.id.rb_sound_set);
            } else {
                rbSoundSet.setNextFocusLeftId(R.id.frag_menu_logoutBtn);
                logoutBtn.setNextFocusRightId(R.id.rb_sound_set);
            }
            //右焦点
            if (docLayout != null && docLayout.getVisibility() == View.VISIBLE) {
                rbSoundSet.setNextFocusRightId(R.id.frag_menu_docBtn);
                docBtn.setNextFocusLeftId(R.id.rb_sound_set);
            } else if (flowLayout != null && flowLayout.getVisibility() == View.VISIBLE) {
                rbSoundSet.setNextFocusRightId(R.id.frag_menu_flowBtn);
                flowBtn.setNextFocusLeftId(R.id.rb_sound_set);
            } else if (videoLayout != null && videoLayout.getVisibility() == View.VISIBLE) {
                rbSoundSet.setNextFocusRightId(R.id.frag_menu_videoBtn);
                videoBtn.setNextFocusLeftId(R.id.rb_sound_set);
            } else {
                rbSoundSet.setNextFocusRightId(R.id.frag_menu_chatBtn);
                chatBtn.setNextFocusLeftId(R.id.rb_sound_set);
            }
        }
        //文档选择按钮左右焦点
        if (docLayout != null && docLayout.getVisibility() == View.VISIBLE) {
            //左焦点
            if (distributionLayout != null && distributionLayout.getVisibility() == View.VISIBLE) {
                docBtn.setNextFocusLeftId(R.id.rb_sound_set);
                rbSoundSet.setNextFocusRightId(R.id.frag_menu_docBtn);
            } else if (setVideoLayout != null && setVideoLayout.getVisibility() == View.VISIBLE) {
                docBtn.setNextFocusLeftId(R.id.rb_mic_set);
                rbMicSet.setNextFocusRightId(R.id.frag_menu_docBtn);
            } else if (soundLayout != null && soundLayout.getVisibility() == View.VISIBLE) {
                docBtn.setNextFocusLeftId(R.id.rb_sound);
                rbSound.setNextFocusRightId(R.id.frag_menu_docBtn);
            } else if (micLayout != null && micLayout.getVisibility() == View.VISIBLE) {
                docBtn.setNextFocusLeftId(R.id.rb_mic);
                rbMic.setNextFocusRightId(R.id.frag_menu_docBtn);
            } else {
                docBtn.setNextFocusLeftId(R.id.frag_menu_logoutBtn);
                logoutBtn.setNextFocusRightId(R.id.frag_menu_docBtn);
            }
            //右焦点
            if (flowLayout != null && flowLayout.getVisibility() == View.VISIBLE) {
                docBtn.setNextFocusRightId(R.id.frag_menu_flowBtn);
                flowBtn.setNextFocusLeftId(R.id.frag_menu_docBtn);
            } else if (videoLayout != null && videoLayout.getVisibility() == View.VISIBLE) {
                docBtn.setNextFocusRightId(R.id.frag_menu_videoBtn);
                videoBtn.setNextFocusLeftId(R.id.frag_menu_docBtn);
            } else {
                docBtn.setNextFocusRightId(R.id.frag_menu_chatBtn);
                chatBtn.setNextFocusLeftId(R.id.frag_menu_docBtn);
            }
        }
        //共享辅流按钮左右焦点
        if (flowLayout != null && flowLayout.getVisibility() == View.VISIBLE) {
            //左焦点
            if (docLayout != null && docLayout.getVisibility() == View.VISIBLE) {
                flowBtn.setNextFocusLeftId(R.id.frag_menu_docBtn);
                docBtn.setNextFocusRightId(R.id.frag_menu_flowBtn);
            } else if (distributionLayout != null && distributionLayout.getVisibility() == View.VISIBLE) {
                flowBtn.setNextFocusLeftId(R.id.rb_sound_set);
                rbSoundSet.setNextFocusRightId(R.id.frag_menu_flowBtn);
            } else if (setVideoLayout != null && setVideoLayout.getVisibility() == View.VISIBLE) {
                flowBtn.setNextFocusLeftId(R.id.rb_mic_set);
                rbMicSet.setNextFocusRightId(R.id.frag_menu_flowBtn);
            } else if (soundLayout != null && soundLayout.getVisibility() == View.VISIBLE) {
                flowBtn.setNextFocusLeftId(R.id.rb_sound);
                rbSound.setNextFocusRightId(R.id.frag_menu_flowBtn);
            } else if (micLayout != null && micLayout.getVisibility() == View.VISIBLE) {
                flowBtn.setNextFocusLeftId(R.id.rb_mic);
                rbMic.setNextFocusRightId(R.id.frag_menu_flowBtn);
            } else {
                flowBtn.setNextFocusLeftId(R.id.frag_menu_logoutBtn);
                logoutBtn.setNextFocusRightId(R.id.frag_menu_flowBtn);
            }
            //右焦点
            if (videoLayout != null && videoLayout.getVisibility() == View.VISIBLE) {
                flowBtn.setNextFocusRightId(R.id.frag_menu_videoBtn);
                videoBtn.setNextFocusLeftId(R.id.frag_menu_flowBtn);
            } else {
                flowBtn.setNextFocusRightId(R.id.frag_menu_chatBtn);
                chatBtn.setNextFocusLeftId(R.id.frag_menu_flowBtn);
            }
        }
        //视频操作按钮左右焦点
        if (videoLayout != null && videoLayout.getVisibility() == View.VISIBLE) {
            //左焦点
            if (flowLayout != null && flowLayout.getVisibility() == View.VISIBLE) {
                videoBtn.setNextFocusLeftId(R.id.frag_menu_flowBtn);
                flowBtn.setNextFocusRightId(R.id.frag_menu_videoBtn);
            } else if (docLayout != null && docLayout.getVisibility() == View.VISIBLE) {
                videoBtn.setNextFocusLeftId(R.id.frag_menu_docBtn);
                docBtn.setNextFocusRightId(R.id.frag_menu_videoBtn);
            } else if (distributionLayout != null && distributionLayout.getVisibility() == View.VISIBLE) {
                videoBtn.setNextFocusLeftId(R.id.rb_sound_set);
                rbSoundSet.setNextFocusRightId(R.id.frag_menu_videoBtn);
            } else if (setVideoLayout != null && setVideoLayout.getVisibility() == View.VISIBLE) {
                videoBtn.setNextFocusLeftId(R.id.rb_mic_set);
                rbMicSet.setNextFocusRightId(R.id.frag_menu_videoBtn);
            } else if (soundLayout != null && soundLayout.getVisibility() == View.VISIBLE) {
                videoBtn.setNextFocusLeftId(R.id.rb_sound);
                rbSound.setNextFocusRightId(R.id.frag_menu_videoBtn);
            } else if (micLayout != null && micLayout.getVisibility() == View.VISIBLE) {
                videoBtn.setNextFocusLeftId(R.id.rb_mic);
                rbMic.setNextFocusRightId(R.id.frag_menu_videoBtn);
            } else {
                videoBtn.setNextFocusLeftId(R.id.frag_menu_logoutBtn);
                logoutBtn.setNextFocusRightId(R.id.frag_menu_videoBtn);
            }
            //右焦点
            videoBtn.setNextFocusRightId(R.id.frag_menu_chatBtn);
            chatBtn.setNextFocusLeftId(R.id.frag_menu_videoBtn);
        }
        //聊天按钮左右焦点
        //左焦点
        if (videoLayout != null && videoLayout.getVisibility() == View.VISIBLE) {
            chatBtn.setNextFocusLeftId(R.id.frag_menu_videoBtn);
            videoBtn.setNextFocusRightId(R.id.frag_menu_chatBtn);
        } else if (flowLayout != null && flowLayout.getVisibility() == View.VISIBLE) {
            chatBtn.setNextFocusLeftId(R.id.frag_menu_flowBtn);
            flowBtn.setNextFocusRightId(R.id.frag_menu_chatBtn);
        } else if (docLayout != null && docLayout.getVisibility() == View.VISIBLE) {
            chatBtn.setNextFocusLeftId(R.id.frag_menu_docBtn);
            docBtn.setNextFocusRightId(R.id.frag_menu_chatBtn);
        } else if (distributionLayout != null && distributionLayout.getVisibility() == View.VISIBLE) {
            chatBtn.setNextFocusLeftId(R.id.rb_sound_set);
            rbSoundSet.setNextFocusRightId(R.id.frag_menu_chatBtn);
        } else if (setVideoLayout != null && setVideoLayout.getVisibility() == View.VISIBLE) {
            chatBtn.setNextFocusLeftId(R.id.rb_mic_set);
            rbMicSet.setNextFocusRightId(R.id.frag_menu_chatBtn);
        } else if (soundLayout != null && soundLayout.getVisibility() == View.VISIBLE) {
            chatBtn.setNextFocusLeftId(R.id.rb_sound);
            rbSound.setNextFocusRightId(R.id.frag_menu_chatBtn);
        } else if (micLayout != null && micLayout.getVisibility() == View.VISIBLE) {
            chatBtn.setNextFocusLeftId(R.id.rb_mic);
            rbMic.setNextFocusRightId(R.id.frag_menu_chatBtn);
        } else {
            if (chatBtn != null) {
                chatBtn.setNextFocusLeftId(R.id.frag_menu_logoutBtn);
            }
            if (logoutBtn != null) {
                logoutBtn.setNextFocusRightId(R.id.frag_menu_chatBtn);
            }
        }
        //右焦点
        if (inviteLayout != null && inviteLayout.getVisibility() == View.VISIBLE) {
            if (chatBtn != null) {
                chatBtn.setNextFocusRightId(R.id.frag_menu_inviteBtn);
            }
            if (inviteBtn != null) {
                inviteBtn.setNextFocusLeftId(R.id.frag_menu_chatBtn);
            }
        } else {
            if (chatBtn != null) {
                chatBtn.setNextFocusRightId(R.id.frag_menu_canjiaBtn);
            }
            canjiaBtn.setNextFocusLeftId(R.id.frag_menu_chatBtn);
        }
        //邀请按钮左右焦点
        if (inviteLayout != null && inviteLayout.getVisibility() == View.VISIBLE) {
            inviteBtn.setNextFocusLeftId(R.id.frag_menu_chatBtn);
            inviteBtn.setNextFocusRightId(R.id.frag_menu_canjiaBtn);
        }
        //参加者按钮左右焦点
        //左焦点
        if (inviteLayout != null && inviteLayout.getVisibility() == View.VISIBLE) {
            canjiaBtn.setNextFocusLeftId(R.id.frag_menu_inviteBtn);
        } else {
            canjiaBtn.setNextFocusLeftId(R.id.frag_menu_chatBtn);
        }
        //右焦点
        canjiaBtn.setNextFocusRightId(R.id.frag_menu_logoutBtn);
        //退出按钮左右焦点
        logoutBtn.setNextFocusLeftId(R.id.frag_menu_canjiaBtn);
        //右焦点
        if (micLayout != null && micLayout.getVisibility() == View.VISIBLE) {
            logoutBtn.setNextFocusRightId(R.id.rb_mic);
        } else if (soundLayout != null && soundLayout.getVisibility() == View.VISIBLE) {
            logoutBtn.setNextFocusRightId(R.id.rb_sound);
        } else if (setVideoLayout != null && setVideoLayout.getVisibility() == View.VISIBLE) {
            logoutBtn.setNextFocusRightId(R.id.rb_mic_set);
        } else if (distributionLayout != null && distributionLayout.getVisibility() == View.VISIBLE) {
            logoutBtn.setNextFocusRightId(R.id.rb_sound_set);
        } else if (docLayout != null && docLayout.getVisibility() == View.VISIBLE) {
            logoutBtn.setNextFocusRightId(R.id.frag_menu_docBtn);
        } else if (flowLayout != null && flowLayout.getVisibility() == View.VISIBLE) {
            logoutBtn.setNextFocusRightId(R.id.frag_menu_flowBtn);
        } else if (videoLayout != null && videoLayout.getVisibility() == View.VISIBLE) {
            logoutBtn.setNextFocusRightId(R.id.frag_menu_videoBtn);
        } else {
            logoutBtn.setNextFocusRightId(R.id.frag_menu_chatBtn);
        }
    }

    private void showDialog() {
        if (dialog == null) {
            dialog = new VideoListDialog(ActConf.this);
            dialog.setLimit(limit);
            if (isShare) {
                dialog.setCameraId(Uvc.ID_HDMI);
            } else {
                dialog.setCameraId(Uvc.ID_LOCAL);
            }
            dialog.setOnResultListener(new VideoListDialog.OnResultListener() {
                @Override
                public void doYes() {
//                    showLoading();
////                    videoCommon.setVideoSyncAll(true);
//                    //fromIterable接收一个Iterable，每次发射一个元素（与for循环效果相同）
//                    Observable<FacilityListBean> observable = Observable.fromIterable(dialog.getDatas());
//                    //interval定时器，间隔1秒发射一次
//                    Observable<Long> timerObservable = Observable.interval(500, TimeUnit.MILLISECONDS);
//                    //使用zip操作符合并两个Observable
//                    Observable.zip(observable, timerObservable, new BiFunction<FacilityListBean, Long, FacilityListBean>() {
//                        @Override
//                        public FacilityListBean apply(FacilityListBean bean, Long aLong) throws Exception {
//                            return bean;
//                        }
//                    }).doOnNext(new Consumer<FacilityListBean>() {
//                        @Override
//                        public void accept(FacilityListBean bean) throws Exception {
//                            //此处接收
//                            if (bean.isSelected()){
//                                videoCommon.openVideo(bean.getChannelId(), null);
//                                videoCommon.setSyncVedio(bean.getChannelId(), true);
//                            }else {
//                                videoCommon.closeVideo(bean.getChannelId());
//                                videoCommon.setSyncVedio(bean.getChannelId(), false);
//                            }
//                        }
//                    }).subscribe();
//                    if (isShare || (fragDs != null && fragDs.isAdded() && !fragDs.isHidden())
//                            || (fragAs != null && fragAs.isAdded() && !fragAs.isHidden())){
//                        setViewHandler.postDelayed(new Runnable() {
//                            @Override
//                            public void run() {
//                                setViewHandler.removeCallbacksAndMessages(null);
//                                setViewHandler.sendEmptyMessage(0);
//                            }
//                        }, 3500);
//                    }
                }

                @Override
                public void doNo() {
                    // TODO Auto-generated method stub
                }

                @Override
                public void openVideo(boolean isOpen, FacilityListBean bean) {
                    int channelId = bean.getChannelId();
                    if (isOpen) {
                        if (!conferenceCommon.getAvOpenConfirm()){
                            videoCommon.openVideo(channelId, (SurfaceView) null);
                            videoCommon.setSyncVedio(channelId, true);
                        }
                        else {
                            if (!syncVideo(channelId, true)) bean.setSelected(false);
                        }

                    } else {
                        videoCommon.closeVideo(channelId);
                        videoCommon.setSyncVedio(channelId, false);
                    }
                }

            });
            if (!dialog.isShowing()) {
                dialog.show();
            }
        } else {
            dialog.setLimit(limit);
            if (dialog.isShowing()) {
                dialog.dismiss();
            } else {
                if (isShare) {
                    dialog.setCameraId(Uvc.ID_HDMI);
                } else {
                    dialog.setCameraId(Uvc.ID_LOCAL);
                }
                dialog.show();
            }
        }
    }

    private boolean syncVideo(int channelId, boolean isOpen) {

        //同步视频
        if (channelId == videoCommon.LOCAL_VIDEO_CHANNEL_ID) {
            videoCommon.setSyncVedio(channelId, true);
            //showShortToast(R.string.vs_synced);
            return true;
        }
        else
        {
            //同步其他人的视频
            if (userCommon.isHost()){
                if (true/*conferencePattern == 1*/) {
                    int userId = videoCommon.getUserId(channelId);
                    if (userId > 0) {
                        if (confManageCommon != null) {

                            Log.d(TAG, ">>>>>> confManageCommon.request: userId = " + userId + "; channelId = " + channelId);
                            requestType = 1;
                            confManageCommon.request(2, userId, channelId, 0, 0, "");
                            showShortToast(R.string.hst_request_sent);
                            return false;
                        }
                    }
                }
                else
                {
                    videoCommon.openVideo(channelId, (SurfaceView) null);
                    videoCommon.setSyncVedio(channelId, true);
                    //showShortToast(R.string.vs_synced);
                }
            }
        }

        return true;
    }

    private void micOpre(boolean isOpen) {
        for (Integer key : videoCommon.getDeviceMap().keySet()) {
            if (videoCommon.getDeviceMap().get(key) != userCommon.getSelf().getUid()) {
                if (isOpen) {
                    //打开
                    audioCommon.openAudio(videoCommon.getDeviceMap().get(key));
                } else {
                    //关闭
                    audioCommon.closeAudio(videoCommon.getDeviceMap().get(key));
                }
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.rb_mic:
                rbMic.setFocusable(true);
                rbMic.requestFocus();
                if (((AudioCommonImpl) CommonFactory.getInstance().getAudioCommon()).isMICWork()) {
                    if (!SharedPreferencesUrls.getInstance().getBoolean("isOpenMic", false)) {
                        rbMic.setChecked(true);
                        micOn();
                    } else {
                        rbMic.setChecked(false);
                        micOff();
                    }
                } else {
                    ToastUtil.showMessage(ActConf.this, "对不起，您暂未操作麦克风权限", 5 * 1000);
                }
                break;
            case R.id.rb_sound:
                if (Utils.isFastClick()) return;
                rbSound.setFocusable(true);
                rbSound.requestFocus();
                if (((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_HOST) {

                    micOpre(false);
                    SharedPreferencesUrls.getInstance().putBoolean("allAute", false);

//                    if (SharedPreferencesUrls.getInstance().getBoolean("allAute", true)) {
//                        rbSound.setChecked(false);
//                        //关闭
//                        micOpre(false);
//                        SharedPreferencesUrls.getInstance().putBoolean("allAute", false);
//                    } else {
//                        //打开
//                        micOpre(true);
//                        rbSound.setChecked(true);
//                        SharedPreferencesUrls.getInstance().putBoolean("allAute", true);
//                    }
                }
                break;
            case R.id.rb_mic_set:
                rbMicSet.setFocusable(true);
                rbMicSet.requestFocus();
                //操作视频
                for (int key : videoCommon.getDeviceMap().keySet()) {
                    if (userCommon.getSelf().getUid() == videoCommon.getDeviceMap().get(key)) {
                        if (!videoCommon.getSyncMap().containsKey(key)) {
                            videoCommon.openVideo(key, (SurfaceView) null);
                            videoCommon.setSyncVedio(key, true);
                            rbMicSet.setChecked(true);
//                            if (fragVs != null){
//                                fragVs.setVideoSize();
//                            }
                        } else {
                            rbMicSet.setChecked(false);
                            videoCommon.closeMyVideo();
                            videoCommon.setSyncVedio(key, false);
                        }
                    }
                }
                break;
            case R.id.rb_sound_set:

                rbSoundSet.setFocusable(true);
                rbSoundSet.requestFocus();
                if (fragAs != null && fragAs.isAdded()
                    && !fragAs.isHidden() && !fragAs.isEmpty()){
                    ToastUtil.showMessage(ActConf.this,"正在桌面共享，暂不支持布局切换",5 * 1000);
                    return;
                }
                //布局
                if (buJuDialog != null) {
                    if (!buJuDialog.isShowing()) {
                        buJuDialog.setType(bujuType);
                        buJuDialog.show();
                    } else {
                        buJuDialog.dismiss();
                    }
                }
                break;
            case R.id.frag_menu_docBtn:
                if (menuDialog.isShowing()) {
                    if (Build.MODEL.indexOf("YT-500") != -1|| Build.MODEL.indexOf("I-5300") != -1)
                        menuDialog.dismiss();
                }
                docBtn.setFocusable(true);
                docBtn.requestFocus();
                curPosition = 2;
                if (docCommon.getDocMapList() == null || docCommon.getDocMapList().size() == 0) {
                    ToastUtil.showMessage(ActConf.this, "暂无任何文档共享", 2 * 1000);
                    return;
                }
                if (docListDialog != null && !docListDialog.isShowing()) {
                    docListDialog.show();
                } else {
                    docListDialog.dismiss();
                }
                break;
            case R.id.frag_menu_flowBtn:

                if (userCommon != null){
                    if (!userCommon.isASPriviledge()){
                        ToastUtil.showMessage(ActConf.this, "对不起，您没有共享权限。", 5 * 1000);
                    }
                }

                flowBtn.setFocusable(true);
                flowBtn.requestFocus();
                if (Build.MODEL.indexOf("YT-500") == -1 && Build.MODEL.indexOf("I-5300") == -1
                    && Build.MODEL.indexOf("MV200") == -1) {
                    ToastUtil.showMessage(ActConf.this, "对不起，你的设备暂不支持该功能", 5 * 1000);
                    flowLayout.setVisibility(View.GONE);
                    return;
                }
                if (fragAs != null && fragAs.isAdded() && !fragAs.isHidden()
                    && !fragAs.isEmpty()){
                    ToastUtil.showMessage(ActConf.this, "对不起，正在桌面共享", 5 * 1000);
                    return;
                }
                if (getResources().getString(R.string.flow_title).equals(flowText.getText().toString().trim())) {
                    flowText.setText(getResources().getString(R.string.stop_share));
                    shareDS(true);
                } else {
                    flowText.setText(getResources().getString(R.string.flow_title));
                    shareDS(false);
                }
                break;
            case R.id.frag_menu_videoBtn:
                videoBtn.setFocusable(true);
                videoBtn.requestFocus();
                videoPre();
                break;
            case R.id.frag_menu_chatBtn:
                if (menuDialog.isShowing()) {
                    menuDialog.dismiss();
                }

                chatBtn.setFocusable(true);
                chatBtn.requestFocus();

                //Object obj = ((LinkedList<UserBean>) userCommon.getUserArrayList().clone()).get(0);
                //if (obj instanceof UserBean) {
                    //chatingUser = (UserBean) obj;
                    if (chatingUser == null) chatingUser = new UserBean();

                    if (chatAdatper == null){
                        initChatView();
                    }

                    chatingUser.setUid(userCommon.ALL_USER_ID);

                    if (!userCommon.getPublicChatPriviledge()) {
                        rlChatEdit.setVisibility(View.GONE);
                        showLongToast(R.string.not_public_chat_permission);
                    } else {
                        rlChatEdit.setVisibility(View.VISIBLE);
                    }

                //if (chatingUser.getUid() != userCommon.getSelf().getUid()) {
                    userCommon.setCurrentChatingId(chatingUser.getUid());
                    showChat();

                    //}
                //}
                break;
            case R.id.btn_inconf_attender_chat_back:
                userCommon.getHandler().sendEmptyMessage(UserCommon.CHAT_RECEIVE);// 更新参会者列表中未阅读的消息
                findViewById(R.id.view_inconf_attender_chat)
                        .setVisibility(View.GONE);
                hideInput(v);
                if (menuDialog != null && !menuDialog.isShowing())
                    menuDialog.show();
                break;
            case R.id.btn_inconf_attender_chat_send:
                //发送聊天消息
                sendMessage();
                hideInput(v);
                break;
            case R.id.frag_menu_inviteBtn:
                inviteBtn.setFocusable(true);
                inviteBtn.requestFocus();
                if (PublicWay.socketBinder != null && PublicWay.socketBinder.getService() != null) {
                    SharedPreferencesUrls.getInstance().putBoolean("isPopup1", true);
                    SharedPreferencesUrls.getInstance().putBoolean("isPopup", false);
                    if (loadingDialog != null && !loadingDialog.isShowing()) {
                        loadingDialog.setTitle("正在获取列表");
                        loadingDialog.show();
                    }
                    String RequestID = "" + ConferenceApplication.currentTimeMillis();
                    String deviceId = DeviceIdFactory.getUUID1(ActConf.this);
                    String siteId = FileUtil.readSharedPreferences(ActConf.this, Constants.SHARED_PREFERENCES,
                            Constants.SITE_ID);
                    PublicWay.socketBinder.getService().sendOrderEx(XMLUtils.getXml_list(RequestID, deviceId, siteId));
                    Runnable r = new Runnable() {
                        @Override
                        public void run() {
                            if (curR != this) return;
                            hideLoading();
                            if (loadingDialog != null && loadingDialog.isShowing()) {
                                loadingDialog.dismiss();
                            }
                            if (isShow) {
                                ToastUtil.showMessage(ActConf.this, "获取列表失败，请重试", 2 * 1000);
                                isShow = false;
                                SharedPreferencesUrls.getInstance().putBoolean("isPopup1", false);
                            }
                        }
                    };
                    curR = r;
                    setViewHandler.postDelayed(r, 10 * 1000);
                    isShow = true;
                } else {
                    if (loadingDialog != null && !loadingDialog.isShowing()) {
                        loadingDialog.setTitle("正在连接服务");
                        loadingDialog.show();
                    }
                    PublicWay.getIpAndPort(ActConf.this, Config.Site_URL, FileUtil.readSharedPreferences(ActConf.this,
                            Constants.SHARED_PREFERENCES, Constants.SITE_ID));
                    setViewHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            hideLoading();
                            if (loadingDialog != null && loadingDialog.isShowing()) {
                                loadingDialog.dismiss();
                            }
                        }
                    }, 5 * 1000);
                }
                break;
            case R.id.view_inconf_attender_menuBtn:
                if (menuDialog != null) {
                    if (menuDialog.isShowing()) {
                        if (Build.MODEL.indexOf("YT-500") != -1|| Build.MODEL.indexOf("I-5300") != -1)
                            menuDialog.dismiss();
                    } else {
                        menuDialog.show();
                        btnState();
                    }
                }
                break;
            case R.id.frag_menu_canjiaBtn:

                canjiaBtn.setFocusable(true);
                canjiaBtn.requestFocus();

                if (personListDialog != null) {
                    if (personListDialog.isShowing()) {
                        personListDialog.dismiss();
                    } else {
                        personListDialog.show();
                    }
                }
                break;
            case R.id.frag_menu_logoutBtn:
                logoutBtn.setFocusable(true);
                logoutBtn.requestFocus();
                showExitDialog();
                break;
            default:
                break;
        }
    }

    protected void hideInput(View v) {
        InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (null != getCurrentFocus() && null != getCurrentFocus().getApplicationWindowToken()) {
            inputMethodManager.hideSoftInputFromWindow(getCurrentFocus()
                            .getApplicationWindowToken(),
                    InputMethodManager.HIDE_NOT_ALWAYS);
        }
    }

    /**
     * 显示聊天界面
     */
    private void showChat() {
        // TranslateAnimation mHiddenAction = new TranslateAnimation(
        // Animation.RELATIVE_TO_SELF, 0.0f, Animation.RELATIVE_TO_SELF,
        // 0.0f, Animation.RELATIVE_TO_SELF, 0.0f,
        // Animation.RELATIVE_TO_SELF, -1.0f);
        // mHiddenAction.setDuration(200);
        // attendersView.findViewById(R.id.rl_inconf_attender_list).setAnimation(
        TranslateAnimation mShowAction = new TranslateAnimation(
                Animation.RELATIVE_TO_SELF, 1.0f, Animation.RELATIVE_TO_SELF,
                0.0f, Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f);
        mShowAction.setDuration(200);
        findViewById(R.id.view_inconf_attender_chat)
                .startAnimation(mShowAction);
        findViewById(R.id.view_inconf_attender_chat)
                .setVisibility(View.VISIBLE);
    }

    /**
     * 发送
     */
    private void sendMessage() {
        final String message = etChat.getText().toString().trim();
        if (message.length() > 0) {
            try {
                UserBean userbean = userCommon.getUser(chatingUser.getUid());
                boolean isPubilc = true;
                int toUid = UserCommon.ALL_USER_ID;
                if (userbean != null
                        && userbean.getUid() != UserCommon.ALL_USER_ID) {
                    toUid = userbean.getUid();
                    isPubilc = false;
                }
                //System.out.println(message);

                MessageBean messageBean = new MessageBean();
                messageBean.setDate(StringUtil
                        .dateToStr(new Date(), "HH:mm:ss"));
                messageBean.setMessage(message);
                messageBean.setUsername(userCommon.getSelf().getUsername());
                messageBean.setPublic(isPubilc);
                messageBean.setUid(toUid);
                userCommon.sortMessageByTime(messageBean);// 对自己发送的聊天内容按时间排序
                CommonFactory.getInstance().getChatCommom()
                        .chatSendMsg(messageBean);

            } catch (Exception e) {
                e.printStackTrace();
            }

            chatAdatper.notifyDataSetChanged();
            etChat.setText("");
            lvChat.setSelection(chatAdatper.getCount());
        } else {
//            showShortToast(R.string.attenders_chat_msgnull);
        }
    }

    /**
     * 初始化聊天界面
     */
    private void initChatView() {
//        tvChatName = (TextView)findViewById(R.id.tv_inconf_attender_chat_name);
        lvChat = (ListView) findViewById(R.id.lv_inconf_attender_chat);
        etChat = (EditText) findViewById(R.id.et_inconf_attender_chat);
        btnChatBack = (ImageView) findViewById(R.id.btn_inconf_attender_chat_back);
        btnSend = (ImageView) findViewById(R.id.btn_inconf_attender_chat_send);
        btnChatBack.setOnClickListener(this);
        btnSend.setOnClickListener(this);

        etChat.setFocusable(true);
        btnChatBack.setFocusable(true);
        btnSend.setFocusable(true);
        etChat.requestFocus();

        etChat.setNextFocusRightId(R.id.btn_inconf_attender_chat_send);
        etChat.setNextFocusUpId(R.id.btn_inconf_attender_chat_back);

        btnSend.setNextFocusLeftId(R.id.et_inconf_attender_chat);
        btnSend.setNextFocusUpId(R.id.btn_inconf_attender_chat_back);

        btnChatBack.setNextFocusDownId(R.id.et_inconf_attender_chat);

        // 设置名字
//        if(chatingUser.getUid() == UserCommon.ALL_USER_ID){
//            tvChatName.setText(getResources().getString(R.string.attenders_adapter_publicchat));
//        }else {
//            String name = chatingUser.getUsername();
//            name = CutString.cutString(name, 8);
//            tvChatName.setText(name);
//        }
        userCommon.setCurrentChatingId(chatingUser.getUid());
        if (userCommon.getMessageMap().get(chatingUser.getUid()) == null) {
            ArrayList<MessageBean> msg = new ArrayList<MessageBean>();
            userCommon.getMessageMap().put(chatingUser.getUid(), msg);
        }

        chatAdatper = new ChatContentAdapter(this, userCommon
                .getMessageMap().get(chatingUser.getUid()),
                chatingUser.getUid() == UserCommon.ALL_USER_ID ? true : false);
        lvChat.setAdapter(chatAdatper);
        chatAdatper.notifyDataSetChanged();
        // 聊天数据定位到最新未读消息或者最后
        lvChat.setSelection(userCommon.getLastNotReadedMessage(chatingUser
                .getUid()) - 1 );
        userCommon.setReadAllMessage(chatingUser.getUid());
        userCommon.getUser4Phone(chatingUser.getUid()).setReadedMsg(true);

        if (chatingUser.getUid() == UserCommon.ALL_USER_ID) {
            //默认都具有公聊权限
            if (!userCommon.getPublicChatPriviledge()) {
                rlChatEdit.setVisibility(View.GONE);
                //Log.d("InfowareLab.Right", " ===> not_public_chat_permission(1)");
                //showLongToast(R.string.not_public_chat_permission);
            } else {
                rlChatEdit.setVisibility(View.VISIBLE);
            }
        } else {
            if (!userCommon.getPrivateChatPriviledge()) {
                Log.d("InfowareLab.Right", " ===> not_public_chat_permission(2)");
                rlChatEdit.setVisibility(View.GONE);
                showLongToast(R.string.not_private_chat_permission);
            } else {
                rlChatEdit.setVisibility(View.VISIBLE);
            }
        }
        etChat.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (userCommon.getMessageMap().get(chatingUser.getUid()).size() > 2
                        && etChat.isFocused()
                        && event.getAction() == MotionEvent.ACTION_UP) {
                    int a = userCommon.getLastNotReadedMessage(chatingUser
                            .getUid());
                    lvChat.setSelection(a);
                }
                return false;
            }

        });

        etChat.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId,
                                          KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEND
                        || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                    sendMessage();
                }
                return false;
            }
        });
    }

    private void showInviteDialog() {
        if (inviteDialog == null) {
            inviteDialog = new InviteDialog(this, 0);
            if (!inviteDialog.isShowing()) {
                inviteDialog.show();
                inviteDialog.setFouse();
            }
        } else {
            if (!inviteDialog.isShowing()) {
                inviteDialog.show();
                inviteDialog.setFouse();
            }
        }
        inviteDialog.setOnResultListener(new InviteDialog.OnResultListener() {
            @Override
            public void doYes() {
                SharedPreferencesUrls.getInstance().putBoolean("isPopup1", false);
                if (PublicWay.socketBinder != null) {
//                    if (loadingDialog != null && !loadingDialog.isShowing()){
//                        loadingDialog.setTitle("正在加会");
//                        loadingDialog.show();
//                    }
                    String RequestID = "" + ConferenceApplication.currentTimeMillis();
                    String siteId1 = FileUtil.readSharedPreferences(ActConf.this, Constants.SHARED_PREFERENCES,
                            Constants.SITE_ID);
                    if (inviteDialog.getSelDatas().size() != 0) {
                        PublicWay.socketBinder.getService().sendOrderEx(XMLUtils.getInvite(ActConf.this, RequestID,
                                XMLUtils.CONFIGID + XMLUtils.CONFIGNAME, siteId1, inviteDialog.getSelDatas()));
                    }
                    setViewHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            hideLoading();
                            if (loadingDialog != null && loadingDialog.isShowing()) {
                                loadingDialog.dismiss();
                            }
                        }
                    }, 2 * 1000);
                }
//                }

            }

            @Override
            public void doNo() {
                SharedPreferencesUrls.getInstance().putBoolean("isPopup1", false);
                // TODO Auto-generated method stub
            }
        });
        inviteDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                SharedPreferencesUrls.getInstance().putBoolean("isPopup1", false);
                hideLoading();
            }
        });
    }

    public static int dip2px(Context context, float dpValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }

    public static int px2dip(Context context, float pxValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }

    private void initMenuDialog() {

        menuDialog = new Dialog(ActConf.this, R.style.dialog);
        //填充对话框的布局
        inflate = LayoutInflater.from(this).inflate(R.layout.dialog_menu, null);

        LinearLayout menuLayout = (LinearLayout) inflate.findViewById(R.id.view_inconf_attender_menuLayout);

        micLayout = (LinearLayout) inflate.findViewById(R.id.frag_menu_micLayout);
        docLayout = (LinearLayout) inflate.findViewById(R.id.frag_menu_docLayout);
        flowLayout = (LinearLayout) inflate.findViewById(R.id.frag_menu_flowLayout);
        videoLayout = (LinearLayout) inflate.findViewById(R.id.frag_menu_videoLayout);
        soundLayout = (LinearLayout) inflate.findViewById(R.id.frag_menu_soundLayout);
        setVideoLayout = (LinearLayout) inflate.findViewById(R.id.frag_menu_setVideoLayout);
        inviteLayout = (LinearLayout) inflate.findViewById(R.id.frag_menu_inviteLayout);
        distributionLayout = (LinearLayout) inflate.findViewById(R.id.frag_menu_distributionLayout);

        rbMic = (RadioButton) inflate.findViewById(R.id.rb_mic);
        rbSound = (RadioButton) inflate.findViewById(R.id.rb_sound);
        rbMicSet = (RadioButton) inflate.findViewById(R.id.rb_mic_set);
        rbSoundSet = (RadioButton) inflate.findViewById(R.id.rb_sound_set);
        docBtn = (RadioButton) inflate.findViewById(R.id.frag_menu_docBtn);
        flowBtn = (RadioButton) inflate.findViewById(R.id.frag_menu_flowBtn);
        videoBtn = (RadioButton) inflate.findViewById(R.id.frag_menu_videoBtn);
        flowText = (TextView) inflate.findViewById(R.id.frag_menu_flowText);
        inviteBtn = (RadioButton) inflate.findViewById(R.id.frag_menu_inviteBtn);
        canjiaBtn = (RadioButton) inflate.findViewById(R.id.frag_menu_canjiaBtn);
        chatBtn = (RadioButton) inflate.findViewById(R.id.frag_menu_chatBtn);
        logoutBtn = (RadioButton) inflate.findViewById(R.id.frag_menu_logoutBtn);

        rbSound.requestFocus();
        rbSound.setChecked(SharedPreferencesUrls.getInstance().getBoolean("allAute", false));

        if (!conferenceCommon.isAcceptSharingDesktop()) {
            boolean syncMyAudio = conferenceCommon.isMyAudioSync();
            Log.d("InfowareLab.Net", "initMenuDialog: syncMyAudio = " + syncMyAudio);

            if (syncMyAudio || ((UserCommonImpl) CommonFactory.getInstance().getUserCommon()).getSelf().getRole() == UserCommon.ROLE_HOST) {
                rbMic.setChecked(true);
                SharedPreferencesUrls.getInstance().putBoolean("isOpenMic", false);
                micOn();
            } else {
                rbMic.setChecked(false);
                SharedPreferencesUrls.getInstance().putBoolean("isOpenMic", true);
                micOff();
            }
        }
        else {
            rbMic.setChecked(false);
            SharedPreferencesUrls.getInstance().putBoolean("isOpenMic", true);
            micOff();
        }

        rbMic.setOnClickListener(this);
        rbSound.setOnClickListener(this);
        rbMicSet.setOnClickListener(this);
        rbSoundSet.setOnClickListener(this);
        docBtn.setOnClickListener(this);
        flowBtn.setOnClickListener(this);
        videoBtn.setOnClickListener(this);
        inviteBtn.setOnClickListener(this);
        canjiaBtn.setOnClickListener(this);
        docLayout.setOnClickListener(this);
        chatBtn.setOnClickListener(this);
        logoutBtn.setOnClickListener(this);

        //监听焦点
//        rbMic.setOnFocusChangeListener(this);
        rbSound.setOnFocusChangeListener(this);
        rbMicSet.setOnFocusChangeListener(this);
        rbSoundSet.setOnFocusChangeListener(this);
        docBtn.setOnFocusChangeListener(this);
        flowBtn.setOnFocusChangeListener(this);
        videoBtn.setOnFocusChangeListener(this);
        inviteBtn.setOnFocusChangeListener(this);
        canjiaBtn.setOnFocusChangeListener(this);
        docLayout.setOnFocusChangeListener(this);
        chatBtn.setOnFocusChangeListener(this);
        logoutBtn.setOnFocusChangeListener(this);

        menuLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Build.MODEL.indexOf("YT-500") != -1|| Build.MODEL.indexOf("I-5300") != -1)
                    menuDialog.dismiss();
            }
        });

        //将布局设置给Dialog
        menuDialog.setContentView(inflate);

        //获取当前Activity所在的窗体
        Window dialogWindow = menuDialog.getWindow();
        dialogWindow.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        //设置Dialog从窗体底部弹出
        dialogWindow.setGravity(Gravity.BOTTOM);
        //获得窗体的属性
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = DensityUtil.getWindowWidth(ActConf.this);

        if (ActConf.this.getResources().getDisplayMetrics().density == 1.0f){
            lp.height = 120;
        }
        else
            lp.height = dip2px(ActConf.this, 80);

//       将属性设置给窗体
        dialogWindow.setAttributes(lp);

        if (Build.MODEL.indexOf("YT-500") != -1|| Build.MODEL.indexOf("I-5300") != -1)
            menuDialog.setCanceledOnTouchOutside(true);
        else
            menuDialog.setCanceledOnTouchOutside(false);

        if (menuDialog != null && !menuDialog.isShowing()) {
            btnState();
            menuDialog.show();
        }
    }
    @Override
    public void onFocusChange(View v, boolean hasFocus) {

        //Log.d("InfowareLab.Debug", ">>>>>> MenuDialog.onFocusChange:"+v.getId());

        switch (v.getId()) {
            case R.id.rb_mic:
                rbMic.setChecked(hasFocus);
                break;
            case R.id.rb_sound:
//                rbSound.setChecked(hasFocus);
                break;
            case R.id.rb_mic_set:
//                rbMicSet.setChecked(hasFocus);
                break;
            case R.id.rb_sound_set:
                rbSoundSet.setChecked(hasFocus);
                break;
            case R.id.frag_menu_docBtn:
                docBtn.setChecked(hasFocus);
                break;
            case R.id.frag_menu_flowBtn:
                flowBtn.setChecked(hasFocus);
                break;
            case R.id.frag_menu_videoBtn:
                videoBtn.setChecked(hasFocus);
                break;
            case R.id.frag_menu_chatBtn:
                chatBtn.setChecked(hasFocus);
                break;
            case R.id.frag_menu_inviteBtn:
                inviteBtn.setChecked(hasFocus);
                break;
            case R.id.frag_menu_canjiaBtn:
                chatBtn.setChecked(hasFocus);
                break;
            case R.id.frag_menu_logoutBtn:
                logoutBtn.setChecked(hasFocus);
                break;
            default:
                break;
        }
    }

    @Override
    public void setBuJuType(int type) {
        //bujuType = type;
    }

    @Override
    public void getLimit(int limit) {
        this.limit = limit <= 6 ? 9 : limit;
    }

    @Override
    public void getLimit1(int limit) {
        this.limit = limit <= 6 ? 9 : limit;
    }

    private boolean existSecondScreen() {
//        MediaRouter mediaRouter = (MediaRouter) getSystemService(Context.MEDIA_ROUTER_SERVICE);
//        MediaRouter.RouteInfo localRouteInfo = mediaRouter.getSelectedRoute(MediaRouter.ROUTE_TYPE_LIVE_AUDIO);
//        Display display = localRouteInfo != null ? localRouteInfo.getPresentationDisplay() : null;
//

        if (Build.MODEL.indexOf("rk3588") != -1 || Build.MODEL.indexOf("YT-500") != -1 || Build.MODEL.indexOf("I-5300") != -1){
            //
            return false;
        }

        DisplayManager displayManager = (DisplayManager) getSystemService(Context.DISPLAY_SERVICE);
        Display[] arrayOfDisplay = displayManager.getDisplays(DisplayManager.DISPLAY_CATEGORY_PRESENTATION);
        if (arrayOfDisplay.length > 0) {
            return true;
        } else {
            return false;
        }
    }
    private Display getSecondScreen() {
//        MediaRouter mediaRouter = (MediaRouter) getSystemService(Context.MEDIA_ROUTER_SERVICE);
//        MediaRouter.RouteInfo localRouteInfo = mediaRouter.getSelectedRoute(MediaRouter.ROUTE_TYPE_LIVE_AUDIO);
//        Display display = localRouteInfo != null ? localRouteInfo.getPresentationDisplay() : null;
//

        if (Build.MODEL.indexOf("rk3588") != -1 || Build.MODEL.indexOf("YT-500") != -1 || Build.MODEL.indexOf("I-5300") != -1){
            //
            return null;
        }

        DisplayManager displayManager = (DisplayManager) getSystemService(Context.DISPLAY_SERVICE);
        Display[] arrayOfDisplay = displayManager.getDisplays(DisplayManager.DISPLAY_CATEGORY_PRESENTATION);
        if (arrayOfDisplay.length > 0) {
            return arrayOfDisplay[0];//取第一个分屏使用
        } else {
            return null;
        }

//        displayManager = (DisplayManager) getSystemService(Context.DISPLAY_SERVICE);
//        Display[] presentationDisplays = displayManager.getDisplays();
//        if (presentationDisplays.length > 1) {
//            presentation2 = new MyPresentation2(getApplicationContext(), presentationDisplays[1]);
//
//            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
//                presentation2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
//            else
//                presentation2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE
//
//            presentation2.show();
//        } else {
//            Toast.makeText(MainActivity.this, "不支持分屏", Toast.LENGTH_SHORT).show();
//        }
    }

    private void validateFragAs()
    {
        if (null == fragAs) {
            //            fragAs = new FragAs(this);
            fragAs = new FragAs();
            fragAs.setiCallParentView(this);
            fragAs.initConfData();

            fragmentManager = getSupportFragmentManager();
            FragmentTransaction ft;
            ft = fragmentManager.beginTransaction();

            ft.add(R.id.fl_inconf_content_container_left, fragAs, "AS");

            ft.show(fragAs);

            ft.commitAllowingStateLoss();
        }

        if (null != fragAs2) {
            fragAs2.dismiss();
            fragAs2 = null;
        }
    }

    private void validateFragAs2()
    {
        if (null == fragAs2) {
            Display display = getSecondScreen();

            if (display == null) return;

            fragAs2 = new FragAs2(getApplicationContext(), display);
            fragAs2.setiCallParentView(this);
            fragAs2.initConfData();

            if (Build.VERSION.SDK_INT < 31) {

                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                    fragAs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                else
                    fragAs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE
            }

            //fragAs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;

            fragAs2.show();
        }

        if (null != fragAs) {
            fragmentManager = getSupportFragmentManager();
            FragmentTransaction ft;
            ft = fragmentManager.beginTransaction();

            ft.hide(fragAs);
            ft.remove(fragAs);

            ft.commitAllowingStateLoss();

            fragAs = null;
        }
    }

    private void validateFragDs()
    {
        if (conferenceExit) return;

        if (null == fragDs) {
            //fragDs = new fragDs(this);
            fragDs = new FragDs();
            fragDs.setiCallParentView(this);
            fragDs.initConfData();

            fragmentManager = getSupportFragmentManager();
            FragmentTransaction ft;
            ft = fragmentManager.beginTransaction();

            ft.add(R.id.fl_inconf_content_container_left, fragDs, "DS");

            ft.show(fragDs);

            ft.commitAllowingStateLoss();
        }

        if (null != fragDs2) {
            fragDs2.dismiss();
            fragDs2 = null;
        }
    }
    private void validateFragDs2()
    {
        if (conferenceExit) return;

        if (null == fragDs2) {
            Display display = getSecondScreen();

            if (display == null) return;

            fragDs2 = new FragDs2(getApplicationContext(), display);
            fragDs2.setiCallParentView(this);
            fragDs2.initConfData();

            if (Build.VERSION.SDK_INT < 31) {
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M)//6.0+
                    fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
                else
                    fragDs2.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//TYPE_SYSTEM_ALERT / TYPE_PHONE
            }

            fragDs2.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);

            //fragDs2.getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;

            fragDs2.show();
            fragDs2.initBuju();
        }

        if (null != fragDs) {
            fragmentManager = getSupportFragmentManager();
            FragmentTransaction ft;
            ft = fragmentManager.beginTransaction();

            ft.hide(fragDs);
            ft.remove(fragDs);

            ft.commitAllowingStateLoss();

            fragDs = null;
        }
    }
}
