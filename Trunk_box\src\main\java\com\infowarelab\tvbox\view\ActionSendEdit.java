package com.infowarelab.tvbox.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.widget.EditText;

public class ActionSendEdit extends EditText{

	public ActionSendEdit(Context context) {
		super(context);
		// TODO Auto-generated constructor stub
	}

	public ActionSendEdit(Context context, AttributeSet attrs) {
		super(context, attrs);
		// TODO Auto-generated constructor stub
	}

	public ActionSendEdit(Context context, AttributeSet attrs, int defStyleAttr) {
		super(context, attrs, defStyleAttr);
		// TODO Auto-generated constructor stub
	}

	@Override
	public InputConnection onCreateInputConnection(EditorInfo outAttrs) {
		InputConnection connection = super.onCreateInputConnection(outAttrs);
		if (connection == null) return null;
		//移除EditorInfo.IME_FLAG_NO_ENTER_ACTION标志位
		    outAttrs.imeOptions &= ~EditorInfo.IME_FLAG_NO_ENTER_ACTION;
		    return connection;
	}

}
