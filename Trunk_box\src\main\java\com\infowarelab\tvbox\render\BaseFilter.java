package com.infowarelab.tvbox.render;

import android.content.Context;
import android.opengl.GLES11Ext;
import android.opengl.GLES20;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.util.Log;
import android.util.Size;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;

public abstract class BaseFilter {

    /*********************************************************************************************
     * 网上有很多关于坐标的文章，说法不一，容易造成混乱，这里和google工程师写的grafika项目保持一致
     *********************************************************************************************/

    private static float squareCoords[] = {

            -1.0f, -1.0f,   // 0 bottom left
            1.0f, -1.0f,   // 1 bottom right
            -1.0f, 1.0f,   // 2 top left
            1.0f, 1.0f,   // 3 top right
    };

    private static float squareCoords2[] = {

            0.5f, -1.0f,   // 0 bottom left
            1.0f, -1.0f,   // 1 bottom right
            0.5f, -0.5f,   // 2 top left
            1.0f, -0.5f,   // 3 top right
    };

    private static float squareCoords3[] = {

            -1.0f, -1.0f,   // 0 bottom left
            -0.5f, -1.0f,   // 1 bottom right
            -1.0f, -0.5f,   // 2 top left
            -0.5f, -0.5f,   // 3 top right
    };

    private static float textureVertices[] = {

            0.0f, 0.0f,     // 0 bottom left
            1.0f, 0.0f,     // 1 bottom right
            0.0f, 1.0f,     // 2 top left
            1.0f, 1.0f      // 3 top right

    };

    private static float textureVerticesMirror[] = {

            1.0f, 0.0f,     // 0 bottom left
            0.0f, 0.0f,     // 1 bottom right
            1.0f, 1.0f,     // 2 top left
            0.0f, 1.0f      // 3 top right
    };

    public Context c;
    public int mProgram;
    public FloatBuffer vertexBuffer = null;
    public FloatBuffer textureBuffer = null;
    public FloatBuffer textureBufferMirror = null;
    private FloatBuffer vertexBuffer2 = null;
    private FloatBuffer vertexBuffer3 = null;
    public int path1;
    public int path2;
    protected int mGLAttribPosition;
    protected int mGLUniformTexture;
    protected int mGLAttribTextureCoordinate;
    protected int mHMatrix;
    private Size mTextureSize = null;
    private Size mSurfaceSize = null;
    private boolean textureSizeChanged = false;
    private boolean surfaceSizeChanged = false;
    private boolean mMirror = false;
    protected int mTextureUnit = GLES20.GL_TEXTURE0;

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public BaseFilter(Context c) {

        this.c = c;
        vertexBuffer = createBuffer(squareCoords);
        vertexBuffer2 = createBuffer(squareCoords2);
        vertexBuffer3 = createBuffer(squareCoords3);
        textureBuffer = createBuffer(textureVertices);
        textureBufferMirror = createBuffer(textureVerticesMirror);
        mTextureSize = new Size(640,480);
        mSurfaceSize = new Size(100,100);
        setPath();

        //mRunOnDraw = new LinkedList<>();
    }

    public void setTextureSize(Size textureSize){
//        if ((double)mTextureSize.getWidth()/mTextureSize.getHeight() != (double)textureSize.getWidth()/textureSize.getHeight())
//            textureSizeChanged = true;
//        else
//            textureSizeChanged = false;

        mTextureSize = textureSize;
        textureSizeChanged = true;
    }

    public void setSurfaceSize(Size surfaceSize){

//        if ((double)mSurfaceSize.getWidth()/mSurfaceSize.getHeight() != (double)surfaceSize.getWidth()/surfaceSize.getHeight())
//            surfaceSizeChanged = true;
//        else
//            surfaceSizeChanged = false;

        mSurfaceSize = surfaceSize;
        surfaceSizeChanged = true;

    }

    public abstract void setPath();

    private FloatBuffer createBuffer(float[] vertexData) {
        ByteBuffer byteBuffer = ByteBuffer.allocateDirect(vertexData.length * 4);//要求用allocateDirect()方法,只有ByteBuffer有该方法,so
        byteBuffer.order(ByteOrder.nativeOrder());          //要求nativeOrder  Java 是大端字节序(BigEdian)，
        // 而 OpenGL 所需要的数据是小端字节序(LittleEdian)
        FloatBuffer floatBuffer = byteBuffer.asFloatBuffer();
        floatBuffer.put(vertexData);
        floatBuffer.position(0);
        return floatBuffer;
    }


    /**
     * 创建绘制脚本程序
     */
    public void createProgram() {

        String vertexShaderCode = readRawTextFile(c, path1);
        String fragmentShaderCode = readRawTextFile(c, path2);
        int vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, vertexShaderCode);
        int fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, fragmentShaderCode);
        mProgram = GLES20.glCreateProgram();             // create empty OpenGL ES Program
        GLES20.glAttachShader(mProgram, vertexShader);   // add the vertex shader to program
        GLES20.glAttachShader(mProgram, fragmentShader); // add the fragment shader to program
        GLES20.glLinkProgram(mProgram);                  // creates OpenGL ES program executables

        if (mProgram == 0) {
            throw new RuntimeException("Unable to create program");
        }
        //Log.d("InfowareLab.Debug", "program created");

        //共用句柄
        mGLAttribPosition = GLES20.glGetAttribLocation(mProgram, "aPosition");
        mGLUniformTexture = GLES20.glGetUniformLocation(mProgram, "inputImageTexture");
        mGLAttribTextureCoordinate = GLES20.glGetAttribLocation(mProgram,
                "aTextureCoordinate");
        mHMatrix = GLES20.glGetUniformLocation(mProgram, "uTextureMatrix");
    }

    /**
     * 开始绘制
     */

    public void drawEx(int textureId1, int textureId2, float[] metrix, float[] metrix2) {

        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);

        //if (mSurfaceSize == null || mSurfaceSize.getWidth() <= 1 || mSurfaceSize.getHeight() <= 1)
        //   return;

        GLES20.glUseProgram(mProgram);

        //if (surfaceSizeChanged || textureSizeChanged) updateVertexBuffer();

        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (mMirror) {
            textureBufferMirror.position(0);
            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBufferMirror);
            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
        }
        else {
            textureBuffer.position(0);
            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
        }

        if (textureId1 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId1);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix, 0);

        //onDrawArraysPre();
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        if (mMirror) {
            GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);
            textureBuffer.position(0);
            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
        }

        vertexBuffer2.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer2);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId2 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId2);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix2, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);

        GLES20.glUseProgram(0);

    }

    public void drawEx_record(int textureId1, int textureId2, float[] metrix, float[] metrix2) {

        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);

        //if (mSurfaceSize == null || mSurfaceSize.getWidth() <= 1 || mSurfaceSize.getHeight() <= 1)
        //   return;

        GLES20.glUseProgram(mProgram);

        //if (surfaceSizeChanged || textureSizeChanged) updateVertexBuffer();

        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        textureBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        if (textureId1 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId1);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix, 0);

        //onDrawArraysPre();
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

//        if (mMirror) {
//            GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);
//            textureBuffer.position(0);
//            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
//            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
//        }

        vertexBuffer2.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer2);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId2 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId2);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix2, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);

        GLES20.glUseProgram(0);

    }

    public void drawEx_rotated_record(int textureId1, int textureId2, float[] metrix, float[] metrix2) {

        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);

        //if (mSurfaceSize == null || mSurfaceSize.getWidth() <= 1 || mSurfaceSize.getHeight() <= 1)
        //   return;

        GLES20.glUseProgram(mProgram);

        //if (surfaceSizeChanged || textureSizeChanged) updateVertexBuffer();

        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        textureBufferMirror.position(0);
        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBufferMirror);
        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        if (textureId1 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId1);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix, 0);

        //onDrawArraysPre();
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        if (mMirror) {
            GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);
            textureBuffer.position(0);
            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
        }

        vertexBuffer2.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer2);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId2 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId2);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix2, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);

        GLES20.glUseProgram(0);

    }


    public void drawEx_rotated(int textureId1, int textureId2, float[] metrix, float[] metrix2) {

        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);

        //if (mSurfaceSize == null || mSurfaceSize.getWidth() <= 1 || mSurfaceSize.getHeight() <= 1)
        //   return;

        GLES20.glUseProgram(mProgram);

        //if (surfaceSizeChanged || textureSizeChanged) updateVertexBuffer();

        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        textureBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        if (textureId1 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId1);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix, 0);

        //onDrawArraysPre();
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

//        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);
//        textureBufferMirror.position(0);
//        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBufferMirror);
//        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        vertexBuffer2.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer2);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId2 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId2);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix2, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);

        GLES20.glUseProgram(0);

    }

    /**
     * 开始绘制
     */

    public void drawEx3(int textureId1, int textureId2, int textureId3, float[] metrix, float[] metrix2, float[] metrix3) {

        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);

        //if (mSurfaceSize == null || mSurfaceSize.getWidth() <= 1 || mSurfaceSize.getHeight() <= 1)
        //   return;

        GLES20.glUseProgram(mProgram);

        //if (surfaceSizeChanged || textureSizeChanged) updateVertexBuffer();

        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (mMirror) {
            textureBufferMirror.position(0);
            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBufferMirror);
            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
        }
        else {
            textureBuffer.position(0);
            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
        }

        if (textureId1 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId1);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix, 0);

        //onDrawArraysPre();
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        if (mMirror) {
            GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);
            textureBuffer.position(0);
            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
        }

        vertexBuffer3.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer3);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId3 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId3);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix3, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        vertexBuffer2.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer2);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId2 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId2);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix2, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);

        GLES20.glUseProgram(0);

    }

    public void drawEx3_record(int textureId1, int textureId2, int textureId3, float[] metrix, float[] metrix2, float[] metrix3) {

        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);

        //if (mSurfaceSize == null || mSurfaceSize.getWidth() <= 1 || mSurfaceSize.getHeight() <= 1)
        //   return;

        GLES20.glUseProgram(mProgram);

        //if (surfaceSizeChanged || textureSizeChanged) updateVertexBuffer();

        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        textureBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        if (textureId1 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId1);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix, 0);

        //onDrawArraysPre();
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);
        textureBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        vertexBuffer3.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer3);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId3 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId3);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix3, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        vertexBuffer2.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer2);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId2 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId2);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix2, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);

        GLES20.glUseProgram(0);

    }

    public void drawEx3_rotated_record(int textureId1, int textureId2, int textureId3, float[] metrix, float[] metrix2, float[] metrix3) {

        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);

        //if (mSurfaceSize == null || mSurfaceSize.getWidth() <= 1 || mSurfaceSize.getHeight() <= 1)
        //   return;

        GLES20.glUseProgram(mProgram);

        //if (surfaceSizeChanged || textureSizeChanged) updateVertexBuffer();

        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        textureBufferMirror.position(0);
        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBufferMirror);
        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        if (textureId1 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId1);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix, 0);

        //onDrawArraysPre();
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);
        textureBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        vertexBuffer3.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer3);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId3 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId3);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix3, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        vertexBuffer2.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer2);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId2 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId2);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix2, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);

        GLES20.glUseProgram(0);

    }

    public void drawEx3_rotated(int textureId1, int textureId2, int textureId3, float[] metrix, float[] metrix2, float[] metrix3) {

        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);

        //if (mSurfaceSize == null || mSurfaceSize.getWidth() <= 1 || mSurfaceSize.getHeight() <= 1)
        //   return;

        GLES20.glUseProgram(mProgram);

        //if (surfaceSizeChanged || textureSizeChanged) updateVertexBuffer();

        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        textureBufferMirror.position(0);
        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBufferMirror);
        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        if (textureId1 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId1);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix, 0);

        //onDrawArraysPre();
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        vertexBuffer2.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer2);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId2 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId2);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix2, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);
        textureBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);

        vertexBuffer3.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer3);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (textureId3 != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId3);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, metrix3, 0);

        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);

        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);

        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);

        GLES20.glUseProgram(0);

    }

    public void draw(int textureId, float[] matrix) {

        //GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f);
        //GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);

        //if (mSurfaceSize == null || mSurfaceSize.getWidth() <= 1 || mSurfaceSize.getHeight() <= 1)
        //   return;

        GLES20.glUseProgram(mProgram);

        //if (surfaceSizeChanged || textureSizeChanged) updateVertexBuffer();

        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mGLAttribPosition, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glEnableVertexAttribArray(mGLAttribPosition);

        if (mMirror){
            textureBufferMirror.position(0);
            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBufferMirror);
            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
        }
        else {
            textureBuffer.position(0);
            GLES20.glVertexAttribPointer(mGLAttribTextureCoordinate, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
            GLES20.glEnableVertexAttribArray(mGLAttribTextureCoordinate);
        }

        if (textureId != OpenGlUtils.NO_TEXTURE) {
            //int textureUnit = ConferenceApplication.getTextureUnit();
            GLES20.glActiveTexture(mTextureUnit);
            //Log.d("InfowareLab.Debug", ">>>>>>BaseFilter.textureUnit = " + mTextureUnit);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId);
            GLES20.glUniform1i(mGLUniformTexture, 0);
        }

        GLES20.glUniformMatrix4fv(mHMatrix, 1, false, matrix, 0);

        //onDrawArraysPre();
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
        GLES20.glDisableVertexAttribArray(mGLAttribPosition);
        GLES20.glDisableVertexAttribArray(mGLAttribTextureCoordinate);
        //onDrawArraysAfter();
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0);
        GLES20.glUseProgram(0);

    }

    public void setMirror(boolean mirror){
        if (mMirror == mirror) return;
        mMirror = mirror;

//        if (mirror)
//            textureBuffer = createBuffer(textureVerticesMirror);
//        else
//            textureBuffer = createBuffer(textureVertices);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private void updateVertexBuffer() {

        float textureRatio = (float)mTextureSize.getWidth()/mTextureSize.getHeight();
        Log.d("InfowareLab.Debug", "BaseFilter.updateVertexBuffer: textureRatio="+textureRatio);

        float surfaceRatio = (float)mSurfaceSize.getWidth()/mSurfaceSize.getHeight();
        Log.d("InfowareLab.Debug", "BaseFilter.updateVertexBuffer: surfaceRatio="+textureRatio);

        if (textureRatio > surfaceRatio){

//            private static float squareCoords[] = {
//
//                    -1.0f, -1.0f,   // 0 bottom left
//                    1.0f, -1.0f,   // 1 bottom right
//                    -1.0f, 1.0f,   // 2 top left
//                    1.0f, 1.0f,   // 3 top right
//            };

            float newSurfaceHeight = mTextureSize.getHeight();
            float newSurfaceWidth = newSurfaceHeight*surfaceRatio;

            squareCoords[1] = -1.0f;
            squareCoords[3] = -1.0f;
            squareCoords[5] = 1.0f;
            squareCoords[7] = 1.0f;

            squareCoords[0] = -1.0f;
            squareCoords[2] = 1.0f;
            squareCoords[4] = -1.0f;
            squareCoords[6] = 1.0f;


            squareCoords[0] = squareCoords[0]*mTextureSize.getWidth()/newSurfaceWidth;
            squareCoords[2] = squareCoords[2]*mTextureSize.getWidth()/newSurfaceWidth;
            squareCoords[4] = squareCoords[4]*mTextureSize.getWidth()/newSurfaceWidth;
            squareCoords[6] = squareCoords[6]*mTextureSize.getWidth()/newSurfaceWidth;

            //Log.d("InfowareLab.Debug", "BaseFilter.updateVertexBuffer: squareCoords="+squareCoords.toString());

            textureSizeChanged = false;
            surfaceSizeChanged = false;
        }
        else if (textureRatio < surfaceRatio)
        {
            float newSurfaceWidth = mTextureSize.getWidth();
            float newSurfaceHeight = newSurfaceWidth/surfaceRatio;

            squareCoords[1] = -1.0f;
            squareCoords[3] = -1.0f;
            squareCoords[5] = 1.0f;
            squareCoords[7] = 1.0f;

            squareCoords[0] = -1.0f;
            squareCoords[2] = 1.0f;
            squareCoords[4] = -1.0f;
            squareCoords[6] = 1.0f;

            squareCoords[1] = squareCoords[1]*mTextureSize.getHeight()/newSurfaceHeight;
            squareCoords[3] = squareCoords[3]*mTextureSize.getHeight()/newSurfaceHeight;
            squareCoords[5] = squareCoords[5]*mTextureSize.getHeight()/newSurfaceHeight;
            squareCoords[7] = squareCoords[7]*mTextureSize.getHeight()/newSurfaceHeight;
            //Log.d("InfowareLab.Debug", "BaseFilter.updateVertexBuffer: squareCoords="+squareCoords.toString());

            textureSizeChanged = false;
            surfaceSizeChanged = false;
        }
        else
        {
            Log.d("InfowareLab.Debug", "BaseFilter.updateVertexBuffer: No need to change");
            textureSizeChanged = false;
            surfaceSizeChanged = false;

            return;
        }

        vertexBuffer = createBuffer(squareCoords);
    }

    protected abstract void onDrawArraysPre();

    protected abstract void onDrawArraysAfter();

    public void releaseProgram() {

        Log.d("InfowareLab.Debug", "deleting program " + mProgram);
        GLES20.glDeleteProgram(mProgram);
        mProgram = 0;

    }

    /**
     * 注意此处一定要用runondraw,因为要在useprogram之后执行
     *
     * @param location
     * @param floatValue
     */

    protected void setFloat(final int location, final float floatValue) {

//        runOnDraw(new Runnable() {
//            @Override
//            public void run() {
//
//                GLES20.glUniform1f(location, floatValue);
//            }
//        });


    }

    protected void setFloatVec2(final int location, final float[] arrayValue) {

//        runOnDraw(new Runnable() {
//            @Override
//            public void run() {
//                GLES20.glUniform2fv(location, 1, FloatBuffer.wrap(arrayValue));
//            }
//        });


    }


    protected void setFloatVec4(final int location, final float[] arrayValue) {

//        runOnDraw(new Runnable() {
//            @Override
//            public void run() {
//                GLES20.glUniform4fv(location, 1, FloatBuffer.wrap(arrayValue));
//            }
//        });

    }


    protected void setInteger(final int location, final int intValue) {

//        runOnDraw(new Runnable() {
//            @Override
//            public void run() {
//                GLES20.glUniform1i(location, intValue);
//            }
//        });

    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void onInputSizeChanged(final int width, final int height) {

        setSurfaceSize(new Size(width, height));
    }

    private int loadShader(int type, String shaderCode) {

        int shader = GLES20.glCreateShader(type);

        // add the source code to the shader and compile it
        GLES20.glShaderSource(shader, shaderCode);
        GLES20.glCompileShader(shader);

        return shader;
    }


    private String readRawTextFile(Context context, int rawId) {

        InputStream is = context.getResources().openRawResource(rawId);
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        String line;
        StringBuilder sb = new StringBuilder();

        try {
            while ((line = br.readLine()) != null) {
                sb.append(line);
                sb.append("\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            br.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return sb.toString();

    }

    /**
     * 绑定纹理
     *
     * @return
     */

    public static int bindTexture() {
        int[] texture = new int[1];

        GLES20.glGenTextures(1, texture, 0);
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, texture[0]);

        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MIN_FILTER,
                GLES20.GL_NEAREST_MIPMAP_LINEAR);
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MAG_FILTER,
                GLES20.GL_LINEAR_MIPMAP_LINEAR);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_S,
                GLES20.GL_CLAMP_TO_EDGE);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_T,
                GLES20.GL_CLAMP_TO_EDGE);

        return texture[0];
    }

    public static int[] bindTextureEx(int count) {
        int[] texture = new int[count];

        GLES20.glGenTextures(1, texture, 0);
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, texture[0]);

        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MIN_FILTER,
                GLES20.GL_NEAREST);
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MAG_FILTER,
                GLES20.GL_LINEAR);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_S,
                GLES20.GL_CLAMP_TO_EDGE);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_T,
                GLES20.GL_CLAMP_TO_EDGE);

        return texture;
    }

    //private final LinkedList<Runnable> mRunOnDraw;

//    protected void runPendingOnDrawTasks() {
//        while (!mRunOnDraw.isEmpty()) {
//            mRunOnDraw.removeFirst().run();
//        }
//    }
//
//    protected void runOnDraw(final Runnable runnable) {
//        synchronized (mRunOnDraw) {
//            mRunOnDraw.addLast(runnable);
//        }
//    }
}
