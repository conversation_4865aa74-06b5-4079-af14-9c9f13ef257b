    lintOptions {
      abortOnError false
    }
    signingConfigs {
        release {
	    //always's resource loading
	    v1SigningEnabled true
            v2SigningEnabled true
            
            //resource loading
            Properties properties = new Properties()
            InputStream inputStream = project.rootProject.file('local.properties').newDataInputStream() ;
            properties.load( inputStream )

            //File load
            def keyStore = properties.getProperty('key.store')
            storeFile file( keyStore )

            //Text load
            def key_keyAlias = properties.getProperty( 'key.alias' )
            def key_keyPassword = properties.getProperty( 'key.alias.password' ) ;
            def key_storePassword = properties.getProperty( 'key.store.password' ) ;

            storePassword  key_storePassword
            keyAlias  key_keyAlias
            keyPassword  key_keyPassword
        }
     }
     buildTypes {
        debug {
            signingConfig signingConfigs.release
        }
        release {
            signingConfig signingConfigs.release
        }
     }
